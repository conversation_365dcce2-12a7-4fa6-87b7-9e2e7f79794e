/**
 * CrazyStorm 2.0 Integration for Cocos Creator
 * File Loader and Cache Manager
 * 
 * This class handles loading .bgp files, caching parsed data,
 * and providing efficient access to CrazyStorm content.
 */

import { JsonAsset, TextAsset, Asset, resources, error, warn, log } from 'cc';
import { CrazyStormFile, ParticleSystem, ParserConfig } from './CrazyStormTypes';
import { CrazyStormParser } from './CrazyStormParser';
import { CrazyStormBinaryParser } from './BinaryParser';

/**
 * Cache entry for loaded CrazyStorm files
 */
interface CacheEntry {
    file: CrazyStormFile;
    loadTime: number;
    accessCount: number;
    lastAccess: number;
}

/**
 * Loading options for CrazyStorm files
 */
export interface LoadOptions {
    useCache?: boolean;
    parserConfig?: ParserConfig;
    preloadResources?: boolean;
}

/**
 * CrazyStorm file loader with caching and resource management
 */
export class CrazyStormLoader {
    private static instance: CrazyStormLoader;
    private cache: Map<string, CacheEntry> = new Map();
    private xmlParser: CrazyStormParser;
    private binaryParser: CrazyStormBinaryParser;
    private maxCacheSize: number = 50;
    private cacheTimeout: number = 300000; // 5 minutes

    private constructor() {
        this.xmlParser = new CrazyStormParser();
        this.binaryParser = new CrazyStormBinaryParser();
    }

    /**
     * Get singleton instance
     */
    public static getInstance(): CrazyStormLoader {
        if (!CrazyStormLoader.instance) {
            CrazyStormLoader.instance = new CrazyStormLoader();
        }
        return CrazyStormLoader.instance;
    }

    /**
     * Load a CrazyStorm file from resources
     */
    public async loadFile(path: string, options: LoadOptions = {}): Promise<CrazyStormFile | null> {
        const {
            useCache = true,
            parserConfig = {
                validateStructure: true,
                optimizeForMobile: true,
                debugMode: false
            },
            preloadResources = true
        } = options;

        // Check cache first
        if (useCache && this.cache.has(path)) {
            const entry = this.cache.get(path)!;
            entry.accessCount++;
            entry.lastAccess = Date.now();
            log(`CrazyStormLoader: Loaded from cache: ${path}`);
            return entry.file;
        }

        try {
            // Determine file type and load accordingly
            const isBinaryFile = path.endsWith('.bg');
            let file: CrazyStormFile | null = null;

            if (isBinaryFile) {
                // Load binary file
                const binaryAsset = await this.loadBinaryAsset(path);
                if (!binaryAsset) {
                    error(`CrazyStormLoader: Failed to load binary asset: ${path}`);
                    return null;
                }

                // Parse the binary content
                file = this.binaryParser.parse(binaryAsset);
            } else {
                // Load XML file (.bgp)
                const textAsset = await this.loadTextAsset(path);
                if (!textAsset) {
                    error(`CrazyStormLoader: Failed to load text asset: ${path}`);
                    return null;
                }

                // Parse the XML content
                this.xmlParser = new CrazyStormParser(parserConfig);
                file = this.xmlParser.parse(textAsset.text);
            }

            if (!file) {
                error(`CrazyStormLoader: Failed to parse file: ${path}`);
                return null;
            }

            // Preload referenced resources if requested
            if (preloadResources) {
                await this.preloadFileResources(file, path);
            }

            // Cache the result
            if (useCache) {
                this.addToCache(path, file);
            }

            log(`CrazyStormLoader: Successfully loaded and parsed: ${path}`);
            return file;

        } catch (err) {
            error(`CrazyStormLoader: Error loading file ${path}:`, err);
            return null;
        }
    }

    /**
     * Load a specific particle system by name from a file
     */
    public async loadParticleSystem(filePath: string, systemName: string, options: LoadOptions = {}): Promise<ParticleSystem | null> {
        const file = await this.loadFile(filePath, options);
        if (!file) {
            return null;
        }

        const system = file.particleSystems.find(ps => ps.name === systemName);
        if (!system) {
            warn(`CrazyStormLoader: Particle system '${systemName}' not found in file: ${filePath}`);
            return null;
        }

        return system;
    }

    /**
     * Load multiple files in parallel
     */
    public async loadMultipleFiles(paths: string[], options: LoadOptions = {}): Promise<Map<string, CrazyStormFile | null>> {
        const results = new Map<string, CrazyStormFile | null>();
        
        const loadPromises = paths.map(async (path) => {
            const file = await this.loadFile(path, options);
            results.set(path, file);
        });

        await Promise.all(loadPromises);
        return results;
    }

    /**
     * Preload all particle systems from a directory
     */
    public async preloadDirectory(directoryPath: string, options: LoadOptions = {}): Promise<void> {
        try {
            // This would need to be implemented based on how Cocos Creator handles directory listing
            // For now, we'll assume a manifest file or known file list
            warn('CrazyStormLoader: Directory preloading not yet implemented');
        } catch (err) {
            error('CrazyStormLoader: Failed to preload directory:', err);
        }
    }

    /**
     * Get cache statistics
     */
    public getCacheStats(): { size: number; totalAccess: number; hitRate: number } {
        let totalAccess = 0;
        this.cache.forEach(entry => {
            totalAccess += entry.accessCount;
        });

        return {
            size: this.cache.size,
            totalAccess,
            hitRate: this.cache.size > 0 ? totalAccess / this.cache.size : 0
        };
    }

    /**
     * Clear cache
     */
    public clearCache(): void {
        this.cache.clear();
        log('CrazyStormLoader: Cache cleared');
    }

    /**
     * Remove expired entries from cache
     */
    public cleanupCache(): void {
        const now = Date.now();
        const expiredKeys: string[] = [];

        this.cache.forEach((entry, key) => {
            if (now - entry.lastAccess > this.cacheTimeout) {
                expiredKeys.push(key);
            }
        });

        expiredKeys.forEach(key => {
            this.cache.delete(key);
        });

        if (expiredKeys.length > 0) {
            log(`CrazyStormLoader: Cleaned up ${expiredKeys.length} expired cache entries`);
        }
    }

    /**
     * Set cache configuration
     */
    public setCacheConfig(maxSize: number, timeoutMs: number): void {
        this.maxCacheSize = maxSize;
        this.cacheTimeout = timeoutMs;
    }

    // Private helper methods

    /**
     * Load text asset from resources
     */
    private loadTextAsset(path: string): Promise<TextAsset | null> {
        return new Promise((resolve) => {
            resources.load(path, TextAsset, (err, asset) => {
                if (err) {
                    error(`CrazyStormLoader: Failed to load text asset: ${path}`, err);
                    resolve(null);
                } else {
                    resolve(asset);
                }
            });
        });
    }

    /**
     * Load binary asset from resources
     */
    private loadBinaryAsset(path: string): Promise<ArrayBuffer | null> {
        return new Promise((resolve) => {
            resources.load(path, Asset, (err, asset) => {
                if (err) {
                    error(`CrazyStormLoader: Failed to load binary asset: ${path}`, err);
                    resolve(null);
                } else {
                    // Convert asset to ArrayBuffer
                    // This might need adjustment based on how Cocos Creator handles binary assets
                    if (asset && (asset as any)._nativeAsset) {
                        const nativeAsset = (asset as any)._nativeAsset;
                        if (nativeAsset instanceof ArrayBuffer) {
                            resolve(nativeAsset);
                        } else {
                            // Try to convert to ArrayBuffer
                            try {
                                const buffer = this.convertToArrayBuffer(nativeAsset);
                                resolve(buffer);
                            } catch (convertErr) {
                                error(`CrazyStormLoader: Failed to convert asset to ArrayBuffer: ${path}`, convertErr);
                                resolve(null);
                            }
                        }
                    } else {
                        error(`CrazyStormLoader: Asset has no native data: ${path}`);
                        resolve(null);
                    }
                }
            });
        });
    }

    /**
     * Convert various data types to ArrayBuffer
     */
    private convertToArrayBuffer(data: any): ArrayBuffer {
        if (data instanceof ArrayBuffer) {
            return data;
        } else if (data instanceof Uint8Array) {
            return data.buffer.slice(data.byteOffset, data.byteOffset + data.byteLength);
        } else if (typeof data === 'string') {
            // If it's a base64 string or similar, decode it
            const encoder = new TextEncoder();
            return encoder.encode(data).buffer;
        } else {
            throw new Error('Unsupported data type for ArrayBuffer conversion');
        }
    }

    /**
     * Preload file resources (images, sounds) referenced in the CrazyStorm file
     */
    private async preloadFileResources(file: CrazyStormFile, basePath: string): Promise<void> {
        const baseDir = basePath.substring(0, basePath.lastIndexOf('/'));
        
        // Preload images
        const imagePromises = file.images.map(async (image) => {
            const imagePath = `${baseDir}/${image.relativePath}`;
            try {
                await this.loadImageResource(imagePath);
            } catch (err) {
                warn(`CrazyStormLoader: Failed to preload image: ${imagePath}`, err);
            }
        });

        // Preload sounds
        const soundPromises = file.sounds.map(async (sound) => {
            const soundPath = `${baseDir}/${sound.relativePath}`;
            try {
                await this.loadSoundResource(soundPath);
            } catch (err) {
                warn(`CrazyStormLoader: Failed to preload sound: ${soundPath}`, err);
            }
        });

        await Promise.all([...imagePromises, ...soundPromises]);
    }

    /**
     * Load image resource
     */
    private loadImageResource(path: string): Promise<void> {
        return new Promise((resolve, reject) => {
            resources.load(path, (err, asset) => {
                if (err) {
                    reject(err);
                } else {
                    resolve();
                }
            });
        });
    }

    /**
     * Load sound resource
     */
    private loadSoundResource(path: string): Promise<void> {
        return new Promise((resolve, reject) => {
            resources.load(path, (err, asset) => {
                if (err) {
                    reject(err);
                } else {
                    resolve();
                }
            });
        });
    }

    /**
     * Add file to cache
     */
    private addToCache(path: string, file: CrazyStormFile): void {
        // Remove oldest entries if cache is full
        if (this.cache.size >= this.maxCacheSize) {
            this.removeOldestCacheEntry();
        }

        const entry: CacheEntry = {
            file,
            loadTime: Date.now(),
            accessCount: 1,
            lastAccess: Date.now()
        };

        this.cache.set(path, entry);
    }

    /**
     * Remove the oldest cache entry
     */
    private removeOldestCacheEntry(): void {
        let oldestKey: string | null = null;
        let oldestTime = Date.now();

        this.cache.forEach((entry, key) => {
            if (entry.lastAccess < oldestTime) {
                oldestTime = entry.lastAccess;
                oldestKey = key;
            }
        });

        if (oldestKey) {
            this.cache.delete(oldestKey);
        }
    }
}

/**
 * Convenience function to get the loader instance
 */
export function getCrazyStormLoader(): CrazyStormLoader {
    return CrazyStormLoader.getInstance();
}
