Perfect! Thank you for the detailed answers. Now I have a clear understanding of your requirements. Let's move to **Step 2: Design**.

## Level Editor Design Document

### Project Structure

```
LevelEditor/
├── assets/
│   ├── scripts/
│   │   ├── core/
│   │   │   ├── LevelData.ts              # Core level data structures
│   │   │   ├── LevelSerializer.ts        # JSON/Binary serialization
│   │   │   └── LevelValidator.ts         # Level validation logic
│   │   ├── editor/
│   │   │   ├── LevelEditor.ts            # Main editor component
│   │   │   ├── EditorGizmos.ts           # Scene view gizmos and tools
│   │   │   ├── PathEditor.ts             # Bezier path editing
│   │   │   ├── SpawnerEditor.ts          # Spawner configuration
│   │   │   ├── BackgroundEditor.ts       # Background layer management
│   │   │   └── PrefabSelector.ts         # Prefab selection UI
│   │   ├── runtime/
│   │   │   ├── LevelPlayer.ts            # Level playback/preview
│   │   │   ├── PathFollower.ts           # Entity path following
│   │   │   └── SpawnerSystem.ts          # Runtime spawning logic
│   │   ├── events/
│   │   │   ├── EventNode.ts              # Base event node
│   │   │   ├── EventGraph.ts             # Event graph container
│   │   │   ├── EventEditor.ts            # Visual node editor
│   │   │   └── EventTypes.ts             # Specific event implementations
│   │   └── ui/
│   │       ├── LevelEditorPanel.ts       # Main editor panel
│   │       ├── LayerPanel.ts             # Layer management panel
│   │       ├── TimelinePanel.ts          # Timeline view (optional)
│   │       └── PropertyPanel.ts          # Property inspector
│   ├── prefabs/
│   │   └── LevelEditorPrefab.prefab      # Main editor prefab
│   └── scenes/
│       └── LevelEditorScene.scene        # Editor scene
```

### Core Classes and Interfaces

#### 1. Core Data Structures

```typescript
// LevelData.ts
interface ILevelData {
    metadata: LevelMetadata;
    backgrounds: BackgroundLayer[];
    spawners: Spawner[];
    paths: Path[];
    events: EventGraph;
    staticEntities: StaticEntity[];
}

interface LevelMetadata {
    name: string;
    version: string;
    duration: number;
    difficulty: number;
    description?: string;
}

interface BackgroundLayer {
    id: string;
    prefabPath: string;
    depth: number;
    scrollSpeed: Vec2;
    repeatMode: 'none' | 'horizontal' | 'vertical' | 'both';
    offset: Vec2;
}

interface Spawner {
    id: string;
    position: Vec3;
    prefabPath: string;
    spawnPattern: SpawnPattern;
    pathId?: string;
    isActive: boolean;
    waves: Wave[];
}

interface Path {
    id: string;
    name: string;
    points: BezierPoint[];
    isLoop: boolean;
    totalLength: number;
}

interface BezierPoint {
    position: Vec3;
    controlPoint1: Vec3;
    controlPoint2: Vec3;
}
```

#### 2. Editor Components

```typescript
// LevelEditor.ts
@ccclass('LevelEditor')
export class LevelEditor extends Component {
    // Main editor controller
    public loadLevel(path: string): void;
    public saveLevel(path: string): void;
    public createNewLevel(): void;
    public previewLevel(): void;
    public stopPreview(): void;
    
    // Editor state management
    public setEditMode(mode: EditMode): void;
    public getSelectedObjects(): any[];
    public selectObject(obj: any): void;
}

enum EditMode {
    Select,
    AddSpawner,
    EditPath,
    AddBackground,
    EditEvents
}

// EditorGizmos.ts
@ccclass('EditorGizmos')
export class EditorGizmos extends Component {
    public drawPathGizmos(path: Path): void;
    public drawSpawnerGizmos(spawner: Spawner): void;
    public drawBackgroundGizmos(layer: BackgroundLayer): void;
    public handleGizmoInteraction(event: Event): void;
}

// PathEditor.ts
@ccclass('PathEditor')
export class PathEditor extends Component {
    public createPath(startPosition: Vec3): Path;
    public addPoint(path: Path, position: Vec3): void;
    public removePoint(path: Path, index: number): void;
    public updateControlPoint(path: Path, pointIndex: number, controlIndex: number, position: Vec3): void;
    public calculatePathLength(path: Path): number;
}
```

#### 3. Event System

```typescript
// EventNode.ts
interface IEventNode {
    // Runtime properties (serialized)
    id: string;
    type: string;
    properties: { [key: string]: any };
    inputs: EventConnection[];
    outputs: EventConnection[];
    
    // Editor properties (not serialized)
    editorData?: {
        position: Vec2;
        name: string;
        color: Color;
        collapsed: boolean;
    };
    
    execute(context: EventContext): void;
    validate(): boolean;
}

interface EventConnection {
    nodeId: string;
    portName: string;
}

// EventGraph.ts
@ccclass('EventGraph')
export class EventGraph {
    public nodes: Map<string, IEventNode>;
    public connections: EventConnection[];
    
    public addNode(node: IEventNode): void;
    public removeNode(nodeId: string): void;
    public connectNodes(outputNode: string, outputPort: string, inputNode: string, inputPort: string): void;
    public execute(triggerType: string, context: EventContext): void;
}
```

#### 4. Serialization System

```typescript
// LevelSerializer.ts
interface ILevelSerializer {
    serialize(levelData: ILevelData): string | ArrayBuffer;
    deserialize(data: string | ArrayBuffer): ILevelData;
    getFormat(): 'json' | 'binary';
}

@ccclass('JsonLevelSerializer')
export class JsonLevelSerializer implements ILevelSerializer {
    serialize(levelData: ILevelData): string;
    deserialize(data: string): ILevelData;
    getFormat(): 'json';
}

@ccclass('BinaryLevelSerializer')
export class BinaryLevelSerializer implements ILevelSerializer {
    serialize(levelData: ILevelData): ArrayBuffer;
    deserialize(data: ArrayBuffer): ILevelData;
    getFormat(): 'binary';
}
```

#### 5. Runtime System

```typescript
// LevelPlayer.ts
@ccclass('LevelPlayer')
export class LevelPlayer extends Component {
    public loadLevel(levelData: ILevelData): void;
    public play(): void;
    public pause(): void;
    public stop(): void;
    public setTime(time: number): void;
    
    private updateSpawners(deltaTime: number): void;
    private updateBackgrounds(deltaTime: number): void;
    private processEvents(deltaTime: number): void;
}

// SpawnerSystem.ts
@ccclass('SpawnerSystem')
export class SpawnerSystem extends Component {
    public registerSpawner(spawner: Spawner): void;
    public unregisterSpawner(spawnerId: string): void;
    public updateSpawners(deltaTime: number): void;
    public spawnEntity(spawner: Spawner, waveIndex: number, entityIndex: number): Node;
}
```

### Key Design Decisions

1. **Separation of Runtime and Editor Data**: Event nodes have both runtime properties and editor-only data to optimize performance.

2. **Extensible Serialization**: Interface-based serialization system allows easy addition of binary format later.

3. **Component-Based Architecture**: Following Cocos Creator patterns with component-based design.

4. **Gizmo-Based Editing**: Scene view editing through gizmos rather than separate windows.

5. **Path System**: Bezier curves with visual handles for smooth entity movement paths.

6. **Event Graph**: Node-based event system with runtime optimization in mind.

### Editor Workflow

1. **Level Creation**: Create new level → Set metadata → Add backgrounds → Place spawners → Create paths → Configure events
2. **Path Editing**: Select path tool → Click to add points → Drag handles to adjust curves
3. **Spawner Configuration**: Place spawner → Select prefab → Configure waves → Assign path (optional)
4. **Event Setup**: Open event editor → Add nodes → Connect nodes → Configure properties
5. **Preview**: Play level in editor → Test and iterate → Save level

Does this design align with your vision? Any aspects you'd like me to modify or expand upon before we move to implementation?
