import { _decorator, Component, Node, Vec3 } from 'cc';
import { Path, BezierPoint } from '../core/LevelData';
import { PathUtils } from '../core/Types';

const { ccclass, property } = _decorator;

/**
 * Path follower component for entities moving along paths
 */
@ccclass('PathFollower')
export class PathFollower extends Component {
    @property
    public speed: number = 100; // Units per second
    
    @property
    public autoStart: boolean = true;
    
    @property
    public loop: boolean = false;
    
    // Path data
    private path: Path | null = null;
    private currentTime: number = 0;
    private totalDuration: number = 0;
    private isPlaying: boolean = false;
    private isPaused: boolean = false;
    
    // Current state
    private currentSegment: number = 0;
    private segmentProgress: number = 0;
    private currentPosition: Vec3 = Vec3.ZERO;
    private currentRotation: number = 0;
    private currentSpeed: number = 0;
    
    // Events
    public onPathStarted?: (path: Path) => void;
    public onPathCompleted?: (path: Path) => void;
    public onPointReached?: (point: BezierPoint, index: number) => void;
    
    protected start(): void {
        if (this.autoStart && this.path) {
            this.play();
        }
    }
    
    protected update(deltaTime: number): void {
        if (this.isPlaying && !this.isPaused && this.path) {
            this.updateMovement(deltaTime);
        }
    }
    
    /**
     * Set the path to follow
     */
    public setPath(path: Path): void {
        this.path = path;
        this.calculateTotalDuration();
        this.reset();
    }
    
    /**
     * Start following the path
     */
    public play(): void {
        if (!this.path) {
            console.warn('PathFollower: No path assigned');
            return;
        }
        
        this.isPlaying = true;
        this.isPaused = false;
        this.onPathStarted?.(this.path);
    }
    
    /**
     * Pause path following
     */
    public pause(): void {
        this.isPaused = true;
    }
    
    /**
     * Resume path following
     */
    public resume(): void {
        this.isPaused = false;
    }
    
    /**
     * Stop and reset path following
     */
    public stop(): void {
        this.isPlaying = false;
        this.isPaused = false;
        this.reset();
    }
    
    /**
     * Reset to beginning of path
     */
    public reset(): void {
        this.currentTime = 0;
        this.currentSegment = 0;
        this.segmentProgress = 0;
        
        if (this.path && this.path.points.length > 0) {
            this.currentPosition = this.path.points[0].position.clone();
            this.currentRotation = this.path.points[0].rotation;
            this.currentSpeed = this.path.points[0].speed;
            this.updateNodeTransform();
        }
    }
    
    /**
     * Set progress along path (0-1)
     */
    public setProgress(progress: number): void {
        if (!this.path) return;
        
        progress = Math.max(0, Math.min(1, progress));
        this.currentTime = progress * this.totalDuration;
        this.updatePositionFromTime();
    }
    
    /**
     * Get current progress (0-1)
     */
    public getProgress(): number {
        if (this.totalDuration <= 0) return 0;
        return this.currentTime / this.totalDuration;
    }
    
    /**
     * Update movement along path
     */
    private updateMovement(deltaTime: number): void {
        if (!this.path || this.path.points.length < 2) return;
        
        // Update time based on current speed
        this.currentTime += deltaTime;
        
        // Check for completion
        if (this.currentTime >= this.totalDuration) {
            if (this.loop) {
                this.currentTime = 0;
            } else {
                this.currentTime = this.totalDuration;
                this.isPlaying = false;
                this.onPathCompleted?.(this.path);
                return;
            }
        }
        
        // Update position based on current time
        this.updatePositionFromTime();
        this.updateNodeTransform();
    }
    
    /**
     * Update position based on current time
     */
    private updatePositionFromTime(): void {
        if (!this.path || this.path.points.length < 2) return;
        
        // Find current segment
        let accumulatedTime = 0;
        let segmentIndex = 0;
        
        for (let i = 0; i < this.path.points.length - 1; i++) {
            const segmentDuration = this.getSegmentDuration(i);
            
            if (accumulatedTime + segmentDuration >= this.currentTime) {
                segmentIndex = i;
                this.segmentProgress = (this.currentTime - accumulatedTime) / segmentDuration;
                break;
            }
            
            accumulatedTime += segmentDuration;
        }
        
        this.currentSegment = segmentIndex;
        
        // Calculate position on current segment
        this.calculateSegmentPosition(segmentIndex, this.segmentProgress);
    }
    
    /**
     * Calculate position on a specific segment
     */
    private calculateSegmentPosition(segmentIndex: number, progress: number): void {
        if (!this.path || segmentIndex >= this.path.points.length - 1) return;
        
        const point1 = this.path.points[segmentIndex];
        const point2 = this.path.points[segmentIndex + 1];
        
        // Calculate bezier curve position
        const p0 = point1.position;
        const p1 = point1.position.clone().add(point1.controlPoint2);
        const p2 = point2.position.clone().add(point2.controlPoint1);
        const p3 = point2.position;
        
        this.currentPosition = PathUtils.calculateBezierPoint(p0, p1, p2, p3, progress);
        
        // Interpolate speed and rotation
        this.currentSpeed = this.lerp(point1.speed, point2.speed, progress);
        this.currentRotation = this.lerpAngle(point1.rotation, point2.rotation, progress);
    }
    
    /**
     * Calculate total duration for the path
     */
    private calculateTotalDuration(): void {
        if (!this.path || this.path.points.length < 2) {
            this.totalDuration = 0;
            return;
        }
        
        let totalDuration = 0;
        
        for (let i = 0; i < this.path.points.length - 1; i++) {
            totalDuration += this.getSegmentDuration(i);
        }
        
        this.totalDuration = totalDuration;
    }
    
    /**
     * Get duration for a specific segment
     */
    private getSegmentDuration(segmentIndex: number): number {
        if (!this.path || segmentIndex >= this.path.points.length - 1) return 0;
        
        const point1 = this.path.points[segmentIndex];
        const point2 = this.path.points[segmentIndex + 1];
        
        // Calculate segment length
        const p0 = point1.position;
        const p1 = point1.position.clone().add(point1.controlPoint2);
        const p2 = point2.position.clone().add(point2.controlPoint1);
        const p3 = point2.position;
        
        const segmentLength = PathUtils.calculateBezierLength(p0, p1, p2, p3);
        
        // Calculate average speed for this segment
        const avgSpeed = (point1.speed + point2.speed) / 2;
        
        return avgSpeed > 0 ? segmentLength / avgSpeed : 0;
    }
    
    /**
     * Update node transform based on current position and rotation
     */
    private updateNodeTransform(): void {
        this.node.position = this.currentPosition;
        
        // Apply rotation if needed
        if (this.currentRotation !== 0) {
            this.node.angle = this.currentRotation;
        }
    }
    
    /**
     * Linear interpolation
     */
    private lerp(a: number, b: number, t: number): number {
        return a + (b - a) * t;
    }
    
    /**
     * Angle interpolation (handles wrapping)
     */
    private lerpAngle(a: number, b: number, t: number): number {
        const diff = b - a;
        const wrappedDiff = ((diff + 180) % 360) - 180;
        return a + wrappedDiff * t;
    }
    
    // Getters
    public getCurrentPosition(): Vec3 {
        return this.currentPosition.clone();
    }
    
    public getCurrentSpeed(): number {
        return this.currentSpeed;
    }
    
    public getCurrentRotation(): number {
        return this.currentRotation;
    }
    
    public isPathPlaying(): boolean {
        return this.isPlaying && !this.isPaused;
    }
}
