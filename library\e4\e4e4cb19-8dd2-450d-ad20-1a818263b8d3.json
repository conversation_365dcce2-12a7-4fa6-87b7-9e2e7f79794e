{"__type__": "cc.EffectAsset", "_name": "internal/editor/light", "_objFlags": 0, "__editorExtras__": {}, "_native": "", "techniques": [{"passes": [{"rasterizerState": {"cullMode": 0}, "blendState": {"targets": [{"blend": true, "blendSrc": 2, "blendDst": 4, "blendDstAlpha": 4}]}, "program": "internal/editor/light|light-vs:vert|light-fs:frag", "depthStencilState": {"depthTest": true, "depthWrite": false}}]}], "shaders": [{"blocks": [{"name": "Constant", "members": [{"name": "mainColor", "type": 16, "count": 1}, {"name": "intensitySize", "type": 16, "count": 1}], "defines": [], "stageFlags": 16, "binding": 0}], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": [], "attributes": [{"name": "a_position", "defines": [], "format": 32, "location": 0}, {"name": "a_texCoord", "defines": [], "format": 21, "location": 1}], "varyings": [{"name": "v_uv", "type": 14, "count": 1, "defines": [], "stageFlags": 17, "location": 0}], "fragColors": [{"name": "cc_FragColor", "typename": "vec4", "type": 16, "count": 1, "defines": [], "stageFlags": 16, "location": 0}], "descriptors": [{"rate": 0, "blocks": [{"tags": {"builtin": "local"}, "name": "CCLocal", "members": [{"name": "cc_matWorld", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matWorldIT", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_lightingMapUVParam", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_localShadowBias", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_reflectionProbeData1", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_reflectionProbeData2", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_reflectionProbeBlendData1", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_reflectionProbeBlendData2", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}], "defines": [], "stageFlags": 1}], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}, {"rate": 1, "blocks": [{"name": "Constant", "members": [{"name": "mainColor", "type": 16, "count": 1}, {"name": "intensitySize", "type": 16, "count": 1}], "defines": [], "stageFlags": 16, "binding": 0}], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}, {"rate": 2, "blocks": [], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}, {"rate": 3, "blocks": [{"tags": {"builtin": "global"}, "name": "CCGlobal", "members": [{"name": "cc_time", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_screenSize", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_nativeSize", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_probeInfo", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_debug_view_mode", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}], "defines": [], "stageFlags": 17}, {"tags": {"builtin": "global"}, "name": "CCCamera", "members": [{"name": "cc_matView", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matViewInv", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matProj", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matProjInv", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matViewProj", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matViewProjInv", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_cameraPos", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_surfaceTransform", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_screenScale", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_exposure", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_mainLitDir", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_mainLitColor", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_ambientSky", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_ambientGround", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_fogColor", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_fogBase", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_fogAdd", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_nearFar", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_viewPort", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}], "defines": [], "stageFlags": 17}], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}], "hash": 1133658448, "glsl4": {"vert": "\nprecision mediump float;\nlayout(set = 2, binding = 0) uniform CCLocal {\n  highp mat4 cc_matWorld;\n  highp mat4 cc_matWorldIT;\n  highp vec4 cc_lightingMapUVParam;\n  highp vec4 cc_localShadowBias;\n  highp vec4 cc_reflectionProbeData1;\n  highp vec4 cc_reflectionProbeData2;\n  highp vec4 cc_reflectionProbeBlendData1;\n  highp vec4 cc_reflectionProbeBlendData2;\n};\nlayout(set = 0, binding = 0) uniform CCGlobal {\n  highp   vec4 cc_time;\n  mediump vec4 cc_screenSize;\n  mediump vec4 cc_nativeSize;\n  mediump vec4 cc_probeInfo;\n  mediump vec4 cc_debug_view_mode;\n};\nlayout(set = 0, binding = 1) uniform CCCamera {\n  highp   mat4 cc_matView;\n  highp   mat4 cc_matViewInv;\n  highp   mat4 cc_matProj;\n  highp   mat4 cc_matProjInv;\n  highp   mat4 cc_matViewProj;\n  highp   mat4 cc_matViewProjInv;\n  highp   vec4 cc_cameraPos;\n  mediump vec4 cc_surfaceTransform;\n  mediump vec4 cc_screenScale;\n  mediump vec4 cc_exposure;\n  mediump vec4 cc_mainLitDir;\n  mediump vec4 cc_mainLitColor;\n  mediump vec4 cc_ambientSky;\n  mediump vec4 cc_ambientGround;\n  mediump vec4 cc_fogColor;\n  mediump vec4 cc_fogBase;\n  mediump vec4 cc_fogAdd;\n  mediump vec4 cc_nearFar;\n  mediump vec4 cc_viewPort;\n};\n#define QUATER_PI         0.78539816340\n#define HALF_PI           1.57079632679\n#define PI                3.14159265359\n#define PI2               6.28318530718\n#define PI4               12.5663706144\n#define INV_QUATER_PI     1.27323954474\n#define INV_HALF_PI       0.63661977237\n#define INV_PI            0.31830988618\n#define INV_PI2           0.15915494309\n#define INV_PI4           0.07957747155\n#define EPSILON           1e-6\n#define EPSILON_LOWP      1e-4\n#define LOG2              1.442695\n#define EXP_VALUE         2.71828183\n#define FP_MAX            65504.0\n#define FP_SCALE          0.0009765625\n#define FP_SCALE_INV      1024.0\n#define GRAY_VECTOR       vec3(0.299, 0.587, 0.114)\n#define LIGHT_MAP_TYPE_DISABLED 0\n#define LIGHT_MAP_TYPE_ALL_IN_ONE 1\n#define LIGHT_MAP_TYPE_INDIRECT_OCCLUSION 2\n#define REFLECTION_PROBE_TYPE_NONE 0\n#define REFLECTION_PROBE_TYPE_CUBE 1\n#define REFLECTION_PROBE_TYPE_PLANAR 2\n#define REFLECTION_PROBE_TYPE_BLEND 3\n#define REFLECTION_PROBE_TYPE_BLEND_AND_SKYBOX 4\n#define LIGHT_TYPE_DIRECTIONAL 0.0\n#define LIGHT_TYPE_SPHERE 1.0\n#define LIGHT_TYPE_SPOT 2.0\n#define LIGHT_TYPE_POINT 3.0\n#define LIGHT_TYPE_RANGED_DIRECTIONAL 4.0\n#define IS_DIRECTIONAL_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_DIRECTIONAL)) < EPSILON_LOWP)\n#define IS_SPHERE_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_SPHERE)) < EPSILON_LOWP)\n#define IS_SPOT_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_SPOT)) < EPSILON_LOWP)\n#define IS_POINT_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_POINT)) < EPSILON_LOWP)\n#define IS_RANGED_DIRECTIONAL_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_RANGED_DIRECTIONAL)) < EPSILON_LOWP)\n#define TONE_MAPPING_ACES 0\n#define TONE_MAPPING_LINEAR 1\n#define SURFACES_MAX_TRANSMIT_DEPTH_VALUE 999999.0\n#ifndef CC_SURFACES_DEBUG_VIEW_SINGLE\n  #define CC_SURFACES_DEBUG_VIEW_SINGLE 1\n#endif\n#ifndef CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC\n  #define CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC 2\n#endif\nlayout(location = 0) in vec3 a_position;\nlayout(location = 1) in vec2 a_texCoord;\nlayout(location = 0) out vec2 v_uv;\nvec4 vert () {\n  v_uv = a_texCoord;\n  return cc_matViewProj * cc_matWorld * vec4(a_position, 1.0);\n}\nvoid main() { gl_Position = vert(); }", "frag": "\nprecision mediump float;\nlayout(set = 0, binding = 0) uniform CCGlobal {\n  highp   vec4 cc_time;\n  mediump vec4 cc_screenSize;\n  mediump vec4 cc_nativeSize;\n  mediump vec4 cc_probeInfo;\n  mediump vec4 cc_debug_view_mode;\n};\nlayout(set = 0, binding = 1) uniform CCCamera {\n  highp   mat4 cc_matView;\n  highp   mat4 cc_matViewInv;\n  highp   mat4 cc_matProj;\n  highp   mat4 cc_matProjInv;\n  highp   mat4 cc_matViewProj;\n  highp   mat4 cc_matViewProjInv;\n  highp   vec4 cc_cameraPos;\n  mediump vec4 cc_surfaceTransform;\n  mediump vec4 cc_screenScale;\n  mediump vec4 cc_exposure;\n  mediump vec4 cc_mainLitDir;\n  mediump vec4 cc_mainLitColor;\n  mediump vec4 cc_ambientSky;\n  mediump vec4 cc_ambientGround;\n  mediump vec4 cc_fogColor;\n  mediump vec4 cc_fogBase;\n  mediump vec4 cc_fogAdd;\n  mediump vec4 cc_nearFar;\n  mediump vec4 cc_viewPort;\n};\nvec4 CCFragOutput (vec4 color) {\n  return color;\n}\nlayout(set = 1, binding = 0) uniform Constant {\n  vec4 mainColor;\n  vec4 intensitySize;\n};\nlayout(location = 0) in vec2 v_uv;\nfloat sphere(vec3 d, vec3 p) {\n  return smoothstep(1.0 - intensitySize.y, 1.0, 1.0 / length(cross(d, p)));\n}\nvec4 frag () {\n  vec2 uv = v_uv - 0.5;\n  vec3 R = normalize(vec3(1.0, uv));\n  return CCFragOutput(vec4(mainColor.rgb, mix(sphere(R, vec3(5.0, 0.0, 0.0)), 1.0, dot(uv, uv) < 0.0625 ? 1.0 : 0.0)));\n}\nlayout(location = 0) out vec4 cc_FragColor;\nvoid main() { cc_FragColor = frag(); }"}, "glsl3": {"vert": "\nprecision mediump float;\nlayout(std140) uniform CCLocal {\n  highp mat4 cc_matWorld;\n  highp mat4 cc_matWorldIT;\n  highp vec4 cc_lightingMapUVParam;\n  highp vec4 cc_localShadowBias;\n  highp vec4 cc_reflectionProbeData1;\n  highp vec4 cc_reflectionProbeData2;\n  highp vec4 cc_reflectionProbeBlendData1;\n  highp vec4 cc_reflectionProbeBlendData2;\n};\nlayout(std140) uniform CCGlobal {\n  highp   vec4 cc_time;\n  mediump vec4 cc_screenSize;\n  mediump vec4 cc_nativeSize;\n  mediump vec4 cc_probeInfo;\n  mediump vec4 cc_debug_view_mode;\n};\nlayout(std140) uniform CCCamera {\n  highp   mat4 cc_matView;\n  highp   mat4 cc_matViewInv;\n  highp   mat4 cc_matProj;\n  highp   mat4 cc_matProjInv;\n  highp   mat4 cc_matViewProj;\n  highp   mat4 cc_matViewProjInv;\n  highp   vec4 cc_cameraPos;\n  mediump vec4 cc_surfaceTransform;\n  mediump vec4 cc_screenScale;\n  mediump vec4 cc_exposure;\n  mediump vec4 cc_mainLitDir;\n  mediump vec4 cc_mainLitColor;\n  mediump vec4 cc_ambientSky;\n  mediump vec4 cc_ambientGround;\n  mediump vec4 cc_fogColor;\n  mediump vec4 cc_fogBase;\n  mediump vec4 cc_fogAdd;\n  mediump vec4 cc_nearFar;\n  mediump vec4 cc_viewPort;\n};\n#define QUATER_PI         0.78539816340\n#define HALF_PI           1.57079632679\n#define PI                3.14159265359\n#define PI2               6.28318530718\n#define PI4               12.5663706144\n#define INV_QUATER_PI     1.27323954474\n#define INV_HALF_PI       0.63661977237\n#define INV_PI            0.31830988618\n#define INV_PI2           0.15915494309\n#define INV_PI4           0.07957747155\n#define EPSILON           1e-6\n#define EPSILON_LOWP      1e-4\n#define LOG2              1.442695\n#define EXP_VALUE         2.71828183\n#define FP_MAX            65504.0\n#define FP_SCALE          0.0009765625\n#define FP_SCALE_INV      1024.0\n#define GRAY_VECTOR       vec3(0.299, 0.587, 0.114)\n#define LIGHT_MAP_TYPE_DISABLED 0\n#define LIGHT_MAP_TYPE_ALL_IN_ONE 1\n#define LIGHT_MAP_TYPE_INDIRECT_OCCLUSION 2\n#define REFLECTION_PROBE_TYPE_NONE 0\n#define REFLECTION_PROBE_TYPE_CUBE 1\n#define REFLECTION_PROBE_TYPE_PLANAR 2\n#define REFLECTION_PROBE_TYPE_BLEND 3\n#define REFLECTION_PROBE_TYPE_BLEND_AND_SKYBOX 4\n#define LIGHT_TYPE_DIRECTIONAL 0.0\n#define LIGHT_TYPE_SPHERE 1.0\n#define LIGHT_TYPE_SPOT 2.0\n#define LIGHT_TYPE_POINT 3.0\n#define LIGHT_TYPE_RANGED_DIRECTIONAL 4.0\n#define IS_DIRECTIONAL_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_DIRECTIONAL)) < EPSILON_LOWP)\n#define IS_SPHERE_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_SPHERE)) < EPSILON_LOWP)\n#define IS_SPOT_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_SPOT)) < EPSILON_LOWP)\n#define IS_POINT_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_POINT)) < EPSILON_LOWP)\n#define IS_RANGED_DIRECTIONAL_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_RANGED_DIRECTIONAL)) < EPSILON_LOWP)\n#define TONE_MAPPING_ACES 0\n#define TONE_MAPPING_LINEAR 1\n#define SURFACES_MAX_TRANSMIT_DEPTH_VALUE 999999.0\n#ifndef CC_SURFACES_DEBUG_VIEW_SINGLE\n  #define CC_SURFACES_DEBUG_VIEW_SINGLE 1\n#endif\n#ifndef CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC\n  #define CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC 2\n#endif\nin vec3 a_position;\nin vec2 a_texCoord;\nout vec2 v_uv;\nvec4 vert () {\n  v_uv = a_texCoord;\n  return cc_matViewProj * cc_matWorld * vec4(a_position, 1.0);\n}\nvoid main() { gl_Position = vert(); }", "frag": "\nprecision mediump float;\nlayout(std140) uniform CCGlobal {\n  highp   vec4 cc_time;\n  mediump vec4 cc_screenSize;\n  mediump vec4 cc_nativeSize;\n  mediump vec4 cc_probeInfo;\n  mediump vec4 cc_debug_view_mode;\n};\nlayout(std140) uniform CCCamera {\n  highp   mat4 cc_matView;\n  highp   mat4 cc_matViewInv;\n  highp   mat4 cc_matProj;\n  highp   mat4 cc_matProjInv;\n  highp   mat4 cc_matViewProj;\n  highp   mat4 cc_matViewProjInv;\n  highp   vec4 cc_cameraPos;\n  mediump vec4 cc_surfaceTransform;\n  mediump vec4 cc_screenScale;\n  mediump vec4 cc_exposure;\n  mediump vec4 cc_mainLitDir;\n  mediump vec4 cc_mainLitColor;\n  mediump vec4 cc_ambientSky;\n  mediump vec4 cc_ambientGround;\n  mediump vec4 cc_fogColor;\n  mediump vec4 cc_fogBase;\n  mediump vec4 cc_fogAdd;\n  mediump vec4 cc_nearFar;\n  mediump vec4 cc_viewPort;\n};\nvec4 CCFragOutput (vec4 color) {\n  return color;\n}\nlayout(std140) uniform Constant {\n  vec4 mainColor;\n  vec4 intensitySize;\n};\nin vec2 v_uv;\nfloat sphere(vec3 d, vec3 p) {\n  return smoothstep(1.0 - intensitySize.y, 1.0, 1.0 / length(cross(d, p)));\n}\nvec4 frag () {\n  vec2 uv = v_uv - 0.5;\n  vec3 R = normalize(vec3(1.0, uv));\n  return CCFragOutput(vec4(mainColor.rgb, mix(sphere(R, vec3(5.0, 0.0, 0.0)), 1.0, dot(uv, uv) < 0.0625 ? 1.0 : 0.0)));\n}\nlayout(location = 0) out vec4 cc_FragColor;\nvoid main() { cc_FragColor = frag(); }"}, "glsl1": {"vert": "\nprecision mediump float;\nuniform highp mat4 cc_matWorld;\nuniform highp mat4 cc_matViewProj;\n#define QUATER_PI         0.78539816340\n#define HALF_PI           1.57079632679\n#define PI                3.14159265359\n#define PI2               6.28318530718\n#define PI4               12.5663706144\n#define INV_QUATER_PI     1.27323954474\n#define INV_HALF_PI       0.63661977237\n#define INV_PI            0.31830988618\n#define INV_PI2           0.15915494309\n#define INV_PI4           0.07957747155\n#define EPSILON           1e-6\n#define EPSILON_LOWP      1e-4\n#define LOG2              1.442695\n#define EXP_VALUE         2.71828183\n#define FP_MAX            65504.0\n#define FP_SCALE          0.0009765625\n#define FP_SCALE_INV      1024.0\n#define GRAY_VECTOR       vec3(0.299, 0.587, 0.114)\n#define LIGHT_MAP_TYPE_DISABLED 0\n#define LIGHT_MAP_TYPE_ALL_IN_ONE 1\n#define LIGHT_MAP_TYPE_INDIRECT_OCCLUSION 2\n#define REFLECTION_PROBE_TYPE_NONE 0\n#define REFLECTION_PROBE_TYPE_CUBE 1\n#define REFLECTION_PROBE_TYPE_PLANAR 2\n#define REFLECTION_PROBE_TYPE_BLEND 3\n#define REFLECTION_PROBE_TYPE_BLEND_AND_SKYBOX 4\n#define LIGHT_TYPE_DIRECTIONAL 0.0\n#define LIGHT_TYPE_SPHERE 1.0\n#define LIGHT_TYPE_SPOT 2.0\n#define LIGHT_TYPE_POINT 3.0\n#define LIGHT_TYPE_RANGED_DIRECTIONAL 4.0\n#define IS_DIRECTIONAL_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_DIRECTIONAL)) < EPSILON_LOWP)\n#define IS_SPHERE_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_SPHERE)) < EPSILON_LOWP)\n#define IS_SPOT_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_SPOT)) < EPSILON_LOWP)\n#define IS_POINT_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_POINT)) < EPSILON_LOWP)\n#define IS_RANGED_DIRECTIONAL_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_RANGED_DIRECTIONAL)) < EPSILON_LOWP)\n#define TONE_MAPPING_ACES 0\n#define TONE_MAPPING_LINEAR 1\n#define SURFACES_MAX_TRANSMIT_DEPTH_VALUE 999999.0\n#ifndef CC_SURFACES_DEBUG_VIEW_SINGLE\n  #define CC_SURFACES_DEBUG_VIEW_SINGLE 1\n#endif\n#ifndef CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC\n  #define CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC 2\n#endif\nattribute vec3 a_position;\nattribute vec2 a_texCoord;\nvarying vec2 v_uv;\nvec4 vert () {\n  v_uv = a_texCoord;\n  return cc_matViewProj * cc_matWorld * vec4(a_position, 1.0);\n}\nvoid main() { gl_Position = vert(); }", "frag": "\nprecision mediump float;\nvec4 CCFragOutput (vec4 color) {\n  return color;\n}\n    uniform vec4 mainColor;\n    uniform vec4 intensitySize;\nvarying vec2 v_uv;\nfloat sphere(vec3 d, vec3 p) {\n  return smoothstep(1.0 - intensitySize.y, 1.0, 1.0 / length(cross(d, p)));\n}\nvec4 frag () {\n  vec2 uv = v_uv - 0.5;\n  vec3 R = normalize(vec3(1.0, uv));\n  return CCFragOutput(vec4(mainColor.rgb, mix(sphere(R, vec3(5.0, 0.0, 0.0)), 1.0, dot(uv, uv) < 0.0625 ? 1.0 : 0.0)));\n}\nvoid main() { gl_FragColor = frag(); }"}, "builtins": {"globals": {"blocks": [{"name": "CCGlobal", "defines": []}, {"name": "CCCamera", "defines": []}], "samplerTextures": [], "buffers": [], "images": []}, "locals": {"blocks": [{"name": "CCLocal", "defines": []}], "samplerTextures": [], "buffers": [], "images": []}, "statistics": {"CC_EFFECT_USED_VERTEX_UNIFORM_VECTORS": 56, "CC_EFFECT_USED_FRAGMENT_UNIFORM_VECTORS": 44}}, "defines": [], "name": "internal/editor/light|light-vs:vert|light-fs:frag"}], "combinations": [], "hideInEditor": true}