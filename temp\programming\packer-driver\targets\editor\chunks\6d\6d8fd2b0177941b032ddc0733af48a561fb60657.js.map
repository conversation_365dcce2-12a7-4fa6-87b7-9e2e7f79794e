{"version": 3, "sources": ["cce:/internal/x/prerequisite-imports"], "names": ["requests", "request", "_err"], "mappings": ";;;;;;AACA;AAEA,YAAM,CAAC,YAAY;AACf,cAAMA,QAAQ,GAAG,CAAC,uCAAD,EAA4J,uCAA5J,EAA4T,uCAA5T,EAAge,uCAAhe,EAAioB,uCAAjoB,EAA4xB,uCAA5xB,EAAg7B,uCAAh7B,EAAmhC,uCAAnhC,EAA+nC,uCAA/nC,EAA0uC,uCAA1uC,EAA20C,wCAA30C,EAAk7C,wCAAl7C,EAAohD,wCAAphD,EAA4nD,wCAA5nD,EAAiuD,wCAAjuD,EAAo0D,wCAAp0D,EAAs6D,wCAAt6D,EAA2gE,wCAA3gE,EAA4mE,wCAA5mE,EAAotE,wCAAptE,EAAuzE,wCAAvzE,EAAg6E,wCAAh6E,EAAghF,wCAAhhF,EAAuoF,wCAAvoF,EAAwvF,wCAAxvF,EAAw2F,wCAAx2F,EAAy9F,wCAAz9F,EAAilG,wCAAjlG,EAA6rG,wCAA7rG,EAAmzG,wCAAnzG,EAAi6G,wCAAj6G,EAAwhH,wCAAxhH,EAAsoH,wCAAtoH,EAAmvH,wCAAnvH,EAAm2H,wCAAn2H,EAAy9H,wCAAz9H,EAA0kI,wCAA1kI,EAAkrI,wCAAlrI,EAAsyI,wCAAtyI,EAA45I,wCAA55I,EAAggJ,wCAAhgJ,EAA4lJ,wCAA5lJ,EAAksJ,wCAAlsJ,EAAgyJ,wCAAhyJ,EAAs4J,wCAAt4J,CAAjB;;AACA,aAAK,MAAMC,OAAX,IAAsBD,QAAtB,EAAgC;AAC5B,cAAI;AACA,kBAAMC,OAAO,EAAb;AACH,WAFD,CAEE,OAAOC,IAAP,EAAa,CACX;AACH;AACJ;AACJ,OATK,GAAN", "sourcesContent": ["\n// Auto generated represents the prerequisite imports of project modules.\n\nawait (async () => {\n    const requests = [() => import(\"file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts\"), () => import(\"file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts\"), () => import(\"file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts\"), () => import(\"file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts\"), () => import(\"file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts\"), () => import(\"file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/gizmos/EmitterGizmo.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/AutoGen/Luban/schema.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/AutoGen/PB/cs_proto.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Anim.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Background.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Enemy.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/EnemyBullet.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/GameOver.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Global.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Goods.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/MainGame.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Menu.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/PersistNode.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Player.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/PlayerBullet.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/AnimFactory.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/EnemyBulletFactory.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/EnemyFactory.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/GameFactory.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/GoodsFactory.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/PlayerBulletFactory.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/Bootstrap.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/WorldInitializeData.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/base/System.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/base/SystemContainer.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/base/TypeID.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/base/World.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/bullet/Bullet.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/bullet/BulletSystem.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/bullet/Emitter.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/index.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/level/LevelSystem.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/player/PlayerSystem.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameInstance.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/IMgr.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Luban/LubanMgr.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/MainUI.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Network/NetMgr.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ResUpdate/ResUpdate.ts\")];\n    for (const request of requests) {\n        try {\n            await request();\n        } catch (_err) {\n            // The error should have been caught by executor.\n        }\n    }\n})();\n    "]}