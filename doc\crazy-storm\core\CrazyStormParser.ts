/**
 * CrazyStorm 2.0 Integration for Cocos Creator
 * XML Parser for .bgp Files
 * 
 * This parser converts CrazyStorm .bgp (XML) files into TypeScript data structures
 * that can be efficiently used at runtime in Cocos Creator.
 */

import { Vec2, Color, warn, error } from 'cc';
import {
    CrazyStormFile,
    ParticleSystem,
    Layer,
    Component,
    ComponentType,
    LayerColor,
    BlendType,
    FieldShape,
    Reach,
    ForceType,
    ParserConfig,
    PropertyValue,
    ComponentData,
    EmitterData,
    ParticleBaseData,
    CurveParticleData,
    ForceFieldData,
    VariableResource,
    FileResource,
    ParticleType,
    EventGroup,
    CurveEmitterComponent,
    MultiEmitterComponent,
    ForceFieldComponent
} from './CrazyStormTypes';

/**
 * Main parser class for CrazyStorm .bgp files
 */
export class CrazyStormParser {
    private config: ParserConfig;
    private xmlDoc: Document;

    constructor(config: ParserConfig = {
        validateStructure: true,
        optimizeForMobile: true,
        debugMode: false
    }) {
        this.config = config;
    }

    /**
     * Parse a .bgp file content and return structured data
     */
    public parse(xmlContent: string): CrazyStormFile | null {
        try {
            // Parse XML content
            const parser = new DOMParser();
            this.xmlDoc = parser.parseFromString(xmlContent, 'text/xml');

            // Check for parsing errors
            const parserError = this.xmlDoc.querySelector('parsererror');
            if (parserError) {
                error('CrazyStormParser: XML parsing failed', parserError.textContent);
                return null;
            }

            // Get root element
            const rootElement = this.xmlDoc.documentElement;
            if (rootElement.tagName !== 'CrazyStorm') {
                error('CrazyStormParser: Invalid root element, expected "CrazyStorm"');
                return null;
            }

            // Parse the complete file structure
            const crazyStormFile = this.parseCrazyStormFile(rootElement);

            if (this.config.validateStructure) {
                this.validateFileStructure(crazyStormFile);
            }

            if (this.config.optimizeForMobile) {
                this.optimizeForMobile(crazyStormFile);
            }

            if (this.config.debugMode) {
                console.log('CrazyStormParser: Successfully parsed file', crazyStormFile);
            }

            return crazyStormFile;

        } catch (err) {
            error('CrazyStormParser: Failed to parse .bgp file', err);
            return null;
        }
    }

    /**
     * Parse the root CrazyStorm element
     */
    private parseCrazyStormFile(rootElement: Element): CrazyStormFile {
        const file: CrazyStormFile = {
            version: rootElement.getAttribute('version') || '0.92',
            fileResourceIndex: parseInt(rootElement.getAttribute('fileResourceIndex') || '0'),
            particleIndex: parseInt(rootElement.getAttribute('particleIndex') || '0'),
            particleSystems: [],
            images: [],
            sounds: [],
            globals: []
        };

        // Parse particle systems
        const particleSystemsElement = rootElement.querySelector('ParticleSystems');
        if (particleSystemsElement) {
            const systemElements = particleSystemsElement.querySelectorAll('ParticleSystem');
            systemElements.forEach(element => {
                const system = this.parseParticleSystem(element);
                if (system) {
                    file.particleSystems.push(system);
                }
            });
        }

        // Parse images
        const imagesElement = rootElement.querySelector('Images');
        if (imagesElement) {
            const imageElements = imagesElement.querySelectorAll('Resource');
            imageElements.forEach(element => {
                const image = this.parseFileResource(element);
                if (image) {
                    file.images.push(image);
                }
            });
        }

        // Parse sounds
        const soundsElement = rootElement.querySelector('Sounds');
        if (soundsElement) {
            const soundElements = soundsElement.querySelectorAll('Resource');
            soundElements.forEach(element => {
                const sound = this.parseFileResource(element);
                if (sound) {
                    file.sounds.push(sound);
                }
            });
        }

        // Parse globals
        const globalsElement = rootElement.querySelector('Globals');
        if (globalsElement) {
            const globalElements = globalsElement.querySelectorAll('Resource');
            globalElements.forEach(element => {
                const global = this.parseVariableResource(element);
                if (global) {
                    file.globals.push(global);
                }
            });
        }

        return file;
    }

    /**
     * Parse a ParticleSystem element
     */
    private parseParticleSystem(element: Element): ParticleSystem | null {
        const system: ParticleSystem = {
            name: element.getAttribute('name') || 'Untitled',
            customTypeIndex: parseInt(element.getAttribute('customTypeIndex') || '0'),
            layerIndex: parseInt(element.getAttribute('layerIndex') || '0'),
            customTypes: [],
            layers: [],
            componentIndex: {}
        };

        // Parse custom types
        const customTypesElement = element.querySelector('CustomTypes');
        if (customTypesElement) {
            // Custom types parsing will be implemented when needed
        }

        // Parse layers
        const layersElement = element.querySelector('Layers');
        if (layersElement) {
            const layerElements = layersElement.querySelectorAll('Layer');
            layerElements.forEach(layerElement => {
                const layer = this.parseLayer(layerElement);
                if (layer) {
                    system.layers.push(layer);
                }
            });
        }

        // Parse component index
        const componentIndexElement = element.querySelector('ComponentIndex');
        if (componentIndexElement) {
            const dictElements = componentIndexElement.querySelectorAll('Dictionary');
            dictElements.forEach(dictElement => {
                const key = dictElement.getAttribute('Key');
                const value = dictElement.getAttribute('Value');
                if (key && value) {
                    system.componentIndex[key] = parseInt(value);
                }
            });
        }

        return system;
    }

    /**
     * Parse a Layer element
     */
    private parseLayer(element: Element): Layer | null {
        const layer: Layer = {
            name: element.getAttribute('name') || 'Layer',
            visible: element.getAttribute('visible') === 'True',
            color: this.parseLayerColor(element.getAttribute('color') || 'Blue'),
            beginFrame: parseInt(element.getAttribute('beginFrame') || '0'),
            totalFrame: parseInt(element.getAttribute('totalFrame') || '200'),
            components: []
        };

        // Parse components
        const componentsElement = element.querySelector('Components');
        if (componentsElement) {
            const componentElements = componentsElement.querySelectorAll('Component');
            componentElements.forEach(componentElement => {
                const component = this.parseComponent(componentElement);
                if (component) {
                    layer.components.push(component);
                }
            });
        }

        return layer;
    }

    /**
     * Parse a Component element
     */
    private parseComponent(element: Element): Component | null {
        const specificType = element.getAttribute('specificType') as ComponentType;
        if (!specificType) {
            warn('CrazyStormParser: Component missing specificType attribute');
            return null;
        }

        const baseComponent = {
            specificType,
            id: parseInt(element.getAttribute('id') || '0'),
            name: element.getAttribute('name') || 'Component',
            properties: this.parseProperties(element),
            componentData: this.parseComponentData(element),
            variables: [],
            componentEventGroups: [],
            children: []
        };

        // Parse specific component types
        switch (specificType) {
            case ComponentType.CurveEmitter:
                return this.parseCurveEmitter(element, baseComponent);
            case ComponentType.MultiEmitter:
                return this.parseMultiEmitter(element, baseComponent);
            case ComponentType.ForceField:
                return this.parseForceField(element, baseComponent);
            case ComponentType.EventField:
                // TODO: Implement EventField parsing
                return baseComponent;
            case ComponentType.Rebounder:
                // TODO: Implement Rebounder parsing
                return baseComponent;
            default:
                warn(`CrazyStormParser: Unknown component type: ${specificType}`);
                return baseComponent;
        }
    }

    /**
     * Parse Properties element
     */
    private parseProperties(element: Element): { [key: string]: PropertyValue } {
        const properties: { [key: string]: PropertyValue } = {};
        const propertiesElement = element.querySelector('Properties');
        
        if (propertiesElement) {
            const dictElements = propertiesElement.querySelectorAll('Dictionary');
            dictElements.forEach(dictElement => {
                const key = dictElement.getAttribute('Key');
                const value = dictElement.getAttribute('Value');
                if (key && value) {
                    properties[key] = {
                        expression: true, // Assume expression by default
                        value: value
                    };
                }
            });
        }

        return properties;
    }

    /**
     * Parse ComponentData element
     */
    private parseComponentData(element: Element): ComponentData {
        const componentDataElement = element.querySelector('ComponentData');
        
        if (componentDataElement) {
            return {
                layerFrame: parseInt(componentDataElement.getAttribute('layerFrame') || '0'),
                currentFrame: parseInt(componentDataElement.getAttribute('currentFrame') || '0'),
                beginFrame: parseInt(componentDataElement.getAttribute('beginFrame') || '0'),
                totalFrame: parseInt(componentDataElement.getAttribute('totalFrame') || '200'),
                position: this.parseVector2(componentDataElement.getAttribute('position') || '[0,0]'),
                speed: parseFloat(componentDataElement.getAttribute('speed') || '0'),
                speedAngle: parseFloat(componentDataElement.getAttribute('speedAngle') || '0'),
                acspeed: parseFloat(componentDataElement.getAttribute('acspeed') || '0'),
                acspeedAngle: parseFloat(componentDataElement.getAttribute('acspeedAngle') || '0'),
                visibility: componentDataElement.getAttribute('visibility') === 'True'
            };
        }

        // Default component data
        return {
            layerFrame: 0,
            currentFrame: 0,
            beginFrame: 0,
            totalFrame: 200,
            position: new Vec2(0, 0),
            speed: 0,
            speedAngle: 0,
            acspeed: 0,
            acspeedAngle: 0,
            visibility: true
        };
    }

    /**
     * Parse CurveEmitter specific data
     */
    private parseCurveEmitter(element: Element, baseComponent: any): CurveEmitterComponent {
        const emitterElement = element.querySelector('Emitter');
        const curveEmitter: CurveEmitterComponent = {
            ...baseComponent,
            specificType: ComponentType.CurveEmitter,
            emitter: {
                emitterData: this.parseEmitterData(emitterElement),
                particleBase: this.parseParticleBase(emitterElement, true),
                particleEventGroups: []
            }
        };

        return curveEmitter;
    }

    /**
     * Parse MultiEmitter specific data
     */
    private parseMultiEmitter(element: Element, baseComponent: any): MultiEmitterComponent {
        const emitterElement = element.querySelector('Emitter');
        const multiEmitter: MultiEmitterComponent = {
            ...baseComponent,
            specificType: ComponentType.MultiEmitter,
            emitter: {
                emitterData: this.parseEmitterData(emitterElement),
                particleBase: this.parseParticleBase(emitterElement, false),
                particleEventGroups: []
            }
        };

        return multiEmitter;
    }

    /**
     * Parse ForceField specific data
     */
    private parseForceField(element: Element, baseComponent: any): ForceFieldComponent {
        // TODO: Implement ForceField parsing based on CrazyStorm structure
        return {
            ...baseComponent,
            specificType: ComponentType.ForceField,
            forceFieldData: {
                halfWidth: 50,
                halfHeight: 50,
                fieldShape: FieldShape.Rectangle,
                reach: Reach.All,
                targetName: '',
                force: 1,
                direction: 0,
                forceType: ForceType.Direction
            }
        };
    }

    /**
     * Parse EmitterData from Emitter element
     */
    private parseEmitterData(emitterElement: Element | null): EmitterData {
        if (!emitterElement) {
            return this.getDefaultEmitterData();
        }

        const emitterDataElement = emitterElement.querySelector('EmitterData');
        if (!emitterDataElement) {
            return this.getDefaultEmitterData();
        }

        return {
            emitPosition: this.parseVector2(emitterDataElement.getAttribute('emitPosition') || '[0,0]'),
            emitCount: parseInt(emitterDataElement.getAttribute('emitCount') || '1'),
            emitCycle: parseInt(emitterDataElement.getAttribute('emitCycle') || '10'),
            emitAngle: parseFloat(emitterDataElement.getAttribute('emitAngle') || '0'),
            emitRange: parseFloat(emitterDataElement.getAttribute('emitRange') || '360'),
            emitRadius: parseFloat(emitterDataElement.getAttribute('emitRadius') || '0')
        };
    }

    /**
     * Parse ParticleBase data
     */
    private parseParticleBase(emitterElement: Element | null, isCurveParticle: boolean): any {
        if (!emitterElement) {
            return this.getDefaultParticleBase(isCurveParticle);
        }

        const particleBaseElement = emitterElement.querySelector('ParticleBase');
        if (!particleBaseElement) {
            return this.getDefaultParticleBase(isCurveParticle);
        }

        const particleBase = {
            type: parseInt(particleBaseElement.getAttribute('type') || '1000'),
            properties: this.parseProperties(particleBaseElement),
            particleBaseData: this.parseParticleBaseData(particleBaseElement)
        };

        if (isCurveParticle) {
            const curveParticleElement = particleBaseElement.querySelector('CurveParticle');
            if (curveParticleElement) {
                const curveParticleDataElement = curveParticleElement.querySelector('CurveParticleData');
                if (curveParticleDataElement) {
                    (particleBase as any).curveParticle = {
                        curveParticleData: {
                            length: parseInt(curveParticleDataElement.getAttribute('length') || '10')
                        }
                    };
                }
            }
        }

        return particleBase;
    }

    /**
     * Parse ParticleBaseData element
     */
    private parseParticleBaseData(particleBaseElement: Element): ParticleBaseData {
        const dataElement = particleBaseElement.querySelector('ParticleBaseData');
        if (!dataElement) {
            return this.getDefaultParticleBaseData();
        }

        return {
            maxLife: parseInt(dataElement.getAttribute('maxLife') || '200'),
            pcurrentFrame: parseInt(dataElement.getAttribute('pcurrentFrame') || '0'),
            pposition: this.parseVector2(dataElement.getAttribute('pposition') || '[0,0]'),
            widthScale: parseFloat(dataElement.getAttribute('widthScale') || '1'),
            rgb: this.parseRGB(dataElement.getAttribute('rgb') || '[255,255,255]'),
            mass: parseFloat(dataElement.getAttribute('mass') || '1'),
            opacity: parseFloat(dataElement.getAttribute('opacity') || '100'),
            pspeed: parseFloat(dataElement.getAttribute('pspeed') || '5'),
            pspeedAngle: parseFloat(dataElement.getAttribute('pspeedAngle') || '0'),
            pacspeed: parseFloat(dataElement.getAttribute('pacspeed') || '0'),
            pacspeedAngle: parseFloat(dataElement.getAttribute('pacspeedAngle') || '0'),
            protation: parseFloat(dataElement.getAttribute('protation') || '0'),
            blendType: this.parseBlendType(dataElement.getAttribute('blendType') || 'AlphaBlend'),
            killOutside: dataElement.getAttribute('killOutside') === 'True',
            collision: dataElement.getAttribute('collision') === 'True',
            ignoreMask: dataElement.getAttribute('ignoreMask') === 'True',
            ignoreRebound: dataElement.getAttribute('ignoreRebound') === 'True',
            ignoreForce: dataElement.getAttribute('ignoreForce') === 'True',
            fogEffect: dataElement.getAttribute('fogEffect') === 'True',
            fadeEffect: dataElement.getAttribute('fadeEffect') === 'True'
        };
    }

    /**
     * Parse VariableResource
     */
    private parseVariableResource(element: Element): VariableResource | null {
        const label = element.getAttribute('label');
        if (!label) return null;

        const variableResourceElement = element.querySelector('VariableResource');
        const value = variableResourceElement ?
            parseFloat(variableResourceElement.getAttribute('value') || '0') : 0;

        return {
            label,
            value
        };
    }

    /**
     * Parse FileResource
     */
    private parseFileResource(element: Element): FileResource | null {
        const label = element.getAttribute('label');
        if (!label) return null;

        const fileResourceElement = element.querySelector('FileResource');
        if (!fileResourceElement) return null;

        return {
            label,
            id: parseInt(fileResourceElement.getAttribute('id') || '0'),
            relativePath: fileResourceElement.getAttribute('relativePath') || ''
        };
    }

    /**
     * Parse Vector2 from string format "[x,y]"
     */
    private parseVector2(vectorStr: string): Vec2 {
        try {
            const cleaned = vectorStr.replace(/[\[\]]/g, '');
            const parts = cleaned.split(',');
            if (parts.length >= 2) {
                return new Vec2(parseFloat(parts[0]), parseFloat(parts[1]));
            }
        } catch (err) {
            warn('CrazyStormParser: Failed to parse Vector2', vectorStr);
        }
        return new Vec2(0, 0);
    }

    /**
     * Parse RGB color from string format "[r,g,b]"
     */
    private parseRGB(rgbStr: string): Color {
        try {
            const cleaned = rgbStr.replace(/[\[\]]/g, '');
            const parts = cleaned.split(',');
            if (parts.length >= 3) {
                return new Color(
                    parseInt(parts[0]),
                    parseInt(parts[1]),
                    parseInt(parts[2]),
                    255
                );
            }
        } catch (err) {
            warn('CrazyStormParser: Failed to parse RGB', rgbStr);
        }
        return new Color(255, 255, 255, 255);
    }

    /**
     * Parse LayerColor enum
     */
    private parseLayerColor(colorStr: string): LayerColor {
        return (LayerColor as any)[colorStr] || LayerColor.Blue;
    }

    /**
     * Parse BlendType enum
     */
    private parseBlendType(blendStr: string): BlendType {
        return (BlendType as any)[blendStr] || BlendType.AlphaBlend;
    }

    // Default data generators
    private getDefaultEmitterData(): EmitterData {
        return {
            emitPosition: new Vec2(0, 0),
            emitCount: 1,
            emitCycle: 10,
            emitAngle: 0,
            emitRange: 360,
            emitRadius: 0
        };
    }

    private getDefaultParticleBase(isCurveParticle: boolean): any {
        const base = {
            type: 1000,
            properties: {},
            particleBaseData: this.getDefaultParticleBaseData()
        };

        if (isCurveParticle) {
            (base as any).curveParticle = {
                curveParticleData: { length: 10 }
            };
        }

        return base;
    }

    private getDefaultParticleBaseData(): ParticleBaseData {
        return {
            maxLife: 200,
            pcurrentFrame: 0,
            pposition: new Vec2(0, 0),
            widthScale: 1,
            rgb: new Color(255, 255, 255, 255),
            mass: 1,
            opacity: 100,
            pspeed: 5,
            pspeedAngle: 0,
            pacspeed: 0,
            pacspeedAngle: 0,
            protation: 0,
            blendType: BlendType.AlphaBlend,
            killOutside: true,
            collision: true,
            ignoreMask: false,
            ignoreRebound: false,
            ignoreForce: false,
            fogEffect: true,
            fadeEffect: true
        };
    }

    /**
     * Validate the parsed file structure
     */
    private validateFileStructure(file: CrazyStormFile): void {
        if (!file.particleSystems || file.particleSystems.length === 0) {
            warn('CrazyStormParser: No particle systems found in file');
        }

        file.particleSystems.forEach((system, index) => {
            if (!system.layers || system.layers.length === 0) {
                warn(`CrazyStormParser: Particle system ${index} has no layers`);
            }
        });
    }

    /**
     * Optimize the parsed data for mobile performance
     */
    private optimizeForMobile(file: CrazyStormFile): void {
        // Reduce precision for mobile performance
        file.particleSystems.forEach(system => {
            system.layers.forEach(layer => {
                layer.components.forEach(component => {
                    // Round position values to reduce precision
                    component.componentData.position.x = Math.round(component.componentData.position.x * 100) / 100;
                    component.componentData.position.y = Math.round(component.componentData.position.y * 100) / 100;

                    // Round speed values
                    component.componentData.speed = Math.round(component.componentData.speed * 100) / 100;
                    component.componentData.acspeed = Math.round(component.componentData.acspeed * 100) / 100;
                });
            });
        });
    }
}
