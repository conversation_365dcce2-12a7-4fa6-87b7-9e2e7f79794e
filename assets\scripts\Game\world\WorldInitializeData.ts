/**
 * Enumeration for different game modes
 */
export enum GameMode {
    STORY = "story",
    ENDLESS = "endless",
    CHALLENGE = "challenge",
    TUTORIAL = "tutorial",
    CUSTOM = "custom"
}

/**
 * Enumeration for difficulty levels
 */
export enum DifficultyLevel {
    EASY = "easy",
    NORMAL = "normal",
    HARD = "hard",
    EXPERT = "expert"
}

/**
 * Configuration for world physics
 */
export interface WorldPhysicsConfig {
    readonly gravity: number;
    readonly timeScale: number;
    readonly maxVelocity: number;
    readonly enableCollision: boolean;
}

/**
 * Configuration for world rendering
 */
export interface WorldRenderConfig {
    readonly enableParticles: boolean;
    readonly maxParticles: number;
    readonly enablePostProcessing: boolean;
    readonly renderScale: number;
}

/**
 * Player configuration for world initialization
 */
export interface PlayerConfig {
    readonly startPosition: { readonly x: number; readonly y: number };
    readonly maxHealth: number;
    readonly startingWeapon: string;
    readonly abilities: readonly string[];
}

/**
 * Level configuration for world initialization
 */
export interface LevelConfig {
    readonly backgroundMusic: string;
    readonly backgroundImage: string;
    readonly environmentEffects: readonly string[];
    readonly boundaries: {
        readonly minX: number;
        readonly maxX: number;
        readonly minY: number;
        readonly maxY: number;
    };
}

/**
 * Debug flags configuration
 */
export interface DebugFlags {
    readonly enableDebugDraw: boolean;
    readonly showCollisionBounds: boolean;
    readonly showPerformanceStats: boolean;
    readonly logSystemUpdates: boolean;
}

/**
 * Interface for world initialization data
 * All members are readonly to ensure immutability
 */
export interface IWorldInitializeData {
    /** Unique identifier for the game mode */
    readonly modeId: GameMode;

    /** Unique identifier for the level */
    readonly levelId: string;

    /** Random seed for deterministic gameplay */
    readonly randomSeed: number;

    /** Difficulty level for the game session */
    readonly difficulty: DifficultyLevel;

    /** Physics configuration for the world */
    readonly physicsConfig: WorldPhysicsConfig;

    /** Rendering configuration for the world */
    readonly renderConfig: WorldRenderConfig;

    /** Player configuration */
    readonly playerConfig: PlayerConfig;

    /** Level configuration */
    readonly levelConfig: LevelConfig;

    /** Custom data for specific game modes or levels */
    readonly customData: { readonly [key: string]: any };

    /** Debug flags for development */
    readonly debugFlags: DebugFlags;

    /**
     * Validate the initialization data
     * @returns Array of validation errors, empty if valid
     */
    validate(): string[];

    /**
     * Clone the initialization data
     * @returns Deep copy of the initialization data
     */
    clone(): IWorldInitializeData;
}

/**
 * Implementation of world initialization data
 * Contains all necessary parameters to set up a complete game session
 */
export class WorldInitializeData implements IWorldInitializeData {

    /** Unique identifier for the game mode */
    public readonly modeId: GameMode;

    /** Unique identifier for the level */
    public readonly levelId: string;

    /** Random seed for deterministic gameplay */
    public readonly randomSeed: number;

    /** Difficulty level for the game session */
    public readonly difficulty: DifficultyLevel;

    /** Physics configuration for the world */
    public readonly physicsConfig: WorldPhysicsConfig;

    /** Rendering configuration for the world */
    public readonly renderConfig: WorldRenderConfig;

    /** Player configuration */
    public readonly playerConfig: PlayerConfig;

    /** Level configuration */
    public readonly levelConfig: LevelConfig;

    /** Custom data for specific game modes or levels */
    public readonly customData: { readonly [key: string]: any };

    /** Debug flags for development */
    public readonly debugFlags: DebugFlags;

    /**
     * Create a new WorldInitializeData with default values
     */
    constructor(config?: Partial<IWorldInitializeData>) {
        // Set default values
        this.modeId = config?.modeId ?? GameMode.STORY;
        this.levelId = config?.levelId ?? "";
        this.randomSeed = config?.randomSeed ?? Date.now();
        this.difficulty = config?.difficulty ?? DifficultyLevel.NORMAL;

        // Set default physics config
        this.physicsConfig = {
            gravity: config?.physicsConfig?.gravity ?? -9.8,
            timeScale: config?.physicsConfig?.timeScale ?? 1.0,
            maxVelocity: config?.physicsConfig?.maxVelocity ?? 1000,
            enableCollision: config?.physicsConfig?.enableCollision ?? true
        };

        // Set default render config
        this.renderConfig = {
            enableParticles: config?.renderConfig?.enableParticles ?? true,
            maxParticles: config?.renderConfig?.maxParticles ?? 1000,
            enablePostProcessing: config?.renderConfig?.enablePostProcessing ?? true,
            renderScale: config?.renderConfig?.renderScale ?? 1.0
        };

        // Set default player config
        this.playerConfig = {
            startPosition: {
                x: config?.playerConfig?.startPosition?.x ?? 0,
                y: config?.playerConfig?.startPosition?.y ?? 0
            },
            maxHealth: config?.playerConfig?.maxHealth ?? 100,
            startingWeapon: config?.playerConfig?.startingWeapon ?? "default",
            abilities: config?.playerConfig?.abilities ?? []
        };

        // Set default level config
        this.levelConfig = {
            backgroundMusic: config?.levelConfig?.backgroundMusic ?? "",
            backgroundImage: config?.levelConfig?.backgroundImage ?? "",
            environmentEffects: config?.levelConfig?.environmentEffects ?? [],
            boundaries: {
                minX: config?.levelConfig?.boundaries?.minX ?? -1000,
                maxX: config?.levelConfig?.boundaries?.maxX ?? 1000,
                minY: config?.levelConfig?.boundaries?.minY ?? -1000,
                maxY: config?.levelConfig?.boundaries?.maxY ?? 1000
            }
        };

        // Set default custom data
        this.customData = config?.customData ?? {};

        // Set default debug flags
        this.debugFlags = {
            enableDebugDraw: config?.debugFlags?.enableDebugDraw ?? false,
            showCollisionBounds: config?.debugFlags?.showCollisionBounds ?? false,
            showPerformanceStats: config?.debugFlags?.showPerformanceStats ?? false,
            logSystemUpdates: config?.debugFlags?.logSystemUpdates ?? false
        };
    }

    /**
     * Create WorldInitializeData from a configuration object
     * @param config Partial configuration object
     * @returns New WorldInitializeData instance
     */
    public static fromConfig(config: Partial<IWorldInitializeData>): WorldInitializeData {
        return new WorldInitializeData(config);
    }

    /**
     * Validate the initialization data
     * @returns Array of validation errors, empty if valid
     */
    public validate(): string[] {
        const errors: string[] = [];

        if (!this.levelId || this.levelId.trim() === "") {
            errors.push("Level ID is required");
        }

        if (this.randomSeed < 0) {
            errors.push("Random seed must be non-negative");
        }

        if (this.physicsConfig.timeScale <= 0) {
            errors.push("Physics time scale must be positive");
        }

        if (this.renderConfig.maxParticles < 0) {
            errors.push("Max particles must be non-negative");
        }

        if (this.playerConfig.maxHealth <= 0) {
            errors.push("Player max health must be positive");
        }

        return errors;
    }

    /**
     * Clone the initialization data
     * @returns Deep copy of the initialization data
     */
    public clone(): IWorldInitializeData {
        return WorldInitializeData.fromConfig(JSON.parse(JSON.stringify(this)));
    }
}