{"version": 3, "sources": ["file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Goods.ts"], "names": ["_decorator", "Component", "Sprite", "find", "Collider2D", "Contact2DType", "ProgressBar", "Global", "PersistNode", "Player", "ccclass", "property", "Goods", "goodType", "bloodGoodsMoveSpeed", "lightGoodsMoveSpeed", "missileGoodsMoveSpeed", "persistNode", "goodsFactory", "curPos", "onLoad", "getComponent", "collider", "node", "on", "BEGIN_CONTACT", "onBeginContact", "selfCollider", "otherCollider", "contact", "BLOOD_GOODS", "getChildByName", "progress", "planeBlood", "planeTotalBlood", "LIGHT_GOODS", "isShootLight", "MISSILE_GOODS", "isShootMissile", "recycleProduct", "init", "spriteFrame", "update", "deltaTime", "bloodGoodsMove", "lightGoodsMove", "missileGoodsMove", "getPosition", "y", "setPosition", "HEIGHT"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAA8BC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,I,OAAAA,I;AAAYC,MAAAA,U,OAAAA,U;AAAYC,MAAAA,a,OAAAA,a;AAAkCC,MAAAA,W,OAAAA,W;;AAE5GC,MAAAA,M,iBAAAA,M;;AACAC,MAAAA,W,iBAAAA,W;;AACAC,MAAAA,M,iBAAAA,M;;;;;;;;;OACH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBX,U;;uBAGjBY,K,WADZF,OAAO,CAAC,OAAD,C,gBAAR,MACaE,KADb,SAC2BX,SAD3B,CACqC;AAAA;AAAA;AAAA,eAEjCY,QAFiC,GAEd,IAFc;AAAA,eAIjCC,mBAJiC,GAIH,CAJG;AAIG;AAJH,eAMjCC,mBANiC,GAMH,CANG;AAMK;AANL,eAQjCC,qBARiC,GAQD,CARC;AAQO;AARP,eAUjCC,WAViC,GAUb,IAVa;AAAA,eAYjCC,YAZiC,GAYL,IAZK;AAAA,eAcjCC,MAdiC,GAclB,IAdkB;AAAA;;AAgBjCC,QAAAA,MAAM,GAAG;AACL,eAAKH,WAAL,GAAmBd,IAAI,CAAC,aAAD,CAAvB;AACA,eAAKe,YAAL,GAAoB,KAAKD,WAAL,CAAiBI,YAAjB;AAAA;AAAA,0CAA2CH,YAA/D,CAFK,CAIL;;AACA,eAAKJ,mBAAL,GAA2B,KAAKG,WAAL,CAAiBI,YAAjB;AAAA;AAAA,0CAA2CP,mBAAtE;AACA,eAAKC,mBAAL,GAA2B,KAAKE,WAAL,CAAiBI,YAAjB;AAAA;AAAA,0CAA2CN,mBAAtE;AACA,eAAKC,qBAAL,GAA6B,KAAKC,WAAL,CAAiBI,YAAjB;AAAA;AAAA,0CAA2CL,qBAAxE;AAEA,cAAIM,QAAQ,GAAG,KAAKC,IAAL,CAAUF,YAAV,CAAuBjB,UAAvB,CAAf;;AAEA,cAAIkB,QAAJ,EAAc;AACVA,YAAAA,QAAQ,CAACE,EAAT,CAAYnB,aAAa,CAACoB,aAA1B,EAAyC,KAAKC,cAA9C,EAA8D,IAA9D;AACH;AACJ;;AAEDA,QAAAA,cAAc,CAACC,YAAD,EAA2BC,aAA3B,EAAsDC,OAAtD,EAAyF;AAEnG;AACA,cAAI,KAAKhB,QAAL,IAAiB;AAAA;AAAA,gCAAOiB,WAA5B,EAAyC;AACrCF,YAAAA,aAAa,CAACL,IAAd,CAAmBQ,cAAnB,CAAkC,OAAlC,EAA2CV,YAA3C,CAAwDf,WAAxD,EAAqE0B,QAArE,GAAgF,CAAhF;AACAJ,YAAAA,aAAa,CAACL,IAAd,CAAmBF,YAAnB;AAAA;AAAA,kCAAwCY,UAAxC,GAAqDL,aAAa,CAACL,IAAd,CAAmBF,YAAnB;AAAA;AAAA,kCAAwCa,eAA7F;AACH,WAHD,MAGO,IAAI,KAAKrB,QAAL,IAAiB;AAAA;AAAA,gCAAOsB,WAA5B,EAAyC;AAC5CP,YAAAA,aAAa,CAACL,IAAd,CAAmBF,YAAnB;AAAA;AAAA,kCAAwCe,YAAxC,GAAuD,IAAvD;AACH,WAFM,MAEA,IAAI,KAAKvB,QAAL,IAAiB;AAAA;AAAA,gCAAOwB,aAA5B,EAA2C;AAC9CT,YAAAA,aAAa,CAACL,IAAd,CAAmBF,YAAnB;AAAA;AAAA,kCAAwCiB,cAAxC,GAAyD,IAAzD;AACH;;AAED,eAAKpB,YAAL,CAAkBqB,cAAlB,CAAiC,KAAKhB,IAAtC;AACH;AAED;AACJ;AACA;;;AACKiB,QAAAA,IAAI,CAAC3B,QAAD,EAAmB4B,WAAnB,EAA6C;AAC9C,eAAK5B,QAAL,GAAgBA,QAAhB;AACA,eAAKU,IAAL,CAAUF,YAAV,CAAuBnB,MAAvB,EAA+BuC,WAA/B,GAA6CA,WAA7C;AACH;;AAEDC,QAAAA,MAAM,CAACC,SAAD,EAAoB;AACtB,cAAI,KAAK9B,QAAL,IAAiB;AAAA;AAAA,gCAAOiB,WAA5B,EAAyC;AACrC,iBAAKc,cAAL,CAAoBD,SAApB;AACH,WAFD,MAEO,IAAI,KAAK9B,QAAL,IAAiB;AAAA;AAAA,gCAAOsB,WAA5B,EAAyC;AAC5C,iBAAKU,cAAL,CAAoBF,SAApB;AACH,WAFM,MAEA,IAAI,KAAK9B,QAAL,IAAiB;AAAA;AAAA,gCAAOwB,aAA5B,EAA2C;AAC9C,iBAAKS,gBAAL,CAAsBH,SAAtB;AACH;AACJ;AAED;AACJ;AACA;AACA;;;AACIG,QAAAA,gBAAgB,CAACH,SAAD,EAAoB;AAChC,eAAKxB,MAAL,GAAc,KAAKI,IAAL,CAAUwB,WAAV,EAAd;AACA,eAAK5B,MAAL,CAAY6B,CAAZ,IAAiB,KAAKhC,qBAAL,GAA6B2B,SAA9C;AACA,eAAKpB,IAAL,CAAU0B,WAAV,CAAsB,KAAK9B,MAA3B;;AAEA,cAAI,KAAKA,MAAL,CAAY6B,CAAZ,GAAgB,CAAC;AAAA;AAAA,gCAAOE,MAAR,GAAiB,CAArC,EAAwC;AACpC,iBAAKhC,YAAL,CAAkBqB,cAAlB,CAAiC,KAAKhB,IAAtC;AACH;AACJ;AAED;AACJ;AACA;AACA;;;AACIsB,QAAAA,cAAc,CAACF,SAAD,EAAoB;AAC9B,eAAKxB,MAAL,GAAc,KAAKI,IAAL,CAAUwB,WAAV,EAAd;AACA,eAAK5B,MAAL,CAAY6B,CAAZ,IAAiB,KAAKjC,mBAAL,GAA2B4B,SAA5C;AACA,eAAKpB,IAAL,CAAU0B,WAAV,CAAsB,KAAK9B,MAA3B;;AAEA,cAAI,KAAKA,MAAL,CAAY6B,CAAZ,GAAgB,CAAC;AAAA;AAAA,gCAAOE,MAAR,GAAiB,CAArC,EAAwC;AACpC,iBAAKhC,YAAL,CAAkBqB,cAAlB,CAAiC,KAAKhB,IAAtC;AACH;AACJ;AAGD;AACJ;AACA;AACA;;;AACIqB,QAAAA,cAAc,CAACD,SAAD,EAAoB;AAC9B,eAAKxB,MAAL,GAAc,KAAKI,IAAL,CAAUwB,WAAV,EAAd;AACA,eAAK5B,MAAL,CAAY6B,CAAZ,IAAiB,KAAKlC,mBAAL,GAA2B6B,SAA5C;AACA,eAAKpB,IAAL,CAAU0B,WAAV,CAAsB,KAAK9B,MAA3B;;AAEA,cAAI,KAAKA,MAAL,CAAY6B,CAAZ,GAAgB,CAAC;AAAA;AAAA,gCAAOE,MAAR,GAAiB,CAArC,EAAwC;AACpC,iBAAKhC,YAAL,CAAkBqB,cAAlB,CAAiC,KAAKhB,IAAtC;AACH;AACJ;;AA1GgC,O", "sourcesContent": ["import { _decorator, Component, Node, Sprite<PERSON>rame, Sprite, find, Vec3, Collider2D, Contact2DType, IPhysics2DContact, ProgressBar } from 'cc';\nimport { GameFactory } from './factroy/GameFactory';\nimport { Global } from './Global';\nimport { PersistNode } from './PersistNode';\nimport { Player } from './Player';\nconst { ccclass, property } = _decorator;\n\n@ccclass('Goods')\nexport class Goods extends Component {\n    \n    goodType: string = null;\n\n    bloodGoodsMoveSpeed: number = 0;    //加血物资移动速度\n\n    lightGoodsMoveSpeed: number = 0;      //激光物资移动速度\n\n    missileGoodsMoveSpeed: number = 0;      //导弹物资移动速度\n\n    persistNode: Node = null;\n\n    goodsFactory: GameFactory = null;\n\n    curPos: Vec3 = null;\n\n    onLoad() {\n        this.persistNode = find(\"PersistNode\");\n        this.goodsFactory = this.persistNode.getComponent(PersistNode).goodsFactory;\n\n        //拿到面板值\n        this.bloodGoodsMoveSpeed = this.persistNode.getComponent(PersistNode).bloodGoodsMoveSpeed;\n        this.lightGoodsMoveSpeed = this.persistNode.getComponent(PersistNode).lightGoodsMoveSpeed;\n        this.missileGoodsMoveSpeed = this.persistNode.getComponent(PersistNode).missileGoodsMoveSpeed;\n\n        let collider = this.node.getComponent(Collider2D);\n\n        if (collider) {\n            collider.on(Contact2DType.BEGIN_CONTACT, this.onBeginContact, this);\n        }\n    }\n\n    onBeginContact(selfCollider: Collider2D, otherCollider: Collider2D, contact: IPhysics2DContact | null) {\n\n        //判断吃到了那种道具\n        if (this.goodType == Global.BLOOD_GOODS) {\n            otherCollider.node.getChildByName(\"Blood\").getComponent(ProgressBar).progress = 1;\n            otherCollider.node.getComponent(Player).planeBlood = otherCollider.node.getComponent(Player).planeTotalBlood;\n        } else if (this.goodType == Global.LIGHT_GOODS) {\n            otherCollider.node.getComponent(Player).isShootLight = true;\n        } else if (this.goodType == Global.MISSILE_GOODS) {\n            otherCollider.node.getComponent(Player).isShootMissile = true;\n        }\n\n        this.goodsFactory.recycleProduct(this.node);\n    }\n\n    /**\n     * 物资初始化函数\n     */\n     init(goodType: string, spriteFrame: SpriteFrame) {\n        this.goodType = goodType;\n        this.node.getComponent(Sprite).spriteFrame = spriteFrame;\n    }\n\n    update(deltaTime: number) {\n        if (this.goodType == Global.BLOOD_GOODS) {     \n            this.bloodGoodsMove(deltaTime);            \n        } else if (this.goodType == Global.LIGHT_GOODS) {      \n            this.lightGoodsMove(deltaTime);\n        } else if (this.goodType == Global.MISSILE_GOODS) {      \n            this.missileGoodsMove(deltaTime);\n        }\n    }\n\n    /**\n     * 导弹物资移动\n     * @param deltaTime \n     */\n    missileGoodsMove(deltaTime: number) {\n        this.curPos = this.node.getPosition();\n        this.curPos.y -= this.missileGoodsMoveSpeed * deltaTime;\n        this.node.setPosition(this.curPos);\n\n        if (this.curPos.y < -Global.HEIGHT / 2) {\n            this.goodsFactory.recycleProduct(this.node);\n        }\n    }\n\n    /**\n     * 激光物资移动\n     * @param deltaTime \n     */\n    lightGoodsMove(deltaTime: number) {\n        this.curPos = this.node.getPosition();\n        this.curPos.y -= this.lightGoodsMoveSpeed * deltaTime;\n        this.node.setPosition(this.curPos);\n\n        if (this.curPos.y < -Global.HEIGHT / 2) {\n            this.goodsFactory.recycleProduct(this.node);\n        }\n    }\n\n\n    /**\n     * 加血物资移动\n     * @param deltaTime \n     */\n    bloodGoodsMove(deltaTime: number) {\n        this.curPos = this.node.getPosition();\n        this.curPos.y -= this.bloodGoodsMoveSpeed * deltaTime;\n        this.node.setPosition(this.curPos);\n\n        if (this.curPos.y < -Global.HEIGHT / 2) {\n            this.goodsFactory.recycleProduct(this.node);\n        }\n    }\n}\n"]}