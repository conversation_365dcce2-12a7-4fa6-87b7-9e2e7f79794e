{"version": 3, "sources": ["file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/AnimFactory.ts"], "names": ["AnimFactory", "_decorator", "instantiate", "PersistNode", "GameFactory", "ccclass", "property", "createAnim", "anim<PERSON>emp", "productPool", "size", "get", "persistNode", "getComponent", "animPreb"], "mappings": ";;;kIAMaA,W;;;;;;;;;;;;;;;;;;;AANJC,MAAAA,U,OAAAA,U;AAA6BC,MAAAA,W,OAAAA,W;;AAC7BC,MAAAA,W,iBAAAA,W;;AACAC,MAAAA,W,iBAAAA,W;;;;;;;;;OACH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBL,U;;6BAGjBD,W,GAAN,MAAMA,WAAN;AAAA;AAAA,sCAAsC;AAClCO,QAAAA,UAAU,GAAS;AACtB,cAAIC,QAAc,GAAG,IAArB;;AAEA,cAAG,KAAKC,WAAL,CAAiBC,IAAjB,KAA0B,CAA7B,EAAgC;AAC5BF,YAAAA,QAAQ,GAAG,KAAKC,WAAL,CAAiBE,GAAjB,EAAX,CAD4B,CACQ;AACvC,WAFD,MAEO;AACHH,YAAAA,QAAQ,GAAGN,WAAW,CAAC,KAAKU,WAAL,CAAiBC,YAAjB;AAAA;AAAA,4CAA2CC,QAA5C,CAAtB,CADG,CAC2E;AACjF;;AAED,iBAAON,QAAP;AACH;;AAXwC,O", "sourcesContent": ["import { _decorator, Component, Node, instantiate } from 'cc';\nimport { PersistNode } from '../PersistNode';\nimport { GameFactory } from './GameFactory';\nconst { ccclass, property } = _decorator;\n\n\nexport class AnimFactory extends GameFactory {\n    public createAnim(): Node {\n        let animTemp: Node = null;\n\n        if(this.productPool.size() > 0) {\n            animTemp = this.productPool.get();  //如果池里有敌机，就直接拿来用\n        } else {\n            animTemp = instantiate(this.persistNode.getComponent(PersistNode).animPreb);  //从常驻节点拿到预制体原料\n        }\n\n        return animTemp;\n    } \n}\n\n"]}