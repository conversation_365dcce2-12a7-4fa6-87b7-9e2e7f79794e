{"version": 3, "sources": ["file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts"], "names": ["_decorator", "assert", "CCBoolean", "CCFloat", "CCInteger", "gfx", "Material", "rendering", "Vec3", "Vec4", "EDITOR", "BuiltinPipelineSettings", "BuiltinPipelinePassBuilder", "getPingPongRenderTarget", "ccclass", "disallowMultiple", "executeInEditMode", "menu", "property", "requireComponent", "type", "Color", "LoadOp", "StoreOp", "BuiltinDepthOfFieldPass", "group", "id", "name", "style", "visible", "min", "range", "slide", "_clearColorTransparentBlack", "_cocParams", "_focusPosVec4", "_cocTexSize", "dofEnable", "value", "_enableDof", "_parent", "_tryEnableEditorPreview", "dofMaterial", "_material", "dofMinRange", "_minRange", "dofMaxRange", "_max<PERSON>ange", "dofIntensity", "_intensity", "dofBlurRadius", "_blurRadius", "dofFocusPos", "_focusPos", "getConfigOrder", "config<PERSON><PERSON>r", "getRenderOrder", "renderOrder", "configCamera", "camera", "pplConfigs", "cameraConfigs", "enableDof", "supportDepthSample", "enableStoreSceneDepth", "remainingPasses", "windowResize", "ppl", "window", "renderWindowId", "addR<PERSON><PERSON>arget", "radianceFormat", "width", "height", "setup", "context", "prevRenderPass", "_addDepthOfFieldPasses", "colorName", "depthStencilName", "prefix", "enableShadingScale", "outputRadianceName", "inputRadianceName", "inputRadiance", "inputDepthStencil", "x", "y", "z", "w", "tempRadiance", "blurPass", "addRenderPass", "CLEAR", "STORE", "addTexture", "setVec4", "platform", "addQueue", "QueueHint", "OPAQUE", "addCameraQuad", "<PERSON><PERSON><PERSON><PERSON>", "setMat4", "<PERSON><PERSON><PERSON><PERSON>", "matProjInv", "node", "worldMatrix"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyBIA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,O,OAAAA,O;AAASC,MAAAA,S,OAAAA,S;AACxCC,MAAAA,G,OAAAA,G;AAAKC,MAAAA,Q,OAAAA,Q;AAAoBC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,I,OAAAA,I;;AAGrCC,MAAAA,M,UAAAA,M;;AAGLC,MAAAA,uB,iBAAAA,uB;;AAIAC,MAAAA,0B,iBAAAA,0B;;AAKAC,MAAAA,uB,iBAAAA,uB;;;;;;AAzCJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;OAwBM;AAAEC,QAAAA,OAAF;AAAWC,QAAAA,gBAAX;AAA6BC,QAAAA,iBAA7B;AAAgDC,QAAAA,IAAhD;AAAsDC,QAAAA,QAAtD;AAAgEC,QAAAA,gBAAhE;AAAkFC,QAAAA;AAAlF,O,GAA2FpB,U;OAE3F;AAAEqB,QAAAA,KAAF;AAASC,QAAAA,MAAT;AAAiBC,QAAAA;AAAjB,O,GAA6BlB,G;;yCAWtBmB,uB,WALZV,OAAO,CAAC,yBAAD,C,UACPG,IAAI,CAAC,mCAAD,C,UACJE,gBAAgB;AAAA;AAAA,6D,UAKZD,QAAQ,CAAC;AACNO,QAAAA,KAAK,EAAE;AAAEC,UAAAA,EAAE,EAAE,aAAN;AAAqBC,UAAAA,IAAI,EAAE,eAA3B;AAA4CC,UAAAA,KAAK,EAAE;AAAnD,SADD;AAENR,QAAAA,IAAI,EAAEhB;AAFA,OAAD,C,UAKRc,QAAQ,CAAC;AACNO,QAAAA,KAAK,EAAE;AAAEC,UAAAA,EAAE,EAAE,aAAN;AAAqBC,UAAAA,IAAI,EAAE,eAA3B;AAA4CC,UAAAA,KAAK,EAAE;AAAnD,SADD;AAENR,QAAAA,IAAI,EAAEhB;AAFA,OAAD,C,UAsBRc,QAAQ,CAAC;AACNO,QAAAA,KAAK,EAAE;AAAEC,UAAAA,EAAE,EAAE,cAAN;AAAsBC,UAAAA,IAAI,EAAE,+BAA5B;AAA6DC,UAAAA,KAAK,EAAE;AAApE,SADD;AAENR,QAAAA,IAAI,EAAElB,SAFA;AAGN2B,QAAAA,OAAO,EAAE;AAHH,OAAD,C,UAeRX,QAAQ,CAAC;AACNO,QAAAA,KAAK,EAAE;AAAEC,UAAAA,EAAE,EAAE,cAAN;AAAsBC,UAAAA,IAAI,EAAE,+BAA5B;AAA6DC,UAAAA,KAAK,EAAE;AAApE,SADD;AAENR,QAAAA,IAAI,EAAEd,QAFA;AAGNuB,QAAAA,OAAO,EAAE;AAHH,OAAD,C,UAkBRX,QAAQ,CAAC;AACNO,QAAAA,KAAK,EAAE;AAAEC,UAAAA,EAAE,EAAE,cAAN;AAAsBC,UAAAA,IAAI,EAAE,+BAA5B;AAA6DC,UAAAA,KAAK,EAAE;AAApE,SADD;AAENR,QAAAA,IAAI,EAAEjB,OAFA;AAGN2B,QAAAA,GAAG,EAAE,CAHC;AAIND,QAAAA,OAAO,EAAE;AAJH,OAAD,C,UAaRX,QAAQ,CAAC;AACNO,QAAAA,KAAK,EAAE;AAAEC,UAAAA,EAAE,EAAE,cAAN;AAAsBC,UAAAA,IAAI,EAAE,+BAA5B;AAA6DC,UAAAA,KAAK,EAAE;AAApE,SADD;AAENR,QAAAA,IAAI,EAAEjB,OAFA;AAGN2B,QAAAA,GAAG,EAAE,CAHC;AAIND,QAAAA,OAAO,EAAE;AAJH,OAAD,C,WAaRX,QAAQ,CAAC;AACNO,QAAAA,KAAK,EAAE;AAAEC,UAAAA,EAAE,EAAE,cAAN;AAAsBC,UAAAA,IAAI,EAAE,+BAA5B;AAA6DC,UAAAA,KAAK,EAAE;AAApE,SADD;AAENR,QAAAA,IAAI,EAAEjB,OAFA;AAGN4B,QAAAA,KAAK,EAAE,CAAC,GAAD,EAAM,CAAN,EAAS,IAAT,CAHD;AAINC,QAAAA,KAAK,EAAE,IAJD;AAKNH,QAAAA,OAAO,EAAE;AALH,OAAD,C,WAcRT,IAAI,CAACjB,OAAD,C,WACJe,QAAQ,CAAC;AACNO,QAAAA,KAAK,EAAE;AAAEC,UAAAA,EAAE,EAAE,cAAN;AAAsBC,UAAAA,IAAI,EAAE,+BAA5B;AAA6DC,UAAAA,KAAK,EAAE;AAApE,SADD;AAENR,QAAAA,IAAI,EAAEjB,OAFA;AAGN4B,QAAAA,KAAK,EAAE,CAAC,IAAD,EAAO,EAAP,EAAW,IAAX,CAHD;AAINC,QAAAA,KAAK,EAAE,IAJD;AAKNH,QAAAA,OAAO,EAAE;AALH,OAAD,C,WAcRT,IAAI,CAACZ,IAAD,C,WACJU,QAAQ,CAAC;AACNO,QAAAA,KAAK,EAAE;AAAEC,UAAAA,EAAE,EAAE,cAAN;AAAsBC,UAAAA,IAAI,EAAE,+BAA5B;AAA6DC,UAAAA,KAAK,EAAE;AAApE,SADD;AAENR,QAAAA,IAAI,EAAEZ,IAFA;AAGNqB,QAAAA,OAAO,EAAE;AAHH,OAAD,C,8CAxHZd,gB,UACAC,iB,qBAJD,MAKaQ,uBALb;AAAA;AAAA,oEAM6C;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AA+PzC;AA/PyC,eAgQxBS,2BAhQwB,GAgQM,IAAIZ,KAAJ,CAAU,CAAV,EAAa,CAAb,EAAgB,CAAhB,EAAmB,CAAnB,CAhQN;AAAA,eAiQxBa,UAjQwB,GAiQX,IAAIzB,IAAJ,CAAS,CAAT,EAAY,CAAZ,EAAe,CAAf,EAAkB,CAAlB,CAjQW;AAAA,eAkQxB0B,aAlQwB,GAkQR,IAAI1B,IAAJ,CAAS,CAAT,EAAY,CAAZ,EAAe,CAAf,EAAkB,CAAlB,CAlQQ;AAAA,eAmQxB2B,WAnQwB,GAmQV,IAAI3B,IAAJ,CAAS,CAAT,EAAY,CAAZ,EAAe,CAAf,EAAkB,CAAlB,CAnQU;AAAA;;AA2BzC;AAMa,YAAT4B,SAAS,CAACC,KAAD,EAAiB;AAC1B,eAAKC,UAAL,GAAkBD,KAAlB;;AACA,cAAI5B,MAAJ,EAAY;AACR,iBAAK8B,OAAL,CAAaC,uBAAb;AACH;AACJ;;AACY,YAATJ,SAAS,GAAY;AACrB,iBAAO,KAAKE,UAAZ;AACH;;AAOc,YAAXG,WAAW,CAACJ,KAAD,EAAkB;AAC7B,cAAI,KAAKK,SAAL,KAAmBL,KAAvB,EAA8B;AAC1B;AACH;;AACD,eAAKK,SAAL,GAAiBL,KAAjB;;AACA,cAAI5B,MAAJ,EAAY;AACR,iBAAK8B,OAAL,CAAaC,uBAAb;AACH;AACJ;;AACc,YAAXC,WAAW,GAAa;AACxB,iBAAO,KAAKC,SAAZ;AACH;;AAQc,YAAXC,WAAW,CAACN,KAAD,EAAgB;AAC3B,eAAKO,SAAL,GAAiBP,KAAjB;AACH;;AACc,YAAXM,WAAW,GAAW;AACtB,iBAAO,KAAKC,SAAZ;AACH;;AAQc,YAAXC,WAAW,CAACR,KAAD,EAAgB;AAC3B,eAAKS,SAAL,GAAiBT,KAAjB;AACH;;AACc,YAAXQ,WAAW,GAAW;AACtB,iBAAO,KAAKC,SAAZ;AACH;;AASe,YAAZC,YAAY,CAACV,KAAD,EAAgB;AAC5B,eAAKW,UAAL,GAAkBX,KAAlB;AACH;;AACe,YAAZU,YAAY,GAAW;AACvB,iBAAO,KAAKC,UAAZ;AACH;;AAUgB,YAAbC,aAAa,CAACZ,KAAD,EAAgB;AAC7B,eAAKa,WAAL,GAAmBb,KAAnB;AACH;;AACgB,YAAbY,aAAa,GAAW;AACxB,iBAAO,KAAKC,WAAZ;AACH;;AAQc,YAAXC,WAAW,CAACd,KAAD,EAAc;AACzB,eAAKe,SAAL,GAAiBf,KAAjB;AACH;;AACc,YAAXc,WAAW,GAAS;AACpB,iBAAO,KAAKC,SAAZ;AACH,SA/HwC,CAiIzC;;;AACAC,QAAAA,cAAc,GAAW;AACrB,iBAAO,KAAKC,WAAZ;AACH;;AACDC,QAAAA,cAAc,GAAW;AACrB,iBAAO,KAAKC,WAAZ;AACH;;AACDC,QAAAA,YAAY,CACRC,MADQ,EAERC,UAFQ,EAGRC,aAHQ,EAG6C;AACrDA,UAAAA,aAAa,CAACC,SAAd,GAA0BF,UAAU,CAACG,kBAAX,IACnB,KAAKxB,UADc,IAEnB,CAAC,CAAC,KAAKI,SAFd;;AAIA,cAAIkB,aAAa,CAACC,SAAlB,EAA6B;AACzB;AACAD,YAAAA,aAAa,CAACG,qBAAd,GAAsC,IAAtC;AACA,cAAEH,aAAa,CAACI,eAAhB;AACH;AACJ;;AACDC,QAAAA,YAAY,CACRC,GADQ,EAERP,UAFQ,EAGRC,aAHQ,EAIRO,MAJQ,EAI6B;AACrC,cAAM1C,EAAE,GAAG0C,MAAM,CAACC,cAAlB;;AACA,cAAIR,aAAa,CAACC,SAAlB,EAA6B;AACzBK,YAAAA,GAAG,CAACG,eAAJ,iBAAkC5C,EAAlC,EACImC,aAAa,CAACU,cADlB,EAEIV,aAAa,CAACW,KAFlB,EAGIX,aAAa,CAACY,MAHlB;AAIH;AACJ;;AACDC,QAAAA,KAAK,CACDP,GADC,EAEDP,UAFC,EAGDC,aAHC,EAIDF,MAJC,EAKDgB,OALC,EAMDC,cANC,EAMgG;AACjG,cAAI,CAACf,aAAa,CAACC,SAAnB,EAA8B;AAC1B,mBAAOc,cAAP;AACH;;AACD,YAAEf,aAAa,CAACI,eAAhB;AAEAhE,UAAAA,MAAM,CAAC,CAAC,CAAC,KAAK0C,SAAR,CAAN;;AACA,cAAIkB,aAAa,CAACI,eAAd,KAAkC,CAAtC,EAAyC;AACrC,mBAAO,KAAKY,sBAAL,CAA4BV,GAA5B,EAAiCP,UAAjC,EACHC,aADG,EACY,KAAKlB,SADjB,EAEHgB,MAFG,EAEKE,aAAa,CAACW,KAFnB,EAE0BX,aAAa,CAACY,MAFxC,EAGHE,OAAO,CAACG,SAHL,EAIHH,OAAO,CAACI,gBAJL,EAKHlB,aAAa,CAACiB,SALX,CAAP;AAMH,WAPD,MAOO;AACH,gBAAME,MAAM,GAAGnB,aAAa,CAACoB,kBAAd,gCAAf;AAGA,gBAAMC,kBAAkB,GAAG;AAAA;AAAA,oEACvBP,OAAO,CAACG,SADe,EACJE,MADI,EACInB,aAAa,CAACQ,cADlB,CAA3B;AAEA,gBAAMc,iBAAiB,GAAGR,OAAO,CAACG,SAAlC;AACAH,YAAAA,OAAO,CAACG,SAAR,GAAoBI,kBAApB;AACA,mBAAO,KAAKL,sBAAL,CAA4BV,GAA5B,EAAiCP,UAAjC,EACHC,aADG,EACY,KAAKlB,SADjB,EAEHgB,MAFG,EAEKE,aAAa,CAACW,KAFnB,EAE0BX,aAAa,CAACY,MAFxC,EAGHU,iBAHG,EAIHR,OAAO,CAACI,gBAJL,EAKHG,kBALG,CAAP;AAMH;AACJ;;AACOL,QAAAA,sBAAsB,CAC1BV,GAD0B,EAE1BP,UAF0B,EAG1BC,aAH0B,EAI1BnB,WAJ0B,EAK1BiB,MAL0B,EAM1Ba,KAN0B,EAO1BC,MAP0B,EAQ1BW,aAR0B,EAS1BC,iBAT0B,EAU1BH,kBAV0B,EAWM;AAChC,eAAKhD,UAAL,CAAgBoD,CAAhB,GAAoB,KAAKzC,SAAzB;AACA,eAAKX,UAAL,CAAgBqD,CAAhB,GAAoB,KAAKxC,SAAzB;AACA,eAAKb,UAAL,CAAgBsD,CAAhB,GAAoB,KAAKrC,WAAzB;AACA,eAAKjB,UAAL,CAAgBuD,CAAhB,GAAoB,KAAKxC,UAAzB;AACA,eAAKd,aAAL,CAAmBmD,CAAnB,GAAuB,KAAKjC,SAAL,CAAeiC,CAAtC;AACA,eAAKnD,aAAL,CAAmBoD,CAAnB,GAAuB,KAAKlC,SAAL,CAAekC,CAAtC;AACA,eAAKpD,aAAL,CAAmBqD,CAAnB,GAAuB,KAAKnC,SAAL,CAAemC,CAAtC;AACA,eAAKpD,WAAL,CAAiBkD,CAAjB,GAAqB,MAAMd,KAA3B;AACA,eAAKpC,WAAL,CAAiBmD,CAAjB,GAAqB,MAAMd,MAA3B;AACA,eAAKrC,WAAL,CAAiBoD,CAAjB,GAAqBhB,KAArB;AACA,eAAKpC,WAAL,CAAiBqD,CAAjB,GAAqBhB,MAArB;AAEA,cAAM/C,EAAE,GAAGmC,aAAa,CAACQ,cAAzB;AACA,cAAMqB,YAAY,mBAAiBhE,EAAnC,CAdgC,CAgBhC;;AACA,cAAMiE,QAAQ,GAAGxB,GAAG,CAACyB,aAAJ,CAAkBpB,KAAlB,EAAyBC,MAAzB,EAAiC,aAAjC,CAAjB;AACAkB,UAAAA,QAAQ,CAACrB,eAAT,CAAyBoB,YAAzB,EAAuCpE,MAAM,CAACuE,KAA9C,EAAqDtE,OAAO,CAACuE,KAA7D,EAAoE,KAAK7D,2BAAzE;AACA0D,UAAAA,QAAQ,CAACI,UAAT,CAAoBX,aAApB,EAAmC,WAAnC;AACAO,UAAAA,QAAQ,CAACK,OAAT,CAAiB,YAAjB,EAA+BpC,UAAU,CAACqC,QAA1C;AACAN,UAAAA,QAAQ,CAACK,OAAT,CAAiB,YAAjB,EAA+B,KAAK9D,UAApC;AACAyD,UAAAA,QAAQ,CAACK,OAAT,CAAiB,kBAAjB,EAAqC,KAAK5D,WAA1C;AACAuD,UAAAA,QAAQ,CACHO,QADL,CACc3F,SAAS,CAAC4F,SAAV,CAAoBC,MADlC,EAEKC,aAFL,CAEmB1C,MAFnB,EAE2BjB,WAF3B,EAEwC,CAFxC,EAvBgC,CAyBY;AAC5C;;AACA,cAAM4D,OAAO,GAAGnC,GAAG,CAACyB,aAAJ,CAAkBpB,KAAlB,EAAyBC,MAAzB,EAAiC,YAAjC,CAAhB;AACA6B,UAAAA,OAAO,CAAChC,eAAR,CAAwBY,kBAAxB,EAA4C5D,MAAM,CAACuE,KAAnD,EAA0DtE,OAAO,CAACuE,KAAlE,EAAyE,KAAK7D,2BAA9E;AACAqE,UAAAA,OAAO,CAACP,UAAR,CAAmBL,YAAnB,EAAiC,UAAjC;AACAY,UAAAA,OAAO,CAACP,UAAR,CAAmBV,iBAAnB,EAAsC,UAAtC;AACAiB,UAAAA,OAAO,CAACP,UAAR,CAAmBX,aAAnB,EAAkC,WAAlC;AACAkB,UAAAA,OAAO,CAACN,OAAR,CAAgB,YAAhB,EAA8BpC,UAAU,CAACqC,QAAzC;AACAK,UAAAA,OAAO,CAACC,OAAR,CAAgB,MAAhB,EAAwB5C,MAAM,CAAC6C,OAA/B;AACAF,UAAAA,OAAO,CAACC,OAAR,CAAgB,SAAhB,EAA2B5C,MAAM,CAAC8C,UAAlC;AACAH,UAAAA,OAAO,CAACC,OAAR,CAAgB,YAAhB,EAA8B5C,MAAM,CAAC+C,IAAP,CAAYC,WAA1C;AACAL,UAAAA,OAAO,CAACN,OAAR,CAAgB,WAAhB,EAA6B,KAAK9D,UAAlC;AACAoE,UAAAA,OAAO,CAACN,OAAR,CAAgB,OAAhB,EAAyB,KAAK7D,aAA9B;AACAmE,UAAAA,OAAO,CACFJ,QADL,CACc3F,SAAS,CAAC4F,SAAV,CAAoBC,MADlC,EAEKC,aAFL,CAEmB1C,MAFnB,EAE2BjB,WAF3B,EAEwC,CAFxC;AAIA,iBAAO4D,OAAP;AACH;;AA7PwC,O;;;;;iBAK3B,C;;;;;;;iBAKA,G;;qFAEbpF,Q;;;;;iBACoB,K;;oFACpBA,Q;;;;;iBACoC,I;;oFACpCA,Q;;;;;iBACmB,C;;oFACnBA,Q;;;;;iBACmB,C;;sFACnBA,Q;;;;;iBACqB,C;;qFACrBA,Q;;;;;iBACoB,C;;oFACpBA,Q;;;;;iBACmB,IAAIV,IAAJ,CAAS,CAAT,EAAY,CAAZ,EAAe,CAAf,C", "sourcesContent": ["/*\r\n Copyright (c) 2021-2024 Xiamen Yaji Software Co., Ltd.\r\n\r\n https://www.cocos.com/\r\n\r\n Permission is hereby granted, free of charge, to any person obtaining a copy\r\n of this software and associated documentation files (the \"Software\"), to deal\r\n in the Software without restriction, including without limitation the rights to\r\n use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies\r\n of the Software, and to permit persons to whom the Software is furnished to do so,\r\n subject to the following conditions:\r\n\r\n The above copyright notice and this permission notice shall be included in\r\n all copies or substantial portions of the Software.\r\n\r\n THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\r\n IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\r\n FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\r\n AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\r\n LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\r\n OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\r\n THE SOFTWARE.\r\n*/\r\n\r\nimport {\r\n    _decorator, assert, CCBoolean, CCFloat, CCInteger,\r\n    gfx, Material, renderer, rendering, Vec3, Vec4,\r\n} from 'cc';\r\n\r\nimport { EDITOR } from 'cc/env';\r\n\r\nimport {\r\n    BuiltinPipelineSettings,\r\n} from './builtin-pipeline-settings';\r\n\r\nimport {\r\n    BuiltinPipelinePassBuilder,\r\n} from './builtin-pipeline-pass';\r\n\r\nimport {\r\n    CameraConfigs,\r\n    getPingPongRenderTarget,\r\n    PipelineConfigs,\r\n    PipelineContext,\r\n} from './builtin-pipeline';\r\n\r\nconst { ccclass, disallowMultiple, executeInEditMode, menu, property, requireComponent, type } = _decorator;\r\n\r\nconst { Color, LoadOp, StoreOp } = gfx;\r\n\r\nexport interface DofPassConfigs {\r\n    enableDof: boolean;\r\n}\r\n\r\n@ccclass('BuiltinDepthOfFieldPass')\r\n@menu('Rendering/BuiltinDepthOfFieldPass')\r\n@requireComponent(BuiltinPipelineSettings)\r\n@disallowMultiple\r\n@executeInEditMode\r\nexport class BuiltinDepthOfFieldPass extends BuiltinPipelinePassBuilder\r\n    implements rendering.PipelinePassBuilder {\r\n    @property({\r\n        group: { id: 'BuiltinPass', name: 'Pass Settings', style: 'section' },\r\n        type: CCInteger,\r\n    })\r\n    configOrder = 0;\r\n    @property({\r\n        group: { id: 'BuiltinPass', name: 'Pass Settings', style: 'section' },\r\n        type: CCInteger,\r\n    })\r\n    renderOrder = 150;\r\n\r\n    @property\r\n    private _enableDof = false;\r\n    @property\r\n    private _material: Material | null = null;\r\n    @property\r\n    private _minRange = 0;\r\n    @property\r\n    private _maxRange = 2;\r\n    @property\r\n    private _blurRadius = 1;\r\n    @property\r\n    private _intensity = 1;\r\n    @property\r\n    private _focusPos = new Vec3(0, 0, 0);\r\n\r\n    // DepthOfField\r\n    @property({\r\n        group: { id: 'DepthOfField', name: 'DepthOfField (PostProcessing)', style: 'section' },\r\n        type: CCBoolean,\r\n        visible: true,\r\n    })\r\n    set dofEnable(value: boolean) {\r\n        this._enableDof = value;\r\n        if (EDITOR) {\r\n            this._parent._tryEnableEditorPreview();\r\n        }\r\n    }\r\n    get dofEnable(): boolean {\r\n        return this._enableDof;\r\n    }\r\n\r\n    @property({\r\n        group: { id: 'DepthOfField', name: 'DepthOfField (PostProcessing)', style: 'section' },\r\n        type: Material,\r\n        visible: true,\r\n    })\r\n    set dofMaterial(value: Material) {\r\n        if (this._material === value) {\r\n            return;\r\n        }\r\n        this._material = value;\r\n        if (EDITOR) {\r\n            this._parent._tryEnableEditorPreview();\r\n        }\r\n    }\r\n    get dofMaterial(): Material {\r\n        return this._material!;\r\n    }\r\n\r\n    @property({\r\n        group: { id: 'DepthOfField', name: 'DepthOfField (PostProcessing)', style: 'section' },\r\n        type: CCFloat,\r\n        min: 0,\r\n        visible: true,\r\n    })\r\n    set dofMinRange(value: number) {\r\n        this._minRange = value;\r\n    }\r\n    get dofMinRange(): number {\r\n        return this._minRange;\r\n    }\r\n\r\n    @property({\r\n        group: { id: 'DepthOfField', name: 'DepthOfField (PostProcessing)', style: 'section' },\r\n        type: CCFloat,\r\n        min: 0,\r\n        visible: true,\r\n    })\r\n    set dofMaxRange(value: number) {\r\n        this._maxRange = value;\r\n    }\r\n    get dofMaxRange(): number {\r\n        return this._maxRange;\r\n    }\r\n\r\n    @property({\r\n        group: { id: 'DepthOfField', name: 'DepthOfField (PostProcessing)', style: 'section' },\r\n        type: CCFloat,\r\n        range: [0.0, 2, 0.01],\r\n        slide: true,\r\n        visible: true,\r\n    })\r\n    set dofIntensity(value: number) {\r\n        this._intensity = value;\r\n    }\r\n    get dofIntensity(): number {\r\n        return this._intensity;\r\n    }\r\n\r\n    @type(CCFloat)\r\n    @property({\r\n        group: { id: 'DepthOfField', name: 'DepthOfField (PostProcessing)', style: 'section' },\r\n        type: CCFloat,\r\n        range: [0.01, 10, 0.01],\r\n        slide: true,\r\n        visible: true,\r\n    })\r\n    set dofBlurRadius(value: number) {\r\n        this._blurRadius = value;\r\n    }\r\n    get dofBlurRadius(): number {\r\n        return this._blurRadius;\r\n    }\r\n\r\n    @type(Vec3)\r\n    @property({\r\n        group: { id: 'DepthOfField', name: 'DepthOfField (PostProcessing)', style: 'section' },\r\n        type: Vec3,\r\n        visible: true,\r\n    })\r\n    set dofFocusPos(value: Vec3) {\r\n        this._focusPos = value;\r\n    }\r\n    get dofFocusPos(): Vec3 {\r\n        return this._focusPos;\r\n    }\r\n\r\n    // PipelinePassBuilder\r\n    getConfigOrder(): number {\r\n        return this.configOrder;\r\n    }\r\n    getRenderOrder(): number {\r\n        return this.renderOrder;\r\n    }\r\n    configCamera(\r\n        camera: Readonly<renderer.scene.Camera>,\r\n        pplConfigs: Readonly<PipelineConfigs>,\r\n        cameraConfigs: CameraConfigs & DofPassConfigs): void {\r\n        cameraConfigs.enableDof = pplConfigs.supportDepthSample\r\n            && this._enableDof\r\n            && !!this._material;\r\n\r\n        if (cameraConfigs.enableDof) {\r\n            // Output scene depth, this is allowed but has performance impact\r\n            cameraConfigs.enableStoreSceneDepth = true;\r\n            ++cameraConfigs.remainingPasses;\r\n        }\r\n    }\r\n    windowResize(\r\n        ppl: rendering.BasicPipeline,\r\n        pplConfigs: Readonly<PipelineConfigs>,\r\n        cameraConfigs: Readonly<CameraConfigs & DofPassConfigs>,\r\n        window: renderer.RenderWindow): void {\r\n        const id = window.renderWindowId;\r\n        if (cameraConfigs.enableDof) {\r\n            ppl.addRenderTarget(`DofRadiance${id}`,\r\n                cameraConfigs.radianceFormat,\r\n                cameraConfigs.width,\r\n                cameraConfigs.height);\r\n        }\r\n    }\r\n    setup(\r\n        ppl: rendering.BasicPipeline,\r\n        pplConfigs: Readonly<PipelineConfigs>,\r\n        cameraConfigs: CameraConfigs & Readonly<DofPassConfigs>,\r\n        camera: renderer.scene.Camera,\r\n        context: PipelineContext,\r\n        prevRenderPass?: rendering.BasicRenderPassBuilder): rendering.BasicRenderPassBuilder | undefined {\r\n        if (!cameraConfigs.enableDof) {\r\n            return prevRenderPass;\r\n        }\r\n        --cameraConfigs.remainingPasses;\r\n\r\n        assert(!!this._material);\r\n        if (cameraConfigs.remainingPasses === 0) {\r\n            return this._addDepthOfFieldPasses(ppl, pplConfigs,\r\n                cameraConfigs, this._material,\r\n                camera, cameraConfigs.width, cameraConfigs.height,\r\n                context.colorName,\r\n                context.depthStencilName,\r\n                cameraConfigs.colorName);\r\n        } else {\r\n            const prefix = cameraConfigs.enableShadingScale\r\n                ? `ScaledRadiance`\r\n                : `Radiance`;\r\n            const outputRadianceName = getPingPongRenderTarget(\r\n                context.colorName, prefix, cameraConfigs.renderWindowId);\r\n            const inputRadianceName = context.colorName;\r\n            context.colorName = outputRadianceName;\r\n            return this._addDepthOfFieldPasses(ppl, pplConfigs,\r\n                cameraConfigs, this._material,\r\n                camera, cameraConfigs.width, cameraConfigs.height,\r\n                inputRadianceName,\r\n                context.depthStencilName,\r\n                outputRadianceName);\r\n        }\r\n    }\r\n    private _addDepthOfFieldPasses(\r\n        ppl: rendering.BasicPipeline,\r\n        pplConfigs: Readonly<PipelineConfigs>,\r\n        cameraConfigs: CameraConfigs & Readonly<DofPassConfigs>,\r\n        dofMaterial: Material,\r\n        camera: renderer.scene.Camera,\r\n        width: number,\r\n        height: number,\r\n        inputRadiance: string,\r\n        inputDepthStencil: string,\r\n        outputRadianceName: string,\r\n    ): rendering.BasicRenderPassBuilder {\r\n        this._cocParams.x = this._minRange;\r\n        this._cocParams.y = this._maxRange;\r\n        this._cocParams.z = this._blurRadius;\r\n        this._cocParams.w = this._intensity;\r\n        this._focusPosVec4.x = this._focusPos.x;\r\n        this._focusPosVec4.y = this._focusPos.y;\r\n        this._focusPosVec4.z = this._focusPos.z;\r\n        this._cocTexSize.x = 1.0 / width;\r\n        this._cocTexSize.y = 1.0 / height;\r\n        this._cocTexSize.z = width;\r\n        this._cocTexSize.w = height;\r\n\r\n        const id = cameraConfigs.renderWindowId;\r\n        const tempRadiance = `DofRadiance${id}`;\r\n\r\n        // Blur Pass\r\n        const blurPass = ppl.addRenderPass(width, height, 'cc-dof-blur');\r\n        blurPass.addRenderTarget(tempRadiance, LoadOp.CLEAR, StoreOp.STORE, this._clearColorTransparentBlack);\r\n        blurPass.addTexture(inputRadiance, 'screenTex');\r\n        blurPass.setVec4('g_platform', pplConfigs.platform);\r\n        blurPass.setVec4('blurParams', this._cocParams);\r\n        blurPass.setVec4('mainTexTexelSize', this._cocTexSize);\r\n        blurPass\r\n            .addQueue(rendering.QueueHint.OPAQUE)\r\n            .addCameraQuad(camera, dofMaterial, 0); // addCameraQuad will set camera related UBOs\r\n        // coc pass\r\n        const cocPass = ppl.addRenderPass(width, height, 'cc-dof-coc');\r\n        cocPass.addRenderTarget(outputRadianceName, LoadOp.CLEAR, StoreOp.STORE, this._clearColorTransparentBlack);\r\n        cocPass.addTexture(tempRadiance, 'colorTex');\r\n        cocPass.addTexture(inputDepthStencil, \"DepthTex\");\r\n        cocPass.addTexture(inputRadiance, \"screenTex\");\r\n        cocPass.setVec4('g_platform', pplConfigs.platform);\r\n        cocPass.setMat4('proj', camera.matProj);\r\n        cocPass.setMat4('invProj', camera.matProjInv);\r\n        cocPass.setMat4('viewMatInv', camera.node.worldMatrix);\r\n        cocPass.setVec4('cocParams', this._cocParams);\r\n        cocPass.setVec4('focus', this._focusPosVec4);\r\n        cocPass\r\n            .addQueue(rendering.QueueHint.OPAQUE)\r\n            .addCameraQuad(camera, dofMaterial, 1);\r\n\r\n        return cocPass;\r\n    }\r\n\r\n    // Runtime members\r\n    private readonly _clearColorTransparentBlack = new Color(0, 0, 0, 0);\r\n    private readonly _cocParams = new Vec4(0, 0, 0, 0);\r\n    private readonly _focusPosVec4 = new Vec4(0, 0, 0, 1);\r\n    private readonly _cocTexSize = new Vec4(0, 0, 0, 0);\r\n}\r\n"]}