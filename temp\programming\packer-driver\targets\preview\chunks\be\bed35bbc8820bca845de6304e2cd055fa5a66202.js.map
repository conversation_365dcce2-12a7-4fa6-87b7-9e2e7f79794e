{"version": 3, "sources": ["file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/gizmos/index.ts"], "names": ["setupDefaultGizmos", "autoRegisterDrawers", "console", "log", "createGizmoManagerNode", "scene<PERSON><PERSON>", "Node", "find", "director", "require", "scene", "getScene", "error", "gizmoNode", "addComponent", "<PERSON><PERSON><PERSON><PERSON>", "quickSetupGizmos", "GizmoDrawer", "RegisterGizmoDrawer", "autoRegisterGizmoDrawers", "getRegisteredGizmoDrawers", "GizmoManager", "Gizmo<PERSON><PERSON>s", "EmitterGizmo", "EmitterArcGizmo", "GIZMO_SYSTEM_VERSION"], "mappings": ";;;;;AAmBA;AACA;AACA;AACA;AACA;AACO,WAASA,kBAAT,GAAoC;AACvC;AACA;AAAA;AAAA,sCAAaC,mBAAb;AAEAC,IAAAA,OAAO,CAACC,GAAR,CAAY,sCAAZ;AACH;AAED;AACA;AACA;AACA;AACA;;;AACO,WAASC,sBAAT,CAAgCC,SAAhC,EAAgE;AACnE,QAAM;AAAEC,MAAAA,IAAF;AAAQC,MAAAA,IAAR;AAAcC,MAAAA;AAAd,QAA2BC,OAAO,CAAC,IAAD,CAAxC;;AAEA,QAAIC,KAAK,GAAG,IAAZ;;AACA,QAAIL,SAAJ,EAAe;AACXK,MAAAA,KAAK,GAAGH,IAAI,CAACF,SAAD,CAAZ;AACH,KAFD,MAEO;AACHK,MAAAA,KAAK,GAAGF,QAAQ,CAACG,QAAT,EAAR;AACH;;AAED,QAAI,CAACD,KAAL,EAAY;AACRR,MAAAA,OAAO,CAACU,KAAR,CAAc,2DAAd;AACA,aAAO,IAAP;AACH,KAbkE,CAenE;;;AACA,QAAMC,SAAS,GAAG,IAAIP,IAAJ,CAAS,cAAT,CAAlB;AACAO,IAAAA,SAAS,CAACC,YAAV;AAAA;AAAA,sCAjBmE,CAmBnE;;AACAJ,IAAAA,KAAK,CAACK,QAAN,CAAeF,SAAf;AAEAX,IAAAA,OAAO,CAACC,GAAR,CAAY,yCAAZ;AACA,WAAOU,SAAP;AACH;AAED;AACA;AACA;AACA;AACA;;;AACO,WAASG,gBAAT,CAA0BX,SAA1B,EAA0D;AAC7D,QAAMQ,SAAS,GAAGT,sBAAsB,CAACC,SAAD,CAAxC;;AACA,QAAIQ,SAAJ,EAAe;AACXb,MAAAA,kBAAkB;AACrB;;AACD,WAAOa,SAAP;AACH;AAED;AACA;AACA;;;;;;;;wBArDgBb,kB;4BAYAI,sB;sBA+BAY;;;;;;;;;;mCA3DPC,W;2CAAaC,mB;gDAAqBC,wB;iDAA0BC;;;4CAC5DC,Y;;0CACAC,U;;4CAGAC,Y;;+CACAC,e;;AAGAH,MAAAA,Y,iBAAAA,Y;;;;;;AAjBT;AACA;AACA;AACA;AACA;AACA;AAEA;AAKA;AAIA;;;sCA8DaI,oB,GAAuB,O;AAEpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "sourcesContent": ["/**\n * Gizmo System Index\n * \n * This file exports all components of the Gizmo system for easy importing\n * and provides utilities for setting up the gizmo system.\n */\n\n// Core gizmo system\nexport { GizmoDrawer, RegisterGizmoDrawer, autoRegisterGizmoDrawers, getRegisteredGizmoDrawers } from './GizmoDrawer';\nexport { GizmoManager } from './GizmoManager';\nexport { GizmoUtils } from './GizmoUtils';\n\n// Specific gizmo drawers (importing these triggers the @RegisterGizmoDrawer decorators)\nexport { EmitterGizmo } from './EmitterGizmo';\nexport { EmitterArcGizmo } from './EmitterArcGizmo';\n\n// Setup utilities\nimport { GizmoManager } from './GizmoManager';\n\n/**\n * Setup the default gizmo system with auto-registered drawers\n * Call this once when your game starts or in the editor\n * Note: The gizmo classes are already imported above, so their decorators have been executed\n */\nexport function setupDefaultGizmos(): void {\n    // Auto-register all decorated gizmo drawers\n    GizmoManager.autoRegisterDrawers();\n\n    console.log('Gizmo System: Auto-registered gizmos');\n}\n\n/**\n * Create a gizmo manager node and attach it to the scene\n * @param sceneName Optional scene name, if not provided uses current scene\n * @returns The created gizmo manager node\n */\nexport function createGizmoManagerNode(sceneName?: string): any | null {\n    const { Node, find, director } = require('cc');\n\n    let scene = null;\n    if (sceneName) {\n        scene = find(sceneName);\n    } else {\n        scene = director.getScene();\n    }\n\n    if (!scene) {\n        console.error('GizmoSystem: Could not find scene to attach gizmo manager');\n        return null;\n    }\n\n    // Create gizmo manager node\n    const gizmoNode = new Node('GizmoManager');\n    gizmoNode.addComponent(GizmoManager);\n\n    // Add to scene\n    scene.addChild(gizmoNode);\n\n    console.log('GizmoSystem: Created gizmo manager node');\n    return gizmoNode;\n}\n\n/**\n * Quick setup function that creates the gizmo manager and registers default drawers\n * @param sceneName Optional scene name\n * @returns The created gizmo manager node\n */\nexport function quickSetupGizmos(sceneName?: string): any | null {\n    const gizmoNode = createGizmoManagerNode(sceneName);\n    if (gizmoNode) {\n        setupDefaultGizmos();\n    }\n    return gizmoNode;\n}\n\n/**\n * Version information for the Gizmo system\n */\nexport const GIZMO_SYSTEM_VERSION = \"1.0.0\";\n\n/**\n * Example usage documentation\n * \n * Basic setup in your game initialization:\n * ```typescript\n * import { quickSetupGizmos } from \"./Game/gizmos\";\n * \n * // In your game startup code\n * quickSetupGizmos();\n * ```\n * \n * Manual setup:\n * ```typescript\n * import { \n *     GizmoManager, \n *     EmitterArcGizmo, \n *     EmitterGizmo,\n *     setupDefaultGizmos \n * } from \"./Game/gizmos\";\n * \n * // Create gizmo manager node manually\n * const gizmoNode = new Node('GizmoManager');\n * gizmoNode.addComponent(GizmoManager);\n * scene.addChild(gizmoNode);\n * \n * // Register default drawers\n * setupDefaultGizmos();\n * \n * // Or register custom drawers\n * const customEmitterGizmo = new EmitterArcGizmo();\n * customEmitterGizmo.configure({\n *     showRadius: true,\n *     showDirections: true,\n *     directionColor: Color.BLUE\n * });\n * GizmoManager.registerDrawer(customEmitterGizmo);\n * ```\n * \n * Creating custom gizmo drawers:\n * ```typescript\n * import { GizmoDrawer } from \"./Game/gizmos\";\n * import { MyCustomComponent } from \"./MyCustomComponent\";\n * \n * class MyCustomGizmo extends GizmoDrawer<MyCustomComponent> {\n *     public readonly componentType = MyCustomComponent;\n *     public readonly drawerName = \"MyCustomGizmo\";\n *     \n *     public drawGizmos(component: MyCustomComponent, graphics: Graphics, node: Node): void {\n *         // Your custom drawing logic here\n *         this.drawCross(graphics, 0, 0, 10, Color.GREEN);\n *     }\n * }\n * \n * // Register your custom drawer\n * GizmoManager.registerDrawer(new MyCustomGizmo());\n * ```\n */\n"]}