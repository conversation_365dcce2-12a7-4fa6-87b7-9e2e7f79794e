/**
 * CrazyStorm 2.0 Integration for Cocos Creator
 * EventField Component Implementation
 * 
 * This component triggers events when particles enter or exit a defined area,
 * useful for creating interactive effects and game mechanics.
 */
import { _decorator, Vec2, log, warn, Enum } from 'cc';
import { CrazyStormComponent } from './CrazyStormComponent';
import { Particle } from '../physics/ParticlePhysics';
import { 
    EventFieldComponent as EventFieldData,
    FieldShape,
    Reach,
    ComponentType,
    EventGroup,
    ParticleEvent
} from '../core/CrazyStormTypes';

const { ccclass, property } = _decorator;

/**
 * Event types for EventField
 */
export enum EventFieldEventType {
    OnEnter = 'OnEnter',
    OnExit = 'OnExit',
    OnStay = 'OnStay'
}

// Register the enum with Cocos Creator
Enum(EventFieldEventType);

/**
 * EventField component for Cocos Creator
 */
@ccclass('EventFieldComponent')
export class EventFieldComponent extends CrazyStormComponent {
    @property({ displayName: "Half Width" })
    halfWidth: number = 50;

    @property({ displayName: "Half Height" })
    halfHeight: number = 50;

    @property({ type: Enum(FieldShape), displayName: "Field Shape" })
    fieldShape: FieldShape = FieldShape.Rectangle;

    @property({ type: Enum(Reach), displayName: "Reach" })
    reach: Reach = Reach.All;

    @property({ displayName: "Target Name" })
    targetName: string = '';

    @property({ displayName: "Debug Draw" })
    debugDraw: boolean = false;

    // Runtime data
    private particlesInField: Set<number> = new Set(); // Track particles currently in field
    private eventGroups: EventGroup[] = [];
    private particleManager: any = null;

    // Event callbacks
    public onParticleEnter: ((particle: Particle) => void) | null = null;
    public onParticleExit: ((particle: Particle) => void) | null = null;
    public onParticleStay: ((particle: Particle) => void) | null = null;

    protected initializeComponent(): void {
        this.particlesInField = new Set();
        log(`EventFieldComponent: Initialized at position [${this.node.position.x}, ${this.node.position.y}]`);
    }

    protected onDataInitialized(): void {
        if (!this.crazyStormData || this.crazyStormData.specificType !== ComponentType.EventField) {
            warn('EventFieldComponent: Invalid CrazyStorm data');
            return;
        }

        const data = this.crazyStormData as EventFieldData;
        
        // Store event groups
        this.eventGroups = [...data.componentEventGroups];

        log(`EventFieldComponent: Initialized with ${this.eventGroups.length} event groups`);
    }

    protected onReset(): void {
        this.particlesInField.clear();
    }

    protected onUpdate(deltaTime: number): void {
        // Check particles for enter/exit events
        this.checkParticleEvents();
    }

    /**
     * Check for particle enter/exit events
     */
    private checkParticleEvents(): void {
        const particlesInRange = this.getParticlesInRange();
        const currentParticleIds = new Set<number>();

        // Check each particle in range
        particlesInRange.forEach(particle => {
            currentParticleIds.add(particle.id);

            if (!this.particlesInField.has(particle.id)) {
                // Particle entered the field
                this.particlesInField.add(particle.id);
                this.onParticleEnterField(particle);
            } else {
                // Particle is staying in the field
                this.onParticleStayInField(particle);
            }
        });

        // Check for particles that exited
        this.particlesInField.forEach(particleId => {
            if (!currentParticleIds.has(particleId)) {
                // Particle exited the field
                this.particlesInField.delete(particleId);
                // Note: We don't have the particle reference here, so we'll trigger a generic exit event
                this.onParticleExitField(particleId);
            }
        });
    }

    /**
     * Get particles within the event field range
     */
    private getParticlesInRange(): Particle[] {
        // This would typically get particles from a particle manager
        // For now, we'll return an empty array as a placeholder
        const particles: Particle[] = [];

        // In a real implementation, this would query the particle manager:
        // const particles = this.particleManager.getParticlesInRect(
        //     this.node.position.x - this.halfWidth,
        //     this.node.position.x + this.halfWidth,
        //     this.node.position.y - this.halfHeight,
        //     this.node.position.y + this.halfHeight
        // );

        // Filter by shape and reach criteria
        return particles.filter(particle => 
            this.isParticleInShape(particle) && this.shouldAffectParticle(particle)
        );
    }

    /**
     * Check if particle is within the event field shape
     */
    private isParticleInShape(particle: Particle): boolean {
        const fieldPos = new Vec2(this.node.position.x, this.node.position.y);
        const particlePos = particle.position;

        switch (this.fieldShape) {
            case FieldShape.Rectangle:
                return Math.abs(particlePos.x - fieldPos.x) <= this.halfWidth &&
                       Math.abs(particlePos.y - fieldPos.y) <= this.halfHeight;

            case FieldShape.Circle:
                const distance = Math.sqrt(
                    Math.pow(particlePos.x - fieldPos.x, 2) +
                    Math.pow(particlePos.y - fieldPos.y, 2)
                );
                return distance <= this.halfWidth; // Use halfWidth as radius

            default:
                return false;
        }
    }

    /**
     * Check if the event field should affect this particle based on reach criteria
     */
    private shouldAffectParticle(particle: Particle): boolean {
        switch (this.reach) {
            case Reach.All:
                return true;

            case Reach.Layer:
                // Check if particle is from the target layer or same layer
                return particle.layerId === this.getLayerId() || 
                       this.getLayerName() === this.targetName;

            case Reach.Name:
                // Check if particle emitter name matches target name
                return this.getEmitterName(particle) === this.targetName;

            default:
                return false;
        }
    }

    /**
     * Handle particle entering the field
     */
    private onParticleEnterField(particle: Particle): void {
        log(`EventFieldComponent: Particle ${particle.id} entered field`);
        
        // Execute enter events
        this.executeEvents(EventFieldEventType.OnEnter, particle);
        
        // Call callback if set
        if (this.onParticleEnter) {
            this.onParticleEnter(particle);
        }
    }

    /**
     * Handle particle staying in the field
     */
    private onParticleStayInField(particle: Particle): void {
        // Execute stay events
        this.executeEvents(EventFieldEventType.OnStay, particle);
        
        // Call callback if set
        if (this.onParticleStay) {
            this.onParticleStay(particle);
        }
    }

    /**
     * Handle particle exiting the field
     */
    private onParticleExitField(particleId: number): void {
        log(`EventFieldComponent: Particle ${particleId} exited field`);
        
        // Execute exit events
        this.executeEvents(EventFieldEventType.OnExit, null);
        
        // Call callback if set (note: we don't have particle reference)
        if (this.onParticleExit) {
            // Create a dummy particle for the callback
            // In a real implementation, we'd maintain particle references
        }
    }

    /**
     * Execute events for a specific event type
     */
    private executeEvents(eventType: EventFieldEventType, particle: Particle | null): void {
        this.eventGroups.forEach(group => {
            group.events.forEach(event => {
                if (this.shouldExecuteEvent(event, eventType)) {
                    this.executeEvent(event, particle);
                }
            });
        });
    }

    /**
     * Check if an event should be executed for the given event type
     */
    private shouldExecuteEvent(event: ParticleEvent, eventType: EventFieldEventType): boolean {
        // This would check the event condition against the event type
        // For now, we'll use a simple string match
        return event.type === eventType || event.condition.includes(eventType);
    }

    /**
     * Execute a specific event
     */
    private executeEvent(event: ParticleEvent, particle: Particle | null): void {
        log(`EventFieldComponent: Executing event ${event.type}: ${event.action}`);
        
        // This would execute the actual event action
        // For now, we'll just log the action
        switch (event.action) {
            case 'DestroyParticle':
                if (particle) {
                    particle.alive = false;
                }
                break;
                
            case 'ChangeColor':
                if (particle) {
                    // Change particle color
                }
                break;
                
            case 'PlaySound':
                // Play sound effect
                break;
                
            default:
                log(`EventFieldComponent: Unknown action: ${event.action}`);
                break;
        }
    }

    /**
     * Set particle manager reference
     */
    public setParticleManager(manager: any): void {
        this.particleManager = manager;
    }

    /**
     * Get layer ID (placeholder implementation)
     */
    private getLayerId(): number {
        return 0;
    }

    /**
     * Get layer name (placeholder implementation)
     */
    private getLayerName(): string {
        return 'Main';
    }

    /**
     * Get emitter name for a particle (placeholder implementation)
     */
    private getEmitterName(particle: Particle): string {
        return `Emitter_${particle.emitterId}`;
    }

    /**
     * Set field size at runtime
     */
    public setFieldSize(halfWidth: number, halfHeight: number): void {
        this.halfWidth = halfWidth;
        this.halfHeight = halfHeight;
    }

    /**
     * Set field shape at runtime
     */
    public setFieldShape(shape: FieldShape): void {
        this.fieldShape = shape;
    }

    /**
     * Get field bounds for debugging
     */
    public getFieldBounds(): { min: Vec2; max: Vec2 } {
        const center = new Vec2(this.node.position.x, this.node.position.y);
        return {
            min: new Vec2(center.x - this.halfWidth, center.y - this.halfHeight),
            max: new Vec2(center.x + this.halfWidth, center.y + this.halfHeight)
        };
    }

    /**
     * Check if a point is within the event field
     */
    public isPointInField(point: Vec2): boolean {
        const fieldPos = new Vec2(this.node.position.x, this.node.position.y);

        switch (this.fieldShape) {
            case FieldShape.Rectangle:
                return Math.abs(point.x - fieldPos.x) <= this.halfWidth &&
                       Math.abs(point.y - fieldPos.y) <= this.halfHeight;

            case FieldShape.Circle:
                const distance = Math.sqrt(
                    Math.pow(point.x - fieldPos.x, 2) +
                    Math.pow(point.y - fieldPos.y, 2)
                );
                return distance <= this.halfWidth;

            default:
                return false;
        }
    }

    /**
     * Get count of particles currently in field
     */
    public getParticleCount(): number {
        return this.particlesInField.size;
    }

    /**
     * Add custom event group
     */
    public addEventGroup(group: EventGroup): void {
        this.eventGroups.push(group);
    }

    /**
     * Clear all event groups
     */
    public clearEventGroups(): void {
        this.eventGroups = [];
    }
}
