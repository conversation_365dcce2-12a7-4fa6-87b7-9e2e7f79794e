System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, JsonAsset, resources, IMgr, cfg, _dec, _class, _class2, _crd, ccclass, property, LubanMgr;

  function _reportPossibleCrUseOfIMgr(extras) {
    _reporterNs.report("IMgr", "../IMgr", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      JsonAsset = _cc.JsonAsset;
      resources = _cc.resources;
    }, function (_unresolved_2) {
      IMgr = _unresolved_2.IMgr;
    }, function (_unresolved_3) {
      cfg = _unresolved_3;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "3d990WyxytKWYTZq0IoKew7", "LubanMgr", undefined);

      __checkObsolete__(['_decorator', 'Component', 'Node', 'JsonAsset', 'AssetManager', 'resources']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("LubanMgr", LubanMgr = (_dec = ccclass("LubanMgr"), _dec(_class = (_class2 = class LubanMgr extends (_crd && IMgr === void 0 ? (_reportPossibleCrUseOfIMgr({
        error: Error()
      }), IMgr) : IMgr) {
        constructor() {
          super(...arguments);
          this._table = null;
        }

        init() {
          super.init();
          resources.loadDir(LubanMgr.LUBAN_PATH, JsonAsset, (err, assets) => {
            var dataMap = new Map();

            for (var asset of assets) {
              dataMap.set(asset.name, asset);
            }

            this._table = new cfg.Tables(file => {
              if (dataMap.has(file)) {
                return dataMap.get(file).json;
              }

              console.warn("LubanMgr: File " + file + " not found in loaded assets.");
              return null;
            });
            console.log("LubanMgr initialized with tables:", this._table.TbGlobalAttr.GoldProducion);
          });
        }

      }, _class2.LUBAN_PATH = 'Luban/', _class2)) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=8cc014d3522682d36f2178eea244a3c19e4d8ca1.js.map