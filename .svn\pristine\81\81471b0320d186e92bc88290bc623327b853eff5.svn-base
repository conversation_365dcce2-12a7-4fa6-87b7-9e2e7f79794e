/**
 * CrazyStorm 2.0 Integration for Cocos Creator
 * Game Integration Module Index
 * 
 * This file exports all game integration functionality including
 * the main integration component, collision detection, enemy patterns,
 * and game state management.
 */

// Main game integration
export { GameIntegration } from './GameIntegration';
export type { 
    GameConfig, 
    PlayerState, 
    GameEvents 
} from './GameIntegration';

// Collision system
export { CollisionManager } from './CollisionManager';
export type { 
    CollisionObject, 
    CollisionResult 
} from './CollisionManager';

// Enemy pattern system
export { EnemyPatternController } from './EnemyPatternController';
export type { 
    EnemyPattern, 
    PatternSpawnConfig 
} from './EnemyPatternController';

// Game state management
export { GameStateManager } from './GameStateManager';
export { GameState } from './GameStateManager';
export type { 
    DifficultySettings, 
    WaveConfig, 
    GameProgression 
} from './GameStateManager';

/**
 * Quick start function for game integration
 */
export function initializeGameIntegration(): void {
    console.log('CrazyStorm Game Integration: Initializing...');
    
    // The GameIntegration component will handle initialization
    // when added to a node in the scene
    
    console.log('CrazyStorm Game Integration: Ready for use');
}

/**
 * Example usage patterns for common game scenarios
 */
export const GameIntegrationExamples = {
    /**
     * Basic enemy spawn with pattern
     */
    spawnBasicEnemy: `
// In your enemy spawning code:
const gameIntegration = this.getComponent(GameIntegration);
await gameIntegration.spawnEnemyPattern('basic_spread', enemyPosition, enemyId);
`,

    /**
     * Player collision handling
     */
    playerCollision: `
// Set up player collision callback:
gameIntegration.setGameEvents({
    onPlayerHit: (damage, position) => {
        // Handle player damage
        this.playerHealth -= damage;
        this.showDamageEffect(position);
    }
});
`,

    /**
     * Boss pattern sequence
     */
    bossPattern: `
// Spawn boss with multiple patterns:
await gameIntegration.spawnEnemyPattern('boss_pattern_1', bossPosition, 'boss1', {
    duration: 10, // 10 seconds
    looping: false
});

// After first pattern completes, start second pattern
gameIntegration.setGameEvents({
    onPatternComplete: async (patternName) => {
        if (patternName === 'boss_pattern_1') {
            await gameIntegration.spawnEnemyPattern('boss_pattern_2', bossPosition, 'boss1');
        }
    }
});
`,

    /**
     * Performance monitoring
     */
    performanceMonitoring: `
// Enable performance monitoring and handle issues:
gameIntegration.setGameEvents({
    onPerformanceIssue: (recommendations) => {
        recommendations.forEach(rec => {
            if (rec.severity === 'high') {
                // Reduce particle count or quality
                this.adjustGameQuality(rec.action);
            }
        });
    }
});
`,

    /**
     * Game state integration
     */
    gameStateIntegration: `
// Integrate with game state:
const gameState = gameIntegration.getGameStateManager();

gameState.onStateChange(GameState.Paused, () => {
    gameIntegration.pauseGame();
});

gameState.onStateChange(GameState.Playing, () => {
    gameIntegration.resumeGame();
});

// Handle wave changes
gameState.onWaveChange((wave) => {
    // Spawn enemies for the new wave
    wave.enemies.forEach(enemy => {
        this.spawnEnemyWithPatterns(enemy);
    });
});
`
};

/**
 * Configuration templates for different game types
 */
export const GameConfigTemplates = {
    /**
     * Mobile-optimized configuration
     */
    mobile: {
        enableCollision: true,
        enablePerformanceMonitoring: true,
        enableDebugDraw: false,
        playerCollisionRadius: 12,
        bulletCollisionRadius: 4,
        maxConcurrentPatterns: 3,
        patternDirectory: 'crazy-storm/patterns'
    },

    /**
     * Desktop high-performance configuration
     */
    desktop: {
        enableCollision: true,
        enablePerformanceMonitoring: false,
        enableDebugDraw: false,
        playerCollisionRadius: 15,
        bulletCollisionRadius: 5,
        maxConcurrentPatterns: 8,
        patternDirectory: 'crazy-storm/patterns'
    },

    /**
     * Development/debug configuration
     */
    debug: {
        enableCollision: true,
        enablePerformanceMonitoring: true,
        enableDebugDraw: true,
        playerCollisionRadius: 15,
        bulletCollisionRadius: 5,
        maxConcurrentPatterns: 5,
        patternDirectory: 'crazy-storm/patterns'
    }
};

/**
 * Utility functions for common game integration tasks
 */
export const GameIntegrationUtils = {
    /**
     * Calculate distance between two points
     */
    distance: (pos1: { x: number; y: number }, pos2: { x: number; y: number }): number => {
        const dx = pos1.x - pos2.x;
        const dy = pos1.y - pos2.y;
        return Math.sqrt(dx * dx + dy * dy);
    },

    /**
     * Calculate angle between two points
     */
    angleBetween: (from: { x: number; y: number }, to: { x: number; y: number }): number => {
        const dx = to.x - from.x;
        const dy = to.y - from.y;
        return Math.atan2(dy, dx) * 180 / Math.PI;
    },

    /**
     * Check if point is within screen bounds
     */
    isOnScreen: (position: { x: number; y: number }, screenBounds: { width: number; height: number }): boolean => {
        return position.x >= -screenBounds.width / 2 &&
               position.x <= screenBounds.width / 2 &&
               position.y >= -screenBounds.height / 2 &&
               position.y <= screenBounds.height / 2;
    },

    /**
     * Interpolate between two values
     */
    lerp: (a: number, b: number, t: number): number => {
        return a + (b - a) * t;
    },

    /**
     * Clamp value between min and max
     */
    clamp: (value: number, min: number, max: number): number => {
        return Math.max(min, Math.min(max, value));
    }
};
