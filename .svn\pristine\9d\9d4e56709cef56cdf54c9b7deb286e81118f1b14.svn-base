
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------


 
export enum TargetScanStrategy {
    /**
     * 更新
     */
    Refresh = 0,
    /**
     * 保持
     */
    Keep = 1,
}

 




export namespace builtin {
export class vector2 {

    constructor(_json_: any) {
        if (_json_.x === undefined) { throw new Error() }
        this.x = _json_.x
        if (_json_.y === undefined) { throw new Error() }
        this.y = _json_.y
    }

    readonly x: number
    readonly y: number

    resolve(tables:Tables) {
        
        
    }
}

}


export namespace builtin {
export class vector3 {

    constructor(_json_: any) {
        if (_json_.x === undefined) { throw new Error() }
        this.x = _json_.x
        if (_json_.y === undefined) { throw new Error() }
        this.y = _json_.y
        if (_json_.z === undefined) { throw new Error() }
        this.z = _json_.z
    }

    readonly x: number
    readonly y: number
    readonly z: number

    resolve(tables:Tables) {
        
        
        
    }
}

}


export namespace builtin {
export class vector4 {

    constructor(_json_: any) {
        if (_json_.x === undefined) { throw new Error() }
        this.x = _json_.x
        if (_json_.y === undefined) { throw new Error() }
        this.y = _json_.y
        if (_json_.z === undefined) { throw new Error() }
        this.z = _json_.z
        if (_json_.w === undefined) { throw new Error() }
        this.w = _json_.w
    }

    readonly x: number
    readonly y: number
    readonly z: number
    readonly w: number

    resolve(tables:Tables) {
        
        
        
        
    }
}

}



export class GlobalAttr {

    constructor(_json_: any) {
        if (_json_.GoldProducion === undefined) { throw new Error() }
        this.GoldProducion = _json_.GoldProducion
    }

    /**
     * 每回合发放的金币
     */
    readonly GoldProducion: number

    resolve(tables:Tables) {
        
    }
}





/**
 * 卡牌
 */
export class ResWeapon {

    constructor(_json_: any) {
        if (_json_.id === undefined) { throw new Error() }
        this.id = _json_.id
        if (_json_.name === undefined) { throw new Error() }
        this.name = _json_.name
    }

    readonly id: number
    readonly name: string

    resolve(tables:Tables) {
        
        
    }
}





export class ResWhiteList {

    constructor(_json_: any) {
        if (_json_.openid === undefined) { throw new Error() }
        this.openid = _json_.openid
        if (_json_.password === undefined) { throw new Error() }
        this.password = _json_.password
        if (_json_.status === undefined) { throw new Error() }
        this.status = _json_.status
        if (_json_.privilege === undefined) { throw new Error() }
        this.privilege = _json_.privilege
    }

    /**
     * 第一行默认是主键
     */
    readonly openid: string
    /**
     * 密码的MD5
     */
    readonly password: string
    /**
     * account  status: normal/disable
     */
    readonly status: number
    /**
     * 可以访问的内容
     */
    readonly privilege: number

    resolve(tables:Tables) {
        
        
        
        
    }
}






/**
 * 武器
 */
export class TbWeapon {
    private _dataMap: Map<number, ResWeapon>
    private _dataList: ResWeapon[]
    constructor(_json_: any) {
        this._dataMap = new Map<number, ResWeapon>()
        this._dataList = []
        for(var _json2_ of _json_) {
            let _v: ResWeapon
            _v = new ResWeapon(_json2_)
            this._dataList.push(_v)
            this._dataMap.set(_v.id, _v)
        }
    }

    getDataMap(): Map<number, ResWeapon> { return this._dataMap; }
    getDataList(): ResWeapon[] { return this._dataList; }

    get(key: number): ResWeapon | undefined { return this._dataMap.get(key); }

    resolve(tables:Tables) {
        for(let  data of this._dataList)
        {
            data.resolve(tables)
        }
    }

}




export class TbGlobalAttr {

    private _data: GlobalAttr
    constructor(_json_: any) {
        if (_json_.length != 1) throw new Error('table mode=one, but size != 1')
        this._data = new GlobalAttr(_json_[0])
    }

    getData(): GlobalAttr { return this._data; }

    /**
     * 每回合发放的金币
     */
    get  GoldProducion(): number { return this._data.GoldProducion; }

    resolve(tables:Tables)
    {
        this._data.resolve(tables)
    }
    
}




type JsonLoader = (file: string) => any

export class Tables {
    private _TbWeapon: TbWeapon
    /**
     * 武器
     */
    get TbWeapon(): TbWeapon  { return this._TbWeapon;}
    private _TbGlobalAttr: TbGlobalAttr
    get TbGlobalAttr(): TbGlobalAttr  { return this._TbGlobalAttr;}

    constructor(loader: JsonLoader) {
        this._TbWeapon = new TbWeapon(loader('tbweapon'))
        this._TbGlobalAttr = new TbGlobalAttr(loader('tbglobalattr'))

        this._TbWeapon.resolve(this)
        this._TbGlobalAttr.resolve(this)
    }
}

