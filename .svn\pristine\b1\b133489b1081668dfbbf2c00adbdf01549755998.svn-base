/**
 * CrazyStorm 2.0 Integration for Cocos Creator
 * Enemy Pattern Controller
 * 
 * This system manages enemy bullet patterns, spawning, timing,
 * and lifecycle management for the air-fighter game.
 */

import { Node, Vec2, log, warn, error } from 'cc';
import { CrazyStormManager } from '../runtime/CrazyStormManager';
import { getCrazyStormLoader } from '../core/CrazyStormLoader';
import { GameConfig } from './GameIntegration';

/**
 * Enemy pattern instance
 */
export interface EnemyPattern {
    id: string;
    name: string;
    enemyId: string;
    position: Vec2;
    node: Node;
    manager: CrazyStormManager;
    isActive: boolean;
    startTime: number;
    duration: number; // -1 for infinite
    looping: boolean;
}

/**
 * Pattern spawn configuration
 */
export interface PatternSpawnConfig {
    position: Vec2;
    duration?: number; // -1 for infinite, 0 for single play
    looping?: boolean;
    delay?: number; // Delay before starting
    scale?: number; // Pattern scale multiplier
    rotation?: number; // Pattern rotation in degrees
}

/**
 * Enemy pattern controller
 */
export class EnemyPatternController {
    private parentNode: Node;
    private crazyStormManager: CrazyStormManager;
    private config: GameConfig;
    
    private patterns: Map<string, EnemyPattern> = new Map();
    private patternTemplates: Map<string, string> = new Map(); // name -> file path
    private nextPatternId: number = 1;
    
    // Callbacks
    private patternCompleteCallback: ((patternName: string, enemyId: string) => void) | null = null;
    private enemyDestroyedCallback: ((enemyId: string, position: Vec2) => void) | null = null;
    
    // Performance tracking
    private maxConcurrentPatterns: number;
    private activePatternCount: number = 0;

    constructor(parentNode: Node, crazyStormManager: CrazyStormManager, config: GameConfig) {
        this.parentNode = parentNode;
        this.crazyStormManager = crazyStormManager;
        this.config = config;
        this.maxConcurrentPatterns = config.maxConcurrentPatterns;
        
        this.initializePatternTemplates();
    }

    /**
     * Update all active patterns
     */
    public update(deltaTime: number): void {
        const currentTime = Date.now();
        const patternsToRemove: string[] = [];

        this.patterns.forEach((pattern, id) => {
            if (!pattern.isActive) {
                return;
            }

            // Check if pattern should end
            if (pattern.duration > 0) {
                const elapsed = currentTime - pattern.startTime;
                if (elapsed >= pattern.duration * 1000) {
                    if (pattern.looping) {
                        // Restart pattern
                        pattern.manager.reset();
                        pattern.manager.play();
                        pattern.startTime = currentTime;
                    } else {
                        // End pattern
                        this.stopPattern(id);
                        patternsToRemove.push(id);
                    }
                }
            }
        });

        // Remove completed patterns
        patternsToRemove.forEach(id => {
            const pattern = this.patterns.get(id);
            if (pattern && this.patternCompleteCallback) {
                this.patternCompleteCallback(pattern.name, pattern.enemyId);
            }
            this.removePattern(id);
        });
    }

    /**
     * Spawn enemy pattern
     */
    public async spawnPattern(
        patternName: string, 
        position: Vec2, 
        enemyId?: string,
        config?: PatternSpawnConfig
    ): Promise<boolean> {
        // Check if we've reached the maximum concurrent patterns
        if (this.activePatternCount >= this.maxConcurrentPatterns) {
            warn(`EnemyPatternController: Maximum concurrent patterns reached (${this.maxConcurrentPatterns})`);
            return false;
        }

        // Get pattern file path
        const filePath = this.patternTemplates.get(patternName);
        if (!filePath) {
            error(`EnemyPatternController: Pattern '${patternName}' not found`);
            return false;
        }

        try {
            // Create pattern node
            const patternId = this.generatePatternId();
            const patternNode = new Node(`Pattern_${patternName}_${patternId}`);
            this.parentNode.addChild(patternNode);

            // Set position
            const finalPosition = config?.position || position;
            patternNode.setPosition(finalPosition.x, finalPosition.y, 0);

            // Apply scale and rotation if specified
            if (config?.scale) {
                patternNode.setScale(config.scale, config.scale, 1);
            }
            if (config?.rotation) {
                patternNode.setRotationFromEuler(0, 0, config.rotation);
            }

            // Create CrazyStorm manager for this pattern
            const patternManager = patternNode.addComponent(CrazyStormManager);
            
            // Load the pattern
            const success = await patternManager.loadFile(filePath);
            if (!success) {
                error(`EnemyPatternController: Failed to load pattern file: ${filePath}`);
                patternNode.destroy();
                return false;
            }

            // Create pattern instance
            const pattern: EnemyPattern = {
                id: patternId,
                name: patternName,
                enemyId: enemyId || `enemy_${patternId}`,
                position: new Vec2(finalPosition.x, finalPosition.y),
                node: patternNode,
                manager: patternManager,
                isActive: true,
                startTime: Date.now() + (config?.delay || 0) * 1000,
                duration: config?.duration !== undefined ? config.duration : -1,
                looping: config?.looping || false
            };

            this.patterns.set(patternId, pattern);
            this.activePatternCount++;

            // Start pattern (with delay if specified)
            if (config?.delay && config.delay > 0) {
                setTimeout(() => {
                    if (this.patterns.has(patternId)) {
                        patternManager.play();
                    }
                }, config.delay * 1000);
            } else {
                patternManager.play();
            }

            log(`EnemyPatternController: Spawned pattern '${patternName}' at [${finalPosition.x}, ${finalPosition.y}]`);
            return true;

        } catch (err) {
            error('EnemyPatternController: Error spawning pattern:', err);
            return false;
        }
    }

    /**
     * Stop specific pattern
     */
    public stopPattern(patternId: string): boolean {
        const pattern = this.patterns.get(patternId);
        if (!pattern) {
            return false;
        }

        pattern.isActive = false;
        pattern.manager.stop();
        
        log(`EnemyPatternController: Stopped pattern '${pattern.name}' (${patternId})`);
        return true;
    }

    /**
     * Stop patterns by enemy ID
     */
    public stopPatternsByEnemy(enemyId: string): number {
        let stoppedCount = 0;
        
        this.patterns.forEach((pattern, id) => {
            if (pattern.enemyId === enemyId) {
                this.stopPattern(id);
                stoppedCount++;
            }
        });

        return stoppedCount;
    }

    /**
     * Clear all patterns
     */
    public clearAllPatterns(): void {
        this.patterns.forEach((pattern, id) => {
            this.stopPattern(id);
            this.removePattern(id);
        });
        
        log('EnemyPatternController: Cleared all patterns');
    }

    /**
     * Pause all patterns
     */
    public pauseAll(): void {
        this.patterns.forEach(pattern => {
            if (pattern.isActive) {
                pattern.manager.pause();
            }
        });
    }

    /**
     * Resume all patterns
     */
    public resumeAll(): void {
        this.patterns.forEach(pattern => {
            if (pattern.isActive) {
                pattern.manager.resume();
            }
        });
    }

    /**
     * Get active pattern count
     */
    public getActivePatternCount(): number {
        return this.activePatternCount;
    }

    /**
     * Get pattern by ID
     */
    public getPattern(patternId: string): EnemyPattern | null {
        return this.patterns.get(patternId) || null;
    }

    /**
     * Get patterns by enemy ID
     */
    public getPatternsByEnemy(enemyId: string): EnemyPattern[] {
        const patterns: EnemyPattern[] = [];
        this.patterns.forEach(pattern => {
            if (pattern.enemyId === enemyId) {
                patterns.push(pattern);
            }
        });
        return patterns;
    }

    /**
     * Get all active patterns
     */
    public getActivePatterns(): EnemyPattern[] {
        const activePatterns: EnemyPattern[] = [];
        this.patterns.forEach(pattern => {
            if (pattern.isActive) {
                activePatterns.push(pattern);
            }
        });
        return activePatterns;
    }

    /**
     * Set pattern complete callback
     */
    public setPatternCompleteCallback(callback: (patternName: string, enemyId: string) => void): void {
        this.patternCompleteCallback = callback;
    }

    /**
     * Set enemy destroyed callback
     */
    public setEnemyDestroyedCallback(callback: (enemyId: string, position: Vec2) => void): void {
        this.enemyDestroyedCallback = callback;
    }

    /**
     * Register pattern template
     */
    public registerPattern(name: string, filePath: string): void {
        this.patternTemplates.set(name, filePath);
        log(`EnemyPatternController: Registered pattern '${name}' -> '${filePath}'`);
    }

    /**
     * Unregister pattern template
     */
    public unregisterPattern(name: string): boolean {
        const removed = this.patternTemplates.delete(name);
        if (removed) {
            log(`EnemyPatternController: Unregistered pattern '${name}'`);
        }
        return removed;
    }

    /**
     * Get available pattern names
     */
    public getAvailablePatterns(): string[] {
        return Array.from(this.patternTemplates.keys());
    }

    /**
     * Destroy enemy (stop all its patterns)
     */
    public destroyEnemy(enemyId: string, position: Vec2): void {
        const stoppedCount = this.stopPatternsByEnemy(enemyId);
        
        if (stoppedCount > 0 && this.enemyDestroyedCallback) {
            this.enemyDestroyedCallback(enemyId, position);
        }

        log(`EnemyPatternController: Destroyed enemy '${enemyId}' (${stoppedCount} patterns stopped)`);
    }

    /**
     * Get controller statistics
     */
    public getStats(): {
        activePatterns: number;
        totalPatterns: number;
        registeredTemplates: number;
        maxConcurrent: number;
    } {
        return {
            activePatterns: this.activePatternCount,
            totalPatterns: this.patterns.size,
            registeredTemplates: this.patternTemplates.size,
            maxConcurrent: this.maxConcurrentPatterns
        };
    }

    /**
     * Destroy controller
     */
    public destroy(): void {
        this.clearAllPatterns();
        this.patternTemplates.clear();
        this.patternCompleteCallback = null;
        this.enemyDestroyedCallback = null;
    }

    // Private methods

    /**
     * Initialize pattern templates
     */
    private async initializePatternTemplates(): Promise<void> {
        // Register common patterns
        // In a real game, you'd load these from a configuration file
        this.registerPattern('basic_spread', `${this.config.patternDirectory}/basic_spread`);
        this.registerPattern('spiral', `${this.config.patternDirectory}/spiral`);
        this.registerPattern('aimed_burst', `${this.config.patternDirectory}/aimed_burst`);
        this.registerPattern('circle_pattern', `${this.config.patternDirectory}/circle_pattern`);
        this.registerPattern('boss_pattern_1', `${this.config.patternDirectory}/boss_patterns/boss1`);
        this.registerPattern('boss_pattern_2', `${this.config.patternDirectory}/boss_patterns/boss2`);
        
        log(`EnemyPatternController: Initialized with ${this.patternTemplates.size} pattern templates`);
    }

    /**
     * Generate unique pattern ID
     */
    private generatePatternId(): string {
        return `pattern_${this.nextPatternId++}`;
    }

    /**
     * Remove pattern from tracking
     */
    private removePattern(patternId: string): void {
        const pattern = this.patterns.get(patternId);
        if (pattern) {
            if (pattern.node && pattern.node.isValid) {
                pattern.node.destroy();
            }
            this.patterns.delete(patternId);
            this.activePatternCount = Math.max(0, this.activePatternCount - 1);
        }
    }
}
