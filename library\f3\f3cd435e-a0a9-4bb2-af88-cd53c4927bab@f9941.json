{"__type__": "cc.SpriteFrame", "content": {"name": "light", "atlas": "", "rect": {"x": 36, "y": 11, "width": 45, "height": 82}, "offset": {"x": 8.5, "y": -2}, "originalSize": {"width": 100, "height": 100}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-22.5, -41, 0, 22.5, -41, 0, -22.5, 41, 0, 22.5, 41, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [36, 89, 81, 89, 36, 7, 81, 7], "nuv": [0.36, 0.07, 0.81, 0.07, 0.36, 0.89, 0.81, 0.89], "minPos": {"x": -22.5, "y": -41, "z": 0}, "maxPos": {"x": 22.5, "y": 41, "z": 0}}, "texture": "f3cd435e-a0a9-4bb2-af88-cd53c4927bab@6c48a", "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}}