/**
 * CrazyStorm 2.0 Integration for Cocos Creator
 * Expression Virtual Machine
 * 
 * Executes bytecode instructions for CrazyStorm expressions.
 * Based on the C# VM.cs implementation with stack-based execution.
 */

import { Vec2, Color, log, warn } from 'cc';
import { 
    VMInstruction, 
    VMCode, 
    RGB, 
    BUILTIN_FUNCTIONS, 
    FunctionSignature,
    VMContext 
} from './VMInstruction';

/**
 * Virtual Machine for executing CrazyStorm expressions
 * Implements a stack-based execution model matching the C# version
 */
export class ExpressionVM {
    // Execution stacks (static for performance, like C# version)
    private static floatStack: number[] = [];
    private static boolStack: boolean[] = [];
    private static vector2Stack: Vec2[] = [];
    private static rgbStack: RGB[] = [];
    private static stringStack: string[] = [];

    // Random number generator
    private static random: () => number = Math.random;

    /**
     * Execute a sequence of VM instructions
     */
    public static execute(instructions: VMInstruction[], context?: VMContext): any {
        // Clear all stacks before execution
        this.clearStacks();

        try {
            for (let i = 0; i < instructions.length; i++) {
                this.executeInstruction(instructions[i], context);
            }

            // Return the top value from the appropriate stack
            return this.getResult();
        } catch (error) {
            warn('ExpressionVM: Execution error:', error);
            return 0; // Default return value
        }
    }

    /**
     * Execute a single VM instruction
     */
    private static executeInstruction(instruction: VMInstruction, context?: VMContext): void {
        switch (instruction.code) {
            case VMCode.NUMBER:
                this.pushFloat(instruction.floatOperand);
                break;

            case VMCode.BOOL:
                this.pushBool(instruction.boolOperand);
                break;

            case VMCode.NAME:
                this.executeName(instruction.stringOperand, context);
                break;

            case VMCode.VECTOR2:
                this.executeVector2();
                break;

            case VMCode.RGB:
                this.executeRGB();
                break;

            case VMCode.ARGUMENTS:
                this.pushFloat(instruction.intOperand);
                break;

            case VMCode.CALL:
                this.executeCall(instruction.intOperand);
                break;

            // Logical operations
            case VMCode.AND:
                this.executeAnd();
                break;

            case VMCode.OR:
                this.executeOr();
                break;

            // Comparison operations
            case VMCode.EQUAL:
                this.executeEqual();
                break;

            case VMCode.NOTEQUAL:
                this.executeNotEqual();
                break;

            case VMCode.MORE:
                this.executeMore();
                break;

            case VMCode.LESS:
                this.executeLess();
                break;

            case VMCode.MOREOREQUAL:
                this.executeMoreOrEqual();
                break;

            case VMCode.LESSOREQUAL:
                this.executeLessOrEqual();
                break;

            // Arithmetic operations
            case VMCode.ADD:
                this.executeAdd();
                break;

            case VMCode.SUB:
                this.executeSub();
                break;

            case VMCode.MUL:
                this.executeMul();
                break;

            case VMCode.DIV:
                this.executeDiv();
                break;

            case VMCode.MOD:
                this.executeMod();
                break;

            default:
                warn(`ExpressionVM: Unknown instruction code: ${instruction.code}`);
                break;
        }
    }

    // ============================================================================
    // STACK OPERATIONS
    // ============================================================================

    private static pushFloat(value: number): void {
        this.floatStack.push(value);
    }

    private static popFloat(): number {
        if (this.floatStack.length === 0) {
            warn('ExpressionVM: Float stack underflow');
            return 0;
        }
        return this.floatStack.pop()!;
    }

    private static pushBool(value: boolean): void {
        this.boolStack.push(value);
    }

    private static popBool(): boolean {
        if (this.boolStack.length === 0) {
            warn('ExpressionVM: Bool stack underflow');
            return false;
        }
        return this.boolStack.pop()!;
    }

    private static pushVector2(value: Vec2): void {
        this.vector2Stack.push(value);
    }

    private static popVector2(): Vec2 {
        if (this.vector2Stack.length === 0) {
            warn('ExpressionVM: Vector2 stack underflow');
            return new Vec2(0, 0);
        }
        return this.vector2Stack.pop()!;
    }

    private static pushRGB(value: RGB): void {
        this.rgbStack.push(value);
    }

    private static popRGB(): RGB {
        if (this.rgbStack.length === 0) {
            warn('ExpressionVM: RGB stack underflow');
            return { r: 0, g: 0, b: 0 };
        }
        return this.rgbStack.pop()!;
    }

    private static pushString(value: string): void {
        this.stringStack.push(value);
    }

    private static popString(): string {
        if (this.stringStack.length === 0) {
            warn('ExpressionVM: String stack underflow');
            return '';
        }
        return this.stringStack.pop()!;
    }

    private static clearStacks(): void {
        this.floatStack.length = 0;
        this.boolStack.length = 0;
        this.vector2Stack.length = 0;
        this.rgbStack.length = 0;
        this.stringStack.length = 0;
    }

    // ============================================================================
    // INSTRUCTION IMPLEMENTATIONS
    // ============================================================================

    private static executeName(name: string, context?: VMContext): void {
        // Handle property and variable references
        let value = 0;

        // Try to get from property container first
        if (context?.propertyContainer && typeof context.propertyContainer.pushProperty === 'function') {
            if (context.propertyContainer.pushProperty(name)) {
                return; // Property container handled the push
            }
        }

        // Try global variables
        if (context?.globalVariables?.has(name)) {
            value = context.globalVariables.get(name)!;
        }
        // Try local variables
        else if (context?.localVariables?.has(name)) {
            value = context.localVariables.get(name)!;
        }
        // Handle special built-in variables
        else {
            switch (name.toLowerCase()) {
                case 'pi':
                    value = Math.PI;
                    break;
                case 'e':
                    value = Math.E;
                    break;
                default:
                    warn(`ExpressionVM: Unknown variable or property: ${name}`);
                    value = 0;
                    break;
            }
        }

        this.pushFloat(value);
    }

    private static executeVector2(): void {
        const y = this.popFloat();
        const x = this.popFloat();
        this.pushVector2(new Vec2(x, y));
    }

    private static executeRGB(): void {
        const b = this.popFloat();
        const g = this.popFloat();
        const r = this.popFloat();
        this.pushRGB({ r, g, b });
    }

    private static executeCall(functionId: number): void {
        const argCount = this.popFloat();
        const args: number[] = [];
        
        // Pop arguments from stack (in reverse order)
        for (let i = 0; i < argCount; i++) {
            args.unshift(this.popFloat());
        }

        // Get function name from string stack
        const functionName = this.popString();
        
        // Execute built-in function
        const func = BUILTIN_FUNCTIONS.get(functionName);
        if (func) {
            if (args.length !== func.argCount) {
                warn(`ExpressionVM: Function ${functionName} expects ${func.argCount} arguments, got ${args.length}`);
                this.pushFloat(0);
                return;
            }
            
            const result = func.execute(...args);
            this.pushFloat(result);
        } else {
            warn(`ExpressionVM: Unknown function: ${functionName}`);
            this.pushFloat(0);
        }
    }

    // ============================================================================
    // LOGICAL OPERATIONS
    // ============================================================================

    private static executeAnd(): void {
        const b = this.popBool();
        const a = this.popBool();
        this.pushBool(a && b);
    }

    private static executeOr(): void {
        const b = this.popBool();
        const a = this.popBool();
        this.pushBool(a || b);
    }

    // ============================================================================
    // COMPARISON OPERATIONS
    // ============================================================================

    private static executeEqual(): void {
        const b = this.popFloat();
        const a = this.popFloat();
        this.pushBool(Math.abs(a - b) < Number.EPSILON);
    }

    private static executeNotEqual(): void {
        const b = this.popFloat();
        const a = this.popFloat();
        this.pushBool(Math.abs(a - b) >= Number.EPSILON);
    }

    private static executeMore(): void {
        const b = this.popFloat();
        const a = this.popFloat();
        this.pushBool(a > b);
    }

    private static executeLess(): void {
        const b = this.popFloat();
        const a = this.popFloat();
        this.pushBool(a < b);
    }

    private static executeMoreOrEqual(): void {
        const b = this.popFloat();
        const a = this.popFloat();
        this.pushBool(a >= b);
    }

    private static executeLessOrEqual(): void {
        const b = this.popFloat();
        const a = this.popFloat();
        this.pushBool(a <= b);
    }

    // ============================================================================
    // ARITHMETIC OPERATIONS
    // ============================================================================

    private static executeAdd(): void {
        const b = this.popFloat();
        const a = this.popFloat();
        this.pushFloat(a + b);
    }

    private static executeSub(): void {
        const b = this.popFloat();
        const a = this.popFloat();
        this.pushFloat(a - b);
    }

    private static executeMul(): void {
        const b = this.popFloat();
        const a = this.popFloat();
        this.pushFloat(a * b);
    }

    private static executeDiv(): void {
        const b = this.popFloat();
        const a = this.popFloat();
        if (Math.abs(b) < Number.EPSILON) {
            warn('ExpressionVM: Division by zero');
            this.pushFloat(0);
        } else {
            this.pushFloat(a / b);
        }
    }

    private static executeMod(): void {
        const b = this.popFloat();
        const a = this.popFloat();
        if (Math.abs(b) < Number.EPSILON) {
            warn('ExpressionVM: Modulo by zero');
            this.pushFloat(0);
        } else {
            this.pushFloat(a % b);
        }
    }

    // ============================================================================
    // RESULT HANDLING
    // ============================================================================

    private static getResult(): any {
        // Return the top value from the most recently used stack
        if (this.floatStack.length > 0) {
            return this.popFloat();
        }
        if (this.boolStack.length > 0) {
            return this.popBool();
        }
        if (this.vector2Stack.length > 0) {
            return this.popVector2();
        }
        if (this.rgbStack.length > 0) {
            return this.popRGB();
        }
        if (this.stringStack.length > 0) {
            return this.popString();
        }
        
        return 0; // Default return value
    }

    // ============================================================================
    // PUBLIC UTILITY METHODS
    // ============================================================================

    /**
     * Evaluate a simple expression string (for backward compatibility)
     */
    public static evaluateSimple(expression: string, context?: VMContext): number {
        // Simple expression evaluator for basic cases
        // This is a fallback for when we don't have compiled bytecode
        
        if (!expression || expression.trim() === '') {
            return 0;
        }

        // Handle numeric literals
        const numValue = parseFloat(expression);
        if (!isNaN(numValue)) {
            return numValue;
        }

        // Handle variable references
        if (context?.globalVariables?.has(expression)) {
            return context.globalVariables.get(expression)!;
        }
        if (context?.localVariables?.has(expression)) {
            return context.localVariables.get(expression)!;
        }

        // Handle special cases
        switch (expression.toLowerCase()) {
            case 'pi':
                return Math.PI;
            case 'e':
                return Math.E;
            case 'true':
                return 1;
            case 'false':
                return 0;
            default:
                warn(`ExpressionVM: Cannot evaluate simple expression: ${expression}`);
                return 0;
        }
    }

    /**
     * Set custom random number generator
     */
    public static setRandomGenerator(generator: () => number): void {
        this.random = generator;
    }

    /**
     * Compile a simple expression to bytecode instructions
     * This is a basic compiler for common expression patterns
     */
    public static compileExpression(expression: string): VMInstruction[] {
        const instructions: VMInstruction[] = [];

        if (!expression || expression.trim() === '') {
            instructions.push(new VMInstruction(VMCode.NUMBER, 0));
            return instructions;
        }

        const trimmed = expression.trim();

        // Handle numeric literals
        const numValue = parseFloat(trimmed);
        if (!isNaN(numValue)) {
            instructions.push(new VMInstruction(VMCode.NUMBER, numValue));
            return instructions;
        }

        // Handle boolean literals
        if (trimmed.toLowerCase() === 'true') {
            instructions.push(new VMInstruction(VMCode.BOOL, true));
            return instructions;
        }
        if (trimmed.toLowerCase() === 'false') {
            instructions.push(new VMInstruction(VMCode.BOOL, false));
            return instructions;
        }

        // Handle simple variable references
        if (/^[a-zA-Z_][a-zA-Z0-9_]*$/.test(trimmed)) {
            instructions.push(new VMInstruction(VMCode.NAME, trimmed));
            return instructions;
        }

        // Handle simple arithmetic expressions (basic implementation)
        if (trimmed.includes('+')) {
            const parts = trimmed.split('+').map(p => p.trim());
            if (parts.length === 2) {
                instructions.push(...this.compileExpression(parts[0]));
                instructions.push(...this.compileExpression(parts[1]));
                instructions.push(new VMInstruction(VMCode.ADD));
                return instructions;
            }
        }

        if (trimmed.includes('-')) {
            const parts = trimmed.split('-').map(p => p.trim());
            if (parts.length === 2) {
                instructions.push(...this.compileExpression(parts[0]));
                instructions.push(...this.compileExpression(parts[1]));
                instructions.push(new VMInstruction(VMCode.SUB));
                return instructions;
            }
        }

        if (trimmed.includes('*')) {
            const parts = trimmed.split('*').map(p => p.trim());
            if (parts.length === 2) {
                instructions.push(...this.compileExpression(parts[0]));
                instructions.push(...this.compileExpression(parts[1]));
                instructions.push(new VMInstruction(VMCode.MUL));
                return instructions;
            }
        }

        if (trimmed.includes('/')) {
            const parts = trimmed.split('/').map(p => p.trim());
            if (parts.length === 2) {
                instructions.push(...this.compileExpression(parts[0]));
                instructions.push(...this.compileExpression(parts[1]));
                instructions.push(new VMInstruction(VMCode.DIV));
                return instructions;
            }
        }

        // Handle function calls (basic implementation)
        const funcMatch = trimmed.match(/^([a-zA-Z_][a-zA-Z0-9_]*)\s*\(\s*(.*)\s*\)$/);
        if (funcMatch) {
            const funcName = funcMatch[1];
            const argsStr = funcMatch[2];

            // Push function name
            instructions.push(new VMInstruction(VMCode.NAME, funcName));

            // Parse and compile arguments
            const args = argsStr ? argsStr.split(',').map(arg => arg.trim()) : [];
            for (const arg of args) {
                instructions.push(...this.compileExpression(arg));
            }

            // Push argument count and call
            instructions.push(new VMInstruction(VMCode.ARGUMENTS, args.length));
            instructions.push(new VMInstruction(VMCode.CALL, 0));

            return instructions;
        }

        // Fallback: treat as variable name
        warn(`ExpressionVM: Complex expression compilation not fully implemented: ${expression}`);
        instructions.push(new VMInstruction(VMCode.NAME, trimmed));
        return instructions;
    }
}
