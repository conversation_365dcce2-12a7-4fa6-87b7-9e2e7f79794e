{"$schema": "https://json.schemastore.org/tsconfig", "compilerOptions": {"target": "ES2015", "module": "ES2015", "strict": true, "types": ["./temp/declarations/cc", "./temp/declarations/cc.custom-macro", "./temp/declarations/jsb", "./temp/declarations/cc.env"], "paths": {"db://internal/*": ["C:\\ProgramData\\cocos\\editors\\Creator\\3.8.6\\resources\\resources\\3d\\engine\\editor\\assets\\*"], "db://assets/*": ["D:\\Workspace\\Projects\\Moolego\\M2Game\\Client\\assets\\*"]}, "experimentalDecorators": true, "isolatedModules": true, "moduleResolution": "node", "noEmit": true, "forceConsistentCasingInFileNames": true}}