import { _decorator, Component, Node, Vec2, Color } from 'cc';
import { IEventNode, EventGraph, EventConnection, EventTrigger, EventContext, EventPort } from '../core/LevelData';
import { EventType, EventActionType, IDGenerator } from '../core/Types';

const { ccclass, property } = _decorator;

/**
 * Base event node implementation
 */
export abstract class BaseEventNode implements IEventNode {
    public id: string;
    public type: string;
    public properties: { [key: string]: any } = {};
    public inputs: EventPort[] = [];
    public outputs: EventPort[] = [];
    
    // Editor properties (not serialized)
    public editorData?: {
        position: Vec2;
        name: string;
        color: Color;
        collapsed: boolean;
        notes: string;
    };
    
    constructor(type: string, id?: string) {
        this.id = id || IDGenerator.generate('event_node');
        this.type = type;
        this.setupPorts();
        this.setupEditorData();
    }
    
    /**
     * Setup input/output ports for this node type
     */
    protected abstract setupPorts(): void;
    
    /**
     * Execute the node's functionality
     */
    public abstract execute(context: EventContext): void;
    
    /**
     * Validate the node's configuration
     */
    public validate(): boolean {
        // Basic validation - can be overridden
        return this.inputs.every(port => this.validatePort(port)) &&
               this.outputs.every(port => this.validatePort(port));
    }
    
    /**
     * Setup default editor data
     */
    private setupEditorData(): void {
        this.editorData = {
            position: Vec2.ZERO,
            name: this.type,
            color: Color.BLUE,
            collapsed: false,
            notes: ''
        };
    }
    
    /**
     * Validate a port
     */
    private validatePort(port: EventPort): boolean {
        return port.name && port.name.trim() !== '' && port.type && port.type.trim() !== '';
    }
    
    /**
     * Add input port
     */
    protected addInputPort(name: string, type: string): void {
        this.inputs.push({
            name,
            type,
            isConnected: false,
            connections: []
        });
    }
    
    /**
     * Add output port
     */
    protected addOutputPort(name: string, type: string): void {
        this.outputs.push({
            name,
            type,
            isConnected: false,
            connections: []
        });
    }
    
    /**
     * Get property value with default
     */
    protected getProperty<T>(key: string, defaultValue: T): T {
        return this.properties[key] !== undefined ? this.properties[key] : defaultValue;
    }
    
    /**
     * Set property value
     */
    protected setProperty(key: string, value: any): void {
        this.properties[key] = value;
    }
}

/**
 * Time-based event node
 */
export class TimeEventNode extends BaseEventNode {
    constructor(id?: string) {
        super(EventType.TimeReached, id);
    }
    
    protected setupPorts(): void {
        this.addOutputPort('triggered', 'trigger');
        this.setProperty('targetTime', 0);
        this.setProperty('repeat', false);
        this.setProperty('interval', 1.0);
    }
    
    public execute(context: EventContext): void {
        const targetTime = this.getProperty('targetTime', 0);
        const repeat = this.getProperty('repeat', false);
        const interval = this.getProperty('interval', 1.0);
        
        if (repeat) {
            // Check if enough time has passed for repeat
            const timeSinceStart = context.currentTime - targetTime;
            if (timeSinceStart >= 0 && timeSinceStart % interval < context.deltaTime) {
                this.triggerOutput('triggered', context);
            }
        } else {
            // One-time trigger
            if (context.currentTime >= targetTime && 
                context.currentTime - context.deltaTime < targetTime) {
                this.triggerOutput('triggered', context);
            }
        }
    }
    
    private triggerOutput(portName: string, context: EventContext): void {
        // This will be implemented when EventGraph is ready
        console.log(`TimeEventNode ${this.id} triggered output: ${portName}`);
    }
}

/**
 * Play animation event node
 */
export class PlayAnimationEventNode extends BaseEventNode {
    constructor(id?: string) {
        super(EventActionType.PlayAnimation, id);
    }
    
    protected setupPorts(): void {
        this.addInputPort('trigger', 'trigger');
        this.addOutputPort('completed', 'trigger');
        
        this.setProperty('targetEntity', '');
        this.setProperty('animationName', '');
        this.setProperty('loop', false);
        this.setProperty('speed', 1.0);
    }
    
    public execute(context: EventContext): void {
        const targetEntity = this.getProperty('targetEntity', '');
        const animationName = this.getProperty('animationName', '');
        const loop = this.getProperty('loop', false);
        const speed = this.getProperty('speed', 1.0);
        
        console.log(`Playing animation: ${animationName} on ${targetEntity}`);
        
        // TODO: Implement actual animation playing
        // For now, just trigger completion immediately
        setTimeout(() => {
            this.triggerOutput('completed', context);
        }, 1000); // Simulate 1 second animation
    }
    
    private triggerOutput(portName: string, context: EventContext): void {
        console.log(`PlayAnimationEventNode ${this.id} triggered output: ${portName}`);
    }
}

/**
 * Play sound event node
 */
export class PlaySoundEventNode extends BaseEventNode {
    constructor(id?: string) {
        super(EventActionType.PlaySound, id);
    }
    
    protected setupPorts(): void {
        this.addInputPort('trigger', 'trigger');
        this.addOutputPort('completed', 'trigger');
        
        this.setProperty('soundPath', '');
        this.setProperty('volume', 1.0);
        this.setProperty('loop', false);
        this.setProperty('fadeIn', 0);
        this.setProperty('fadeOut', 0);
    }
    
    public execute(context: EventContext): void {
        const soundPath = this.getProperty('soundPath', '');
        const volume = this.getProperty('volume', 1.0);
        const loop = this.getProperty('loop', false);
        
        console.log(`Playing sound: ${soundPath} at volume ${volume}`);
        
        // TODO: Implement actual sound playing
        this.triggerOutput('completed', context);
    }
    
    private triggerOutput(portName: string, context: EventContext): void {
        console.log(`PlaySoundEventNode ${this.id} triggered output: ${portName}`);
    }
}

/**
 * Show/Hide UI event node
 */
export class UIEventNode extends BaseEventNode {
    constructor(id?: string) {
        super(EventActionType.ShowUI, id);
    }
    
    protected setupPorts(): void {
        this.addInputPort('show', 'trigger');
        this.addInputPort('hide', 'trigger');
        this.addOutputPort('shown', 'trigger');
        this.addOutputPort('hidden', 'trigger');
        
        this.setProperty('uiElementName', '');
        this.setProperty('animationType', 'fade');
        this.setProperty('duration', 0.3);
    }
    
    public execute(context: EventContext): void {
        const uiElementName = this.getProperty('uiElementName', '');
        const animationType = this.getProperty('animationType', 'fade');
        const duration = this.getProperty('duration', 0.3);
        
        // Check which input was triggered
        // This is a simplified implementation
        console.log(`UI action on: ${uiElementName} with ${animationType} for ${duration}s`);
    }
    
    private triggerOutput(portName: string, context: EventContext): void {
        console.log(`UIEventNode ${this.id} triggered output: ${portName}`);
    }
}

/**
 * Event graph manager
 */
@ccclass('EventGraphManager')
export class EventGraphManager extends Component {
    private eventGraph: EventGraph | null = null;
    private nodeInstances: Map<string, IEventNode> = new Map();
    private isExecuting: boolean = false;
    
    // Execution context
    private currentTime: number = 0;
    private executionQueue: IEventNode[] = [];
    
    protected start(): void {
        console.log('EventGraphManager initialized');
    }
    
    protected update(deltaTime: number): void {
        if (this.isExecuting && this.eventGraph) {
            this.currentTime += deltaTime;
            this.executeGraph(deltaTime);
        }
    }
    
    /**
     * Load an event graph
     */
    public loadEventGraph(eventGraph: EventGraph): void {
        this.eventGraph = eventGraph;
        this.createNodeInstances();
        console.log(`Event graph loaded with ${this.nodeInstances.size} nodes`);
    }
    
    /**
     * Start executing the event graph
     */
    public startExecution(): void {
        this.isExecuting = true;
        this.currentTime = 0;
        console.log('Event graph execution started');
    }
    
    /**
     * Stop executing the event graph
     */
    public stopExecution(): void {
        this.isExecuting = false;
        this.executionQueue = [];
        console.log('Event graph execution stopped');
    }
    
    /**
     * Pause execution
     */
    public pauseExecution(): void {
        this.isExecuting = false;
    }
    
    /**
     * Resume execution
     */
    public resumeExecution(): void {
        this.isExecuting = true;
    }
    
    /**
     * Execute the event graph
     */
    private executeGraph(deltaTime: number): void {
        if (!this.eventGraph) return;
        
        const context: EventContext = {
            deltaTime,
            currentTime: this.currentTime,
            level: null as any, // Will be set by LevelManager
            triggerData: null
        };
        
        // Execute all nodes
        this.nodeInstances.forEach(node => {
            try {
                node.execute(context);
            } catch (error) {
                console.error(`Error executing event node ${node.id}:`, error);
            }
        });
        
        // Process execution queue
        this.processExecutionQueue(context);
    }
    
    /**
     * Process queued node executions
     */
    private processExecutionQueue(context: EventContext): void {
        while (this.executionQueue.length > 0) {
            const node = this.executionQueue.shift();
            if (node) {
                try {
                    node.execute(context);
                } catch (error) {
                    console.error(`Error executing queued node ${node.id}:`, error);
                }
            }
        }
    }
    
    /**
     * Create node instances from graph data
     */
    private createNodeInstances(): void {
        if (!this.eventGraph) return;
        
        this.nodeInstances.clear();
        
        this.eventGraph.nodes.forEach((nodeData, nodeId) => {
            const nodeInstance = this.createNodeInstance(nodeData);
            if (nodeInstance) {
                this.nodeInstances.set(nodeId, nodeInstance);
            }
        });
        
        // Setup connections
        this.setupConnections();
    }
    
    /**
     * Create a node instance based on type
     */
    private createNodeInstance(nodeData: IEventNode): IEventNode | null {
        switch (nodeData.type) {
            case EventType.TimeReached:
                const timeNode = new TimeEventNode(nodeData.id);
                this.copyNodeData(nodeData, timeNode);
                return timeNode;
                
            case EventActionType.PlayAnimation:
                const animNode = new PlayAnimationEventNode(nodeData.id);
                this.copyNodeData(nodeData, animNode);
                return animNode;
                
            case EventActionType.PlaySound:
                const soundNode = new PlaySoundEventNode(nodeData.id);
                this.copyNodeData(nodeData, soundNode);
                return soundNode;
                
            case EventActionType.ShowUI:
            case EventActionType.HideUI:
                const uiNode = new UIEventNode(nodeData.id);
                this.copyNodeData(nodeData, uiNode);
                return uiNode;
                
            default:
                console.warn(`Unknown event node type: ${nodeData.type}`);
                return null;
        }
    }
    
    /**
     * Copy data from serialized node to instance
     */
    private copyNodeData(source: IEventNode, target: IEventNode): void {
        target.properties = { ...source.properties };
        // Note: Don't copy editorData as it's editor-only
    }
    
    /**
     * Setup connections between nodes
     */
    private setupConnections(): void {
        if (!this.eventGraph) return;
        
        // TODO: Implement connection setup
        // This will wire up the node inputs/outputs based on the connection data
        console.log('Setting up event node connections...');
    }
    
    /**
     * Trigger a specific event
     */
    public triggerEvent(eventType: string, data?: any): void {
        // Find nodes that should respond to this event type
        this.nodeInstances.forEach(node => {
            if (this.shouldNodeRespondToEvent(node, eventType)) {
                this.executionQueue.push(node);
            }
        });
    }
    
    /**
     * Check if a node should respond to an event
     */
    private shouldNodeRespondToEvent(node: IEventNode, eventType: string): boolean {
        // This is a simplified implementation
        // In a full system, this would check the node's input connections
        return node.type === eventType;
    }
    
    /**
     * Add a node to the graph
     */
    public addNode(node: IEventNode): void {
        if (!this.eventGraph) {
            this.eventGraph = {
                nodes: new Map(),
                connections: [],
                triggers: []
            };
        }
        
        this.eventGraph.nodes.set(node.id, node);
        this.nodeInstances.set(node.id, node);
    }
    
    /**
     * Remove a node from the graph
     */
    public removeNode(nodeId: string): void {
        if (this.eventGraph) {
            this.eventGraph.nodes.delete(nodeId);
        }
        this.nodeInstances.delete(nodeId);
    }
    
    /**
     * Get current time
     */
    public getCurrentTime(): number {
        return this.currentTime;
    }
    
    /**
     * Reset time
     */
    public resetTime(): void {
        this.currentTime = 0;
    }
}

/**
 * Event node factory for creating different types of event nodes
 */
export class EventNodeFactory {
    private static nodeTypes: Map<string, () => IEventNode> = new Map([
        [EventType.TimeReached, () => new TimeEventNode()],
        [EventActionType.PlayAnimation, () => new PlayAnimationEventNode()],
        [EventActionType.PlaySound, () => new PlaySoundEventNode()],
        [EventActionType.ShowUI, () => new UIEventNode()],
        [EventActionType.HideUI, () => new UIEventNode()]
    ]);

    /**
     * Create a new event node of the specified type
     */
    public static createNode(type: string): IEventNode | null {
        const factory = this.nodeTypes.get(type);
        if (factory) {
            return factory();
        }

        console.warn(`Unknown event node type: ${type}`);
        return null;
    }

    /**
     * Register a new node type
     */
    public static registerNodeType(type: string, factory: () => IEventNode): void {
        this.nodeTypes.set(type, factory);
    }

    /**
     * Get all available node types
     */
    public static getAvailableTypes(): string[] {
        return Array.from(this.nodeTypes.keys());
    }
}
