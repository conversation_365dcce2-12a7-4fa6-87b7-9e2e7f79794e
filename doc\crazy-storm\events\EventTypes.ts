/**
 * CrazyStorm 2.0 Integration for Cocos Creator
 * Event System Types
 * 
 * Defines types and enums for the CrazyStorm event system.
 * Based on the C# EventHelper.cs and related files.
 */

import { Vec2 } from 'cc';
import { RGB, VMInstruction } from '../expression';

/**
 * Property types for event system
 */
export enum PropertyType {
    Boolean = 0,
    Int32 = 1,
    Single = 2,
    Enum = 3,
    Vector2 = 4,
    RGB = 5,
    String = 6
}

/**
 * Event keywords for change modes and types
 */
export enum EventKeyword {
    // Change modes
    Linear = 'Linear',
    Accelerated = 'Accelerated',
    Decelerated = 'Decelerated',
    Instant = 'Instant',
    
    // Change types
    Set = 'Set',
    Increase = 'Increase',
    Decrease = 'Decrease'
}

/**
 * Event operators for conditions
 */
export enum EventOperator {
    None = 0,
    Equal = 1,
    NotEqual = 2,
    Greater = 3,
    Less = 4,
    GreaterOrEqual = 5,
    LessOrEqual = 6,
    And = 7,
    Or = 8
}

/**
 * Type-safe value container for different property types
 */
export class TypeSet {
    public type: PropertyType = PropertyType.Single;
    public boolValue: boolean = false;
    public intValue: number = 0;
    public floatValue: number = 0;
    public enumValue: number = 0;
    public vector2Value: Vec2 = new Vec2(0, 0);
    public rgbValue: RGB = { r: 0, g: 0, b: 0 };
    public stringValue: string = '';

    constructor(type?: PropertyType, value?: any) {
        if (type !== undefined) {
            this.type = type;
            this.setValue(value);
        }
    }

    /**
     * Set value based on type
     */
    public setValue(value: any): void {
        switch (this.type) {
            case PropertyType.Boolean:
                this.boolValue = Boolean(value);
                break;
            case PropertyType.Int32:
                this.intValue = Math.floor(Number(value) || 0);
                break;
            case PropertyType.Single:
                this.floatValue = Number(value) || 0;
                break;
            case PropertyType.Enum:
                this.enumValue = Math.floor(Number(value) || 0);
                break;
            case PropertyType.Vector2:
                if (value instanceof Vec2) {
                    this.vector2Value = value.clone();
                } else if (typeof value === 'object' && value.x !== undefined && value.y !== undefined) {
                    this.vector2Value.set(value.x, value.y);
                } else {
                    this.vector2Value.set(0, 0);
                }
                break;
            case PropertyType.RGB:
                if (typeof value === 'object' && value.r !== undefined) {
                    this.rgbValue = { r: value.r, g: value.g, b: value.b };
                } else {
                    this.rgbValue = { r: 0, g: 0, b: 0 };
                }
                break;
            case PropertyType.String:
                this.stringValue = String(value || '');
                break;
        }
    }

    /**
     * Get value based on type
     */
    public getValue(): any {
        switch (this.type) {
            case PropertyType.Boolean:
                return this.boolValue;
            case PropertyType.Int32:
                return this.intValue;
            case PropertyType.Single:
                return this.floatValue;
            case PropertyType.Enum:
                return this.enumValue;
            case PropertyType.Vector2:
                return this.vector2Value;
            case PropertyType.RGB:
                return this.rgbValue;
            case PropertyType.String:
                return this.stringValue;
            default:
                return 0;
        }
    }

    /**
     * Clone this TypeSet
     */
    public clone(): TypeSet {
        const clone = new TypeSet(this.type);
        clone.boolValue = this.boolValue;
        clone.intValue = this.intValue;
        clone.floatValue = this.floatValue;
        clone.enumValue = this.enumValue;
        clone.vector2Value = this.vector2Value.clone();
        clone.rgbValue = { ...this.rgbValue };
        clone.stringValue = this.stringValue;
        return clone;
    }
}

/**
 * Event information structure
 */
export interface EventInfo {
    // Condition
    hasCondition: boolean;
    leftProperty: string;
    leftOperator: EventOperator;
    leftType: PropertyType;
    leftValue: TypeSet;
    midOperator: EventOperator;
    rightProperty: string;
    rightOperator: EventOperator;
    rightType: PropertyType;
    rightValue: TypeSet;
    
    // Result
    isSpecialEvent: boolean;
    resultProperty: string;
    changeType: EventKeyword;
    isExpressionResult: boolean;
    resultType: PropertyType;
    resultValue: TypeSet;
    resultExpression?: VMInstruction[];
    changeMode: EventKeyword;
    changeTime: number;
    
    // Special events
    specialEvent?: string;
    arguments?: string[];
    argumentExpression?: VMInstruction[];
}

/**
 * Event group containing multiple events with a shared condition
 */
export interface EventGroup {
    name: string;
    condition: string;
    vmCondition?: VMInstruction[];
    events: EventInfo[];
    originalEvents: string[];
}

/**
 * Special event types
 */
export enum SpecialEventType {
    EmitParticle = 'EmitParticle',
    PlaySound = 'PlaySound',
    Loop = 'Loop',
    ChangeType = 'ChangeType'
}

/**
 * Property container interface for event execution
 */
export interface PropertyContainer {
    /**
     * Push property value onto VM stack
     */
    pushProperty(propertyName: string): boolean;
    
    /**
     * Set property value from VM stack
     */
    setProperty(propertyName: string): boolean;
    
    /**
     * Get property value
     */
    getProperty(propertyName: string): any;
    
    /**
     * Set property value directly
     */
    setPropertyValue(propertyName: string, value: any): void;
}

/**
 * Event execution context
 */
export interface EventContext {
    propertyContainer: PropertyContainer;
    bindingContainer?: PropertyContainer;
    globalVariables?: Map<string, number>;
    localVariables?: Map<string, number>;
    currentFrame?: number;
    deltaTime?: number;
}
