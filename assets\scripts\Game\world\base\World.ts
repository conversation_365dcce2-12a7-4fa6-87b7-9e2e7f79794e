import { SystemContainer } from "./SystemContainer";
import { System } from "./System";
import { IWorldInitializeData } from "../WorldInitializeData";



/**
 * World state enumeration
 */
export enum WorldState {
    UNINITIALIZED = "uninitialized",
    INITIALIZING = "initializing",
    RUNNING = "running",
    PAUSED = "paused",
    STOPPING = "stopping",
    STOPPED = "stopped",
    ERROR = "error"
}

/**
 * World class - the main gameplay driver
 * Contains a SystemContainer that manages all game systems
 * Handles world initialization, update loops, and lifecycle management
 */
export class World {

    private _systemContainer: SystemContainer;
    private _initializeData: IWorldInitializeData | null = null;
    private _state: WorldState = WorldState.UNINITIALIZED;
    private _timeScale: number = 1.0;
    private _totalTime: number = 0;
    private _frameCount: number = 0;

    // Event callbacks
    private _onStateChanged: ((oldState: WorldState, newState: WorldState) => void) | null = null;
    private _onError: ((error: Error) => void) | null = null;

    /**
     * Create a new World instance
     */
    constructor() {
        this._systemContainer = new SystemContainer();
        console.log("World: Created new world instance");
    }

    /**
     * Initialize the world with the provided data
     * @param initData World initialization data
     * @returns Promise that resolves when initialization is complete
     */
    public async initialize(initData: IWorldInitializeData): Promise<boolean> {
        if (this._state !== WorldState.UNINITIALIZED) {
            console.warn("World: Cannot initialize - world is not in uninitialized state");
            return false;
        }

        // Validate initialization data
        const validationErrors = initData.validate();
        if (validationErrors.length > 0) {
            const error = new Error(`World initialization failed: ${validationErrors.join(", ")}`);
            this._handleError(error);
            return false;
        }

        this._setState(WorldState.INITIALIZING);

        try {
            // Store initialization data
            this._initializeData = initData.clone();

            // Apply world configuration
            this._timeScale = initData.physicsConfig.timeScale;

            // Initialize system container
            this._systemContainer.init();

            console.log(`World: Initialized successfully with mode: ${initData.modeId}, level: ${initData.levelId}`);
            this._setState(WorldState.RUNNING);

            return true;
        } catch (error) {
            this._handleError(error as Error);
            return false;
        }
    }

    /**
     * Start the world (resume from paused state)
     */
    public start(): void {
        if (this._state === WorldState.PAUSED) {
            this._setState(WorldState.RUNNING);
            console.log("World: Resumed from pause");
        } else if (this._state === WorldState.STOPPED) {
            this._setState(WorldState.RUNNING);
            console.log("World: Started from stopped state");
        } else {
            console.warn(`World: Cannot start from state: ${this._state}`);
        }
    }

    /**
     * Pause the world
     */
    public pause(): void {
        if (this._state === WorldState.RUNNING) {
            this._setState(WorldState.PAUSED);
            console.log("World: Paused");
        } else {
            console.warn(`World: Cannot pause from state: ${this._state}`);
        }
    }

    /**
     * Stop the world
     */
    public stop(): void {
        if (this._state === WorldState.RUNNING || this._state === WorldState.PAUSED) {
            this._setState(WorldState.STOPPING);

            try {
                // Cleanup systems
                this._systemContainer.unInit();

                this._setState(WorldState.STOPPED);
                console.log("World: Stopped");
            } catch (error) {
                this._handleError(error as Error);
            }
        } else {
            console.warn(`World: Cannot stop from state: ${this._state}`);
        }
    }

    /**
     * Destroy the world and cleanup all resources
     */
    public destroy(): void {
        this.stop();

        // Clear references
        this._initializeData = null;
        this._onStateChanged = null;
        this._onError = null;

        this._setState(WorldState.UNINITIALIZED);
        console.log("World: Destroyed");
    }

    /**
     * Update the world
     * @param deltaTime Time elapsed since last frame in seconds
     */
    public update(deltaTime: number): void {
        if (this._state !== WorldState.RUNNING) {
            return;
        }

        try {
            // Apply time scale
            const scaledDeltaTime = deltaTime * this._timeScale;

            // Update total time and frame count
            this._totalTime += scaledDeltaTime;
            this._frameCount++;

            // Update all systems
            this._systemContainer.update(scaledDeltaTime);

        } catch (error) {
            this._handleError(error as Error);
        }
    }

    /**
     * Late update the world
     * @param deltaTime Time elapsed since last frame in seconds
     */
    public lateUpdate(deltaTime: number): void {
        if (this._state !== WorldState.RUNNING) {
            return;
        }

        try {
            // Apply time scale
            const scaledDeltaTime = deltaTime * this._timeScale;

            // Late update all systems
            this._systemContainer.lateUpdate(scaledDeltaTime);

        } catch (error) {
            this._handleError(error as Error);
        }
    }

    /**
     * Register a system to the world
     * @param system The system to register
     * @returns true if registration was successful
     */
    public registerSystem(system: System): boolean {
        return this._systemContainer.registerSystem(system);
    }

    /**
     * Unregister a system from the world
     * @param systemConstructor The constructor of the system to unregister
     * @returns true if unregistration was successful
     */
    public unregisterSystem<T extends System>(systemConstructor: new (...args: any[]) => T): boolean {
        return this._systemContainer.unregisterSystem(systemConstructor);
    }

    /**
     * Get a system by type
     * @param systemConstructor The constructor of the system to get
     * @returns The system instance or null if not found
     */
    public getSystem<T extends System>(systemConstructor: new (...args: any[]) => T): T | null {
        return this._systemContainer.getSystem<T>(systemConstructor);
    }

    /**
     * Check if a system is registered
     * @param systemConstructor The constructor of the system to check
     * @returns true if the system is registered
     */
    public hasSystem<T extends System>(systemConstructor: new (...args: any[]) => T): boolean {
        return this._systemContainer.hasSystem(systemConstructor);
    }

    /**
     * Enable or disable a specific system
     * @param systemConstructor The constructor of the system to enable/disable
     * @param enabled Whether to enable or disable the system
     * @returns true if the operation was successful
     */
    public setSystemEnabled<T extends System>(systemConstructor: new (...args: any[]) => T, enabled: boolean): boolean {
        return this._systemContainer.setSystemEnabled(systemConstructor, enabled);
    }

    /**
     * Get the current world state
     */
    public getState(): WorldState {
        return this._state;
    }

    /**
     * Get the world initialization data
     */
    public getInitializeData(): IWorldInitializeData | null {
        return this._initializeData ? this._initializeData.clone() : null;
    }

    /**
     * Get the current time scale
     */
    public getTimeScale(): number {
        return this._timeScale;
    }

    /**
     * Set the time scale for the world
     * @param timeScale The new time scale (1.0 = normal speed)
     */
    public setTimeScale(timeScale: number): void {
        if (timeScale < 0) {
            console.warn("World: Time scale cannot be negative");
            return;
        }

        this._timeScale = timeScale;
        console.log(`World: Time scale set to ${timeScale}`);
    }

    /**
     * Get the total time the world has been running
     */
    public getTotalTime(): number {
        return this._totalTime;
    }

    /**
     * Get the current frame count
     */
    public getFrameCount(): number {
        return this._frameCount;
    }

    /**
     * Set the state changed callback
     * @param callback Function to call when state changes
     */
    public setOnStateChanged(callback: (oldState: WorldState, newState: WorldState) => void): void {
        this._onStateChanged = callback;
    }

    /**
     * Set the error callback
     * @param callback Function to call when an error occurs
     */
    public setOnError(callback: (error: Error) => void): void {
        this._onError = callback;
    }

    /**
     * Internal method to change the world state
     * @param newState The new state to set
     */
    private _setState(newState: WorldState): void {
        if (this._state === newState) {
            return;
        }

        const oldState = this._state;
        this._state = newState;

        console.log(`World: State changed from ${oldState} to ${newState}`);

        if (this._onStateChanged) {
            try {
                this._onStateChanged(oldState, newState);
            } catch (error) {
                console.error("World: Error in state change callback:", error);
            }
        }
    }

    /**
     * Internal method to handle errors
     * @param error The error that occurred
     */
    private _handleError(error: Error): void {
        console.error("World: Error occurred:", error);
        this._setState(WorldState.ERROR);

        if (this._onError) {
            try {
                this._onError(error);
            } catch (callbackError) {
                console.error("World: Error in error callback:", callbackError);
            }
        }
    }
}