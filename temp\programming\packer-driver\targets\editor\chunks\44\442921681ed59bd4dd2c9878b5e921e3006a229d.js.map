{"version": 3, "sources": ["file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/base/World.ts"], "names": ["World", "SystemContainer", "WorldState", "constructor", "_systemContainer", "_initializeData", "_state", "UNINITIALIZED", "_timeScale", "_totalTime", "_frameCount", "_onStateChanged", "_onError", "console", "log", "initialize", "initData", "warn", "validationErrors", "validate", "length", "error", "Error", "join", "_handleError", "_setState", "INITIALIZING", "clone", "physicsConfig", "timeScale", "init", "modeId", "levelId", "RUNNING", "start", "PAUSED", "STOPPED", "pause", "stop", "STOPPING", "unInit", "destroy", "update", "deltaTime", "scaledDeltaTime", "lateUpdate", "registerSystem", "system", "unregisterSystem", "systemConstructor", "getSystem", "hasSystem", "setSystemEnabled", "enabled", "getState", "getInitializeData", "getTimeScale", "setTimeScale", "getTotalTime", "getFrameCount", "setOnStateChanged", "callback", "setOnError", "newState", "oldState", "ERROR", "callback<PERSON><PERSON><PERSON>"], "mappings": ";;;+CAwBaA,K;;;;;;;;;;;;;;;;;;;;;;AAxBJC,MAAAA,e,iBAAAA,e;;;;;;;AAMT;AACA;AACA;4BACYC,U,0BAAAA,U;AAAAA,QAAAA,U;AAAAA,QAAAA,U;AAAAA,QAAAA,U;AAAAA,QAAAA,U;AAAAA,QAAAA,U;AAAAA,QAAAA,U;AAAAA,QAAAA,U;eAAAA,U;;AAUZ;AACA;AACA;AACA;AACA;;;uBACaF,K,GAAN,MAAMA,KAAN,CAAY;AAaf;AACJ;AACA;AACIG,QAAAA,WAAW,GAAG;AAAA,eAdNC,gBAcM;AAAA,eAbNC,eAaM,GAbyC,IAazC;AAAA,eAZNC,MAYM,GAZeJ,UAAU,CAACK,aAY1B;AAAA,eAXNC,UAWM,GAXe,GAWf;AAAA,eAVNC,UAUM,GAVe,CAUf;AAAA,eATNC,WASM,GATgB,CAShB;AAPd;AAOc,eANNC,eAMM,GAN2E,IAM3E;AAAA,eALNC,QAKM,GALsC,IAKtC;AACV,eAAKR,gBAAL,GAAwB;AAAA;AAAA,mDAAxB;AACAS,UAAAA,OAAO,CAACC,GAAR,CAAY,mCAAZ;AACH;AAED;AACJ;AACA;AACA;AACA;;;AAC2B,cAAVC,UAAU,CAACC,QAAD,EAAmD;AACtE,cAAI,KAAKV,MAAL,KAAgBJ,UAAU,CAACK,aAA/B,EAA8C;AAC1CM,YAAAA,OAAO,CAACI,IAAR,CAAa,gEAAb;AACA,mBAAO,KAAP;AACH,WAJqE,CAMtE;;;AACA,gBAAMC,gBAAgB,GAAGF,QAAQ,CAACG,QAAT,EAAzB;;AACA,cAAID,gBAAgB,CAACE,MAAjB,GAA0B,CAA9B,EAAiC;AAC7B,kBAAMC,KAAK,GAAG,IAAIC,KAAJ,CAAW,gCAA+BJ,gBAAgB,CAACK,IAAjB,CAAsB,IAAtB,CAA4B,EAAtE,CAAd;;AACA,iBAAKC,YAAL,CAAkBH,KAAlB;;AACA,mBAAO,KAAP;AACH;;AAED,eAAKI,SAAL,CAAevB,UAAU,CAACwB,YAA1B;;AAEA,cAAI;AACA;AACA,iBAAKrB,eAAL,GAAuBW,QAAQ,CAACW,KAAT,EAAvB,CAFA,CAIA;;AACA,iBAAKnB,UAAL,GAAkBQ,QAAQ,CAACY,aAAT,CAAuBC,SAAzC,CALA,CAOA;;AACA,iBAAKzB,gBAAL,CAAsB0B,IAAtB;;AAEAjB,YAAAA,OAAO,CAACC,GAAR,CAAa,8CAA6CE,QAAQ,CAACe,MAAO,YAAWf,QAAQ,CAACgB,OAAQ,EAAtG;;AACA,iBAAKP,SAAL,CAAevB,UAAU,CAAC+B,OAA1B;;AAEA,mBAAO,IAAP;AACH,WAdD,CAcE,OAAOZ,KAAP,EAAc;AACZ,iBAAKG,YAAL,CAAkBH,KAAlB;;AACA,mBAAO,KAAP;AACH;AACJ;AAED;AACJ;AACA;;;AACWa,QAAAA,KAAK,GAAS;AACjB,cAAI,KAAK5B,MAAL,KAAgBJ,UAAU,CAACiC,MAA/B,EAAuC;AACnC,iBAAKV,SAAL,CAAevB,UAAU,CAAC+B,OAA1B;;AACApB,YAAAA,OAAO,CAACC,GAAR,CAAY,2BAAZ;AACH,WAHD,MAGO,IAAI,KAAKR,MAAL,KAAgBJ,UAAU,CAACkC,OAA/B,EAAwC;AAC3C,iBAAKX,SAAL,CAAevB,UAAU,CAAC+B,OAA1B;;AACApB,YAAAA,OAAO,CAACC,GAAR,CAAY,mCAAZ;AACH,WAHM,MAGA;AACHD,YAAAA,OAAO,CAACI,IAAR,CAAc,mCAAkC,KAAKX,MAAO,EAA5D;AACH;AACJ;AAED;AACJ;AACA;;;AACW+B,QAAAA,KAAK,GAAS;AACjB,cAAI,KAAK/B,MAAL,KAAgBJ,UAAU,CAAC+B,OAA/B,EAAwC;AACpC,iBAAKR,SAAL,CAAevB,UAAU,CAACiC,MAA1B;;AACAtB,YAAAA,OAAO,CAACC,GAAR,CAAY,eAAZ;AACH,WAHD,MAGO;AACHD,YAAAA,OAAO,CAACI,IAAR,CAAc,mCAAkC,KAAKX,MAAO,EAA5D;AACH;AACJ;AAED;AACJ;AACA;;;AACWgC,QAAAA,IAAI,GAAS;AAChB,cAAI,KAAKhC,MAAL,KAAgBJ,UAAU,CAAC+B,OAA3B,IAAsC,KAAK3B,MAAL,KAAgBJ,UAAU,CAACiC,MAArE,EAA6E;AACzE,iBAAKV,SAAL,CAAevB,UAAU,CAACqC,QAA1B;;AAEA,gBAAI;AACA;AACA,mBAAKnC,gBAAL,CAAsBoC,MAAtB;;AAEA,mBAAKf,SAAL,CAAevB,UAAU,CAACkC,OAA1B;;AACAvB,cAAAA,OAAO,CAACC,GAAR,CAAY,gBAAZ;AACH,aAND,CAME,OAAOO,KAAP,EAAc;AACZ,mBAAKG,YAAL,CAAkBH,KAAlB;AACH;AACJ,WAZD,MAYO;AACHR,YAAAA,OAAO,CAACI,IAAR,CAAc,kCAAiC,KAAKX,MAAO,EAA3D;AACH;AACJ;AAED;AACJ;AACA;;;AACWmC,QAAAA,OAAO,GAAS;AACnB,eAAKH,IAAL,GADmB,CAGnB;;AACA,eAAKjC,eAAL,GAAuB,IAAvB;AACA,eAAKM,eAAL,GAAuB,IAAvB;AACA,eAAKC,QAAL,GAAgB,IAAhB;;AAEA,eAAKa,SAAL,CAAevB,UAAU,CAACK,aAA1B;;AACAM,UAAAA,OAAO,CAACC,GAAR,CAAY,kBAAZ;AACH;AAED;AACJ;AACA;AACA;;;AACW4B,QAAAA,MAAM,CAACC,SAAD,EAA0B;AACnC,cAAI,KAAKrC,MAAL,KAAgBJ,UAAU,CAAC+B,OAA/B,EAAwC;AACpC;AACH;;AAED,cAAI;AACA;AACA,kBAAMW,eAAe,GAAGD,SAAS,GAAG,KAAKnC,UAAzC,CAFA,CAIA;;AACA,iBAAKC,UAAL,IAAmBmC,eAAnB;AACA,iBAAKlC,WAAL,GANA,CAQA;;AACA,iBAAKN,gBAAL,CAAsBsC,MAAtB,CAA6BE,eAA7B;AAEH,WAXD,CAWE,OAAOvB,KAAP,EAAc;AACZ,iBAAKG,YAAL,CAAkBH,KAAlB;AACH;AACJ;AAED;AACJ;AACA;AACA;;;AACWwB,QAAAA,UAAU,CAACF,SAAD,EAA0B;AACvC,cAAI,KAAKrC,MAAL,KAAgBJ,UAAU,CAAC+B,OAA/B,EAAwC;AACpC;AACH;;AAED,cAAI;AACA;AACA,kBAAMW,eAAe,GAAGD,SAAS,GAAG,KAAKnC,UAAzC,CAFA,CAIA;;AACA,iBAAKJ,gBAAL,CAAsByC,UAAtB,CAAiCD,eAAjC;AAEH,WAPD,CAOE,OAAOvB,KAAP,EAAc;AACZ,iBAAKG,YAAL,CAAkBH,KAAlB;AACH;AACJ;AAED;AACJ;AACA;AACA;AACA;;;AACWyB,QAAAA,cAAc,CAACC,MAAD,EAA0B;AAC3C,iBAAO,KAAK3C,gBAAL,CAAsB0C,cAAtB,CAAqCC,MAArC,CAAP;AACH;AAED;AACJ;AACA;AACA;AACA;;;AACWC,QAAAA,gBAAgB,CAAmBC,iBAAnB,EAA0E;AAC7F,iBAAO,KAAK7C,gBAAL,CAAsB4C,gBAAtB,CAAuCC,iBAAvC,CAAP;AACH;AAED;AACJ;AACA;AACA;AACA;;;AACWC,QAAAA,SAAS,CAAmBD,iBAAnB,EAA2E;AACvF,iBAAO,KAAK7C,gBAAL,CAAsB8C,SAAtB,CAAmCD,iBAAnC,CAAP;AACH;AAED;AACJ;AACA;AACA;AACA;;;AACWE,QAAAA,SAAS,CAAmBF,iBAAnB,EAA0E;AACtF,iBAAO,KAAK7C,gBAAL,CAAsB+C,SAAtB,CAAgCF,iBAAhC,CAAP;AACH;AAED;AACJ;AACA;AACA;AACA;AACA;;;AACWG,QAAAA,gBAAgB,CAAmBH,iBAAnB,EAAiEI,OAAjE,EAA4F;AAC/G,iBAAO,KAAKjD,gBAAL,CAAsBgD,gBAAtB,CAAuCH,iBAAvC,EAA0DI,OAA1D,CAAP;AACH;AAED;AACJ;AACA;;;AACWC,QAAAA,QAAQ,GAAe;AAC1B,iBAAO,KAAKhD,MAAZ;AACH;AAED;AACJ;AACA;;;AACWiD,QAAAA,iBAAiB,GAAgC;AACpD,iBAAO,KAAKlD,eAAL,GAAuB,KAAKA,eAAL,CAAqBsB,KAArB,EAAvB,GAAsD,IAA7D;AACH;AAED;AACJ;AACA;;;AACW6B,QAAAA,YAAY,GAAW;AAC1B,iBAAO,KAAKhD,UAAZ;AACH;AAED;AACJ;AACA;AACA;;;AACWiD,QAAAA,YAAY,CAAC5B,SAAD,EAA0B;AACzC,cAAIA,SAAS,GAAG,CAAhB,EAAmB;AACfhB,YAAAA,OAAO,CAACI,IAAR,CAAa,sCAAb;AACA;AACH;;AAED,eAAKT,UAAL,GAAkBqB,SAAlB;AACAhB,UAAAA,OAAO,CAACC,GAAR,CAAa,4BAA2Be,SAAU,EAAlD;AACH;AAED;AACJ;AACA;;;AACW6B,QAAAA,YAAY,GAAW;AAC1B,iBAAO,KAAKjD,UAAZ;AACH;AAED;AACJ;AACA;;;AACWkD,QAAAA,aAAa,GAAW;AAC3B,iBAAO,KAAKjD,WAAZ;AACH;AAED;AACJ;AACA;AACA;;;AACWkD,QAAAA,iBAAiB,CAACC,QAAD,EAAuE;AAC3F,eAAKlD,eAAL,GAAuBkD,QAAvB;AACH;AAED;AACJ;AACA;AACA;;;AACWC,QAAAA,UAAU,CAACD,QAAD,EAAyC;AACtD,eAAKjD,QAAL,GAAgBiD,QAAhB;AACH;AAED;AACJ;AACA;AACA;;;AACYpC,QAAAA,SAAS,CAACsC,QAAD,EAA6B;AAC1C,cAAI,KAAKzD,MAAL,KAAgByD,QAApB,EAA8B;AAC1B;AACH;;AAED,gBAAMC,QAAQ,GAAG,KAAK1D,MAAtB;AACA,eAAKA,MAAL,GAAcyD,QAAd;AAEAlD,UAAAA,OAAO,CAACC,GAAR,CAAa,6BAA4BkD,QAAS,OAAMD,QAAS,EAAjE;;AAEA,cAAI,KAAKpD,eAAT,EAA0B;AACtB,gBAAI;AACA,mBAAKA,eAAL,CAAqBqD,QAArB,EAA+BD,QAA/B;AACH,aAFD,CAEE,OAAO1C,KAAP,EAAc;AACZR,cAAAA,OAAO,CAACQ,KAAR,CAAc,wCAAd,EAAwDA,KAAxD;AACH;AACJ;AACJ;AAED;AACJ;AACA;AACA;;;AACYG,QAAAA,YAAY,CAACH,KAAD,EAAqB;AACrCR,UAAAA,OAAO,CAACQ,KAAR,CAAc,wBAAd,EAAwCA,KAAxC;;AACA,eAAKI,SAAL,CAAevB,UAAU,CAAC+D,KAA1B;;AAEA,cAAI,KAAKrD,QAAT,EAAmB;AACf,gBAAI;AACA,mBAAKA,QAAL,CAAcS,KAAd;AACH,aAFD,CAEE,OAAO6C,aAAP,EAAsB;AACpBrD,cAAAA,OAAO,CAACQ,KAAR,CAAc,iCAAd,EAAiD6C,aAAjD;AACH;AACJ;AACJ;;AAhUc,O", "sourcesContent": ["import { SystemContainer } from \"./SystemContainer\";\r\nimport { System } from \"./System\";\r\nimport { IWorldInitializeData } from \"../WorldInitializeData\";\r\n\r\n\r\n\r\n/**\r\n * World state enumeration\r\n */\r\nexport enum WorldState {\r\n    UNINITIALIZED = \"uninitialized\",\r\n    INITIALIZING = \"initializing\",\r\n    RUNNING = \"running\",\r\n    PAUSED = \"paused\",\r\n    STOPPING = \"stopping\",\r\n    STOPPED = \"stopped\",\r\n    ERROR = \"error\"\r\n}\r\n\r\n/**\r\n * World class - the main gameplay driver\r\n * Contains a SystemContainer that manages all game systems\r\n * Handles world initialization, update loops, and lifecycle management\r\n */\r\nexport class World {\r\n\r\n    private _systemContainer: SystemContainer;\r\n    private _initializeData: IWorldInitializeData | null = null;\r\n    private _state: WorldState = WorldState.UNINITIALIZED;\r\n    private _timeScale: number = 1.0;\r\n    private _totalTime: number = 0;\r\n    private _frameCount: number = 0;\r\n\r\n    // Event callbacks\r\n    private _onStateChanged: ((oldState: WorldState, newState: WorldState) => void) | null = null;\r\n    private _onError: ((error: Error) => void) | null = null;\r\n\r\n    /**\r\n     * Create a new World instance\r\n     */\r\n    constructor() {\r\n        this._systemContainer = new SystemContainer();\r\n        console.log(\"World: Created new world instance\");\r\n    }\r\n\r\n    /**\r\n     * Initialize the world with the provided data\r\n     * @param initData World initialization data\r\n     * @returns Promise that resolves when initialization is complete\r\n     */\r\n    public async initialize(initData: IWorldInitializeData): Promise<boolean> {\r\n        if (this._state !== WorldState.UNINITIALIZED) {\r\n            console.warn(\"World: Cannot initialize - world is not in uninitialized state\");\r\n            return false;\r\n        }\r\n\r\n        // Validate initialization data\r\n        const validationErrors = initData.validate();\r\n        if (validationErrors.length > 0) {\r\n            const error = new Error(`World initialization failed: ${validationErrors.join(\", \")}`);\r\n            this._handleError(error);\r\n            return false;\r\n        }\r\n\r\n        this._setState(WorldState.INITIALIZING);\r\n\r\n        try {\r\n            // Store initialization data\r\n            this._initializeData = initData.clone();\r\n\r\n            // Apply world configuration\r\n            this._timeScale = initData.physicsConfig.timeScale;\r\n\r\n            // Initialize system container\r\n            this._systemContainer.init();\r\n\r\n            console.log(`World: Initialized successfully with mode: ${initData.modeId}, level: ${initData.levelId}`);\r\n            this._setState(WorldState.RUNNING);\r\n\r\n            return true;\r\n        } catch (error) {\r\n            this._handleError(error as Error);\r\n            return false;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Start the world (resume from paused state)\r\n     */\r\n    public start(): void {\r\n        if (this._state === WorldState.PAUSED) {\r\n            this._setState(WorldState.RUNNING);\r\n            console.log(\"World: Resumed from pause\");\r\n        } else if (this._state === WorldState.STOPPED) {\r\n            this._setState(WorldState.RUNNING);\r\n            console.log(\"World: Started from stopped state\");\r\n        } else {\r\n            console.warn(`World: Cannot start from state: ${this._state}`);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Pause the world\r\n     */\r\n    public pause(): void {\r\n        if (this._state === WorldState.RUNNING) {\r\n            this._setState(WorldState.PAUSED);\r\n            console.log(\"World: Paused\");\r\n        } else {\r\n            console.warn(`World: Cannot pause from state: ${this._state}`);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Stop the world\r\n     */\r\n    public stop(): void {\r\n        if (this._state === WorldState.RUNNING || this._state === WorldState.PAUSED) {\r\n            this._setState(WorldState.STOPPING);\r\n\r\n            try {\r\n                // Cleanup systems\r\n                this._systemContainer.unInit();\r\n\r\n                this._setState(WorldState.STOPPED);\r\n                console.log(\"World: Stopped\");\r\n            } catch (error) {\r\n                this._handleError(error as Error);\r\n            }\r\n        } else {\r\n            console.warn(`World: Cannot stop from state: ${this._state}`);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Destroy the world and cleanup all resources\r\n     */\r\n    public destroy(): void {\r\n        this.stop();\r\n\r\n        // Clear references\r\n        this._initializeData = null;\r\n        this._onStateChanged = null;\r\n        this._onError = null;\r\n\r\n        this._setState(WorldState.UNINITIALIZED);\r\n        console.log(\"World: Destroyed\");\r\n    }\r\n\r\n    /**\r\n     * Update the world\r\n     * @param deltaTime Time elapsed since last frame in seconds\r\n     */\r\n    public update(deltaTime: number): void {\r\n        if (this._state !== WorldState.RUNNING) {\r\n            return;\r\n        }\r\n\r\n        try {\r\n            // Apply time scale\r\n            const scaledDeltaTime = deltaTime * this._timeScale;\r\n\r\n            // Update total time and frame count\r\n            this._totalTime += scaledDeltaTime;\r\n            this._frameCount++;\r\n\r\n            // Update all systems\r\n            this._systemContainer.update(scaledDeltaTime);\r\n\r\n        } catch (error) {\r\n            this._handleError(error as Error);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Late update the world\r\n     * @param deltaTime Time elapsed since last frame in seconds\r\n     */\r\n    public lateUpdate(deltaTime: number): void {\r\n        if (this._state !== WorldState.RUNNING) {\r\n            return;\r\n        }\r\n\r\n        try {\r\n            // Apply time scale\r\n            const scaledDeltaTime = deltaTime * this._timeScale;\r\n\r\n            // Late update all systems\r\n            this._systemContainer.lateUpdate(scaledDeltaTime);\r\n\r\n        } catch (error) {\r\n            this._handleError(error as Error);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Register a system to the world\r\n     * @param system The system to register\r\n     * @returns true if registration was successful\r\n     */\r\n    public registerSystem(system: System): boolean {\r\n        return this._systemContainer.registerSystem(system);\r\n    }\r\n\r\n    /**\r\n     * Unregister a system from the world\r\n     * @param systemConstructor The constructor of the system to unregister\r\n     * @returns true if unregistration was successful\r\n     */\r\n    public unregisterSystem<T extends System>(systemConstructor: new (...args: any[]) => T): boolean {\r\n        return this._systemContainer.unregisterSystem(systemConstructor);\r\n    }\r\n\r\n    /**\r\n     * Get a system by type\r\n     * @param systemConstructor The constructor of the system to get\r\n     * @returns The system instance or null if not found\r\n     */\r\n    public getSystem<T extends System>(systemConstructor: new (...args: any[]) => T): T | null {\r\n        return this._systemContainer.getSystem<T>(systemConstructor);\r\n    }\r\n\r\n    /**\r\n     * Check if a system is registered\r\n     * @param systemConstructor The constructor of the system to check\r\n     * @returns true if the system is registered\r\n     */\r\n    public hasSystem<T extends System>(systemConstructor: new (...args: any[]) => T): boolean {\r\n        return this._systemContainer.hasSystem(systemConstructor);\r\n    }\r\n\r\n    /**\r\n     * Enable or disable a specific system\r\n     * @param systemConstructor The constructor of the system to enable/disable\r\n     * @param enabled Whether to enable or disable the system\r\n     * @returns true if the operation was successful\r\n     */\r\n    public setSystemEnabled<T extends System>(systemConstructor: new (...args: any[]) => T, enabled: boolean): boolean {\r\n        return this._systemContainer.setSystemEnabled(systemConstructor, enabled);\r\n    }\r\n\r\n    /**\r\n     * Get the current world state\r\n     */\r\n    public getState(): WorldState {\r\n        return this._state;\r\n    }\r\n\r\n    /**\r\n     * Get the world initialization data\r\n     */\r\n    public getInitializeData(): IWorldInitializeData | null {\r\n        return this._initializeData ? this._initializeData.clone() : null;\r\n    }\r\n\r\n    /**\r\n     * Get the current time scale\r\n     */\r\n    public getTimeScale(): number {\r\n        return this._timeScale;\r\n    }\r\n\r\n    /**\r\n     * Set the time scale for the world\r\n     * @param timeScale The new time scale (1.0 = normal speed)\r\n     */\r\n    public setTimeScale(timeScale: number): void {\r\n        if (timeScale < 0) {\r\n            console.warn(\"World: Time scale cannot be negative\");\r\n            return;\r\n        }\r\n\r\n        this._timeScale = timeScale;\r\n        console.log(`World: Time scale set to ${timeScale}`);\r\n    }\r\n\r\n    /**\r\n     * Get the total time the world has been running\r\n     */\r\n    public getTotalTime(): number {\r\n        return this._totalTime;\r\n    }\r\n\r\n    /**\r\n     * Get the current frame count\r\n     */\r\n    public getFrameCount(): number {\r\n        return this._frameCount;\r\n    }\r\n\r\n    /**\r\n     * Set the state changed callback\r\n     * @param callback Function to call when state changes\r\n     */\r\n    public setOnStateChanged(callback: (oldState: WorldState, newState: WorldState) => void): void {\r\n        this._onStateChanged = callback;\r\n    }\r\n\r\n    /**\r\n     * Set the error callback\r\n     * @param callback Function to call when an error occurs\r\n     */\r\n    public setOnError(callback: (error: Error) => void): void {\r\n        this._onError = callback;\r\n    }\r\n\r\n    /**\r\n     * Internal method to change the world state\r\n     * @param newState The new state to set\r\n     */\r\n    private _setState(newState: WorldState): void {\r\n        if (this._state === newState) {\r\n            return;\r\n        }\r\n\r\n        const oldState = this._state;\r\n        this._state = newState;\r\n\r\n        console.log(`World: State changed from ${oldState} to ${newState}`);\r\n\r\n        if (this._onStateChanged) {\r\n            try {\r\n                this._onStateChanged(oldState, newState);\r\n            } catch (error) {\r\n                console.error(\"World: Error in state change callback:\", error);\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Internal method to handle errors\r\n     * @param error The error that occurred\r\n     */\r\n    private _handleError(error: Error): void {\r\n        console.error(\"World: Error occurred:\", error);\r\n        this._setState(WorldState.ERROR);\r\n\r\n        if (this._onError) {\r\n            try {\r\n                this._onError(error);\r\n            } catch (callbackError) {\r\n                console.error(\"World: Error in error callback:\", callbackError);\r\n            }\r\n        }\r\n    }\r\n}"]}