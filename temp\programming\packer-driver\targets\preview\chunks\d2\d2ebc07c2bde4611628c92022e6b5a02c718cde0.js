System.register(["cc", "__unresolved_0", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4", "__unresolved_5", "__unresolved_6", "__unresolved_7", "__unresolved_8", "__unresolved_9"], function (_export, _context) {
  "use strict";

  var _cclegacy, _crd, WORLD_SYSTEM_VERSION;

  /**
   * Quick start helper function to create a basic world setup
   * @param levelId The level ID to initialize
   * @param gameMode The game mode (defaults to STORY)
   * @param difficulty The difficulty level (defaults to NORMAL)
   * @returns WorldInitializeData configured with the provided parameters
   */
  function createQuickWorldSetup(levelId, gameMode, difficulty) {
    if (gameMode === void 0) {
      gameMode = GameMode.STORY;
    }

    if (difficulty === void 0) {
      difficulty = DifficultyLevel.NORMAL;
    }

    return new WorldInitializeData({
      modeId: gameMode,
      levelId: levelId,
      difficulty: difficulty,
      randomSeed: Date.now()
    });
  }
  /**
   * Helper function to create a world with common systems pre-registered
   * @returns A new World instance with BulletSystem, LevelSystem, and PlayerSystem registered
   */


  function createWorldWithCommonSystems() {
    var world = new World(); // Register common systems

    world.registerSystem(new BulletSystem());
    world.registerSystem(new LevelSystem());
    world.registerSystem(new PlayerSystem());
    return world;
  }
  /**
   * Example usage documentation
   * 
   * Basic usage:
   * ```typescript
   * import { Bootstrap, createQuickWorldSetup } from "./Game/world";
   * 
   * // Add Bootstrap component to a node in the scene
   * const bootstrap = node.addComponent(Bootstrap);
   * bootstrap.levelId = "level_001";
   * bootstrap.gameMode = GameMode.STORY;
   * bootstrap.autoStart = true;
   * ```
   * 
   * Advanced usage:
   * ```typescript
   * import {
   *     World,
   *     WorldInitializeData,
   *     GameMode,
   *     BulletSystem,
   *     LevelSystem,
   *     PlayerSystem
   * } from "./Game/world";
   *
   * // Create world manually
   * const world = new World();
   *
   * // Register systems
   * world.registerSystem(new BulletSystem());
   * world.registerSystem(new LevelSystem());
   * world.registerSystem(new PlayerSystem());
   *
   * // Initialize world
   * const initData = new WorldInitializeData({
   *     levelId: "custom_level",
   *     modeId: GameMode.CUSTOM
   * });
   *
   * world.initialize(initData).then(success => {
   *     if (success) {
   *         console.log("World initialized successfully");
   *
   *         // Get systems using TypeID
   *         const bulletSystem = world.getSystem(BulletSystem);
   *         const playerSystem = world.getSystem(PlayerSystem);
   *
   *         // Start game loop
   *         const updateLoop = (deltaTime: number) => {
   *             world.update(deltaTime);
   *             world.lateUpdate(deltaTime);
   *         };
   *     }
   * });
   * ```
   * 
   * Custom system creation:
   * ```typescript
   * import { System } from "./Game/world";
   * 
   * class CustomSystem extends System {
   *     getSystemName(): string {
   *         return "CustomSystem";
   *     }
   *     
   *     protected onInit(): void {
   *         console.log("Custom system initialized");
   *     }
   *     
   *     protected onUnInit(): void {
   *         console.log("Custom system cleaned up");
   *     }
   *     
   *     protected onUpdate(deltaTime: number): void {
   *         // Custom update logic
   *     }
   *     
   *     protected onLateUpdate(deltaTime: number): void {
   *         // Custom late update logic
   *     }
   * }
   * 
   * // Register to world
   * world.registerSystem(new CustomSystem());
   * ```
   */


  _export({
    createQuickWorldSetup: createQuickWorldSetup,
    createWorldWithCommonSystems: createWorldWithCommonSystems
  });

  return {
    setters: [function (_cc) {
      _cclegacy = _cc.cclegacy;
    }, function (_unresolved_) {
      _export("System", _unresolved_.System);
    }, function (_unresolved_2) {
      _export("SystemContainer", _unresolved_2.SystemContainer);
    }, function (_unresolved_3) {
      _export({
        TypeID: _unresolved_3.TypeID,
        RegisterTypeID: _unresolved_3.RegisterTypeID,
        TypedBase: _unresolved_3.TypedBase
      });
    }, function (_unresolved_4) {
      _export("TypeIDUtils", _unresolved_4.TypeIDUtils);
    }, function (_unresolved_5) {
      _export({
        World: _unresolved_5.World,
        WorldState: _unresolved_5.WorldState
      });
    }, function (_unresolved_6) {
      _export({
        WorldInitializeData: _unresolved_6.WorldInitializeData,
        GameMode: _unresolved_6.GameMode,
        DifficultyLevel: _unresolved_6.DifficultyLevel
      });
    }, function (_unresolved_7) {
      _export("Bootstrap", _unresolved_7.Bootstrap);
    }, function (_unresolved_8) {
      _export("BulletSystem", _unresolved_8.BulletSystem);
    }, function (_unresolved_9) {
      _export({
        LevelSystem: _unresolved_9.LevelSystem,
        LevelEventType: _unresolved_9.LevelEventType
      });
    }, function (_unresolved_10) {
      _export({
        PlayerSystem: _unresolved_10.PlayerSystem,
        PlayerState: _unresolved_10.PlayerState
      });
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "22545+MZm9BNoNhD+X7pXoP", "index", undefined);
      /**
       * World System Index
       * 
       * This file exports all components of the World mechanism for easy importing
       * throughout the game codebase.
       */
      // Base system components
      // World core
      // World initialization data
      // Bootstrap component
      // Example systems


      /**
       * Version information for the World system
       */
      _export("WORLD_SYSTEM_VERSION", WORLD_SYSTEM_VERSION = "1.0.0");

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=d2ebc07c2bde4611628c92022e6b5a02c718cde0.js.map