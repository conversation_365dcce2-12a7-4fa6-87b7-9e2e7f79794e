System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, Color, GizmoDrawer, Emitter, EmitterGizmo, _crd;

  function _reportPossibleCrUseOfGizmoDrawer(extras) {
    _reporterNs.report("GizmoDrawer", "./GizmoDrawer", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEmitter(extras) {
    _reporterNs.report("Emitter", "../world/bullet/Emitter", _context.meta, extras);
  }

  _export("EmitterGizmo", void 0);

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      Color = _cc.Color;
    }, function (_unresolved_2) {
      GizmoDrawer = _unresolved_2.GizmoDrawer;
    }, function (_unresolved_3) {
      Emitter = _unresolved_3.Emitter;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "c633aZUyAZGb66KMv2ve9zS", "EmitterGizmo", undefined);

      __checkObsolete__(['_decorator', 'Graphics', 'Color', 'Node']);

      /**
       * Gizmo drawer for base Emitter components
       * Draws visual debugging information for basic bullet emitters
       */
      _export("EmitterGizmo", EmitterGizmo = class EmitterGizmo extends (_crd && GizmoDrawer === void 0 ? (_reportPossibleCrUseOfGizmoDrawer({
        error: Error()
      }), GizmoDrawer) : GizmoDrawer) {
        constructor(...args) {
          super(...args);
          this.componentType = _crd && Emitter === void 0 ? (_reportPossibleCrUseOfEmitter({
            error: Error()
          }), Emitter) : Emitter;
          this.drawerName = "EmitterGizmo";
          // Gizmo display options
          this.showDirections = true;
          this.showCenter = true;
          // Colors
          this.directionColor = Color.RED;
          this.centerColor = Color.WHITE;
          // Display settings
          this.speedScale = 1.0;
          this.arrowSize = 8;
          this.centerSize = 8;
        }

        drawGizmos(emitter, graphics, node) {
          // Get world position for drawing
          const worldPos = node.worldPosition; // Draw center point

          if (this.showCenter) {
            this.drawCenter(graphics, worldPos.x, worldPos.y);
          } // Draw direction arrows


          if (this.showDirections && emitter.count > 0) {
            this.drawDirections(graphics, emitter, worldPos.x, worldPos.y);
          }
        }

        drawCenter(graphics, worldX, worldY) {
          this.drawCross(graphics, worldX, worldY, this.centerSize, this.centerColor);
        }

        drawDirections(graphics, emitter, worldX, worldY) {
          const baseLength = 30;
          const speedFactor = emitter.speedMultiplier || 1;
          const arrowLength = Math.max(baseLength, baseLength * speedFactor * this.speedScale);

          for (let i = 0; i < emitter.count; i++) {
            // For basic emitters, just draw simple forward direction (up)
            const dirX = 0;
            const dirY = 1;
            const startX = worldX;
            const startY = worldY;
            const endX = worldX + dirX * arrowLength;
            const endY = worldY + dirY * arrowLength; // Draw arrow

            this.drawArrow(graphics, startX, startY, endX, endY, this.directionColor, this.arrowSize);
          }
        }

        getPriority() {
          return 5; // Draw base emitter gizmos with lower priority than EmitterArc
        }
        /**
         * Configure display options
         */


        configure(options) {
          if (options.showDirections !== undefined) this.showDirections = options.showDirections;
          if (options.showCenter !== undefined) this.showCenter = options.showCenter;
          if (options.directionColor !== undefined) this.directionColor = options.directionColor;
          if (options.centerColor !== undefined) this.centerColor = options.centerColor;
          if (options.speedScale !== undefined) this.speedScale = options.speedScale;
          if (options.arrowSize !== undefined) this.arrowSize = options.arrowSize;
          if (options.centerSize !== undefined) this.centerSize = options.centerSize;
        }

      });

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=c27e004a01234347d61d9c89455e457d0c361696.js.map