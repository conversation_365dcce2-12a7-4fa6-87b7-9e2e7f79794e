/**
 * CrazyStorm 2.0 Integration for Cocos Creator
 * Binary Parser for .bg Files
 * 
 * This parser converts CrazyStorm .bg (binary) files into TypeScript data structures.
 * Binary files are more compact and load faster than XML files.
 */

import { Vec2, Color, warn, error } from 'cc';
import {
    CrazyStormFile,
    ParticleSystem,
    Layer,
    Component as CrazyStormComponentData,
    ComponentData,
    EmitterData,
    ParticleBaseData,
    VariableResource,
    FileResource,
    ComponentType,
    LayerColor,
    BlendType
} from './CrazyStormTypes';

/**
 * Binary data reader utility
 */
class BinaryReader {
    private buffer: ArrayBuffer;
    private view: DataView;
    private position: number = 0;

    constructor(buffer: ArrayBuffer) {
        this.buffer = buffer;
        this.view = new DataView(buffer);
    }

    public readByte(): number {
        const value = this.view.getUint8(this.position);
        this.position += 1;
        return value;
    }

    public readInt16(): number {
        const value = this.view.getInt16(this.position, true); // little-endian
        this.position += 2;
        return value;
    }

    public readInt32(): number {
        const value = this.view.getInt32(this.position, true); // little-endian
        this.position += 4;
        return value;
    }

    public readFloat(): number {
        const value = this.view.getFloat32(this.position, true); // little-endian
        this.position += 4;
        return value;
    }

    public readDouble(): number {
        const value = this.view.getFloat64(this.position, true); // little-endian
        this.position += 8;
        return value;
    }

    public readBoolean(): boolean {
        return this.readByte() !== 0;
    }

    public readString(): string {
        const length = this.readInt32();
        if (length === 0) return '';
        
        const bytes = new Uint8Array(this.buffer, this.position, length);
        this.position += length;
        
        // Convert UTF-8 bytes to string
        return new TextDecoder('utf-8').decode(bytes);
    }

    public readVector2(): Vec2 {
        const x = this.readFloat();
        const y = this.readFloat();
        return new Vec2(x, y);
    }

    public readColor(): Color {
        const r = this.readByte();
        const g = this.readByte();
        const b = this.readByte();
        const a = this.readByte();
        return new Color(r, g, b, a);
    }

    public getPosition(): number {
        return this.position;
    }

    public setPosition(position: number): void {
        this.position = position;
    }

    public getRemainingBytes(): number {
        return this.buffer.byteLength - this.position;
    }

    public isAtEnd(): boolean {
        return this.position >= this.buffer.byteLength;
    }
}

/**
 * Binary parser for CrazyStorm .bg files
 */
export class CrazyStormBinaryParser {
    private reader: BinaryReader | null = null;

    /**
     * Parse a .bg file buffer and return structured data
     */
    public parse(buffer: ArrayBuffer): CrazyStormFile | null {
        try {
            this.reader = new BinaryReader(buffer);
            
            // Read file header
            const header = this.readFileHeader();
            if (!header) {
                error('CrazyStormBinaryParser: Invalid file header');
                return null;
            }

            // Read main file structure
            const crazyStormFile: CrazyStormFile = {
                version: header.version,
                fileResourceIndex: header.fileResourceIndex,
                particleIndex: header.particleIndex,
                particleSystems: this.readParticleSystems(),
                images: this.readFileResources('image'),
                sounds: this.readFileResources('sound'),
                globals: this.readGlobalVariables()
            };

            return crazyStormFile;

        } catch (err) {
            error('CrazyStormBinaryParser: Failed to parse .bg file', err);
            return null;
        }
    }

    /**
     * Read file header
     */
    private readFileHeader(): { version: string; fileResourceIndex: number; particleIndex: number } | null {
        if (!this.reader) return null;

        try {
            // Check magic number (first 4 bytes should be "CSTM" or similar)
            const magic = this.reader.readInt32();
            if (magic !== 0x4D545343) { // "CSTM" in little-endian
                warn('CrazyStormBinaryParser: Invalid magic number, attempting to parse anyway...');
                this.reader.setPosition(0); // Reset and try without magic check
            }

            // Read version string
            const version = this.reader.readString();
            
            // Read indices
            const fileResourceIndex = this.reader.readInt32();
            const particleIndex = this.reader.readInt32();

            return { version, fileResourceIndex, particleIndex };

        } catch (err) {
            error('CrazyStormBinaryParser: Failed to read file header', err);
            return null;
        }
    }

    /**
     * Read particle systems
     */
    private readParticleSystems(): ParticleSystem[] {
        if (!this.reader) return [];

        const count = this.reader.readInt32();
        const systems: ParticleSystem[] = [];

        for (let i = 0; i < count; i++) {
            const system = this.readParticleSystem();
            if (system) {
                systems.push(system);
            }
        }

        return systems;
    }

    /**
     * Read single particle system
     */
    private readParticleSystem(): ParticleSystem | null {
        if (!this.reader) return null;

        try {
            const name = this.reader.readString();
            const customTypeIndex = this.reader.readInt32();
            const layerIndex = this.reader.readInt32();

            // Read custom types (simplified)
            const customTypesCount = this.reader.readInt32();
            const customTypes: any[] = [];
            for (let i = 0; i < customTypesCount; i++) {
                // Skip custom type data for now
                const typeDataSize = this.reader.readInt32();
                this.reader.setPosition(this.reader.getPosition() + typeDataSize);
            }

            // Read layers
            const layers = this.readLayers();

            // Read component index
            const componentIndexCount = this.reader.readInt32();
            const componentIndex: { [key: string]: number } = {};
            for (let i = 0; i < componentIndexCount; i++) {
                const key = this.reader.readString();
                const value = this.reader.readInt32();
                componentIndex[key] = value;
            }

            return {
                name,
                customTypeIndex,
                layerIndex,
                customTypes,
                layers,
                componentIndex
            };

        } catch (err) {
            error('CrazyStormBinaryParser: Failed to read particle system', err);
            return null;
        }
    }

    /**
     * Read layers
     */
    private readLayers(): Layer[] {
        if (!this.reader) return [];

        const count = this.reader.readInt32();
        const layers: Layer[] = [];

        for (let i = 0; i < count; i++) {
            const layer = this.readLayer();
            if (layer) {
                layers.push(layer);
            }
        }

        return layers;
    }

    /**
     * Read single layer
     */
    private readLayer(): Layer | null {
        if (!this.reader) return null;

        try {
            const name = this.reader.readString();
            const visible = this.reader.readBoolean();
            const colorValue = this.reader.readInt32();
            const color = this.intToLayerColor(colorValue);
            const beginFrame = this.reader.readInt32();
            const totalFrame = this.reader.readInt32();

            // Read components
            const components = this.readComponents();

            return {
                name,
                visible,
                color,
                beginFrame,
                totalFrame,
                components
            };

        } catch (err) {
            error('CrazyStormBinaryParser: Failed to read layer', err);
            return null;
        }
    }

    /**
     * Read components
     */
    private readComponents(): CrazyStormComponentData[] {
        if (!this.reader) return [];

        const count = this.reader.readInt32();
        const components: CrazyStormComponentData[] = [];

        for (let i = 0; i < count; i++) {
            const component = this.readComponent();
            if (component) {
                components.push(component);
            }
        }

        return components;
    }

    /**
     * Read single component
     */
    private readComponent(): CrazyStormComponentData | null {
        if (!this.reader) return null;

        try {
            const specificTypeValue = this.reader.readInt32();
            const specificType = this.intToComponentType(specificTypeValue);
            const id = this.reader.readInt32();
            const name = this.reader.readString();

            // Read properties (simplified)
            const propertiesCount = this.reader.readInt32();
            const properties: { [key: string]: any } = {};
            for (let i = 0; i < propertiesCount; i++) {
                const key = this.reader.readString();
                const value = this.reader.readString(); // Simplified - could be various types
                properties[key] = { expression: false, value };
            }

            // Read component data
            const componentData = this.readComponentData();

            // Read variables
            const variables = this.readVariableResources();

            // Read event groups (simplified)
            const eventGroupsCount = this.reader.readInt32();
            const componentEventGroups: any[] = [];
            for (let i = 0; i < eventGroupsCount; i++) {
                // Skip event group data for now
                const eventDataSize = this.reader.readInt32();
                this.reader.setPosition(this.reader.getPosition() + eventDataSize);
            }

            // Read children (simplified)
            const childrenCount = this.reader.readInt32();
            const children: any[] = [];
            for (let i = 0; i < childrenCount; i++) {
                // Skip children data for now
                const childDataSize = this.reader.readInt32();
                this.reader.setPosition(this.reader.getPosition() + childDataSize);
            }

            return {
                specificType,
                id,
                name,
                properties,
                componentData,
                variables,
                componentEventGroups,
                children
            } as CrazyStormComponentData;

        } catch (err) {
            error('CrazyStormBinaryParser: Failed to read component', err);
            return null;
        }
    }

    /**
     * Read component data
     */
    private readComponentData(): ComponentData {
        if (!this.reader) {
            return this.getDefaultComponentData();
        }

        try {
            return {
                layerFrame: this.reader.readInt32(),
                currentFrame: this.reader.readInt32(),
                beginFrame: this.reader.readInt32(),
                totalFrame: this.reader.readInt32(),
                position: this.reader.readVector2(),
                speed: this.reader.readFloat(),
                speedAngle: this.reader.readFloat(),
                acspeed: this.reader.readFloat(),
                acspeedAngle: this.reader.readFloat(),
                visibility: this.reader.readBoolean()
            };
        } catch (err) {
            warn('CrazyStormBinaryParser: Failed to read component data, using defaults', err);
            return this.getDefaultComponentData();
        }
    }

    /**
     * Read variable resources
     */
    private readVariableResources(): VariableResource[] {
        if (!this.reader) return [];

        const count = this.reader.readInt32();
        const variables: VariableResource[] = [];

        for (let i = 0; i < count; i++) {
            const label = this.reader.readString();
            const value = this.reader.readFloat();
            variables.push({ label, value });
        }

        return variables;
    }

    /**
     * Read file resources
     */
    private readFileResources(type: 'image' | 'sound'): FileResource[] {
        if (!this.reader) return [];

        const count = this.reader.readInt32();
        const resources: FileResource[] = [];

        for (let i = 0; i < count; i++) {
            const label = this.reader.readString();
            const id = this.reader.readInt32();
            const relativePath = this.reader.readString();
            resources.push({ label, id, relativePath });
        }

        return resources;
    }

    /**
     * Read global variables
     */
    private readGlobalVariables(): VariableResource[] {
        return this.readVariableResources();
    }

    // Helper methods

    private intToComponentType(value: number): ComponentType {
        // Map integer values to component types
        // These mappings would need to match CrazyStorm's internal values
        switch (value) {
            case 0: return ComponentType.CurveEmitter;
            case 1: return ComponentType.MultiEmitter;
            case 2: return ComponentType.ForceField;
            case 3: return ComponentType.EventField;
            case 4: return ComponentType.Rebounder;
            default: return ComponentType.CurveEmitter;
        }
    }

    private intToLayerColor(value: number): LayerColor {
        // Map integer values to layer colors
        switch (value) {
            case 0: return LayerColor.Blue;
            case 1: return LayerColor.Green;
            case 2: return LayerColor.Red;
            case 3: return LayerColor.Yellow;
            default: return LayerColor.Blue;
        }
    }

    private getDefaultComponentData(): ComponentData {
        return {
            layerFrame: 0,
            currentFrame: 0,
            beginFrame: 0,
            totalFrame: 200,
            position: new Vec2(0, 0),
            speed: 0,
            speedAngle: 0,
            acspeed: 0,
            acspeedAngle: 0,
            visibility: true
        };
    }
}
