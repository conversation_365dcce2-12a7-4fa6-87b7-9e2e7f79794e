/**
 * CrazyStorm 2.0 Integration for Cocos Creator
 * Collision Manager
 * 
 * This system handles collision detection between particles and game objects,
 * optimized for mobile performance with spatial partitioning.
 */

import { Vec2, log, warn } from 'cc';
import { Particle } from '../physics/ParticlePhysics';
import { GameConfig } from './GameIntegration';

/**
 * Collision object interface
 */
export interface CollisionObject {
    id: string;
    position: Vec2;
    radius: number;
    type: 'player' | 'enemy' | 'bullet' | 'powerup';
    active: boolean;
    onCollision?: (other: CollisionObject, position: Vec2) => boolean;
}

/**
 * Spatial grid cell
 */
interface GridCell {
    objects: CollisionObject[];
    particles: Particle[];
}

/**
 * Collision result
 */
export interface CollisionResult {
    object1: CollisionObject;
    object2: CollisionObject | Particle;
    position: Vec2;
    distance: number;
}

/**
 * Collision manager with spatial partitioning
 */
export class CollisionManager {
    private config: GameConfig;
    private objects: Map<string, CollisionObject> = new Map();
    private particles: Particle[] = [];
    
    // Spatial partitioning
    private gridSize: number = 100;
    private grid: Map<string, GridCell> = new Map();
    private bounds: { minX: number; maxX: number; minY: number; maxY: number };
    
    // Player tracking
    private playerPositionGetter: (() => Vec2) | null = null;
    private playerRadius: number = 15;
    private playerCollisionCallback: ((position: Vec2) => void) | null = null;
    private bulletCollisionCallback: ((position: Vec2) => boolean) | null = null;
    
    // Performance tracking
    private collisionCheckCount: number = 0;
    private lastFrameCollisionChecks: number = 0;
    
    // Optimization settings
    private enableSpatialPartitioning: boolean = true;
    private maxCollisionChecksPerFrame: number = 1000;

    constructor(config: GameConfig) {
        this.config = config;
        this.bounds = {
            minX: -2000,
            maxX: 2000,
            minY: -2000,
            maxY: 2000
        };
    }

    /**
     * Update collision detection
     */
    public update(deltaTime: number): void {
        if (!this.config.enableCollision) {
            return;
        }

        this.lastFrameCollisionChecks = 0;

        // Update spatial grid
        if (this.enableSpatialPartitioning) {
            this.updateSpatialGrid();
        }

        // Check collisions
        this.checkCollisions();

        // Update collision count
        this.collisionCheckCount += this.lastFrameCollisionChecks;
    }

    /**
     * Register collision object
     */
    public registerObject(object: CollisionObject): void {
        this.objects.set(object.id, object);
    }

    /**
     * Unregister collision object
     */
    public unregisterObject(id: string): void {
        this.objects.delete(id);
    }

    /**
     * Register player for collision detection
     */
    public registerPlayer(positionGetter: () => Vec2, radius: number): void {
        this.playerPositionGetter = positionGetter;
        this.playerRadius = radius;
    }

    /**
     * Set player collision callback
     */
    public setPlayerCollisionCallback(callback: (position: Vec2) => void): void {
        this.playerCollisionCallback = callback;
    }

    /**
     * Set bullet collision callback
     */
    public setBulletCollisionCallback(callback: (position: Vec2) => boolean): void {
        this.bulletCollisionCallback = callback;
    }

    /**
     * Add particles for collision detection
     */
    public addParticles(particles: Particle[]): void {
        this.particles.push(...particles);
    }

    /**
     * Remove particles from collision detection
     */
    public removeParticles(particles: Particle[]): void {
        particles.forEach(particle => {
            const index = this.particles.indexOf(particle);
            if (index >= 0) {
                this.particles.splice(index, 1);
            }
        });
    }

    /**
     * Clear all particles
     */
    public clearParticles(): void {
        this.particles = [];
    }

    /**
     * Clear all collision objects
     */
    public clearAll(): void {
        this.objects.clear();
        this.particles = [];
        this.grid.clear();
    }

    /**
     * Check collision between point and circle
     */
    public checkPointCircleCollision(point: Vec2, center: Vec2, radius: number): boolean {
        const dx = point.x - center.x;
        const dy = point.y - center.y;
        const distanceSquared = dx * dx + dy * dy;
        return distanceSquared <= radius * radius;
    }

    /**
     * Check collision between two circles
     */
    public checkCircleCollision(pos1: Vec2, radius1: number, pos2: Vec2, radius2: number): boolean {
        const dx = pos1.x - pos2.x;
        const dy = pos1.y - pos2.y;
        const distanceSquared = dx * dx + dy * dy;
        const radiusSum = radius1 + radius2;
        return distanceSquared <= radiusSum * radiusSum;
    }

    /**
     * Get collision check count
     */
    public getCollisionCheckCount(): number {
        return this.collisionCheckCount;
    }

    /**
     * Get last frame collision checks
     */
    public getLastFrameCollisionChecks(): number {
        return this.lastFrameCollisionChecks;
    }

    /**
     * Set spatial partitioning enabled
     */
    public setSpatialPartitioning(enabled: boolean): void {
        this.enableSpatialPartitioning = enabled;
        if (!enabled) {
            this.grid.clear();
        }
    }

    /**
     * Set grid size for spatial partitioning
     */
    public setGridSize(size: number): void {
        this.gridSize = size;
        this.grid.clear();
    }

    /**
     * Set collision bounds
     */
    public setBounds(minX: number, maxX: number, minY: number, maxY: number): void {
        this.bounds = { minX, maxX, minY, maxY };
    }

    /**
     * Get collision statistics
     */
    public getStats(): {
        totalObjects: number;
        totalParticles: number;
        gridCells: number;
        collisionChecks: number;
        lastFrameChecks: number;
    } {
        return {
            totalObjects: this.objects.size,
            totalParticles: this.particles.length,
            gridCells: this.grid.size,
            collisionChecks: this.collisionCheckCount,
            lastFrameChecks: this.lastFrameCollisionChecks
        };
    }

    /**
     * Destroy collision manager
     */
    public destroy(): void {
        this.objects.clear();
        this.particles = [];
        this.grid.clear();
        this.playerPositionGetter = null;
        this.playerCollisionCallback = null;
        this.bulletCollisionCallback = null;
    }

    // Private methods

    /**
     * Update spatial grid
     */
    private updateSpatialGrid(): void {
        this.grid.clear();

        // Add objects to grid
        this.objects.forEach(object => {
            if (object.active) {
                const cellKey = this.getCellKey(object.position);
                const cell = this.getOrCreateCell(cellKey);
                cell.objects.push(object);
            }
        });

        // Add particles to grid
        this.particles.forEach(particle => {
            if (particle.alive && particle.collision) {
                const cellKey = this.getCellKey(particle.position);
                const cell = this.getOrCreateCell(cellKey);
                cell.particles.push(particle);
            }
        });
    }

    /**
     * Check all collisions
     */
    private checkCollisions(): void {
        // Check player-particle collisions
        this.checkPlayerParticleCollisions();

        // Check object-particle collisions
        this.checkObjectParticleCollisions();

        // Check object-object collisions
        this.checkObjectObjectCollisions();
    }

    /**
     * Check player-particle collisions
     */
    private checkPlayerParticleCollisions(): void {
        if (!this.playerPositionGetter || !this.playerCollisionCallback) {
            return;
        }

        const playerPos = this.playerPositionGetter();
        
        if (this.enableSpatialPartitioning) {
            // Check only nearby cells
            const nearbyCells = this.getNearbyCells(playerPos);
            nearbyCells.forEach(cell => {
                cell.particles.forEach(particle => {
                    if (this.checkPlayerParticleCollision(playerPos, particle)) {
                        this.playerCollisionCallback!(particle.position);
                    }
                });
            });
        } else {
            // Check all particles
            this.particles.forEach(particle => {
                if (this.checkPlayerParticleCollision(playerPos, particle)) {
                    this.playerCollisionCallback!(particle.position);
                }
            });
        }
    }

    /**
     * Check single player-particle collision
     */
    private checkPlayerParticleCollision(playerPos: Vec2, particle: Particle): boolean {
        if (!particle.alive || !particle.collision) {
            return false;
        }

        this.lastFrameCollisionChecks++;
        
        return this.checkCircleCollision(
            playerPos,
            this.playerRadius,
            particle.position,
            particle.particleType.radius
        );
    }

    /**
     * Check object-particle collisions
     */
    private checkObjectParticleCollisions(): void {
        if (this.enableSpatialPartitioning) {
            this.grid.forEach(cell => {
                cell.objects.forEach(object => {
                    cell.particles.forEach(particle => {
                        if (this.checkObjectParticleCollision(object, particle)) {
                            this.handleObjectParticleCollision(object, particle);
                        }
                    });
                });
            });
        } else {
            this.objects.forEach(object => {
                if (object.active) {
                    this.particles.forEach(particle => {
                        if (this.checkObjectParticleCollision(object, particle)) {
                            this.handleObjectParticleCollision(object, particle);
                        }
                    });
                }
            });
        }
    }

    /**
     * Check single object-particle collision
     */
    private checkObjectParticleCollision(object: CollisionObject, particle: Particle): boolean {
        if (!object.active || !particle.alive || !particle.collision) {
            return false;
        }

        this.lastFrameCollisionChecks++;
        
        return this.checkCircleCollision(
            object.position,
            object.radius,
            particle.position,
            particle.particleType.radius
        );
    }

    /**
     * Handle object-particle collision
     */
    private handleObjectParticleCollision(object: CollisionObject, particle: Particle): void {
        if (object.onCollision) {
            const shouldDestroy = object.onCollision(object, particle.position);
            if (shouldDestroy) {
                particle.alive = false;
            }
        }
    }

    /**
     * Check object-object collisions
     */
    private checkObjectObjectCollisions(): void {
        // This would implement object-object collision detection
        // For now, we'll skip this as it's less common in bullet hell games
    }

    /**
     * Get cell key for position
     */
    private getCellKey(position: Vec2): string {
        const x = Math.floor(position.x / this.gridSize);
        const y = Math.floor(position.y / this.gridSize);
        return `${x},${y}`;
    }

    /**
     * Get or create grid cell
     */
    private getOrCreateCell(key: string): GridCell {
        let cell = this.grid.get(key);
        if (!cell) {
            cell = { objects: [], particles: [] };
            this.grid.set(key, cell);
        }
        return cell;
    }

    /**
     * Get nearby cells for position
     */
    private getNearbyCells(position: Vec2): GridCell[] {
        const cells: GridCell[] = [];
        const centerX = Math.floor(position.x / this.gridSize);
        const centerY = Math.floor(position.y / this.gridSize);

        // Check 3x3 grid around position
        for (let x = centerX - 1; x <= centerX + 1; x++) {
            for (let y = centerY - 1; y <= centerY + 1; y++) {
                const key = `${x},${y}`;
                const cell = this.grid.get(key);
                if (cell) {
                    cells.push(cell);
                }
            }
        }

        return cells;
    }
}
