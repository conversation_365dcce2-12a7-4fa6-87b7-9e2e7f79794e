import { _decorator, Component, Node, find, Vec3 } from 'cc';
import { GameFactory } from './factroy/GameFactory';
import { Global } from './Global';
import { PersistNode } from './PersistNode';
const { ccclass, property } = _decorator;

@ccclass('MainGame')
export class MainGame extends Component {

    enemyFactory: GameFactory = null;  //敌机工厂

    goodsFactory: GameFactory = null;  //物资工厂

    persistNode: Node = null;   //持久节点

    enemy1Timer: number = 0;            //产生敌机1的定时器

    enemy2Timer: number = 0;            //产生敌机2的定时器

    bloodGoodsTimer: number = 0;            //加血物资的定时器

    lightGoodsTimer: number = 0;            //激光物资的定时器

    missileGoodsTimer: number = 0;            //激光物资的定时器

    enemy1MinProduceTime: number = 0;    //enemy1出现时间的间隔的下限

    enemy1MaxProduceTime: number = 0;   //enemy1出现时间的间隔的上限

    enemy2MinProduceTime: number = 0;    //enemy2出现时间的间隔的下限

    enemy2MaxProduceTime: number = 0;   //enemy2出现时间的间隔的上限

    bloodGoodsMinProduceTime: number = 0;    //加血物资出现时间的间隔的下限

    bloodGoodsMaxProduceTime: number = 0;   //加血物资出现时间的间隔的上限

    lightGoodsMinProduceTime: number = 0;    //激光物资出现时间的间隔的下限

    lightGoodsMaxProduceTime: number = 0;   //激光物资出现时间的间隔的上限

    missileGoodsMinProduceTime: number = 0;    //导弹物资出现时间的间隔的下限

    missileGoodsMaxProduceTime: number = 0;   //导弹物资出现时间的间隔的上限

    enemy1Random: number = 0;   //enemy1出现具体时间（随机的）

    enemy2Random: number = 0;   //enemy2出现具体时间（随机的）

    bloodGoodsRandom: number = 0;       //加血物资出现具体时间（随机的）

    lightGoodsRandom: number = 0;       //加血物资出现具体时间（随机的）

    missileGoodsRandom: number = 0;       //加血物资出现具体时间（随机的）

    onLoad() {
        this.persistNode = find("PersistNode");
        this.enemyFactory = this.persistNode.getComponent(PersistNode).enemyFactory;
        this.goodsFactory = this.persistNode.getComponent(PersistNode).goodsFactory;

        //从persist node面板上获取实际的值
        this.enemy1MinProduceTime = this.persistNode.getComponent(PersistNode).enemy1MinProduceTime;
        this.enemy1MaxProduceTime = this.persistNode.getComponent(PersistNode).enemy1MaxProduceTime;

        //从persist node面板上获取实际的值
        this.enemy2MinProduceTime = this.persistNode.getComponent(PersistNode).enemy2MinProduceTime;
        this.enemy2MaxProduceTime = this.persistNode.getComponent(PersistNode).enemy2MaxProduceTime;

        //从persist node面板上获取实际的值
        this.bloodGoodsMinProduceTime = this.persistNode.getComponent(PersistNode).bloodGoodsMinProduceTime;
        this.bloodGoodsMaxProduceTime = this.persistNode.getComponent(PersistNode).bloodGoodsMaxProduceTime;
        this.lightGoodsMinProduceTime = this.persistNode.getComponent(PersistNode).lightGoodsMinProduceTime;
        this.lightGoodsMaxProduceTime = this.persistNode.getComponent(PersistNode).lightGoodsMaxProduceTime;
        this.missileGoodsMinProduceTime = this.persistNode.getComponent(PersistNode).missileGoodsMinProduceTime;
        this.missileGoodsMaxProduceTime = this.persistNode.getComponent(PersistNode).missileGoodsMaxProduceTime;

        //初始化各种随机数
        this.enemy1Random = this.enemy1MinProduceTime + Math.random() * (this.enemy1MaxProduceTime - this.enemy1MinProduceTime);  
        this.enemy2Random = this.enemy2MinProduceTime + Math.random() * (this.enemy2MaxProduceTime - this.enemy2MinProduceTime);  
        this.bloodGoodsRandom = this.bloodGoodsMinProduceTime + Math.random() * (this.bloodGoodsMaxProduceTime - this.bloodGoodsMinProduceTime);  
        this.lightGoodsRandom = this.lightGoodsMinProduceTime + Math.random() * (this.missileGoodsMaxProduceTime - this.lightGoodsMinProduceTime);  
        this.missileGoodsRandom = this.missileGoodsMinProduceTime + Math.random() * (this.missileGoodsMaxProduceTime - this.missileGoodsMinProduceTime);  
    }

    update(deltaTime: number) {
        //产生敌机1
        this.enemy1Timer += deltaTime;
        if (this.enemy1Timer > this.enemy1Random) {
            this.produceEnemy1();
            this.enemy1Timer = 0;
            this.enemy1Random = this.enemy1MinProduceTime + Math.random() * (this.enemy1MaxProduceTime - this.enemy1MinProduceTime);    
        }

        //产生敌机2
        this.enemy2Timer += deltaTime;
        if (this.enemy2Timer > this.enemy2Random) {
            this.produceEnemy2();
            this.enemy2Timer = 0;
            this.enemy2Random = this.enemy2MinProduceTime + Math.random() * (this.enemy2MaxProduceTime - this.enemy2MinProduceTime);
        }

        //产生血量物资
        this.bloodGoodsTimer += deltaTime;
        if (this.bloodGoodsTimer > this.bloodGoodsRandom) {
            this.produceBloodGoods();
            this.bloodGoodsTimer = 0;
            this.bloodGoodsRandom = this.bloodGoodsMinProduceTime + Math.random() * (this.bloodGoodsMaxProduceTime - this.bloodGoodsMinProduceTime);
        }

        //产生激光物资
        this.lightGoodsTimer += deltaTime;
        if (this.lightGoodsTimer > this.lightGoodsRandom) {
            this.produceLightGoods();
            this.lightGoodsTimer = 0;
            this.lightGoodsRandom = this.lightGoodsMinProduceTime + Math.random() * (this.missileGoodsMaxProduceTime - this.lightGoodsMinProduceTime);
        }

        //产生导弹物资
        this.missileGoodsTimer += deltaTime;
        if (this.missileGoodsTimer > this.missileGoodsRandom) {
            this.produceMissileGoods();
            this.missileGoodsTimer = 0;
            this.missileGoodsRandom = this.missileGoodsMinProduceTime + Math.random() * (this.missileGoodsMaxProduceTime - this.missileGoodsMinProduceTime);  
        }
    }

    //产生导弹物资
    produceMissileGoods() {
        let posBegin: Vec3 = new Vec3();
        let goodsTemp: Node = null;

        goodsTemp = this.goodsFactory.createProduct(Global.MISSILE_GOODS);
        this.node.addChild(goodsTemp);

        posBegin.x = ((Math.random() -0.5) * 2) * Global.WIDTH / 2;

        posBegin.y = Global.HEIGHT / 2;

        goodsTemp.setPosition(posBegin);
    }

    //产生激光物资
    produceLightGoods() {
        let posBegin: Vec3 = new Vec3();
        let goodsTemp: Node = null;

        goodsTemp = this.goodsFactory.createProduct(Global.LIGHT_GOODS);
        this.node.addChild(goodsTemp);

        posBegin.x = ((Math.random() -0.5) * 2) * Global.WIDTH / 2;

        posBegin.y = Global.HEIGHT / 2;

        goodsTemp.setPosition(posBegin);
    }

    //产生血量物资
    produceBloodGoods() {
        let posBegin: Vec3 = new Vec3();
        let goodsTemp: Node = null;

        goodsTemp = this.goodsFactory.createProduct(Global.BLOOD_GOODS);
        this.node.addChild(goodsTemp);

        posBegin.x = ((Math.random() -0.5) * 2) * Global.WIDTH / 2;

        posBegin.y = Global.HEIGHT / 2;

        goodsTemp.setPosition(posBegin);
    }

    /**
     * //产生敌机1
     */
    produceEnemy1() {
        let posBegin: Vec3 = new Vec3();
        let enemyTemp: Node = null;

        enemyTemp = this.enemyFactory.createProduct(Global.ENEMY_1);
        this.node.addChild(enemyTemp);

        posBegin.x = ((Math.random() -0.5) * 2) * Global.WIDTH / 2;

        posBegin.y = Global.HEIGHT / 2;

        enemyTemp.setPosition(posBegin);
    }

    /**
     * //产生敌机2
     */
     produceEnemy2() {
        let posBegin: Vec3 = new Vec3();
        let enemyTemp: Node = null;

        enemyTemp = this.enemyFactory.createProduct(Global.ENEMY_2);
        this.node.addChild(enemyTemp);

        posBegin.x = ((Math.random() -0.5) * 2) * Global.WIDTH / 2;

        posBegin.y = Global.HEIGHT / 2;

        enemyTemp.setPosition(posBegin);
    }

}

