import { _decorator, Component, Node, Vec3, Vec2, Color, Graphics, EventMouse, Camera, geometry } from 'cc';
import { Path, BezierPoint } from '../core/LevelData';
import { PathUtils, GizmoType } from '../core/Types';
import { PathManager } from '../runtime/PathManager';

const { ccclass, property } = _decorator;

/**
 * Handle types for path editing
 */
enum HandleType {
    Point = 'point',
    ControlPoint1 = 'control_point_1',
    ControlPoint2 = 'control_point_2'
}

/**
 * Handle data for path editing
 */
interface PathHandle {
    type: HandleType;
    pointIndex: number;
    position: Vec3;
    isSelected: boolean;
    isDragging: boolean;
}

/**
 * Path editor for visual editing of bezier paths
 */
@ccclass('PathEditor')
export class PathEditor extends Component {
    @property({ type: PathManager })
    public pathManager: PathManager | null = null;
    
    @property({ type: Graphics })
    public graphics: Graphics | null = null;
    
    @property
    public handleSize: number = 10;
    
    @property
    public lineWidth: number = 3;
    
    @property
    public previewSegments: number = 50;
    
    // Current editing state
    private currentPath: Path | null = null;
    private handles: PathHandle[] = [];
    private selectedHandle: PathHandle | null = null;
    private isDragging: boolean = false;
    private dragOffset: Vec2 = Vec2.ZERO;
    
    // Visual settings
    private pathColor: Color = Color.CYAN;
    private handleColor: Color = Color.YELLOW;
    private selectedHandleColor: Color = Color.RED;
    private controlLineColor: Color = Color.GRAY;
    
    // Mouse interaction
    private lastMousePosition: Vec2 = Vec2.ZERO;
    private camera: Camera | null = null;
    
    protected onLoad(): void {
        if (!this.graphics) {
            this.graphics = this.getComponent(Graphics) || this.addComponent(Graphics);
        }
        
        this.camera = Camera.main;
        this.setupMouseEvents();
    }
    
    protected start(): void {
        console.log('PathEditor initialized');
    }
    
    /**
     * Set the path to edit
     */
    public setPath(path: Path): void {
        this.currentPath = path;
        this.generateHandles();
        this.redraw();
    }
    
    /**
     * Clear current path
     */
    public clearPath(): void {
        this.currentPath = null;
        this.handles = [];
        this.selectedHandle = null;
        this.redraw();
    }
    
    /**
     * Add a new point to the current path
     */
    public addPoint(worldPosition: Vec3, insertIndex?: number): boolean {
        if (!this.currentPath || !this.pathManager) return false;
        
        const success = this.pathManager.addPointToPath(this.currentPath.id, worldPosition, insertIndex);
        if (success) {
            this.generateHandles();
            this.redraw();
        }
        return success;
    }
    
    /**
     * Remove a point from the current path
     */
    public removePoint(pointIndex: number): boolean {
        if (!this.currentPath || !this.pathManager) return false;
        
        const success = this.pathManager.removePointFromPath(this.currentPath.id, pointIndex);
        if (success) {
            this.generateHandles();
            this.redraw();
        }
        return success;
    }
    
    /**
     * Setup mouse event handlers
     */
    private setupMouseEvents(): void {
        this.node.on(Node.EventType.MOUSE_DOWN, this.onMouseDown, this);
        this.node.on(Node.EventType.MOUSE_MOVE, this.onMouseMove, this);
        this.node.on(Node.EventType.MOUSE_UP, this.onMouseUp, this);
    }
    
    /**
     * Handle mouse down events
     */
    private onMouseDown(event: EventMouse): void {
        if (!this.currentPath) return;
        
        const worldPos = this.screenToWorld(event.getLocation());
        const handle = this.getHandleAtPosition(worldPos);
        
        if (handle) {
            // Start dragging handle
            this.selectedHandle = handle;
            this.isDragging = true;
            this.dragOffset = new Vec2(
                worldPos.x - handle.position.x,
                worldPos.y - handle.position.y
            );
            
            // Update selection
            this.handles.forEach(h => h.isSelected = false);
            handle.isSelected = true;
            
            this.redraw();
        } else {
            // Clear selection
            this.selectedHandle = null;
            this.handles.forEach(h => h.isSelected = false);
            this.redraw();
        }
        
        this.lastMousePosition = event.getLocation();
    }
    
    /**
     * Handle mouse move events
     */
    private onMouseMove(event: EventMouse): void {
        const worldPos = this.screenToWorld(event.getLocation());
        
        if (this.isDragging && this.selectedHandle) {
            // Update handle position
            const newPosition = new Vec3(
                worldPos.x - this.dragOffset.x,
                worldPos.y - this.dragOffset.y,
                this.selectedHandle.position.z
            );
            
            this.updateHandlePosition(this.selectedHandle, newPosition);
            this.redraw();
        }
        
        this.lastMousePosition = event.getLocation();
    }
    
    /**
     * Handle mouse up events
     */
    private onMouseUp(event: EventMouse): void {
        if (this.isDragging) {
            this.isDragging = false;
            
            if (this.selectedHandle) {
                this.selectedHandle.isDragging = false;
            }
        }
    }
    
    /**
     * Convert screen position to world position
     */
    private screenToWorld(screenPos: Vec2): Vec3 {
        if (!this.camera) return new Vec3(screenPos.x, screenPos.y, 0);
        
        // Convert screen coordinates to world coordinates
        const worldPos = new Vec3();
        this.camera.screenToWorld(new Vec3(screenPos.x, screenPos.y, 0), worldPos);
        return worldPos;
    }
    
    /**
     * Get handle at world position
     */
    private getHandleAtPosition(worldPos: Vec3): PathHandle | null {
        const threshold = this.handleSize;
        
        for (const handle of this.handles) {
            const distance = Vec3.distance(worldPos, handle.position);
            if (distance <= threshold) {
                return handle;
            }
        }
        
        return null;
    }
    
    /**
     * Update handle position and corresponding path data
     */
    private updateHandlePosition(handle: PathHandle, newPosition: Vec3): void {
        if (!this.currentPath || !this.pathManager) return;
        
        handle.position = newPosition.clone();
        
        const point = this.currentPath.points[handle.pointIndex];
        if (!point) return;
        
        switch (handle.type) {
            case HandleType.Point:
                // Update main point position
                this.pathManager.updatePathPoint(this.currentPath.id, handle.pointIndex, {
                    position: newPosition.clone()
                });
                
                // Update corresponding handles
                this.updateHandlePositionsForPoint(handle.pointIndex);
                break;
                
            case HandleType.ControlPoint1:
                // Update control point 1 (relative to main point)
                const controlPoint1 = newPosition.clone().subtract(point.position);
                this.pathManager.updatePathPoint(this.currentPath.id, handle.pointIndex, {
                    controlPoint1: controlPoint1
                });
                break;
                
            case HandleType.ControlPoint2:
                // Update control point 2 (relative to main point)
                const controlPoint2 = newPosition.clone().subtract(point.position);
                this.pathManager.updatePathPoint(this.currentPath.id, handle.pointIndex, {
                    controlPoint2: controlPoint2
                });
                break;
        }
    }
    
    /**
     * Update handle positions for a specific point
     */
    private updateHandlePositionsForPoint(pointIndex: number): void {
        if (!this.currentPath) return;
        
        const point = this.currentPath.points[pointIndex];
        if (!point) return;
        
        // Update handles for this point
        this.handles.forEach(handle => {
            if (handle.pointIndex === pointIndex) {
                switch (handle.type) {
                    case HandleType.Point:
                        handle.position = point.position.clone();
                        break;
                    case HandleType.ControlPoint1:
                        handle.position = point.position.clone().add(point.controlPoint1);
                        break;
                    case HandleType.ControlPoint2:
                        handle.position = point.position.clone().add(point.controlPoint2);
                        break;
                }
            }
        });
    }
    
    /**
     * Generate handles for current path
     */
    private generateHandles(): void {
        this.handles = [];
        
        if (!this.currentPath) return;
        
        this.currentPath.points.forEach((point, index) => {
            // Main point handle
            this.handles.push({
                type: HandleType.Point,
                pointIndex: index,
                position: point.position.clone(),
                isSelected: false,
                isDragging: false
            });
            
            // Control point handles
            if (point.controlPoint1.length() > 0.1) {
                this.handles.push({
                    type: HandleType.ControlPoint1,
                    pointIndex: index,
                    position: point.position.clone().add(point.controlPoint1),
                    isSelected: false,
                    isDragging: false
                });
            }
            
            if (point.controlPoint2.length() > 0.1) {
                this.handles.push({
                    type: HandleType.ControlPoint2,
                    pointIndex: index,
                    position: point.position.clone().add(point.controlPoint2),
                    isSelected: false,
                    isDragging: false
                });
            }
        });
    }
    
    /**
     * Redraw the path and handles
     */
    private redraw(): void {
        if (!this.graphics) return;
        
        this.graphics.clear();
        
        if (this.currentPath) {
            this.drawPath();
            this.drawHandles();
        }
    }
    
    /**
     * Draw the path curves
     */
    private drawPath(): void {
        if (!this.graphics || !this.currentPath || this.currentPath.points.length < 2) return;
        
        this.graphics.strokeColor = this.pathColor;
        this.graphics.lineWidth = this.lineWidth;
        
        // Draw each segment
        for (let i = 0; i < this.currentPath.points.length - 1; i++) {
            this.drawPathSegment(i);
        }
        
        // Draw loop connection if needed
        if (this.currentPath.isLoop && this.currentPath.points.length > 2) {
            this.drawPathSegment(this.currentPath.points.length - 1, 0);
        }
        
        this.graphics.stroke();
    }
    
    /**
     * Draw a single path segment
     */
    private drawPathSegment(startIndex: number, endIndex?: number): void {
        if (!this.graphics || !this.currentPath) return;
        
        const endIdx = endIndex !== undefined ? endIndex : startIndex + 1;
        if (endIdx >= this.currentPath.points.length) return;
        
        const point1 = this.currentPath.points[startIndex];
        const point2 = this.currentPath.points[endIdx];
        
        const p0 = point1.position;
        const p1 = point1.position.clone().add(point1.controlPoint2);
        const p2 = point2.position.clone().add(point2.controlPoint1);
        const p3 = point2.position;
        
        // Draw bezier curve using multiple line segments
        this.graphics.moveTo(p0.x, p0.y);
        
        for (let t = 0; t <= 1; t += 1 / this.previewSegments) {
            const point = PathUtils.calculateBezierPoint(p0, p1, p2, p3, t);
            this.graphics.lineTo(point.x, point.y);
        }
    }
    
    /**
     * Draw handles and control lines
     */
    private drawHandles(): void {
        if (!this.graphics || !this.currentPath) return;
        
        // Draw control lines first
        this.drawControlLines();
        
        // Draw handles
        this.handles.forEach(handle => {
            const color = handle.isSelected ? this.selectedHandleColor : this.handleColor;
            const size = handle.type === HandleType.Point ? this.handleSize : this.handleSize * 0.7;
            
            this.graphics.fillColor = color;
            this.graphics.circle(handle.position.x, handle.position.y, size);
            this.graphics.fill();
        });
    }
    
    /**
     * Draw control lines connecting points to their control points
     */
    private drawControlLines(): void {
        if (!this.graphics || !this.currentPath) return;
        
        this.graphics.strokeColor = this.controlLineColor;
        this.graphics.lineWidth = 1;
        
        this.currentPath.points.forEach(point => {
            // Line to control point 1
            if (point.controlPoint1.length() > 0.1) {
                const cp1 = point.position.clone().add(point.controlPoint1);
                this.graphics.moveTo(point.position.x, point.position.y);
                this.graphics.lineTo(cp1.x, cp1.y);
            }
            
            // Line to control point 2
            if (point.controlPoint2.length() > 0.1) {
                const cp2 = point.position.clone().add(point.controlPoint2);
                this.graphics.moveTo(point.position.x, point.position.y);
                this.graphics.lineTo(cp2.x, cp2.y);
            }
        });
        
        this.graphics.stroke();
    }
    
    /**
     * Get current path
     */
    public getCurrentPath(): Path | null {
        return this.currentPath;
    }
    
    /**
     * Set visual properties
     */
    public setVisualProperties(pathColor?: Color, handleColor?: Color, selectedColor?: Color): void {
        if (pathColor) this.pathColor = pathColor;
        if (handleColor) this.handleColor = handleColor;
        if (selectedColor) this.selectedHandleColor = selectedColor;
        this.redraw();
    }
}
