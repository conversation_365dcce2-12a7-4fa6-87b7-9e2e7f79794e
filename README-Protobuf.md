# Protobuf Integration for Cocos Creator

This project demonstrates how to integrate Protocol Buffers (protobuf) with Cocos Creator for efficient network communication.

## Features

- **NetMgr**: A network manager that handles WebSocket connections with protobuf message serialization
- **Automatic Reconnection**: Built-in reconnection logic with configurable retry attempts
- **Heartbeat System**: Automatic heartbeat to maintain connection health
- **Message Queue**: Messages are queued when disconnected and sent when reconnected
- **Type Safety**: Full TypeScript support with protobuf message types

## Project Structure

```
assets/Scripts/
├── Network/
│   └── NetMgr.ts           # Main network manager
├── Proto/
│   └── game.proto          # Protobuf schema definition
├── Test/
│   └── NetworkTest.ts      # Example usage component
└── GameInstance.ts         # Updated to include NetMgr

server-example/
├── server.js               # Example Node.js WebSocket server
├── package.json            # Server dependencies
└── README.md               # This file
```

## Setup Instructions

### 1. Client Setup (Cocos Creator)

The protobuf dependencies are already installed:
- `protobufjs`: Main protobuf library
- `@types/protobufjs`: TypeScript definitions (note: protobufjs provides its own types)

### 2. Server Setup (Optional - for testing)

If you want to test with the example server:

```bash
cd server-example
npm install
npm start
```

The server will start on `ws://localhost:8080/game`

## Usage

### Basic Network Manager Usage

The NetMgr is automatically initialized in GameInstance and provides the following features:

```typescript
// Get NetMgr instance
const gameInstance = GameInstance.GetInstance();
const netMgr = gameInstance['_netMgr']; // Access through GameInstance

// Set up event callbacks
netMgr.setEvents({
    onConnected: () => console.log("Connected!"),
    onDisconnected: () => console.log("Disconnected!"),
    onError: (error) => console.log("Error:", error),
    onMessage: (msgId, data) => console.log("Message:", msgId, data)
});

// Connect to server
netMgr.connect("ws://localhost:8080/game");

// Send login request
netMgr.login("username", "password", "device123");

// Send chat message
netMgr.sendChatMessage("Hello World!", 0);

// Check connection status
if (netMgr.isConnected()) {
    // Send messages
}
```

### Message Types

The system supports the following message types defined in `game.proto`:

- **LoginRequest/LoginResponse**: User authentication
- **HeartbeatRequest/HeartbeatResponse**: Connection health monitoring
- **ChatMessage**: Chat system
- **GameAction**: Generic game actions
- **NetworkPacket**: Message wrapper with ID and timestamp

### Adding New Message Types

1. **Update the .proto file**:
```protobuf
message NewGameMessage {
    int32 action_id = 1;
    string data = 2;
}
```

2. **Add to NetMgr schema** (in `loadProtobufSchema()` method):
```typescript
const newGameMessage = gamePackage.add(new protobuf.Type("NewGameMessage"))
    .add(new protobuf.Field("action_id", 1, "int32"))
    .add(new protobuf.Field("data", 2, "string"));

this._messageTypes.set("NewGameMessage", newGameMessage);
```

3. **Add message ID**:
```typescript
export enum MessageId {
    // ... existing messages
    NEW_GAME_MESSAGE = 5001,
}
```

4. **Add handler in NetMgr**:
```typescript
case MessageId.NEW_GAME_MESSAGE:
    const NewGameMessage = this._messageTypes.get("NewGameMessage");
    if (NewGameMessage) {
        decodedData = NewGameMessage.decode(messageData);
    }
    break;
```

5. **Add public API method**:
```typescript
public sendNewGameMessage(actionId: number, data: string) {
    return this.sendMessage(MessageId.NEW_GAME_MESSAGE, "NewGameMessage", {
        action_id: actionId,
        data: data
    });
}
```

## Testing

Use the `NetworkTest` component to test the network functionality:

1. Add the `NetworkTest` component to a node in your scene
2. Assign Button components to the properties
3. Run the scene and use the buttons to test:
   - Connect to server
   - Send login request
   - Send chat message
   - Disconnect

## Network Architecture

### Message Flow

1. **Outgoing Messages**:
   ```
   Game Logic → NetMgr.sendMessage() → Protobuf Encode → NetworkPacket Wrapper → WebSocket
   ```

2. **Incoming Messages**:
   ```
   WebSocket → NetworkPacket Decode → Message Type Decode → Event Callback → Game Logic
   ```

### Connection Management

- **Automatic Reconnection**: Attempts to reconnect up to 5 times with 3-second delays
- **Heartbeat System**: Sends heartbeat every 30 seconds, disconnects if no response for 60 seconds
- **Message Queuing**: Messages sent while disconnected are queued and sent upon reconnection

## Performance Considerations

- **Binary Efficiency**: Protobuf provides compact binary serialization
- **Type Safety**: Full TypeScript support prevents runtime errors
- **Memory Management**: Object pooling can be added for high-frequency messages
- **Compression**: WebSocket compression can be enabled for additional bandwidth savings

## Security Notes

- Always validate messages on the server side
- Implement proper authentication and authorization
- Use secure WebSocket (WSS) in production
- Consider message rate limiting to prevent spam

## Troubleshooting

### Common Issues

1. **"NetworkPacket type not found"**: Ensure protobuf schema is loaded before connecting
2. **Connection fails**: Check server URL and ensure server is running
3. **Messages not received**: Verify message IDs match between client and server
4. **TypeScript errors**: Ensure protobufjs is properly installed

### Debug Tips

- Enable console logging to see message flow
- Use browser developer tools to inspect WebSocket traffic
- Check server logs for message handling errors
- Verify protobuf message structure matches between client and server

## Next Steps

- Implement message compression for large payloads
- Add encryption for sensitive data
- Implement proper error handling and retry logic
- Add metrics and monitoring for network performance
- Consider using protobuf reflection for dynamic message handling
