23:56:17.147 debug: 2025/7/26 23:56:17
23:56:17.148 debug: Project: D:\Workspace\Projects\Moolego\M2Game\Client
23:56:17.148 debug: Targets: editor,preview
23:56:17.150 debug: Packer deriver version file lost or format incorrect: Error: ENOENT: no such file or directory, open 'D:\Workspace\Projects\Moolego\M2Game\Client\temp\programming\packer-driver\VERSION'
23:56:17.150 debug: Clearing out the targets...
23:56:17.152 debug: Engine path: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine
23:56:17.158 debug: Initializing target [Editor]
23:56:17.158 debug: Loading cache
23:56:17.160 debug: No record files found: Error: ENOENT: no such file or directory, open 'D:\Workspace\Projects\Moolego\M2Game\Client\temp\programming\packer-driver\targets\editor\main-record.json'
23:56:17.160 debug: Loading cache costs 1.4524000000001251ms.
23:56:17.160 debug: Engine features shipped in editor: base,gfx-webgl,gfx-webgl2,gfx-empty,gfx-webgpu,3d,animation,skeletal-animation,2d,rich-text,mask,graphics,ui-skew,ui,affine-transform,particle,particle-2d,physics-framework,physics-cannon,physics-physx,physics-ammo,physics-builtin,physics-2d-framework,physics-2d-box2d-jsb,physics-2d-box2d,physics-2d-builtin,physics-2d-box2d-wasm,intersection-2d,primitive,profiler,occlusion-query,geometry-renderer,debug-renderer,audio,video,xr,light-probe,terrain,webview,tween,tiled-map,vendor-google,spine-3.8,spine-4.2,dragon-bones,marionette,procedural-animation,custom-pipeline,custom-pipeline-builtin-scripts,custom-pipeline-post-process,legacy-pipeline,websocket,websocket-server,meshopt
23:56:17.160 debug: Our import map(foo:/bar): {
  "imports": {
    "cc": "cce:/internal/x/cc"
  }
}
23:56:17.160 debug: Initializing target [Preview]
23:56:17.161 debug: Loading cache
23:56:17.161 debug: No record files found: Error: ENOENT: no such file or directory, open 'D:\Workspace\Projects\Moolego\M2Game\Client\temp\programming\packer-driver\targets\preview\main-record.json'
23:56:17.161 debug: Loading cache costs 0.25859999999920547ms.
23:56:17.161 debug: Our import map(foo:/bar): {
  "imports": {
    "cc": "cce:/internal/x/cc"
  }
}
23:56:17.189 debug: Sync engine features: 2d,affine-transform,animation,audio,base,custom-pipeline,dragon-bones,gfx-webgl,gfx-webgl2,graphics,intersection-2d,marionette,mask,particle-2d,physics-2d-builtin,profiler,rich-text,skeletal-animation,spine-3.8,tiled-map,tween,ui,video,websocket,webview,custom-pipeline
23:56:17.191 debug: Reset databases. Enumerated domains: [
  {
    "root": "db://internal/",
    "physical": "C:\\ProgramData\\cocos\\editors\\Creator\\3.8.6\\resources\\resources\\3d\\engine\\editor\\assets"
  },
  {
    "root": "db://assets/",
    "physical": "D:\\Workspace\\Projects\\Moolego\\M2Game\\Client\\assets"
  }
]
23:56:17.191 debug: Our import map(foo:/bar): {
  "imports": {
    "cc": "cce:/internal/x/cc",
    "db://internal/": "file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/",
    "db://assets/": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/"
  }
}
23:56:17.192 debug: Our import map(foo:/bar): {
  "imports": {
    "cc": "cce:/internal/x/cc",
    "db://internal/": "file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/",
    "db://assets/": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/"
  }
}
23:56:17.192 debug: Pulling asset-db.
23:56:17.200 debug: Fetch asset-db cost: 8.153600000001461ms.
23:56:17.201 debug: Build iteration starts.
Number of accumulated asset changes: 45
Feature changed: false
23:56:17.202 debug: Target(editor) build started.
23:56:17.204 debug: QuickPack.build, lock file failed!, workspace: D:\Workspace\Projects\Moolego\M2Game\Client\temp\programming\packer-driver\targets\editor, err: Error: ENOENT: no such file or directory, lstat 'D:\Workspace\Projects\Moolego\M2Game\Client\temp\programming\packer-driver\targets\editor'
23:56:17.204 debug: Inspect cce:/internal/x/cc
23:56:17.234 debug: transform url: 'cce:/internal/x/cc' costs: 30.60 ms
23:56:17.236 debug: Resolve cce:/internal/x/cc-fu/base from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/base.
23:56:17.236 debug: Resolve cce:/internal/x/cc-fu/gfx-webgl from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/gfx-webgl.
23:56:17.236 debug: Resolve cce:/internal/x/cc-fu/gfx-webgl2 from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/gfx-webgl2.
23:56:17.236 debug: Resolve cce:/internal/x/cc-fu/gfx-empty from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/gfx-empty.
23:56:17.236 debug: Resolve cce:/internal/x/cc-fu/gfx-webgpu from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/gfx-webgpu.
23:56:17.236 debug: Resolve cce:/internal/x/cc-fu/3d from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/3d.
23:56:17.236 debug: Resolve cce:/internal/x/cc-fu/animation from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/animation.
23:56:17.236 debug: Resolve cce:/internal/x/cc-fu/skeletal-animation from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/skeletal-animation.
23:56:17.236 debug: Resolve cce:/internal/x/cc-fu/2d from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/2d.
23:56:17.236 debug: Resolve cce:/internal/x/cc-fu/sorting from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/sorting.
23:56:17.236 debug: Resolve cce:/internal/x/cc-fu/rich-text from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/rich-text.
23:56:17.236 debug: Resolve cce:/internal/x/cc-fu/mask from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/mask.
23:56:17.236 debug: Resolve cce:/internal/x/cc-fu/graphics from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/graphics.
23:56:17.236 debug: Resolve cce:/internal/x/cc-fu/ui-skew from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/ui-skew.
23:56:17.236 debug: Resolve cce:/internal/x/cc-fu/ui from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/ui.
23:56:17.236 debug: Resolve cce:/internal/x/cc-fu/affine-transform from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/affine-transform.
23:56:17.236 debug: Resolve cce:/internal/x/cc-fu/particle from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/particle.
23:56:17.236 debug: Resolve cce:/internal/x/cc-fu/particle-2d from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/particle-2d.
23:56:17.236 debug: Resolve cce:/internal/x/cc-fu/physics-framework from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/physics-framework.
23:56:17.236 debug: Resolve cce:/internal/x/cc-fu/physics-cannon from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/physics-cannon.
23:56:17.236 debug: Resolve cce:/internal/x/cc-fu/physics-physx from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/physics-physx.
23:56:17.236 debug: Resolve cce:/internal/x/cc-fu/physics-ammo from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/physics-ammo.
23:56:17.236 debug: Resolve cce:/internal/x/cc-fu/physics-builtin from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/physics-builtin.
23:56:17.236 debug: Resolve cce:/internal/x/cc-fu/physics-2d-framework from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/physics-2d-framework.
23:56:17.236 debug: Resolve cce:/internal/x/cc-fu/physics-2d-box2d-jsb from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/physics-2d-box2d-jsb.
23:56:17.236 debug: Resolve cce:/internal/x/cc-fu/physics-2d-box2d from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/physics-2d-box2d.
23:56:17.236 debug: Resolve cce:/internal/x/cc-fu/physics-2d-builtin from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/physics-2d-builtin.
23:56:17.236 debug: Resolve cce:/internal/x/cc-fu/physics-2d-box2d-wasm from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/physics-2d-box2d-wasm.
23:56:17.236 debug: Resolve cce:/internal/x/cc-fu/intersection-2d from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/intersection-2d.
23:56:17.236 debug: Resolve cce:/internal/x/cc-fu/primitive from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/primitive.
23:56:17.236 debug: Resolve cce:/internal/x/cc-fu/profiler from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/profiler.
23:56:17.236 debug: Resolve cce:/internal/x/cc-fu/geometry-renderer from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/geometry-renderer.
23:56:17.236 debug: Resolve cce:/internal/x/cc-fu/audio from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/audio.
23:56:17.236 debug: Resolve cce:/internal/x/cc-fu/video from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/video.
23:56:17.236 debug: Resolve cce:/internal/x/cc-fu/xr from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/xr.
23:56:17.236 debug: Resolve cce:/internal/x/cc-fu/light-probe from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/light-probe.
23:56:17.236 debug: Resolve cce:/internal/x/cc-fu/terrain from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/terrain.
23:56:17.236 debug: Resolve cce:/internal/x/cc-fu/webview from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/webview.
23:56:17.236 debug: Resolve cce:/internal/x/cc-fu/tween from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/tween.
23:56:17.236 debug: Resolve cce:/internal/x/cc-fu/tiled-map from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/tiled-map.
23:56:17.236 debug: Resolve cce:/internal/x/cc-fu/vendor-google from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/vendor-google.
23:56:17.236 debug: Resolve cce:/internal/x/cc-fu/spine from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/spine.
23:56:17.236 debug: Resolve cce:/internal/x/cc-fu/dragon-bones from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/dragon-bones.
23:56:17.236 debug: Resolve cce:/internal/x/cc-fu/custom-pipeline from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/custom-pipeline.
23:56:17.236 debug: Resolve cce:/internal/x/cc-fu/custom-pipeline-post-process from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/custom-pipeline-post-process.
23:56:17.236 debug: Resolve cce:/internal/x/cc-fu/legacy-pipeline from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/legacy-pipeline.
23:56:17.237 debug: Inspect cce:/internal/x/prerequisite-imports
23:56:17.255 debug: transform url: 'cce:/internal/x/prerequisite-imports' costs: 18.10 ms
23:56:17.256 debug: Resolve file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts from cce:/internal/x/prerequisite-imports as file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts.
23:56:17.256 debug: Inspect file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts
23:56:17.339 debug: transform url: 'file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts' costs: 82.20 ms
23:56:17.340 debug: Resolve file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts from cce:/internal/x/prerequisite-imports as file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts.
23:56:17.340 debug: Inspect file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts
23:56:17.363 debug: transform url: 'file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts' costs: 22.70 ms
23:56:17.364 debug: Resolve file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts from cce:/internal/x/prerequisite-imports as file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts.
23:56:17.364 debug: Inspect file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts
23:56:17.434 debug: transform url: 'file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts' costs: 70.00 ms
23:56:17.436 debug: Resolve file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts from cce:/internal/x/prerequisite-imports as file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts.
23:56:17.436 debug: Inspect file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts
23:56:17.464 debug: transform url: 'file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts' costs: 28.40 ms
23:56:17.465 debug: Resolve file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts from cce:/internal/x/prerequisite-imports as file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts.
23:56:17.465 debug: Inspect file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts
23:56:17.625 debug: transform url: 'file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts' costs: 159.40 ms
23:56:17.627 debug: Resolve file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts from cce:/internal/x/prerequisite-imports as file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts.
23:56:17.627 debug: Inspect file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts
23:56:17.702 debug: transform url: 'file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts' costs: 75.00 ms
23:56:17.704 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/gizmos/EmitterGizmo.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/gizmos/EmitterGizmo.ts.
23:56:17.704 debug: Inspect file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/gizmos/EmitterGizmo.ts
23:56:17.731 debug: transform url: 'file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/gizmos/EmitterGizmo.ts' costs: 27.20 ms
23:56:17.732 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/AutoGen/Luban/schema.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/AutoGen/Luban/schema.ts.
23:56:17.732 debug: Inspect file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/AutoGen/Luban/schema.ts
23:56:17.761 debug: transform url: 'file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/AutoGen/Luban/schema.ts' costs: 29.30 ms
23:56:17.762 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/AutoGen/PB/cs_proto.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/AutoGen/PB/cs_proto.ts.
23:56:17.762 debug: Inspect file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/AutoGen/PB/cs_proto.ts
23:56:17.946 debug: transform url: 'file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/AutoGen/PB/cs_proto.ts' costs: 183.00 ms
23:56:17.947 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Anim.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Anim.ts.
23:56:17.947 debug: Inspect file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Anim.ts
23:56:17.968 debug: transform url: 'file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Anim.ts' costs: 20.00 ms
23:56:17.968 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Background.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Background.ts.
23:56:17.969 debug: Inspect file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Background.ts
23:56:17.986 debug: transform url: 'file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Background.ts' costs: 17.50 ms
23:56:17.987 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Enemy.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Enemy.ts.
23:56:17.987 debug: Inspect file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Enemy.ts
23:56:18.024 debug: transform url: 'file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Enemy.ts' costs: 37.60 ms
23:56:18.026 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/EnemyBullet.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/EnemyBullet.ts.
23:56:18.026 debug: Inspect file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/EnemyBullet.ts
23:56:18.056 debug: transform url: 'file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/EnemyBullet.ts' costs: 30.00 ms
23:56:18.057 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/GameOver.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/GameOver.ts.
23:56:18.057 debug: Inspect file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/GameOver.ts
23:56:18.070 debug: transform url: 'file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/GameOver.ts' costs: 13.00 ms
23:56:18.070 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Global.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Global.ts.
23:56:18.070 debug: Inspect file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Global.ts
23:56:18.083 debug: transform url: 'file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Global.ts' costs: 12.80 ms
23:56:18.084 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Goods.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Goods.ts.
23:56:18.084 debug: Inspect file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Goods.ts
23:56:18.132 debug: transform url: 'file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Goods.ts' costs: 48.20 ms
23:56:18.134 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/MainGame.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/MainGame.ts.
23:56:18.134 debug: Inspect file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/MainGame.ts
23:56:18.178 debug: transform url: 'file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/MainGame.ts' costs: 43.70 ms
23:56:18.179 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Menu.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Menu.ts.
23:56:18.180 debug: Inspect file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Menu.ts
23:56:18.192 debug: transform url: 'file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Menu.ts' costs: 12.50 ms
23:56:18.193 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/PersistNode.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/PersistNode.ts.
23:56:18.193 debug: Inspect file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/PersistNode.ts
23:56:18.236 debug: transform url: 'file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/PersistNode.ts' costs: 42.60 ms
23:56:18.237 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Player.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Player.ts.
23:56:18.237 debug: Inspect file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Player.ts
23:56:18.288 debug: transform url: 'file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Player.ts' costs: 51.00 ms
23:56:18.290 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/PlayerBullet.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/PlayerBullet.ts.
23:56:18.290 debug: Inspect file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/PlayerBullet.ts
23:56:18.329 debug: transform url: 'file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/PlayerBullet.ts' costs: 38.60 ms
23:56:18.330 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/AnimFactory.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/AnimFactory.ts.
23:56:18.330 debug: Inspect file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/AnimFactory.ts
23:56:18.347 debug: transform url: 'file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/AnimFactory.ts' costs: 17.00 ms
23:56:18.348 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/EnemyBulletFactory.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/EnemyBulletFactory.ts.
23:56:18.348 debug: Inspect file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/EnemyBulletFactory.ts
23:56:18.366 debug: transform url: 'file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/EnemyBulletFactory.ts' costs: 17.90 ms
23:56:18.367 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/EnemyFactory.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/EnemyFactory.ts.
23:56:18.367 debug: Inspect file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/EnemyFactory.ts
23:56:18.385 debug: transform url: 'file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/EnemyFactory.ts' costs: 17.00 ms
23:56:18.386 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/GameFactory.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/GameFactory.ts.
23:56:18.386 debug: Inspect file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/GameFactory.ts
23:56:18.397 debug: transform url: 'file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/GameFactory.ts' costs: 10.70 ms
23:56:18.397 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/GoodsFactory.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/GoodsFactory.ts.
23:56:18.397 debug: Inspect file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/GoodsFactory.ts
23:56:18.415 debug: transform url: 'file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/GoodsFactory.ts' costs: 17.30 ms
23:56:18.416 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/PlayerBulletFactory.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/PlayerBulletFactory.ts.
23:56:18.416 debug: Inspect file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/PlayerBulletFactory.ts
23:56:18.430 debug: transform url: 'file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/PlayerBulletFactory.ts' costs: 14.50 ms
23:56:18.431 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/Bootstrap.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/Bootstrap.ts.
23:56:18.431 debug: Inspect file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/Bootstrap.ts
23:56:18.486 debug: transform url: 'file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/Bootstrap.ts' costs: 55.60 ms
23:56:18.488 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/WorldInitializeData.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/WorldInitializeData.ts.
23:56:18.488 debug: Inspect file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/WorldInitializeData.ts
23:56:18.521 debug: transform url: 'file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/WorldInitializeData.ts' costs: 32.70 ms
23:56:18.522 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/base/System.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/base/System.ts.
23:56:18.522 debug: Inspect file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/base/System.ts
23:56:18.536 debug: transform url: 'file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/base/System.ts' costs: 14.00 ms
23:56:18.537 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/base/SystemContainer.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/base/SystemContainer.ts.
23:56:18.537 debug: Inspect file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/base/SystemContainer.ts
23:56:18.561 debug: transform url: 'file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/base/SystemContainer.ts' costs: 23.90 ms
23:56:18.563 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/base/TypeID.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/base/TypeID.ts.
23:56:18.563 debug: Inspect file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/base/TypeID.ts
23:56:18.580 debug: transform url: 'file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/base/TypeID.ts' costs: 17.10 ms
23:56:18.581 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/base/World.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/base/World.ts.
23:56:18.581 debug: Inspect file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/base/World.ts
23:56:18.602 debug: transform url: 'file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/base/World.ts' costs: 20.80 ms
23:56:18.603 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/bullet/Bullet.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/bullet/Bullet.ts.
23:56:18.603 debug: Inspect file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/bullet/Bullet.ts
23:56:18.627 debug: transform url: 'file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/bullet/Bullet.ts' costs: 23.80 ms
23:56:18.630 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/bullet/BulletSystem.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/bullet/BulletSystem.ts.
23:56:18.630 debug: Inspect file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/bullet/BulletSystem.ts
23:56:18.657 debug: transform url: 'file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/bullet/BulletSystem.ts' costs: 27.10 ms
23:56:18.659 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/bullet/Emitter.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/bullet/Emitter.ts.
23:56:18.659 debug: Inspect file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/bullet/Emitter.ts
23:56:18.674 debug: transform url: 'file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/bullet/Emitter.ts' costs: 15.40 ms
23:56:18.675 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/index.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/index.ts.
23:56:18.675 debug: Inspect file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/index.ts
23:56:18.683 debug: transform url: 'file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/index.ts' costs: 8.10 ms
23:56:18.684 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/level/LevelSystem.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/level/LevelSystem.ts.
23:56:18.684 debug: Inspect file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/level/LevelSystem.ts
23:56:18.711 debug: transform url: 'file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/level/LevelSystem.ts' costs: 27.20 ms
23:56:18.712 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/player/PlayerSystem.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/player/PlayerSystem.ts.
23:56:18.712 debug: Inspect file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/player/PlayerSystem.ts
23:56:18.737 debug: transform url: 'file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/player/PlayerSystem.ts' costs: 25.40 ms
23:56:18.738 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameInstance.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameInstance.ts.
23:56:18.738 debug: Inspect file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameInstance.ts
23:56:18.777 debug: transform url: 'file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameInstance.ts' costs: 38.90 ms
23:56:18.779 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/IMgr.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/IMgr.ts.
23:56:18.779 debug: Inspect file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/IMgr.ts
23:56:18.792 debug: transform url: 'file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/IMgr.ts' costs: 12.60 ms
23:56:18.793 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Luban/LubanMgr.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Luban/LubanMgr.ts.
23:56:18.793 debug: Inspect file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Luban/LubanMgr.ts
23:56:18.810 debug: transform url: 'file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Luban/LubanMgr.ts' costs: 16.60 ms
23:56:18.811 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/MainUI.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/MainUI.ts.
23:56:18.811 debug: Inspect file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/MainUI.ts
23:56:18.826 debug: transform url: 'file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/MainUI.ts' costs: 14.80 ms
23:56:18.827 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Network/NetMgr.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Network/NetMgr.ts.
23:56:18.827 debug: Inspect file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Network/NetMgr.ts
23:56:18.838 debug: transform url: 'file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Network/NetMgr.ts' costs: 11.70 ms
23:56:18.840 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ResUpdate/ResUpdate.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ResUpdate/ResUpdate.ts.
23:56:18.840 debug: Inspect file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ResUpdate/ResUpdate.ts
23:56:18.867 debug: transform url: 'file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ResUpdate/ResUpdate.ts' costs: 26.60 ms
23:56:18.868 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as cce:/internal/code-quality/cr.mjs.
23:56:18.868 debug: Inspect cce:/internal/code-quality/cr.mjs
23:56:18.873 debug: transform url: 'cce:/internal/code-quality/cr.mjs' costs: 5.00 ms
23:56:18.874 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as cce:/internal/x/cc.
23:56:18.874 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as cce:/internal/x/cc.
23:56:18.874 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as cce:/internal/x/cc.
23:56:18.874 debug: Resolve cc/env from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as external dependency cc/env.
23:56:18.874 debug: Resolve ./builtin-pipeline-settings from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts.
23:56:18.875 debug: Resolve ./builtin-pipeline-pass from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts.
23:56:18.875 debug: Resolve ./builtin-pipeline from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts.
23:56:18.875 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts as cce:/internal/code-quality/cr.mjs.
23:56:18.875 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts as cce:/internal/x/cc.
23:56:18.875 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts as cce:/internal/x/cc.
23:56:18.875 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts as cce:/internal/x/cc.
23:56:18.875 debug: Resolve cc/env from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts as external dependency cc/env.
23:56:18.875 debug: Resolve ./builtin-pipeline-types from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts as file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts.
23:56:18.875 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts as cce:/internal/x/cc.
23:56:18.875 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts as cce:/internal/x/cc.
23:56:18.875 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts as cce:/internal/x/cc.
23:56:18.875 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts as cce:/internal/code-quality/cr.mjs.
23:56:18.875 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts as cce:/internal/x/cc.
23:56:18.875 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts as cce:/internal/x/cc.
23:56:18.875 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts as cce:/internal/x/cc.
23:56:18.876 debug: Resolve ./builtin-pipeline-settings from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts as file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts.
23:56:18.876 debug: Resolve cc/env from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts as external dependency cc/env.
23:56:18.876 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts as cce:/internal/code-quality/cr.mjs.
23:56:18.876 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts as cce:/internal/x/cc.
23:56:18.876 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts as cce:/internal/x/cc.
23:56:18.876 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts as cce:/internal/x/cc.
23:56:18.876 debug: Resolve cc/env from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts as external dependency cc/env.
23:56:18.876 debug: Resolve ./builtin-pipeline-types from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts as file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts.
23:56:18.876 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts as cce:/internal/x/cc.
23:56:18.876 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts as cce:/internal/x/cc.
23:56:18.876 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts as cce:/internal/x/cc.
23:56:18.876 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/gizmos/EmitterGizmo.ts as cce:/internal/x/cc.
23:56:18.876 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/gizmos/EmitterGizmo.ts as cce:/internal/x/cc.
23:56:18.876 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/gizmos/EmitterGizmo.ts as cce:/internal/x/cc.
23:56:18.876 debug: Resolve cc/env from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/gizmos/EmitterGizmo.ts as external dependency cc/env.
23:56:18.876 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/AutoGen/Luban/schema.ts as cce:/internal/x/cc.
23:56:18.876 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/AutoGen/PB/cs_proto.ts as cce:/internal/x/cc.
23:56:18.876 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Anim.ts as cce:/internal/code-quality/cr.mjs.
23:56:18.876 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Anim.ts as cce:/internal/x/cc.
23:56:18.876 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Anim.ts as cce:/internal/x/cc.
23:56:18.876 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Anim.ts as cce:/internal/x/cc.
23:56:18.877 debug: Resolve ./PersistNode from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Anim.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/PersistNode.ts.
23:56:18.877 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/PersistNode.ts as cce:/internal/code-quality/cr.mjs.
23:56:18.877 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/PersistNode.ts as cce:/internal/x/cc.
23:56:18.877 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/PersistNode.ts as cce:/internal/x/cc.
23:56:18.877 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/PersistNode.ts as cce:/internal/x/cc.
23:56:18.877 debug: Resolve ./factroy/AnimFactory from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/PersistNode.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/AnimFactory.ts.
23:56:18.878 debug: Resolve ./factroy/EnemyBulletFactory from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/PersistNode.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/EnemyBulletFactory.ts.
23:56:18.878 debug: Resolve ./factroy/EnemyFactory from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/PersistNode.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/EnemyFactory.ts.
23:56:18.878 debug: Resolve ./factroy/GoodsFactory from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/PersistNode.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/GoodsFactory.ts.
23:56:18.878 debug: Resolve ./factroy/PlayerBulletFactory from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/PersistNode.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/PlayerBulletFactory.ts.
23:56:18.878 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/AnimFactory.ts as cce:/internal/code-quality/cr.mjs.
23:56:18.878 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/AnimFactory.ts as cce:/internal/x/cc.
23:56:18.878 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/AnimFactory.ts as cce:/internal/x/cc.
23:56:18.878 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/AnimFactory.ts as cce:/internal/x/cc.
23:56:18.878 debug: Resolve ../PersistNode from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/AnimFactory.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/PersistNode.ts.
23:56:18.878 debug: Resolve ./GameFactory from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/AnimFactory.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/GameFactory.ts.
23:56:18.879 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/GameFactory.ts as cce:/internal/x/cc.
23:56:18.879 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/GameFactory.ts as cce:/internal/x/cc.
23:56:18.879 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/GameFactory.ts as cce:/internal/x/cc.
23:56:18.879 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/EnemyBulletFactory.ts as cce:/internal/code-quality/cr.mjs.
23:56:18.879 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/EnemyBulletFactory.ts as cce:/internal/x/cc.
23:56:18.879 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/EnemyBulletFactory.ts as cce:/internal/x/cc.
23:56:18.879 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/EnemyBulletFactory.ts as cce:/internal/x/cc.
23:56:18.879 debug: Resolve ../EnemyBullet from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/EnemyBulletFactory.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/EnemyBullet.ts.
23:56:18.880 debug: Resolve ../Global from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/EnemyBulletFactory.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Global.ts.
23:56:18.880 debug: Resolve ../PersistNode from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/EnemyBulletFactory.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/PersistNode.ts.
23:56:18.881 debug: Resolve ./GameFactory from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/EnemyBulletFactory.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/GameFactory.ts.
23:56:18.881 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/EnemyBullet.ts as cce:/internal/code-quality/cr.mjs.
23:56:18.881 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/EnemyBullet.ts as cce:/internal/x/cc.
23:56:18.881 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/EnemyBullet.ts as cce:/internal/x/cc.
23:56:18.881 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/EnemyBullet.ts as cce:/internal/x/cc.
23:56:18.881 debug: Resolve ./Global from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/EnemyBullet.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Global.ts.
23:56:18.881 debug: Resolve ./PersistNode from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/EnemyBullet.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/PersistNode.ts.
23:56:18.882 debug: Resolve ./Player from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/EnemyBullet.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Player.ts.
23:56:18.882 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Global.ts as cce:/internal/x/cc.
23:56:18.882 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Global.ts as cce:/internal/x/cc.
23:56:18.882 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Global.ts as cce:/internal/x/cc.
23:56:18.882 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Player.ts as cce:/internal/code-quality/cr.mjs.
23:56:18.882 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Player.ts as cce:/internal/x/cc.
23:56:18.883 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Player.ts as cce:/internal/x/cc.
23:56:18.883 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Player.ts as cce:/internal/x/cc.
23:56:18.883 debug: Resolve ./Global from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Player.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Global.ts.
23:56:18.883 debug: Resolve ./PersistNode from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Player.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/PersistNode.ts.
23:56:18.883 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/EnemyFactory.ts as cce:/internal/code-quality/cr.mjs.
23:56:18.883 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/EnemyFactory.ts as cce:/internal/x/cc.
23:56:18.901 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/EnemyFactory.ts as cce:/internal/x/cc.
23:56:18.901 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/EnemyFactory.ts as cce:/internal/x/cc.
23:56:18.901 debug: Resolve ../Enemy from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/EnemyFactory.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Enemy.ts.
23:56:18.901 debug: Resolve ../Global from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/EnemyFactory.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Global.ts.
23:56:18.901 debug: Resolve ../PersistNode from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/EnemyFactory.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/PersistNode.ts.
23:56:18.901 debug: Resolve ./GameFactory from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/EnemyFactory.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/GameFactory.ts.
23:56:18.901 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Enemy.ts as cce:/internal/code-quality/cr.mjs.
23:56:18.901 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Enemy.ts as cce:/internal/x/cc.
23:56:18.901 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Enemy.ts as cce:/internal/x/cc.
23:56:18.901 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Enemy.ts as cce:/internal/x/cc.
23:56:18.901 debug: Resolve ./Global from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Enemy.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Global.ts.
23:56:18.901 debug: Resolve ./PersistNode from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Enemy.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/PersistNode.ts.
23:56:18.901 debug: Resolve ./Player from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Enemy.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Player.ts.
23:56:18.901 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/GoodsFactory.ts as cce:/internal/code-quality/cr.mjs.
23:56:18.901 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/GoodsFactory.ts as cce:/internal/x/cc.
23:56:18.901 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/GoodsFactory.ts as cce:/internal/x/cc.
23:56:18.901 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/GoodsFactory.ts as cce:/internal/x/cc.
23:56:18.901 debug: Resolve ../Global from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/GoodsFactory.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Global.ts.
23:56:18.901 debug: Resolve ../Goods from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/GoodsFactory.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Goods.ts.
23:56:18.901 debug: Resolve ../PersistNode from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/GoodsFactory.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/PersistNode.ts.
23:56:18.901 debug: Resolve ./GameFactory from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/GoodsFactory.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/GameFactory.ts.
23:56:18.901 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Goods.ts as cce:/internal/code-quality/cr.mjs.
23:56:18.901 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Goods.ts as cce:/internal/x/cc.
23:56:18.901 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Goods.ts as cce:/internal/x/cc.
23:56:18.901 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Goods.ts as cce:/internal/x/cc.
23:56:18.901 debug: Resolve ./Global from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Goods.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Global.ts.
23:56:18.901 debug: Resolve ./PersistNode from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Goods.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/PersistNode.ts.
23:56:18.901 debug: Resolve ./Player from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Goods.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Player.ts.
23:56:18.901 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/PlayerBulletFactory.ts as cce:/internal/code-quality/cr.mjs.
23:56:18.901 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/PlayerBulletFactory.ts as cce:/internal/x/cc.
23:56:18.901 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/PlayerBulletFactory.ts as cce:/internal/x/cc.
23:56:18.901 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/PlayerBulletFactory.ts as cce:/internal/x/cc.
23:56:18.901 debug: Resolve ../Global from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/PlayerBulletFactory.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Global.ts.
23:56:18.901 debug: Resolve ../PersistNode from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/PlayerBulletFactory.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/PersistNode.ts.
23:56:18.901 debug: Resolve ../PlayerBullet from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/PlayerBulletFactory.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/PlayerBullet.ts.
23:56:18.901 debug: Resolve ./GameFactory from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/PlayerBulletFactory.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/GameFactory.ts.
23:56:18.901 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/PlayerBullet.ts as cce:/internal/code-quality/cr.mjs.
23:56:18.901 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/PlayerBullet.ts as cce:/internal/x/cc.
23:56:18.901 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/PlayerBullet.ts as cce:/internal/x/cc.
23:56:18.901 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/PlayerBullet.ts as cce:/internal/x/cc.
23:56:18.901 debug: Resolve ./Enemy from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/PlayerBullet.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Enemy.ts.
23:56:18.901 debug: Resolve ./Global from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/PlayerBullet.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Global.ts.
23:56:18.901 debug: Resolve ./PersistNode from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/PlayerBullet.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/PersistNode.ts.
23:56:18.901 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Background.ts as cce:/internal/code-quality/cr.mjs.
23:56:18.901 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Background.ts as cce:/internal/x/cc.
23:56:18.901 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Background.ts as cce:/internal/x/cc.
23:56:18.901 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Background.ts as cce:/internal/x/cc.
23:56:18.901 debug: Resolve ./Global from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Background.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Global.ts.
23:56:18.901 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/GameOver.ts as cce:/internal/code-quality/cr.mjs.
23:56:18.901 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/GameOver.ts as cce:/internal/x/cc.
23:56:18.901 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/GameOver.ts as cce:/internal/x/cc.
23:56:18.901 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/GameOver.ts as cce:/internal/x/cc.
23:56:18.901 debug: Resolve ./Global from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/GameOver.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Global.ts.
23:56:18.901 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/MainGame.ts as cce:/internal/code-quality/cr.mjs.
23:56:18.901 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/MainGame.ts as cce:/internal/x/cc.
23:56:18.901 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/MainGame.ts as cce:/internal/x/cc.
23:56:18.901 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/MainGame.ts as cce:/internal/x/cc.
23:56:18.901 debug: Resolve ./Global from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/MainGame.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Global.ts.
23:56:18.901 debug: Resolve ./PersistNode from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/MainGame.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/PersistNode.ts.
23:56:18.901 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Menu.ts as cce:/internal/x/cc.
23:56:18.901 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Menu.ts as cce:/internal/x/cc.
23:56:18.901 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Menu.ts as cce:/internal/x/cc.
23:56:18.901 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/Bootstrap.ts as cce:/internal/code-quality/cr.mjs.
23:56:18.901 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/Bootstrap.ts as cce:/internal/x/cc.
23:56:18.901 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/Bootstrap.ts as cce:/internal/x/cc.
23:56:18.901 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/Bootstrap.ts as cce:/internal/x/cc.
23:56:18.901 debug: Resolve ./base/World from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/Bootstrap.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/base/World.ts.
23:56:18.901 debug: Resolve ./WorldInitializeData from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/Bootstrap.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/WorldInitializeData.ts.
23:56:18.901 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/base/World.ts as cce:/internal/code-quality/cr.mjs.
23:56:18.901 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/base/World.ts as cce:/internal/x/cc.
23:56:18.901 debug: Resolve ./SystemContainer from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/base/World.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/base/SystemContainer.ts.
23:56:18.901 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/base/SystemContainer.ts as cce:/internal/code-quality/cr.mjs.
23:56:18.901 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/base/SystemContainer.ts as cce:/internal/x/cc.
23:56:18.901 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/base/SystemContainer.ts as cce:/internal/x/cc.
23:56:18.901 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/base/SystemContainer.ts as cce:/internal/x/cc.
23:56:18.901 debug: Resolve ./TypeID from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/base/SystemContainer.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/base/TypeID.ts.
23:56:18.901 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/base/TypeID.ts as cce:/internal/x/cc.
23:56:18.901 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/WorldInitializeData.ts as cce:/internal/x/cc.
23:56:18.901 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/base/System.ts as cce:/internal/code-quality/cr.mjs.
23:56:18.901 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/base/System.ts as cce:/internal/x/cc.
23:56:18.901 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/base/System.ts as cce:/internal/x/cc.
23:56:18.901 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/base/System.ts as cce:/internal/x/cc.
23:56:18.901 debug: Resolve ./TypeID from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/base/System.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/base/TypeID.ts.
23:56:18.901 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/bullet/Bullet.ts as cce:/internal/x/cc.
23:56:18.901 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/bullet/Bullet.ts as cce:/internal/x/cc.
23:56:18.901 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/bullet/Bullet.ts as cce:/internal/x/cc.
23:56:18.901 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/bullet/BulletSystem.ts as cce:/internal/code-quality/cr.mjs.
23:56:18.901 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/bullet/BulletSystem.ts as cce:/internal/x/cc.
23:56:18.901 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/bullet/BulletSystem.ts as cce:/internal/x/cc.
23:56:18.901 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/bullet/BulletSystem.ts as cce:/internal/x/cc.
23:56:18.904 debug: Resolve ../base/System from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/bullet/BulletSystem.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/base/System.ts.
23:56:18.904 debug: Resolve ../base/TypeID from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/bullet/BulletSystem.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/base/TypeID.ts.
23:56:18.904 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/bullet/Emitter.ts as cce:/internal/x/cc.
23:56:18.904 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/bullet/Emitter.ts as cce:/internal/x/cc.
23:56:18.904 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/bullet/Emitter.ts as cce:/internal/x/cc.
23:56:18.904 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/index.ts as cce:/internal/x/cc.
23:56:18.904 debug: Resolve ./base/System from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/index.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/base/System.ts.
23:56:18.904 debug: Resolve ./base/SystemContainer from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/index.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/base/SystemContainer.ts.
23:56:18.904 debug: Resolve ./base/TypeID from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/index.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/base/TypeID.ts.
23:56:18.904 debug: Resolve ./base/TypeID from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/index.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/base/TypeID.ts.
23:56:18.904 debug: Resolve ./base/World from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/index.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/base/World.ts.
23:56:18.904 debug: Resolve ./WorldInitializeData from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/index.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/WorldInitializeData.ts.
23:56:18.904 debug: Resolve ./Bootstrap from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/index.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/Bootstrap.ts.
23:56:18.904 debug: Resolve ./bullet/BulletSystem from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/index.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/bullet/BulletSystem.ts.
23:56:18.904 debug: Resolve ./level/LevelSystem from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/index.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/level/LevelSystem.ts.
23:56:18.904 debug: Resolve ./player/PlayerSystem from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/index.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/player/PlayerSystem.ts.
23:56:18.904 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/level/LevelSystem.ts as cce:/internal/code-quality/cr.mjs.
23:56:18.904 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/level/LevelSystem.ts as cce:/internal/x/cc.
23:56:18.904 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/level/LevelSystem.ts as cce:/internal/x/cc.
23:56:18.904 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/level/LevelSystem.ts as cce:/internal/x/cc.
23:56:18.904 debug: Resolve ../base/System from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/level/LevelSystem.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/base/System.ts.
23:56:18.904 debug: Resolve ../base/TypeID from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/level/LevelSystem.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/base/TypeID.ts.
23:56:18.904 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/player/PlayerSystem.ts as cce:/internal/code-quality/cr.mjs.
23:56:18.904 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/player/PlayerSystem.ts as cce:/internal/x/cc.
23:56:18.904 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/player/PlayerSystem.ts as cce:/internal/x/cc.
23:56:18.904 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/player/PlayerSystem.ts as cce:/internal/x/cc.
23:56:18.904 debug: Resolve ../base/System from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/player/PlayerSystem.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/base/System.ts.
23:56:18.904 debug: Resolve ../base/TypeID from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/player/PlayerSystem.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/base/TypeID.ts.
23:56:18.904 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameInstance.ts as cce:/internal/code-quality/cr.mjs.
23:56:18.904 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameInstance.ts as cce:/internal/x/cc.
23:56:18.904 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameInstance.ts as cce:/internal/x/cc.
23:56:18.904 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameInstance.ts as cce:/internal/x/cc.
23:56:18.904 debug: Resolve ./Luban/LubanMgr from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameInstance.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Luban/LubanMgr.ts.
23:56:18.904 debug: Resolve ./Network/NetMgr from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameInstance.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Network/NetMgr.ts.
23:56:18.904 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Luban/LubanMgr.ts as cce:/internal/code-quality/cr.mjs.
23:56:18.904 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Luban/LubanMgr.ts as cce:/internal/x/cc.
23:56:18.904 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Luban/LubanMgr.ts as cce:/internal/x/cc.
23:56:18.904 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Luban/LubanMgr.ts as cce:/internal/x/cc.
23:56:18.904 debug: Resolve ../IMgr from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Luban/LubanMgr.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/IMgr.ts.
23:56:18.904 debug: Resolve ../AutoGen/Luban/schema from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Luban/LubanMgr.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/AutoGen/Luban/schema.ts.
23:56:18.904 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/IMgr.ts as cce:/internal/x/cc.
23:56:18.904 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/IMgr.ts as cce:/internal/x/cc.
23:56:18.904 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/IMgr.ts as cce:/internal/x/cc.
23:56:18.904 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Network/NetMgr.ts as cce:/internal/code-quality/cr.mjs.
23:56:18.905 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Network/NetMgr.ts as cce:/internal/x/cc.
23:56:18.905 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Network/NetMgr.ts as cce:/internal/x/cc.
23:56:18.905 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Network/NetMgr.ts as cce:/internal/x/cc.
23:56:18.905 debug: Resolve ../IMgr from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Network/NetMgr.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/IMgr.ts.
23:56:18.905 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/MainUI.ts as cce:/internal/x/cc.
23:56:18.905 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/MainUI.ts as cce:/internal/x/cc.
23:56:18.905 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/MainUI.ts as cce:/internal/x/cc.
23:56:18.905 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ResUpdate/ResUpdate.ts as cce:/internal/x/cc.
23:56:18.905 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ResUpdate/ResUpdate.ts as cce:/internal/x/cc.
23:56:18.905 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ResUpdate/ResUpdate.ts as cce:/internal/x/cc.
23:56:18.905 debug: Resolve cc/env from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ResUpdate/ResUpdate.ts as external dependency cc/env.
23:56:18.905 debug: Target(editor) ends with cost 1701.5709000000006ms.
23:56:18.905 debug: Target(preview) build started.
23:56:18.906 debug: QuickPack.build, lock file failed!, workspace: D:\Workspace\Projects\Moolego\M2Game\Client\temp\programming\packer-driver\targets\preview, err: Error: ENOENT: no such file or directory, lstat 'D:\Workspace\Projects\Moolego\M2Game\Client\temp\programming\packer-driver\targets\preview'
23:56:18.906 debug: Inspect cce:/internal/x/cc
23:56:18.917 debug: transform url: 'cce:/internal/x/cc' costs: 11.40 ms
23:56:18.918 debug: Resolve cce:/internal/x/cc-fu/2d from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/2d.
23:56:18.918 debug: Resolve cce:/internal/x/cc-fu/sorting from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/sorting.
23:56:18.918 debug: Resolve cce:/internal/x/cc-fu/affine-transform from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/affine-transform.
23:56:18.918 debug: Resolve cce:/internal/x/cc-fu/animation from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/animation.
23:56:18.918 debug: Resolve cce:/internal/x/cc-fu/audio from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/audio.
23:56:18.918 debug: Resolve cce:/internal/x/cc-fu/base from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/base.
23:56:18.918 debug: Resolve cce:/internal/x/cc-fu/custom-pipeline from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/custom-pipeline.
23:56:18.918 debug: Resolve cce:/internal/x/cc-fu/dragon-bones from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/dragon-bones.
23:56:18.918 debug: Resolve cce:/internal/x/cc-fu/gfx-webgl from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/gfx-webgl.
23:56:18.918 debug: Resolve cce:/internal/x/cc-fu/gfx-webgl2 from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/gfx-webgl2.
23:56:18.918 debug: Resolve cce:/internal/x/cc-fu/graphics from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/graphics.
23:56:18.918 debug: Resolve cce:/internal/x/cc-fu/intersection-2d from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/intersection-2d.
23:56:18.918 debug: Resolve cce:/internal/x/cc-fu/mask from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/mask.
23:56:18.918 debug: Resolve cce:/internal/x/cc-fu/particle-2d from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/particle-2d.
23:56:18.918 debug: Resolve cce:/internal/x/cc-fu/physics-2d-builtin from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/physics-2d-builtin.
23:56:18.918 debug: Resolve cce:/internal/x/cc-fu/physics-2d-framework from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/physics-2d-framework.
23:56:18.918 debug: Resolve cce:/internal/x/cc-fu/profiler from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/profiler.
23:56:18.918 debug: Resolve cce:/internal/x/cc-fu/rich-text from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/rich-text.
23:56:18.918 debug: Resolve cce:/internal/x/cc-fu/skeletal-animation from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/skeletal-animation.
23:56:18.918 debug: Resolve cce:/internal/x/cc-fu/spine from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/spine.
23:56:18.918 debug: Resolve cce:/internal/x/cc-fu/tiled-map from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/tiled-map.
23:56:18.918 debug: Resolve cce:/internal/x/cc-fu/tween from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/tween.
23:56:18.918 debug: Resolve cce:/internal/x/cc-fu/ui from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/ui.
23:56:18.918 debug: Resolve cce:/internal/x/cc-fu/video from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/video.
23:56:18.918 debug: Resolve cce:/internal/x/cc-fu/webview from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/webview.
23:56:18.919 debug: Inspect cce:/internal/x/prerequisite-imports
23:56:18.924 debug: transform url: 'cce:/internal/x/prerequisite-imports' costs: 5.60 ms
23:56:18.925 debug: Resolve file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts from cce:/internal/x/prerequisite-imports as file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts.
23:56:18.925 debug: Inspect file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts
23:56:18.998 debug: transform url: 'file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts' costs: 73.00 ms
23:56:19.000 debug: Resolve file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts from cce:/internal/x/prerequisite-imports as file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts.
23:56:19.000 debug: Inspect file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts
23:56:19.022 debug: transform url: 'file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts' costs: 21.80 ms
23:56:19.023 debug: Resolve file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts from cce:/internal/x/prerequisite-imports as file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts.
23:56:19.023 debug: Inspect file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts
23:56:19.074 debug: transform url: 'file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts' costs: 50.70 ms
23:56:19.075 debug: Resolve file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts from cce:/internal/x/prerequisite-imports as file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts.
23:56:19.075 debug: Inspect file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts
23:56:19.116 debug: transform url: 'file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts' costs: 40.20 ms
23:56:19.117 debug: Resolve file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts from cce:/internal/x/prerequisite-imports as file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts.
23:56:19.117 debug: Inspect file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts
23:56:19.293 debug: transform url: 'file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts' costs: 176.00 ms
23:56:19.296 debug: Resolve file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts from cce:/internal/x/prerequisite-imports as file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts.
23:56:19.296 debug: Inspect file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts
23:56:19.374 debug: transform url: 'file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts' costs: 77.60 ms
23:56:19.376 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/gizmos/EmitterGizmo.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/gizmos/EmitterGizmo.ts.
23:56:19.376 debug: Inspect file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/gizmos/EmitterGizmo.ts
23:56:19.414 debug: transform url: 'file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/gizmos/EmitterGizmo.ts' costs: 38.00 ms
23:56:19.415 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/AutoGen/Luban/schema.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/AutoGen/Luban/schema.ts.
23:56:19.415 debug: Inspect file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/AutoGen/Luban/schema.ts
23:56:19.444 debug: transform url: 'file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/AutoGen/Luban/schema.ts' costs: 28.50 ms
23:56:19.445 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/AutoGen/PB/cs_proto.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/AutoGen/PB/cs_proto.ts.
23:56:19.445 debug: Inspect file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/AutoGen/PB/cs_proto.ts
23:56:19.661 debug: transform url: 'file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/AutoGen/PB/cs_proto.ts' costs: 215.70 ms
23:56:19.678 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Anim.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Anim.ts.
23:56:19.678 debug: Inspect file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Anim.ts
23:56:19.691 debug: transform url: 'file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Anim.ts' costs: 12.70 ms
23:56:19.696 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Background.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Background.ts.
23:56:19.697 debug: Inspect file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Background.ts
23:56:19.711 debug: transform url: 'file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Background.ts' costs: 14.50 ms
23:56:19.712 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Enemy.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Enemy.ts.
23:56:19.713 debug: Inspect file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Enemy.ts
23:56:19.751 debug: transform url: 'file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Enemy.ts' costs: 38.00 ms
23:56:19.752 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/EnemyBullet.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/EnemyBullet.ts.
23:56:19.752 debug: Inspect file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/EnemyBullet.ts
23:56:19.797 debug: transform url: 'file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/EnemyBullet.ts' costs: 44.20 ms
23:56:19.798 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/GameOver.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/GameOver.ts.
23:56:19.798 debug: Inspect file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/GameOver.ts
23:56:19.812 debug: transform url: 'file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/GameOver.ts' costs: 13.20 ms
23:56:19.813 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Global.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Global.ts.
23:56:19.813 debug: Inspect file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Global.ts
23:56:19.823 debug: transform url: 'file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Global.ts' costs: 10.30 ms
23:56:19.824 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Goods.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Goods.ts.
23:56:19.824 debug: Inspect file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Goods.ts
23:56:19.849 debug: transform url: 'file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Goods.ts' costs: 25.60 ms
23:56:19.851 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/MainGame.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/MainGame.ts.
23:56:19.851 debug: Inspect file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/MainGame.ts
23:56:19.881 debug: transform url: 'file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/MainGame.ts' costs: 30.70 ms
23:56:19.882 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Menu.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Menu.ts.
23:56:19.882 debug: Inspect file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Menu.ts
23:56:19.893 debug: transform url: 'file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Menu.ts' costs: 10.70 ms
23:56:19.894 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/PersistNode.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/PersistNode.ts.
23:56:19.894 debug: Inspect file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/PersistNode.ts
23:56:19.927 debug: transform url: 'file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/PersistNode.ts' costs: 32.40 ms
23:56:19.928 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Player.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Player.ts.
23:56:19.928 debug: Inspect file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Player.ts
23:56:19.978 debug: transform url: 'file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Player.ts' costs: 50.10 ms
23:56:20.966 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/PlayerBullet.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/PlayerBullet.ts.
23:56:20.966 debug: Inspect file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/PlayerBullet.ts
23:56:21.006 debug: transform url: 'file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/PlayerBullet.ts' costs: 39.60 ms
23:56:21.008 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/AnimFactory.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/AnimFactory.ts.
23:56:21.009 debug: Inspect file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/AnimFactory.ts
23:56:21.022 debug: transform url: 'file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/AnimFactory.ts' costs: 12.80 ms
23:56:21.023 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/EnemyBulletFactory.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/EnemyBulletFactory.ts.
23:56:21.023 debug: Inspect file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/EnemyBulletFactory.ts
23:56:21.035 debug: transform url: 'file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/EnemyBulletFactory.ts' costs: 11.40 ms
23:56:21.036 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/EnemyFactory.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/EnemyFactory.ts.
23:56:21.036 debug: Inspect file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/EnemyFactory.ts
23:56:21.046 debug: transform url: 'file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/EnemyFactory.ts' costs: 9.80 ms
23:56:21.046 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/GameFactory.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/GameFactory.ts.
23:56:21.047 debug: Inspect file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/GameFactory.ts
23:56:21.055 debug: transform url: 'file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/GameFactory.ts' costs: 8.50 ms
23:56:21.056 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/GoodsFactory.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/GoodsFactory.ts.
23:56:21.056 debug: Inspect file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/GoodsFactory.ts
23:56:21.069 debug: transform url: 'file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/GoodsFactory.ts' costs: 13.10 ms
23:56:21.070 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/PlayerBulletFactory.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/PlayerBulletFactory.ts.
23:56:21.070 debug: Inspect file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/PlayerBulletFactory.ts
23:56:21.102 debug: transform url: 'file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/PlayerBulletFactory.ts' costs: 31.50 ms
23:56:21.103 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/Bootstrap.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/Bootstrap.ts.
23:56:21.103 debug: Inspect file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/Bootstrap.ts
23:56:21.154 debug: transform url: 'file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/Bootstrap.ts' costs: 50.60 ms
23:56:21.155 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/WorldInitializeData.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/WorldInitializeData.ts.
23:56:21.155 debug: Inspect file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/WorldInitializeData.ts
23:56:21.184 debug: transform url: 'file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/WorldInitializeData.ts' costs: 29.30 ms
23:56:21.185 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/base/System.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/base/System.ts.
23:56:21.185 debug: Inspect file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/base/System.ts
23:56:21.202 debug: transform url: 'file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/base/System.ts' costs: 16.40 ms
23:56:21.203 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/base/SystemContainer.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/base/SystemContainer.ts.
23:56:21.203 debug: Inspect file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/base/SystemContainer.ts
23:56:21.244 debug: transform url: 'file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/base/SystemContainer.ts' costs: 41.30 ms
23:56:21.246 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/base/TypeID.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/base/TypeID.ts.
23:56:21.246 debug: Inspect file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/base/TypeID.ts
23:56:21.272 debug: transform url: 'file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/base/TypeID.ts' costs: 26.30 ms
23:56:21.273 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/base/World.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/base/World.ts.
23:56:21.273 debug: Inspect file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/base/World.ts
23:56:21.304 debug: transform url: 'file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/base/World.ts' costs: 31.30 ms
23:56:21.306 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/bullet/Bullet.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/bullet/Bullet.ts.
23:56:21.306 debug: Inspect file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/bullet/Bullet.ts
23:56:21.317 debug: transform url: 'file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/bullet/Bullet.ts' costs: 10.80 ms
23:56:21.317 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/bullet/BulletSystem.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/bullet/BulletSystem.ts.
23:56:21.317 debug: Inspect file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/bullet/BulletSystem.ts
23:56:21.344 debug: transform url: 'file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/bullet/BulletSystem.ts' costs: 26.50 ms
23:56:21.345 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/bullet/Emitter.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/bullet/Emitter.ts.
23:56:21.345 debug: Inspect file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/bullet/Emitter.ts
23:56:21.361 debug: transform url: 'file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/bullet/Emitter.ts' costs: 16.40 ms
23:56:21.362 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/index.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/index.ts.
23:56:21.362 debug: Inspect file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/index.ts
23:56:21.375 debug: transform url: 'file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/index.ts' costs: 12.80 ms
23:56:21.377 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/level/LevelSystem.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/level/LevelSystem.ts.
23:56:21.377 debug: Inspect file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/level/LevelSystem.ts
23:56:21.437 debug: transform url: 'file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/level/LevelSystem.ts' costs: 60.60 ms
23:56:21.438 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/player/PlayerSystem.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/player/PlayerSystem.ts.
23:56:21.438 debug: Inspect file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/player/PlayerSystem.ts
23:56:21.479 debug: transform url: 'file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/player/PlayerSystem.ts' costs: 41.00 ms
23:56:21.485 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameInstance.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameInstance.ts.
23:56:21.485 debug: Inspect file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameInstance.ts
23:56:21.500 debug: transform url: 'file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameInstance.ts' costs: 15.20 ms
23:56:21.501 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/IMgr.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/IMgr.ts.
23:56:21.501 debug: Inspect file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/IMgr.ts
23:56:21.507 debug: transform url: 'file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/IMgr.ts' costs: 6.10 ms
23:56:21.508 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Luban/LubanMgr.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Luban/LubanMgr.ts.
23:56:21.508 debug: Inspect file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Luban/LubanMgr.ts
23:56:21.519 debug: transform url: 'file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Luban/LubanMgr.ts' costs: 11.00 ms
23:56:21.520 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/MainUI.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/MainUI.ts.
23:56:21.520 debug: Inspect file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/MainUI.ts
23:56:21.533 debug: transform url: 'file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/MainUI.ts' costs: 13.00 ms
23:56:21.534 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Network/NetMgr.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Network/NetMgr.ts.
23:56:21.534 debug: Inspect file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Network/NetMgr.ts
23:56:21.549 debug: transform url: 'file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Network/NetMgr.ts' costs: 14.70 ms
23:56:21.550 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ResUpdate/ResUpdate.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ResUpdate/ResUpdate.ts.
23:56:21.550 debug: Inspect file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ResUpdate/ResUpdate.ts
23:56:21.589 debug: transform url: 'file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ResUpdate/ResUpdate.ts' costs: 38.20 ms
23:56:21.589 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as cce:/internal/code-quality/cr.mjs.
23:56:21.589 debug: Inspect cce:/internal/code-quality/cr.mjs
23:56:21.595 debug: transform url: 'cce:/internal/code-quality/cr.mjs' costs: 5.20 ms
23:56:21.595 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as cce:/internal/x/cc.
23:56:21.596 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as cce:/internal/x/cc.
23:56:21.596 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as cce:/internal/x/cc.
23:56:21.596 debug: Resolve cc/env from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as external dependency cc/env.
23:56:21.596 debug: Resolve ./builtin-pipeline-settings from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts.
23:56:21.596 debug: Resolve ./builtin-pipeline-pass from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts.
23:56:21.597 debug: Resolve ./builtin-pipeline from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts.
23:56:21.597 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts as cce:/internal/code-quality/cr.mjs.
23:56:21.597 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts as cce:/internal/x/cc.
23:56:21.597 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts as cce:/internal/x/cc.
23:56:21.597 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts as cce:/internal/x/cc.
23:56:21.597 debug: Resolve cc/env from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts as external dependency cc/env.
23:56:21.597 debug: Resolve ./builtin-pipeline-types from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts as file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts.
23:56:21.597 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts as cce:/internal/x/cc.
23:56:21.597 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts as cce:/internal/x/cc.
23:56:21.597 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts as cce:/internal/x/cc.
23:56:21.597 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts as cce:/internal/code-quality/cr.mjs.
23:56:21.597 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts as cce:/internal/x/cc.
23:56:21.597 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts as cce:/internal/x/cc.
23:56:21.597 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts as cce:/internal/x/cc.
23:56:21.598 debug: Resolve ./builtin-pipeline-settings from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts as file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts.
23:56:21.598 debug: Resolve cc/env from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts as external dependency cc/env.
23:56:21.598 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts as cce:/internal/code-quality/cr.mjs.
23:56:21.598 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts as cce:/internal/x/cc.
23:56:21.598 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts as cce:/internal/x/cc.
23:56:21.598 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts as cce:/internal/x/cc.
23:56:21.598 debug: Resolve cc/env from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts as external dependency cc/env.
23:56:21.598 debug: Resolve ./builtin-pipeline-types from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts as file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts.
23:56:21.598 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts as cce:/internal/x/cc.
23:56:21.598 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts as cce:/internal/x/cc.
23:56:21.598 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts as cce:/internal/x/cc.
23:56:21.598 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/gizmos/EmitterGizmo.ts as cce:/internal/x/cc.
23:56:21.598 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/gizmos/EmitterGizmo.ts as cce:/internal/x/cc.
23:56:21.598 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/gizmos/EmitterGizmo.ts as cce:/internal/x/cc.
23:56:21.598 debug: Resolve cc/env from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/gizmos/EmitterGizmo.ts as external dependency cc/env.
23:56:21.598 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/AutoGen/Luban/schema.ts as cce:/internal/x/cc.
23:56:21.599 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/AutoGen/PB/cs_proto.ts as cce:/internal/x/cc.
23:56:21.599 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Anim.ts as cce:/internal/code-quality/cr.mjs.
23:56:21.599 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Anim.ts as cce:/internal/x/cc.
23:56:21.599 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Anim.ts as cce:/internal/x/cc.
23:56:21.599 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Anim.ts as cce:/internal/x/cc.
23:56:21.599 debug: Resolve ./PersistNode from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Anim.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/PersistNode.ts.
23:56:21.599 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/PersistNode.ts as cce:/internal/code-quality/cr.mjs.
23:56:21.599 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/PersistNode.ts as cce:/internal/x/cc.
23:56:21.599 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/PersistNode.ts as cce:/internal/x/cc.
23:56:21.599 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/PersistNode.ts as cce:/internal/x/cc.
23:56:21.599 debug: Resolve ./factroy/AnimFactory from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/PersistNode.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/AnimFactory.ts.
23:56:21.600 debug: Resolve ./factroy/EnemyBulletFactory from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/PersistNode.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/EnemyBulletFactory.ts.
23:56:21.600 debug: Resolve ./factroy/EnemyFactory from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/PersistNode.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/EnemyFactory.ts.
23:56:21.600 debug: Resolve ./factroy/GoodsFactory from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/PersistNode.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/GoodsFactory.ts.
23:56:21.600 debug: Resolve ./factroy/PlayerBulletFactory from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/PersistNode.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/PlayerBulletFactory.ts.
23:56:21.600 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/AnimFactory.ts as cce:/internal/code-quality/cr.mjs.
23:56:21.600 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/AnimFactory.ts as cce:/internal/x/cc.
23:56:21.600 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/AnimFactory.ts as cce:/internal/x/cc.
23:56:21.600 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/AnimFactory.ts as cce:/internal/x/cc.
23:56:21.600 debug: Resolve ../PersistNode from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/AnimFactory.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/PersistNode.ts.
23:56:21.601 debug: Resolve ./GameFactory from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/AnimFactory.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/GameFactory.ts.
23:56:21.601 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/GameFactory.ts as cce:/internal/x/cc.
23:56:21.601 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/GameFactory.ts as cce:/internal/x/cc.
23:56:21.601 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/GameFactory.ts as cce:/internal/x/cc.
23:56:21.601 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/EnemyBulletFactory.ts as cce:/internal/code-quality/cr.mjs.
23:56:21.601 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/EnemyBulletFactory.ts as cce:/internal/x/cc.
23:56:21.601 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/EnemyBulletFactory.ts as cce:/internal/x/cc.
23:56:21.601 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/EnemyBulletFactory.ts as cce:/internal/x/cc.
23:56:21.601 debug: Resolve ../EnemyBullet from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/EnemyBulletFactory.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/EnemyBullet.ts.
23:56:21.601 debug: Resolve ../Global from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/EnemyBulletFactory.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Global.ts.
23:56:21.601 debug: Resolve ../PersistNode from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/EnemyBulletFactory.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/PersistNode.ts.
23:56:21.601 debug: Resolve ./GameFactory from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/EnemyBulletFactory.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/GameFactory.ts.
23:56:21.601 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/EnemyBullet.ts as cce:/internal/code-quality/cr.mjs.
23:56:21.601 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/EnemyBullet.ts as cce:/internal/x/cc.
23:56:21.601 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/EnemyBullet.ts as cce:/internal/x/cc.
23:56:21.601 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/EnemyBullet.ts as cce:/internal/x/cc.
23:56:21.602 debug: Resolve ./Global from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/EnemyBullet.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Global.ts.
23:56:21.602 debug: Resolve ./PersistNode from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/EnemyBullet.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/PersistNode.ts.
23:56:21.602 debug: Resolve ./Player from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/EnemyBullet.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Player.ts.
23:56:21.602 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Global.ts as cce:/internal/x/cc.
23:56:21.602 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Global.ts as cce:/internal/x/cc.
23:56:21.602 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Global.ts as cce:/internal/x/cc.
23:56:21.602 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Player.ts as cce:/internal/code-quality/cr.mjs.
23:56:21.602 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Player.ts as cce:/internal/x/cc.
23:56:21.602 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Player.ts as cce:/internal/x/cc.
23:56:21.602 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Player.ts as cce:/internal/x/cc.
23:56:21.602 debug: Resolve ./Global from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Player.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Global.ts.
23:56:21.602 debug: Resolve ./PersistNode from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Player.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/PersistNode.ts.
23:56:21.602 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/EnemyFactory.ts as cce:/internal/code-quality/cr.mjs.
23:56:21.602 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/EnemyFactory.ts as cce:/internal/x/cc.
23:56:21.602 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/EnemyFactory.ts as cce:/internal/x/cc.
23:56:21.602 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/EnemyFactory.ts as cce:/internal/x/cc.
23:56:21.602 debug: Resolve ../Enemy from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/EnemyFactory.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Enemy.ts.
23:56:21.603 debug: Resolve ../Global from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/EnemyFactory.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Global.ts.
23:56:21.603 debug: Resolve ../PersistNode from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/EnemyFactory.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/PersistNode.ts.
23:56:21.603 debug: Resolve ./GameFactory from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/EnemyFactory.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/GameFactory.ts.
23:56:21.603 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Enemy.ts as cce:/internal/code-quality/cr.mjs.
23:56:21.603 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Enemy.ts as cce:/internal/x/cc.
23:56:21.603 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Enemy.ts as cce:/internal/x/cc.
23:56:21.603 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Enemy.ts as cce:/internal/x/cc.
23:56:21.603 debug: Resolve ./Global from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Enemy.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Global.ts.
23:56:21.603 debug: Resolve ./PersistNode from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Enemy.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/PersistNode.ts.
23:56:21.603 debug: Resolve ./Player from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Enemy.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Player.ts.
23:56:21.603 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/GoodsFactory.ts as cce:/internal/code-quality/cr.mjs.
23:56:21.603 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/GoodsFactory.ts as cce:/internal/x/cc.
23:56:21.603 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/GoodsFactory.ts as cce:/internal/x/cc.
23:56:21.603 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/GoodsFactory.ts as cce:/internal/x/cc.
23:56:21.621 debug: Resolve ../Global from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/GoodsFactory.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Global.ts.
23:56:21.621 debug: Resolve ../Goods from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/GoodsFactory.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Goods.ts.
23:56:21.621 debug: Resolve ../PersistNode from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/GoodsFactory.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/PersistNode.ts.
23:56:21.621 debug: Resolve ./GameFactory from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/GoodsFactory.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/GameFactory.ts.
23:56:21.621 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Goods.ts as cce:/internal/code-quality/cr.mjs.
23:56:21.621 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Goods.ts as cce:/internal/x/cc.
23:56:21.621 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Goods.ts as cce:/internal/x/cc.
23:56:21.621 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Goods.ts as cce:/internal/x/cc.
23:56:21.621 debug: Resolve ./Global from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Goods.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Global.ts.
23:56:21.621 debug: Resolve ./PersistNode from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Goods.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/PersistNode.ts.
23:56:21.621 debug: Resolve ./Player from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Goods.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Player.ts.
23:56:21.621 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/PlayerBulletFactory.ts as cce:/internal/code-quality/cr.mjs.
23:56:21.621 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/PlayerBulletFactory.ts as cce:/internal/x/cc.
23:56:21.621 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/PlayerBulletFactory.ts as cce:/internal/x/cc.
23:56:21.621 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/PlayerBulletFactory.ts as cce:/internal/x/cc.
23:56:21.621 debug: Resolve ../Global from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/PlayerBulletFactory.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Global.ts.
23:56:21.621 debug: Resolve ../PersistNode from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/PlayerBulletFactory.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/PersistNode.ts.
23:56:21.621 debug: Resolve ../PlayerBullet from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/PlayerBulletFactory.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/PlayerBullet.ts.
23:56:21.621 debug: Resolve ./GameFactory from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/PlayerBulletFactory.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/GameFactory.ts.
23:56:21.621 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/PlayerBullet.ts as cce:/internal/code-quality/cr.mjs.
23:56:21.621 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/PlayerBullet.ts as cce:/internal/x/cc.
23:56:21.621 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/PlayerBullet.ts as cce:/internal/x/cc.
23:56:21.621 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/PlayerBullet.ts as cce:/internal/x/cc.
23:56:21.621 debug: Resolve ./Enemy from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/PlayerBullet.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Enemy.ts.
23:56:21.621 debug: Resolve ./Global from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/PlayerBullet.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Global.ts.
23:56:21.621 debug: Resolve ./PersistNode from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/PlayerBullet.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/PersistNode.ts.
23:56:21.621 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Background.ts as cce:/internal/code-quality/cr.mjs.
23:56:21.621 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Background.ts as cce:/internal/x/cc.
23:56:21.621 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Background.ts as cce:/internal/x/cc.
23:56:21.621 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Background.ts as cce:/internal/x/cc.
23:56:21.621 debug: Resolve ./Global from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Background.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Global.ts.
23:56:21.621 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/GameOver.ts as cce:/internal/code-quality/cr.mjs.
23:56:21.621 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/GameOver.ts as cce:/internal/x/cc.
23:56:21.621 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/GameOver.ts as cce:/internal/x/cc.
23:56:21.621 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/GameOver.ts as cce:/internal/x/cc.
23:56:21.621 debug: Resolve ./Global from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/GameOver.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Global.ts.
23:56:21.621 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/MainGame.ts as cce:/internal/code-quality/cr.mjs.
23:56:21.621 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/MainGame.ts as cce:/internal/x/cc.
23:56:21.621 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/MainGame.ts as cce:/internal/x/cc.
23:56:21.621 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/MainGame.ts as cce:/internal/x/cc.
23:56:21.621 debug: Resolve ./Global from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/MainGame.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Global.ts.
23:56:21.621 debug: Resolve ./PersistNode from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/MainGame.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/PersistNode.ts.
23:56:21.621 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Menu.ts as cce:/internal/x/cc.
23:56:21.621 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Menu.ts as cce:/internal/x/cc.
23:56:21.621 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Menu.ts as cce:/internal/x/cc.
23:56:21.621 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/Bootstrap.ts as cce:/internal/code-quality/cr.mjs.
23:56:21.621 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/Bootstrap.ts as cce:/internal/x/cc.
23:56:21.621 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/Bootstrap.ts as cce:/internal/x/cc.
23:56:21.621 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/Bootstrap.ts as cce:/internal/x/cc.
23:56:21.621 debug: Resolve ./base/World from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/Bootstrap.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/base/World.ts.
23:56:21.621 debug: Resolve ./WorldInitializeData from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/Bootstrap.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/WorldInitializeData.ts.
23:56:21.621 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/base/World.ts as cce:/internal/code-quality/cr.mjs.
23:56:21.621 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/base/World.ts as cce:/internal/x/cc.
23:56:21.621 debug: Resolve ./SystemContainer from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/base/World.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/base/SystemContainer.ts.
23:56:21.621 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/base/SystemContainer.ts as cce:/internal/code-quality/cr.mjs.
23:56:21.621 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/base/SystemContainer.ts as cce:/internal/x/cc.
23:56:21.621 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/base/SystemContainer.ts as cce:/internal/x/cc.
23:56:21.621 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/base/SystemContainer.ts as cce:/internal/x/cc.
23:56:21.621 debug: Resolve ./TypeID from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/base/SystemContainer.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/base/TypeID.ts.
23:56:21.621 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/base/TypeID.ts as cce:/internal/x/cc.
23:56:21.621 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/WorldInitializeData.ts as cce:/internal/x/cc.
23:56:21.621 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/base/System.ts as cce:/internal/code-quality/cr.mjs.
23:56:21.621 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/base/System.ts as cce:/internal/x/cc.
23:56:21.621 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/base/System.ts as cce:/internal/x/cc.
23:56:21.621 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/base/System.ts as cce:/internal/x/cc.
23:56:21.621 debug: Resolve ./TypeID from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/base/System.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/base/TypeID.ts.
23:56:21.621 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/bullet/Bullet.ts as cce:/internal/x/cc.
23:56:21.621 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/bullet/Bullet.ts as cce:/internal/x/cc.
23:56:21.621 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/bullet/Bullet.ts as cce:/internal/x/cc.
23:56:21.621 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/bullet/BulletSystem.ts as cce:/internal/code-quality/cr.mjs.
23:56:21.621 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/bullet/BulletSystem.ts as cce:/internal/x/cc.
23:56:21.621 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/bullet/BulletSystem.ts as cce:/internal/x/cc.
23:56:21.621 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/bullet/BulletSystem.ts as cce:/internal/x/cc.
23:56:21.621 debug: Resolve ../base/System from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/bullet/BulletSystem.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/base/System.ts.
23:56:21.621 debug: Resolve ../base/TypeID from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/bullet/BulletSystem.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/base/TypeID.ts.
23:56:21.621 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/bullet/Emitter.ts as cce:/internal/x/cc.
23:56:21.621 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/bullet/Emitter.ts as cce:/internal/x/cc.
23:56:21.621 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/bullet/Emitter.ts as cce:/internal/x/cc.
23:56:21.621 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/index.ts as cce:/internal/x/cc.
23:56:21.621 debug: Resolve ./base/System from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/index.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/base/System.ts.
23:56:21.622 debug: Resolve ./base/SystemContainer from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/index.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/base/SystemContainer.ts.
23:56:21.622 debug: Resolve ./base/TypeID from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/index.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/base/TypeID.ts.
23:56:21.622 debug: Resolve ./base/TypeID from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/index.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/base/TypeID.ts.
23:56:21.622 debug: Resolve ./base/World from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/index.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/base/World.ts.
23:56:21.622 debug: Resolve ./WorldInitializeData from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/index.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/WorldInitializeData.ts.
23:56:21.622 debug: Resolve ./Bootstrap from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/index.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/Bootstrap.ts.
23:56:21.622 debug: Resolve ./bullet/BulletSystem from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/index.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/bullet/BulletSystem.ts.
23:56:21.622 debug: Resolve ./level/LevelSystem from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/index.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/level/LevelSystem.ts.
23:56:21.622 debug: Resolve ./player/PlayerSystem from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/index.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/player/PlayerSystem.ts.
23:56:21.622 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/level/LevelSystem.ts as cce:/internal/code-quality/cr.mjs.
23:56:21.622 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/level/LevelSystem.ts as cce:/internal/x/cc.
23:56:21.622 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/level/LevelSystem.ts as cce:/internal/x/cc.
23:56:21.622 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/level/LevelSystem.ts as cce:/internal/x/cc.
23:56:21.622 debug: Resolve ../base/System from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/level/LevelSystem.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/base/System.ts.
23:56:21.622 debug: Resolve ../base/TypeID from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/level/LevelSystem.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/base/TypeID.ts.
23:56:21.622 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/player/PlayerSystem.ts as cce:/internal/code-quality/cr.mjs.
23:56:21.622 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/player/PlayerSystem.ts as cce:/internal/x/cc.
23:56:21.622 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/player/PlayerSystem.ts as cce:/internal/x/cc.
23:56:21.622 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/player/PlayerSystem.ts as cce:/internal/x/cc.
23:56:21.622 debug: Resolve ../base/System from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/player/PlayerSystem.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/base/System.ts.
23:56:21.622 debug: Resolve ../base/TypeID from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/player/PlayerSystem.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/base/TypeID.ts.
23:56:21.622 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameInstance.ts as cce:/internal/code-quality/cr.mjs.
23:56:21.622 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameInstance.ts as cce:/internal/x/cc.
23:56:21.622 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameInstance.ts as cce:/internal/x/cc.
23:56:21.622 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameInstance.ts as cce:/internal/x/cc.
23:56:21.622 debug: Resolve ./Luban/LubanMgr from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameInstance.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Luban/LubanMgr.ts.
23:56:21.622 debug: Resolve ./Network/NetMgr from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameInstance.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Network/NetMgr.ts.
23:56:21.622 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Luban/LubanMgr.ts as cce:/internal/code-quality/cr.mjs.
23:56:21.622 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Luban/LubanMgr.ts as cce:/internal/x/cc.
23:56:21.622 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Luban/LubanMgr.ts as cce:/internal/x/cc.
23:56:21.622 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Luban/LubanMgr.ts as cce:/internal/x/cc.
23:56:21.622 debug: Resolve ../IMgr from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Luban/LubanMgr.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/IMgr.ts.
23:56:21.622 debug: Resolve ../AutoGen/Luban/schema from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Luban/LubanMgr.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/AutoGen/Luban/schema.ts.
23:56:21.622 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/IMgr.ts as cce:/internal/x/cc.
23:56:21.622 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/IMgr.ts as cce:/internal/x/cc.
23:56:21.622 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/IMgr.ts as cce:/internal/x/cc.
23:56:21.622 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Network/NetMgr.ts as cce:/internal/code-quality/cr.mjs.
23:56:21.622 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Network/NetMgr.ts as cce:/internal/x/cc.
23:56:21.622 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Network/NetMgr.ts as cce:/internal/x/cc.
23:56:21.622 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Network/NetMgr.ts as cce:/internal/x/cc.
23:56:21.622 debug: Resolve ../IMgr from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Network/NetMgr.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/IMgr.ts.
23:56:21.623 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/MainUI.ts as cce:/internal/x/cc.
23:56:21.623 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/MainUI.ts as cce:/internal/x/cc.
23:56:21.623 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/MainUI.ts as cce:/internal/x/cc.
23:56:21.623 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ResUpdate/ResUpdate.ts as cce:/internal/x/cc.
23:56:21.623 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ResUpdate/ResUpdate.ts as cce:/internal/x/cc.
23:56:21.623 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ResUpdate/ResUpdate.ts as cce:/internal/x/cc.
23:56:21.623 debug: Resolve cc/env from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ResUpdate/ResUpdate.ts as external dependency cc/env.
23:56:21.623 debug: Target(preview) ends with cost 2719.632099999999ms.
23:56:23.436 debug: Pulling asset-db.
23:56:23.744 debug: Fetch asset-db cost: 307.50840000000244ms.
23:56:23.745 debug: Build iteration starts.
Number of accumulated asset changes: 45
Feature changed: false
23:56:23.745 debug: Target(editor) build started.
23:56:23.747 debug: Detected change: cce:/internal/x/prerequisite-imports. Last mtime: Thu Jan 01 1970 08:00:15 GMT+0800 (中国标准时间), Current mtime: Thu Jan 01 1970 08:00:22 GMT+0800 (中国标准时间)
23:56:23.747 debug: Inspect cce:/internal/x/prerequisite-imports
23:56:23.754 debug: transform url: 'cce:/internal/x/prerequisite-imports' costs: 6.50 ms
23:56:23.754 debug: Resolve file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts from cce:/internal/x/prerequisite-imports as file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts.
23:56:23.755 debug: Resolve file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts from cce:/internal/x/prerequisite-imports as file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts.
23:56:23.755 debug: Resolve file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts from cce:/internal/x/prerequisite-imports as file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts.
23:56:23.755 debug: Resolve file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts from cce:/internal/x/prerequisite-imports as file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts.
23:56:23.755 debug: Resolve file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts from cce:/internal/x/prerequisite-imports as file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts.
23:56:23.756 debug: Resolve file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts from cce:/internal/x/prerequisite-imports as file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts.
23:56:23.757 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/gizmos/EmitterGizmo.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/gizmos/EmitterGizmo.ts.
23:56:23.757 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/AutoGen/Luban/schema.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/AutoGen/Luban/schema.ts.
23:56:23.757 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/AutoGen/PB/cs_proto.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/AutoGen/PB/cs_proto.ts.
23:56:23.757 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Anim.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Anim.ts.
23:56:23.757 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Background.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Background.ts.
23:56:23.757 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Enemy.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Enemy.ts.
23:56:23.757 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/EnemyBullet.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/EnemyBullet.ts.
23:56:23.758 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/GameOver.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/GameOver.ts.
23:56:23.758 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Global.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Global.ts.
23:56:23.758 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Goods.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Goods.ts.
23:56:23.758 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/MainGame.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/MainGame.ts.
23:56:23.758 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Menu.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Menu.ts.
23:56:23.759 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/PersistNode.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/PersistNode.ts.
23:56:23.759 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Player.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Player.ts.
23:56:23.759 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/PlayerBullet.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/PlayerBullet.ts.
23:56:23.759 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/AnimFactory.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/AnimFactory.ts.
23:56:23.759 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/EnemyBulletFactory.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/EnemyBulletFactory.ts.
23:56:23.759 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/EnemyFactory.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/EnemyFactory.ts.
23:56:23.759 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/GameFactory.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/GameFactory.ts.
23:56:23.760 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/GoodsFactory.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/GoodsFactory.ts.
23:56:23.760 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/PlayerBulletFactory.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/PlayerBulletFactory.ts.
23:56:23.760 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/Bootstrap.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/Bootstrap.ts.
23:56:23.760 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/WorldInitializeData.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/WorldInitializeData.ts.
23:56:23.760 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/base/System.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/base/System.ts.
23:56:23.760 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/base/SystemContainer.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/base/SystemContainer.ts.
23:56:23.761 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/base/TypeID.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/base/TypeID.ts.
23:56:23.761 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/base/World.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/base/World.ts.
23:56:23.761 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/bullet/Bullet.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/bullet/Bullet.ts.
23:56:23.761 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/bullet/BulletSystem.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/bullet/BulletSystem.ts.
23:56:23.761 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/bullet/Emitter.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/bullet/Emitter.ts.
23:56:23.761 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/index.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/index.ts.
23:56:23.761 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/level/LevelSystem.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/level/LevelSystem.ts.
23:56:23.762 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/player/PlayerSystem.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/player/PlayerSystem.ts.
23:56:23.762 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameInstance.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameInstance.ts.
23:56:23.762 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/IMgr.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/IMgr.ts.
23:56:23.762 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Luban/LubanMgr.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Luban/LubanMgr.ts.
23:56:23.762 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/MainUI.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/MainUI.ts.
23:56:23.762 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Network/NetMgr.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Network/NetMgr.ts.
23:56:23.762 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ResUpdate/ResUpdate.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ResUpdate/ResUpdate.ts.
23:56:23.787 debug: Target(editor) ends with cost 41.614400000002206ms.
23:56:23.787 debug: Target(preview) build started.
23:56:23.788 debug: Detected change: cce:/internal/x/prerequisite-imports. Last mtime: Thu Jan 01 1970 08:00:17 GMT+0800 (中国标准时间), Current mtime: Thu Jan 01 1970 08:00:22 GMT+0800 (中国标准时间)
23:56:23.788 debug: Inspect cce:/internal/x/prerequisite-imports
23:56:23.795 debug: transform url: 'cce:/internal/x/prerequisite-imports' costs: 6.50 ms
23:56:23.795 debug: Resolve file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts from cce:/internal/x/prerequisite-imports as file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts.
23:56:23.796 debug: Resolve file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts from cce:/internal/x/prerequisite-imports as file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts.
23:56:23.796 debug: Resolve file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts from cce:/internal/x/prerequisite-imports as file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts.
23:56:23.796 debug: Resolve file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts from cce:/internal/x/prerequisite-imports as file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts.
23:56:23.796 debug: Resolve file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts from cce:/internal/x/prerequisite-imports as file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts.
23:56:23.797 debug: Resolve file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts from cce:/internal/x/prerequisite-imports as file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts.
23:56:23.797 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/gizmos/EmitterGizmo.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/gizmos/EmitterGizmo.ts.
23:56:23.797 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/AutoGen/Luban/schema.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/AutoGen/Luban/schema.ts.
23:56:23.797 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/AutoGen/PB/cs_proto.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/AutoGen/PB/cs_proto.ts.
23:56:23.797 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Anim.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Anim.ts.
23:56:23.797 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Background.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Background.ts.
23:56:23.797 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Enemy.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Enemy.ts.
23:56:23.797 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/EnemyBullet.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/EnemyBullet.ts.
23:56:23.798 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/GameOver.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/GameOver.ts.
23:56:23.798 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Global.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Global.ts.
23:56:23.798 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Goods.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Goods.ts.
23:56:23.798 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/MainGame.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/MainGame.ts.
23:56:23.798 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Menu.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Menu.ts.
23:56:23.799 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/PersistNode.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/PersistNode.ts.
23:56:23.799 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Player.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Player.ts.
23:56:23.799 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/PlayerBullet.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/PlayerBullet.ts.
23:56:23.799 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/AnimFactory.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/AnimFactory.ts.
23:56:23.799 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/EnemyBulletFactory.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/EnemyBulletFactory.ts.
23:56:23.799 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/EnemyFactory.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/EnemyFactory.ts.
23:56:23.799 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/GameFactory.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/GameFactory.ts.
23:56:23.800 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/GoodsFactory.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/GoodsFactory.ts.
23:56:23.800 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/PlayerBulletFactory.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/PlayerBulletFactory.ts.
23:56:23.800 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/Bootstrap.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/Bootstrap.ts.
23:56:23.800 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/WorldInitializeData.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/WorldInitializeData.ts.
23:56:23.800 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/base/System.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/base/System.ts.
23:56:23.801 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/base/SystemContainer.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/base/SystemContainer.ts.
23:56:23.801 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/base/TypeID.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/base/TypeID.ts.
23:56:23.801 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/base/World.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/base/World.ts.
23:56:23.801 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/bullet/Bullet.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/bullet/Bullet.ts.
23:56:23.801 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/bullet/BulletSystem.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/bullet/BulletSystem.ts.
23:56:23.801 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/bullet/Emitter.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/bullet/Emitter.ts.
23:56:23.801 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/index.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/index.ts.
23:56:23.802 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/level/LevelSystem.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/level/LevelSystem.ts.
23:56:23.802 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/player/PlayerSystem.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/player/PlayerSystem.ts.
23:56:23.802 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameInstance.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameInstance.ts.
23:56:23.802 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/IMgr.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/IMgr.ts.
23:56:23.802 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Luban/LubanMgr.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Luban/LubanMgr.ts.
23:56:23.802 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/MainUI.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/MainUI.ts.
23:56:23.802 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Network/NetMgr.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Network/NetMgr.ts.
23:56:23.803 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ResUpdate/ResUpdate.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ResUpdate/ResUpdate.ts.
23:56:23.812 debug: Target(preview) ends with cost 25.117300000001705ms.
