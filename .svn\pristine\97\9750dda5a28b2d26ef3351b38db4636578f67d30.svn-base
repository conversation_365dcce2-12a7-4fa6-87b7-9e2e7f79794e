import { _decorator, director, Component, But<PERSON>} from 'cc';
const { ccclass, property } = _decorator;

@ccclass('MainUI')
export class MainUI extends Component {
    @property(Button)
    enterButton: Button = null!;

    start() {
        this.enterButton.node.on(Button.EventType.CLICK, this.onEnterButtonClick, this);
    }
    onEnterButtonClick() {
        director.loadScene("Game");
    }

    update(deltaTime: number) {
        
    }
}

