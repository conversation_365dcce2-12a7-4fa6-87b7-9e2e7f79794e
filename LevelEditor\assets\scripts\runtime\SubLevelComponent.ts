import { _decorator, Component, Node, Vec3, Rect, Camera } from 'cc';
import { SubLevel } from '../core/LevelData';
import { SubLevelState, RectUtils } from '../core/Types';

const { ccclass, property } = _decorator;

/**
 * Runtime component for SubLevel management with camera visibility detection
 */
@ccclass('SubLevelComponent')
export class SubLevelComponent extends Component {
    @property({ type: Object })
    public subLevelData: SubLevel | null = null;
    
    // Callback functions
    public onWillEnterView?: (subLevel: SubLevel) => void;
    public onWillExitView?: (subLevel: SubLevel) => void;
    public onFullyInView?: (subLevel: SubLevel) => void;
    public onFullyOutOfView?: (subLevel: SubLevel) => void;
    public onStateChanged?: (subLevel: SubLevel, oldState: SubLevelState, newState: SubLevelState) => void;
    
    // Private state tracking
    private wasInView: boolean = false;
    private wasFullyInView: boolean = false;
    private currentState: SubLevelState = SubLevelState.Unloaded;
    private predictiveDistance: number = 200; // Distance ahead to predict camera movement
    private lastCameraPosition: Vec3 = Vec3.ZERO;
    private cameraVelocity: Vec3 = Vec3.ZERO;
    
    // Performance optimization
    private checkInterval: number = 0.1; // Check visibility every 0.1 seconds
    private timeSinceLastCheck: number = 0;
    
    protected onLoad(): void {
        if (!this.subLevelData) {
            console.warn('SubLevelComponent: No subLevel data assigned');
            return;
        }
        
        this.setState(SubLevelState.Loaded);
    }
    
    protected start(): void {
        // Initialize camera tracking
        const camera = Camera.main;
        if (camera) {
            this.lastCameraPosition = camera.node.position.clone();
        }
    }
    
    protected update(deltaTime: number): void {
        if (!this.subLevelData) return;
        
        // Update camera velocity tracking
        this.updateCameraVelocity(deltaTime);
        
        // Check visibility at intervals for performance
        this.timeSinceLastCheck += deltaTime;
        if (this.timeSinceLastCheck >= this.checkInterval) {
            this.checkCameraVisibility();
            this.timeSinceLastCheck = 0;
        }
        
        // Update sublevel systems if active
        if (this.currentState === SubLevelState.Active) {
            this.updateSubLevelSystems(deltaTime);
        }
    }
    
    /**
     * Check camera visibility and trigger appropriate callbacks
     */
    private checkCameraVisibility(): void {
        const cameraViewport = this.getCameraViewport();
        if (!cameraViewport || !this.subLevelData) return;
        
        const isInView = this.isInCameraView(cameraViewport);
        const isFullyInView = this.isFullyInCameraView(cameraViewport);
        const willExitView = this.willExitCameraView(cameraViewport);
        
        // Check for entering view
        if (isInView && !this.wasInView) {
            this.onWillEnterView?.(this.subLevelData);
        }
        
        // Check for exiting view (including predictive)
        if ((willExitView || (!isInView && this.wasInView))) {
            this.onWillExitView?.(this.subLevelData);
        }
        
        // Check for fully in view
        if (isFullyInView && !this.wasFullyInView) {
            this.onFullyInView?.(this.subLevelData);
        }
        
        // Check for fully out of view
        if (!isFullyInView && this.wasFullyInView) {
            this.onFullyOutOfView?.(this.subLevelData);
        }
        
        // Update state tracking
        this.wasInView = isInView;
        this.wasFullyInView = isFullyInView;
    }
    
    /**
     * Check if sublevel is in camera view
     */
    private isInCameraView(cameraViewport: Rect): boolean {
        if (!this.subLevelData) return false;
        return RectUtils.intersects(cameraViewport, this.subLevelData.bounds);
    }
    
    /**
     * Check if sublevel is fully in camera view
     */
    private isFullyInCameraView(cameraViewport: Rect): boolean {
        if (!this.subLevelData) return false;
        return RectUtils.contains(cameraViewport, this.subLevelData.bounds);
    }
    
    /**
     * Predict if sublevel will exit camera view based on camera velocity
     */
    private willExitCameraView(cameraViewport: Rect): boolean {
        if (!this.subLevelData || this.cameraVelocity.length() < 0.1) return false;
        
        // Predict camera position in the near future
        const predictTime = this.predictiveDistance / Math.max(this.cameraVelocity.length(), 1);
        const futureViewport = this.predictCameraViewport(cameraViewport, predictTime);
        
        // Check if currently in view but will be out of view
        return this.isInCameraView(cameraViewport) && !this.isInCameraView(futureViewport);
    }
    
    /**
     * Predict camera viewport at a future time
     */
    private predictCameraViewport(currentViewport: Rect, timeAhead: number): Rect {
        const futureMovement = this.cameraVelocity.clone().multiplyScalar(timeAhead);
        const futureCenter = RectUtils.getCenter(currentViewport);
        futureCenter.x += futureMovement.x;
        futureCenter.y += futureMovement.y;
        
        return new Rect(
            futureCenter.x - currentViewport.width / 2,
            futureCenter.y - currentViewport.height / 2,
            currentViewport.width,
            currentViewport.height
        );
    }
    
    /**
     * Update camera velocity tracking
     */
    private updateCameraVelocity(deltaTime: number): void {
        const camera = Camera.main;
        if (!camera || deltaTime <= 0) return;
        
        const currentPosition = camera.node.position;
        const movement = currentPosition.clone().subtract(this.lastCameraPosition);
        this.cameraVelocity = movement.multiplyScalar(1 / deltaTime);
        this.lastCameraPosition = currentPosition.clone();
    }
    
    /**
     * Get current camera viewport in world space
     */
    private getCameraViewport(): Rect | null {
        const camera = Camera.main;
        if (!camera) return null;

        // Convert camera viewport to world space
        const cameraPos = camera.node.position;
        const orthoHeight = camera.orthoHeight || 10; // Default fallback
        const aspect = 16 / 9; // Default aspect ratio, should be configurable
        const orthoWidth = orthoHeight * aspect;

        return new Rect(
            cameraPos.x - orthoWidth / 2,
            cameraPos.y - orthoHeight / 2,
            orthoWidth,
            orthoHeight
        );
    }
    
    /**
     * Update sublevel systems when active
     */
    private updateSubLevelSystems(deltaTime: number): void {
        if (!this.subLevelData) return;
        
        // Update spawners
        this.updateSpawners(deltaTime);
        
        // Update events
        this.updateEvents(deltaTime);
        
        // Update map scrolling
        this.updateMapScrolling(deltaTime);
    }
    
    /**
     * Update spawner systems
     */
    private updateSpawners(deltaTime: number): void {
        // This will be implemented when SpawnerSystem is created
        // For now, just a placeholder
    }
    
    /**
     * Update event systems
     */
    private updateEvents(deltaTime: number): void {
        // This will be implemented when EventSystem is created
        // For now, just a placeholder
    }
    
    /**
     * Update map scrolling
     */
    private updateMapScrolling(deltaTime: number): void {
        // This will be implemented when MapSystem is created
        // For now, just a placeholder
    }
    
    /**
     * Set the state of this sublevel
     */
    public setState(newState: SubLevelState): void {
        if (this.currentState === newState) return;
        
        const oldState = this.currentState;
        this.currentState = newState;
        
        // Update sublevel data
        if (this.subLevelData) {
            this.subLevelData.isActive = newState === SubLevelState.Active;
            this.subLevelData.isLoaded = newState !== SubLevelState.Unloaded;
        }
        
        // Trigger state change callback
        if (this.subLevelData) {
            this.onStateChanged?.(this.subLevelData, oldState, newState);
        }
        
        console.log(`SubLevel ${this.subLevelData?.name} state changed: ${oldState} -> ${newState}`);
    }
    
    /**
     * Get current state
     */
    public getState(): SubLevelState {
        return this.currentState;
    }
    
    /**
     * Check if sublevel is near camera (for preloading decisions)
     */
    public isNearCamera(distance: number = 500): boolean {
        if (!this.subLevelData) return false;
        
        const cameraViewport = this.getCameraViewport();
        if (!cameraViewport) return false;
        
        const expandedViewport = RectUtils.expand(cameraViewport, distance);
        return RectUtils.intersects(expandedViewport, this.subLevelData.bounds);
    }
    
    /**
     * Force visibility check (useful for manual triggers)
     */
    public forceVisibilityCheck(): void {
        this.checkCameraVisibility();
    }
    
    /**
     * Get distance to camera center
     */
    public getDistanceToCamera(): number {
        if (!this.subLevelData) return Infinity;
        
        const cameraViewport = this.getCameraViewport();
        if (!cameraViewport) return Infinity;
        
        const cameraCenter = RectUtils.getCenter(cameraViewport);
        const subLevelCenter = RectUtils.getCenter(this.subLevelData.bounds);
        
        return Vec3.distance(
            new Vec3(cameraCenter.x, cameraCenter.y, 0),
            new Vec3(subLevelCenter.x, subLevelCenter.y, 0)
        );
    }
}
