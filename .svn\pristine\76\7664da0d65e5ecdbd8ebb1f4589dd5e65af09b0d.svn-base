/**
 * CrazyStorm 2.0 Integration for Cocos Creator
 * Event Executor
 * 
 * Executes individual events with property interpolation over time.
 * Based on the C# EventExecutor.cs implementation.
 */

import { Vec2, log, warn } from 'cc';
import { ExpressionVM } from '../expression';
import { 
    PropertyType, 
    EventKeyword, 
    TypeSet, 
    PropertyContainer,
    EventContext 
} from './EventTypes';

/**
 * Executes a single event with interpolation over time
 */
export class EventExecutor {
    // Event properties
    public propertyContainer: PropertyContainer;
    public bindingContainer?: PropertyContainer;
    public propertyName: string = '';
    public changeMode: EventKeyword = EventKeyword.Linear;
    public changeTime: number = 1;
    
    // Value interpolation
    public initialValue: TypeSet = new TypeSet();
    public targetValue: TypeSet = new TypeSet();
    public currentValue: TypeSet = new TypeSet();
    
    // Execution state
    private currentTime: number = 0;
    private _finished: boolean = false;

    constructor(propertyContainer: PropertyContainer, bindingContainer?: PropertyContainer) {
        this.propertyContainer = propertyContainer;
        this.bindingContainer = bindingContainer;
        this.currentValue = new TypeSet();
    }

    /**
     * Check if execution is finished
     */
    public get finished(): boolean {
        return this._finished || this.currentTime >= this.changeTime;
    }

    /**
     * Update the event execution
     */
    public update(): void {
        if (this.finished) {
            return;
        }

        // Calculate interpolation ratio
        let ratio = Math.min(1, (this.currentTime + 1) / this.changeTime);
        
        // Apply interpolation curve based on change mode
        switch (this.changeMode) {
            case EventKeyword.Accelerated:
                ratio = ratio * ratio;
                break;
            case EventKeyword.Decelerated:
                ratio = ratio * (2 - ratio);
                break;
            case EventKeyword.Instant:
                ratio = 1;
                break;
            case EventKeyword.Linear:
            default:
                // Linear interpolation (no change to ratio)
                break;
        }

        // Interpolate value based on property type
        this.currentValue.type = this.initialValue.type;
        this.interpolateValue(ratio);

        // Apply the interpolated value to the property
        this.applyValue();

        // Advance time
        this.currentTime++;

        // Mark as finished if we've reached the target
        if (this.currentTime >= this.changeTime) {
            this._finished = true;
        }
    }

    /**
     * Interpolate between initial and target values
     */
    private interpolateValue(ratio: number): void {
        switch (this.initialValue.type) {
            case PropertyType.Boolean:
                // Boolean values don't interpolate, just use target
                this.currentValue.boolValue = this.targetValue.boolValue;
                break;

            case PropertyType.Int32:
                this.currentValue.intValue = Math.floor(
                    (1 - ratio) * this.initialValue.intValue + ratio * this.targetValue.intValue
                );
                break;

            case PropertyType.Single:
                this.currentValue.floatValue = 
                    (1 - ratio) * this.initialValue.floatValue + ratio * this.targetValue.floatValue;
                break;

            case PropertyType.Enum:
                // Enum values don't interpolate, just use target
                this.currentValue.enumValue = this.targetValue.enumValue;
                break;

            case PropertyType.Vector2:
                this.currentValue.vector2Value.set(
                    (1 - ratio) * this.initialValue.vector2Value.x + ratio * this.targetValue.vector2Value.x,
                    (1 - ratio) * this.initialValue.vector2Value.y + ratio * this.targetValue.vector2Value.y
                );
                break;

            case PropertyType.RGB:
                this.currentValue.rgbValue = {
                    r: (1 - ratio) * this.initialValue.rgbValue.r + ratio * this.targetValue.rgbValue.r,
                    g: (1 - ratio) * this.initialValue.rgbValue.g + ratio * this.targetValue.rgbValue.g,
                    b: (1 - ratio) * this.initialValue.rgbValue.b + ratio * this.targetValue.rgbValue.b
                };
                break;

            case PropertyType.String:
                // String values don't interpolate, just use target
                this.currentValue.stringValue = this.targetValue.stringValue;
                break;

            default:
                warn(`EventExecutor: Unknown property type: ${this.initialValue.type}`);
                break;
        }
    }

    /**
     * Apply the current value to the property
     */
    private applyValue(): void {
        try {
            // Set the property value on the container
            this.propertyContainer.setPropertyValue(this.propertyName, this.currentValue.getValue());
            
            // Also set on binding container if present
            if (this.bindingContainer) {
                this.bindingContainer.setPropertyValue(this.propertyName, this.currentValue.getValue());
            }
        } catch (error) {
            warn(`EventExecutor: Error applying value to property ${this.propertyName}:`, error);
        }
    }

    /**
     * Initialize the executor with event data
     */
    public initialize(
        propertyName: string,
        changeMode: EventKeyword,
        changeTime: number,
        initialValue: TypeSet,
        targetValue: TypeSet
    ): void {
        this.propertyName = propertyName;
        this.changeMode = changeMode;
        this.changeTime = Math.max(1, changeTime); // Ensure minimum time of 1 frame
        this.initialValue = initialValue.clone();
        this.targetValue = targetValue.clone();
        this.currentValue = initialValue.clone();
        this.currentTime = 0;
        this._finished = false;

        // For instant changes, mark as finished immediately
        if (this.changeMode === EventKeyword.Instant) {
            this.currentValue = this.targetValue.clone();
            this.applyValue();
            this._finished = true;
        }
    }

    /**
     * Reset the executor to initial state
     */
    public reset(): void {
        this.currentTime = 0;
        this._finished = false;
        this.currentValue = this.initialValue.clone();
    }

    /**
     * Force completion of the event
     */
    public complete(): void {
        this.currentValue = this.targetValue.clone();
        this.applyValue();
        this._finished = true;
        this.currentTime = this.changeTime;
    }

    /**
     * Get progress ratio (0-1)
     */
    public getProgress(): number {
        if (this.changeTime <= 0) return 1;
        return Math.min(1, this.currentTime / this.changeTime);
    }

    /**
     * Check if this executor matches the given parameters
     */
    public matches(
        propertyContainer: PropertyContainer, 
        bindingContainer: PropertyContainer | undefined, 
        propertyName: string
    ): boolean {
        return this.propertyContainer === propertyContainer &&
               this.bindingContainer === bindingContainer &&
               this.propertyName === propertyName;
    }

    /**
     * Get debug information
     */
    public getDebugInfo(): string {
        return `EventExecutor[${this.propertyName}]: ${this.currentTime}/${this.changeTime} (${this.changeMode}) - ${this.finished ? 'FINISHED' : 'RUNNING'}`;
    }
}

/**
 * Helper functions for event execution
 */
export class EventExecutorHelper {
    /**
     * Create TypeSet from property value
     */
    public static createTypeSet(type: PropertyType, value: any): TypeSet {
        const typeSet = new TypeSet(type);
        typeSet.setValue(value);
        return typeSet;
    }

    /**
     * Get property type from property name (basic implementation)
     */
    public static getPropertyType(propertyName: string): PropertyType {
        // This is a basic implementation - in a full system this would
        // be determined by reflection or property metadata
        const lowerName = propertyName.toLowerCase();
        
        if (lowerName.includes('position') || lowerName.includes('velocity') || lowerName.includes('acceleration')) {
            return PropertyType.Vector2;
        }
        if (lowerName.includes('color') || lowerName.includes('rgb')) {
            return PropertyType.RGB;
        }
        if (lowerName.includes('visible') || lowerName.includes('alive') || lowerName.includes('collision')) {
            return PropertyType.Boolean;
        }
        if (lowerName.includes('count') || lowerName.includes('cycle') || lowerName.includes('life') || lowerName.includes('frame')) {
            return PropertyType.Int32;
        }
        if (lowerName.includes('name') || lowerName.includes('label')) {
            return PropertyType.String;
        }
        
        // Default to float for most numeric properties
        return PropertyType.Single;
    }

    /**
     * Apply change type to target value
     */
    public static applyChangeType(
        changeType: EventKeyword,
        initialValue: TypeSet,
        targetValue: TypeSet
    ): TypeSet {
        const result = targetValue.clone();
        
        if (changeType === EventKeyword.Increase) {
            switch (initialValue.type) {
                case PropertyType.Int32:
                    result.intValue = initialValue.intValue + targetValue.intValue;
                    break;
                case PropertyType.Single:
                    result.floatValue = initialValue.floatValue + targetValue.floatValue;
                    break;
                case PropertyType.Vector2:
                    result.vector2Value = initialValue.vector2Value.clone().add(targetValue.vector2Value);
                    break;
                case PropertyType.RGB:
                    result.rgbValue = {
                        r: initialValue.rgbValue.r + targetValue.rgbValue.r,
                        g: initialValue.rgbValue.g + targetValue.rgbValue.g,
                        b: initialValue.rgbValue.b + targetValue.rgbValue.b
                    };
                    break;
            }
        } else if (changeType === EventKeyword.Decrease) {
            switch (initialValue.type) {
                case PropertyType.Int32:
                    result.intValue = initialValue.intValue - targetValue.intValue;
                    break;
                case PropertyType.Single:
                    result.floatValue = initialValue.floatValue - targetValue.floatValue;
                    break;
                case PropertyType.Vector2:
                    result.vector2Value = initialValue.vector2Value.clone().subtract(targetValue.vector2Value);
                    break;
                case PropertyType.RGB:
                    result.rgbValue = {
                        r: initialValue.rgbValue.r - targetValue.rgbValue.r,
                        g: initialValue.rgbValue.g - targetValue.rgbValue.g,
                        b: initialValue.rgbValue.b - targetValue.rgbValue.b
                    };
                    break;
            }
        }
        // For EventKeyword.Set, just return the target value as-is
        
        return result;
    }
}
