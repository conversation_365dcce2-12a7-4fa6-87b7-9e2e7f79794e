/**
 * CrazyStorm 2.0 Integration for Cocos Creator
 * Global Variable Manager
 * 
 * This manager handles global variables that can be accessed by expressions
 * throughout the CrazyStorm system, including player position (cx, cy) and
 * custom variables.
 */

import { log, warn } from 'cc';
import { CrazyStormFile, VariableResource } from '../core/CrazyStormTypes';

/**
 * Variable change callback type
 */
export type VariableChangeCallback = (label: string, oldValue: number, newValue: number) => void;

/**
 * Variable watcher for monitoring changes
 */
interface VariableWatcher {
    label: string;
    callback: VariableChangeCallback;
    id: number;
}

/**
 * Manager for global variables
 */
export class GlobalVariableManager {
    private variables: Map<string, number> = new Map();
    private watchers: VariableWatcher[] = [];
    private nextWatcherId: number = 1;
    private updateCallbacks: Map<string, () => number> = new Map();

    constructor() {
        // Initialize default variables
        this.initializeDefaults();
    }

    /**
     * Initialize from CrazyStorm file
     */
    public initializeFromFile(file: CrazyStormFile): void {
        // Clear existing variables (except defaults)
        this.clearCustomVariables();

        // Load global variables from file
        file.globals.forEach(globalVar => {
            this.setVariable(globalVar.label, globalVar.value);
        });

        log(`GlobalVariableManager: Initialized with ${file.globals.length} global variables`);
    }

    /**
     * Set variable value
     */
    public setVariable(label: string, value: number): void {
        const oldValue = this.variables.get(label) || 0;
        this.variables.set(label, value);

        // Notify watchers if value changed
        if (oldValue !== value) {
            this.notifyWatchers(label, oldValue, value);
        }
    }

    /**
     * Get variable value
     */
    public getVariable(label: string): number {
        return this.variables.get(label) || 0;
    }

    /**
     * Check if variable exists
     */
    public hasVariable(label: string): boolean {
        return this.variables.has(label);
    }

    /**
     * Get all variables
     */
    public getAllVariables(): Map<string, number> {
        return new Map(this.variables);
    }

    /**
     * Get all variable labels
     */
    public getVariableLabels(): string[] {
        return Array.from(this.variables.keys());
    }

    /**
     * Add variable with initial value
     */
    public addVariable(label: string, initialValue: number = 0): void {
        if (!this.variables.has(label)) {
            this.setVariable(label, initialValue);
            log(`GlobalVariableManager: Added variable '${label}' with value ${initialValue}`);
        } else {
            warn(`GlobalVariableManager: Variable '${label}' already exists`);
        }
    }

    /**
     * Remove variable
     */
    public removeVariable(label: string): boolean {
        if (this.isDefaultVariable(label)) {
            warn(`GlobalVariableManager: Cannot remove default variable '${label}'`);
            return false;
        }

        const removed = this.variables.delete(label);
        if (removed) {
            log(`GlobalVariableManager: Removed variable '${label}'`);
        }
        return removed;
    }

    /**
     * Set player position (updates cx, cy)
     */
    public setPlayerPosition(x: number, y: number): void {
        this.setVariable('cx', x);
        this.setVariable('cy', y);
    }

    /**
     * Get player position
     */
    public getPlayerPosition(): { x: number; y: number } {
        return {
            x: this.getVariable('cx'),
            y: this.getVariable('cy')
        };
    }

    /**
     * Watch variable for changes
     */
    public watchVariable(label: string, callback: VariableChangeCallback): number {
        const watcher: VariableWatcher = {
            label,
            callback,
            id: this.nextWatcherId++
        };

        this.watchers.push(watcher);
        return watcher.id;
    }

    /**
     * Stop watching variable
     */
    public unwatchVariable(watcherId: number): boolean {
        const index = this.watchers.findIndex(w => w.id === watcherId);
        if (index >= 0) {
            this.watchers.splice(index, 1);
            return true;
        }
        return false;
    }

    /**
     * Set update callback for dynamic variables
     */
    public setUpdateCallback(label: string, callback: () => number): void {
        this.updateCallbacks.set(label, callback);
    }

    /**
     * Remove update callback
     */
    public removeUpdateCallback(label: string): void {
        this.updateCallbacks.delete(label);
    }

    /**
     * Update dynamic variables
     */
    public update(): void {
        this.updateCallbacks.forEach((callback, label) => {
            try {
                const newValue = callback();
                this.setVariable(label, newValue);
            } catch (err) {
                warn(`GlobalVariableManager: Error updating variable '${label}':`, err);
            }
        });
    }

    /**
     * Increment variable value
     */
    public incrementVariable(label: string, amount: number = 1): number {
        const currentValue = this.getVariable(label);
        const newValue = currentValue + amount;
        this.setVariable(label, newValue);
        return newValue;
    }

    /**
     * Decrement variable value
     */
    public decrementVariable(label: string, amount: number = 1): number {
        return this.incrementVariable(label, -amount);
    }

    /**
     * Multiply variable value
     */
    public multiplyVariable(label: string, factor: number): number {
        const currentValue = this.getVariable(label);
        const newValue = currentValue * factor;
        this.setVariable(label, newValue);
        return newValue;
    }

    /**
     * Set variable to minimum of current value and given value
     */
    public minVariable(label: string, value: number): number {
        const currentValue = this.getVariable(label);
        const newValue = Math.min(currentValue, value);
        this.setVariable(label, newValue);
        return newValue;
    }

    /**
     * Set variable to maximum of current value and given value
     */
    public maxVariable(label: string, value: number): number {
        const currentValue = this.getVariable(label);
        const newValue = Math.max(currentValue, value);
        this.setVariable(label, newValue);
        return newValue;
    }

    /**
     * Clamp variable value between min and max
     */
    public clampVariable(label: string, min: number, max: number): number {
        const currentValue = this.getVariable(label);
        const newValue = Math.max(min, Math.min(max, currentValue));
        this.setVariable(label, newValue);
        return newValue;
    }

    /**
     * Get variable statistics
     */
    public getStats(): {
        totalVariables: number;
        defaultVariables: number;
        customVariables: number;
        watchers: number;
        updateCallbacks: number;
    } {
        const defaultVars = ['cx', 'cy'];
        const customVarCount = this.variables.size - defaultVars.filter(v => this.variables.has(v)).length;

        return {
            totalVariables: this.variables.size,
            defaultVariables: defaultVars.filter(v => this.variables.has(v)).length,
            customVariables: customVarCount,
            watchers: this.watchers.length,
            updateCallbacks: this.updateCallbacks.size
        };
    }

    /**
     * Export variables to object
     */
    public exportVariables(): { [key: string]: number } {
        const exported: { [key: string]: number } = {};
        this.variables.forEach((value, label) => {
            exported[label] = value;
        });
        return exported;
    }

    /**
     * Import variables from object
     */
    public importVariables(variables: { [key: string]: number }): void {
        Object.keys(variables).forEach(label => {
            this.setVariable(label, variables[label]);
        });
        log(`GlobalVariableManager: Imported ${Object.keys(variables).length} variables`);
    }

    /**
     * Reset all variables to default values
     */
    public reset(): void {
        this.variables.clear();
        this.initializeDefaults();
        log('GlobalVariableManager: Reset to defaults');
    }

    /**
     * Destroy and clean up
     */
    public destroy(): void {
        this.variables.clear();
        this.watchers = [];
        this.updateCallbacks.clear();
        log('GlobalVariableManager: Destroyed');
    }

    // Private helper methods

    /**
     * Initialize default variables
     */
    private initializeDefaults(): void {
        this.variables.set('cx', 0); // Player X position
        this.variables.set('cy', 0); // Player Y position
    }

    /**
     * Clear custom variables (keep defaults)
     */
    private clearCustomVariables(): void {
        const defaultVars = ['cx', 'cy'];
        const toRemove: string[] = [];

        this.variables.forEach((value, label) => {
            if (!defaultVars.includes(label)) {
                toRemove.push(label);
            }
        });

        toRemove.forEach(label => {
            this.variables.delete(label);
        });
    }

    /**
     * Check if variable is a default variable
     */
    private isDefaultVariable(label: string): boolean {
        return ['cx', 'cy'].includes(label);
    }

    /**
     * Notify watchers of variable changes
     */
    private notifyWatchers(label: string, oldValue: number, newValue: number): void {
        this.watchers.forEach(watcher => {
            if (watcher.label === label) {
                try {
                    watcher.callback(label, oldValue, newValue);
                } catch (err) {
                    warn(`GlobalVariableManager: Error in watcher callback for '${label}':`, err);
                }
            }
        });
    }
}
