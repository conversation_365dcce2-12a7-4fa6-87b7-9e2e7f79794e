export const enum RET_CODE {
  NO_ERROR = "NO_ERROR",
  INVALID_OPENID = "INVALID_OPENID",
  NOT_IN_WHITE_LIST = "NOT_IN_WHITE_LIST",
  NOT_RIGHT = "NOT_RIGHT",
  INVALID_PASSWORD = "INVALID_PASSWORD",
  INVALID_INPUT = "INVALID_INPUT",
  SYSTEM_ERROR = "SYSTEM_ERROR",
  INVALID_TOKEN = "INVALID_TOKEN",
  RELOGIN = "RELOGIN",
  NAME_TOO_SHORT = "NAME_TOO_SHORT",
  NAME_TOO_LONG = "NAME_TOO_LONG",
  NAME_INVALID = "NAME_INVALID",
  NOT_AUTHED = "NOT_AUTHED",
  DATA_TOO_LONG = "DATA_TOO_LONG",
  NOT_EXIST = "NOT_EXIST",
  TOO_FREQUENT = "TOO_FREQUENT",
  TIMEOUT = "TIMEOUT",
  INVALID_SEQ = "INVALID_SEQ",
  IN_PROCESS = "IN_PROCESS",
  NOT_FREE_STATUS = "NOT_FREE_STATUS",
  CLIENT_VERSION_TOO_OLD = "CLIENT_VERSION_TOO_OLD",
  CLIENT_VERSION_TOO_NEW = "CLIENT_VERSION_TOO_NEW",
  CLIENT_VERSION_BACK_OLD = "CLIENT_VERSION_BACK_OLD",
  CARD_NOT_UNLOCK = "CARD_NOT_UNLOCK",
  DEFAULT_CARDGROUP_NOSET = "DEFAULT_CARDGROUP_NOSET",
  CARD_NOT_CONFIG = "CARD_NOT_CONFIG",
  CARD_GROUP_INDEX_INVALID = "CARD_GROUP_INDEX_INVALID",
  CARD_GROUP_NAME_INVALID = "CARD_GROUP_NAME_INVALID",
  CARD_GROUP_NAME_TOO_LONG = "CARD_GROUP_NAME_TOO_LONG",
  CARD_GROUP_FORCE_ID_INVALID = "CARD_GROUP_FORCE_ID_INVALID",
  CARD_GROUP_INDEX_NOT_CONFIG = "CARD_GROUP_INDEX_NOT_CONFIG",
  CARD_GROUP_CARD_DUPLICATE = "CARD_GROUP_CARD_DUPLICATE",
  CARD_GROUP_CARD_NUM_INVALID = "CARD_GROUP_CARD_NUM_INVALID",
  CUSTOM_ROOM_IS_FULL = "CUSTOM_ROOM_IS_FULL",
  CUSTOM_ROOM_POSITION_NOT_EMPTY = "CUSTOM_ROOM_POSITION_NOT_EMPTY",
  CUSTOM_ROOM_CAMP_FULL = "CUSTOM_ROOM_CAMP_FULL",
  CUSTOM_ROOM_NOT_EXIST = "CUSTOM_ROOM_NOT_EXIST",
  NOT_IN_CUSTOM_ROOM = "NOT_IN_CUSTOM_ROOM",
  CUSTOM_ROOM_IN_FIGHTING = "CUSTOM_ROOM_IN_FIGHTING",
  CUSTOM_ROOM_PLAYER_NOT_ENOUGH = "CUSTOM_ROOM_PLAYER_NOT_ENOUGH",
  CUSTOM_ROOM_NOT_CREATOR = "CUSTOM_ROOM_NOT_CREATOR",
  ALREADY_IN_CUSTOM_ROOM = "ALREADY_IN_CUSTOM_ROOM",
  CUSTOM_ROOM_HAS_OFFLINE_PLAYER = "CUSTOM_ROOM_HAS_OFFLINE_PLAYER",
  CUSTOM_ROOM_CHANGE_POS_WITH_PLAYER_NOT_SUPPORT = "CUSTOM_ROOM_CHANGE_POS_WITH_PLAYER_NOT_SUPPORT",
  CUSTOM_ROOM_VERSION_NOT_MATCH = "CUSTOM_ROOM_VERSION_NOT_MATCH",
  NOT_IN_GAME = "NOT_IN_GAME",
  GAME_NOT_IN_PREPARE = "GAME_NOT_IN_PREPARE",
  GAME_HAS_READY = "GAME_HAS_READY",
  GAME_NOT_READY = "GAME_NOT_READY",
  GAME_CANNOT_READY = "GAME_CANNOT_READY",
  ROOM_NOT_EXIST = "ROOM_NOT_EXIST",
  ALREADY_IN_ROOM = "ALREADY_IN_ROOM",
  ROOM_NUM_LIMIT = "ROOM_NUM_LIMIT",
  ROOM_HAS_START = "ROOM_HAS_START",
  ROOM_IS_FULL = "ROOM_IS_FULL",
  NOT_IN_ROOM = "NOT_IN_ROOM",
  ROOM_SIDE_FULL = "ROOM_SIDE_FULL",
  ROOM_POSITION_NOT_EMPTY = "ROOM_POSITION_NOT_EMPTY",
  ROOM_NOT_CORRECT = "ROOM_NOT_CORRECT",
  ROOM_NOT_CREATOR = "ROOM_NOT_CREATOR",
  ROOM_NOT_READY = "ROOM_NOT_READY",
  ROOM_CANNOT_SET_OTHER_PLAYER = "ROOM_CANNOT_SET_OTHER_PLAYER",
  ROOM_CANNOT_ADD_AI = "ROOM_CANNOT_ADD_AI",
}

export const encodeRET_CODE: { [key: string]: number } = {
  NO_ERROR: 0,
  INVALID_OPENID: 1,
  NOT_IN_WHITE_LIST: 2,
  NOT_RIGHT: 3,
  INVALID_PASSWORD: 4,
  INVALID_INPUT: 5,
  SYSTEM_ERROR: 6,
  INVALID_TOKEN: 7,
  RELOGIN: 8,
  NAME_TOO_SHORT: 9,
  NAME_TOO_LONG: 10,
  NAME_INVALID: 11,
  NOT_AUTHED: 12,
  DATA_TOO_LONG: 1002,
  NOT_EXIST: 1003,
  TOO_FREQUENT: 1004,
  TIMEOUT: 1005,
  INVALID_SEQ: 1006,
  IN_PROCESS: 1007,
  NOT_FREE_STATUS: 1008,
  CLIENT_VERSION_TOO_OLD: 10007,
  CLIENT_VERSION_TOO_NEW: 10008,
  CLIENT_VERSION_BACK_OLD: 10009,
  CARD_NOT_UNLOCK: 10107,
  DEFAULT_CARDGROUP_NOSET: 10110,
  CARD_NOT_CONFIG: 10111,
  CARD_GROUP_INDEX_INVALID: 10112,
  CARD_GROUP_NAME_INVALID: 10113,
  CARD_GROUP_NAME_TOO_LONG: 10114,
  CARD_GROUP_FORCE_ID_INVALID: 10115,
  CARD_GROUP_INDEX_NOT_CONFIG: 10116,
  CARD_GROUP_CARD_DUPLICATE: 10117,
  CARD_GROUP_CARD_NUM_INVALID: 10118,
  CUSTOM_ROOM_IS_FULL: 10201,
  CUSTOM_ROOM_POSITION_NOT_EMPTY: 10202,
  CUSTOM_ROOM_CAMP_FULL: 10203,
  CUSTOM_ROOM_NOT_EXIST: 10204,
  NOT_IN_CUSTOM_ROOM: 10205,
  CUSTOM_ROOM_IN_FIGHTING: 10206,
  CUSTOM_ROOM_PLAYER_NOT_ENOUGH: 10207,
  CUSTOM_ROOM_NOT_CREATOR: 10208,
  ALREADY_IN_CUSTOM_ROOM: 10209,
  CUSTOM_ROOM_HAS_OFFLINE_PLAYER: 10210,
  CUSTOM_ROOM_CHANGE_POS_WITH_PLAYER_NOT_SUPPORT: 10211,
  CUSTOM_ROOM_VERSION_NOT_MATCH: 10212,
  NOT_IN_GAME: 10501,
  GAME_NOT_IN_PREPARE: 10506,
  GAME_HAS_READY: 10507,
  GAME_NOT_READY: 10511,
  GAME_CANNOT_READY: 10512,
  ROOM_NOT_EXIST: 10701,
  ALREADY_IN_ROOM: 10702,
  ROOM_NUM_LIMIT: 10703,
  ROOM_HAS_START: 10704,
  ROOM_IS_FULL: 10705,
  NOT_IN_ROOM: 10706,
  ROOM_SIDE_FULL: 10707,
  ROOM_POSITION_NOT_EMPTY: 10708,
  ROOM_NOT_CORRECT: 10709,
  ROOM_NOT_CREATOR: 10710,
  ROOM_NOT_READY: 10711,
  ROOM_CANNOT_SET_OTHER_PLAYER: 10712,
  ROOM_CANNOT_ADD_AI: 10713,
};

export const decodeRET_CODE: { [key: number]: RET_CODE } = {
  0: RET_CODE.NO_ERROR,
  1: RET_CODE.INVALID_OPENID,
  2: RET_CODE.NOT_IN_WHITE_LIST,
  3: RET_CODE.NOT_RIGHT,
  4: RET_CODE.INVALID_PASSWORD,
  5: RET_CODE.INVALID_INPUT,
  6: RET_CODE.SYSTEM_ERROR,
  7: RET_CODE.INVALID_TOKEN,
  8: RET_CODE.RELOGIN,
  9: RET_CODE.NAME_TOO_SHORT,
  10: RET_CODE.NAME_TOO_LONG,
  11: RET_CODE.NAME_INVALID,
  12: RET_CODE.NOT_AUTHED,
  1002: RET_CODE.DATA_TOO_LONG,
  1003: RET_CODE.NOT_EXIST,
  1004: RET_CODE.TOO_FREQUENT,
  1005: RET_CODE.TIMEOUT,
  1006: RET_CODE.INVALID_SEQ,
  1007: RET_CODE.IN_PROCESS,
  1008: RET_CODE.NOT_FREE_STATUS,
  10007: RET_CODE.CLIENT_VERSION_TOO_OLD,
  10008: RET_CODE.CLIENT_VERSION_TOO_NEW,
  10009: RET_CODE.CLIENT_VERSION_BACK_OLD,
  10107: RET_CODE.CARD_NOT_UNLOCK,
  10110: RET_CODE.DEFAULT_CARDGROUP_NOSET,
  10111: RET_CODE.CARD_NOT_CONFIG,
  10112: RET_CODE.CARD_GROUP_INDEX_INVALID,
  10113: RET_CODE.CARD_GROUP_NAME_INVALID,
  10114: RET_CODE.CARD_GROUP_NAME_TOO_LONG,
  10115: RET_CODE.CARD_GROUP_FORCE_ID_INVALID,
  10116: RET_CODE.CARD_GROUP_INDEX_NOT_CONFIG,
  10117: RET_CODE.CARD_GROUP_CARD_DUPLICATE,
  10118: RET_CODE.CARD_GROUP_CARD_NUM_INVALID,
  10201: RET_CODE.CUSTOM_ROOM_IS_FULL,
  10202: RET_CODE.CUSTOM_ROOM_POSITION_NOT_EMPTY,
  10203: RET_CODE.CUSTOM_ROOM_CAMP_FULL,
  10204: RET_CODE.CUSTOM_ROOM_NOT_EXIST,
  10205: RET_CODE.NOT_IN_CUSTOM_ROOM,
  10206: RET_CODE.CUSTOM_ROOM_IN_FIGHTING,
  10207: RET_CODE.CUSTOM_ROOM_PLAYER_NOT_ENOUGH,
  10208: RET_CODE.CUSTOM_ROOM_NOT_CREATOR,
  10209: RET_CODE.ALREADY_IN_CUSTOM_ROOM,
  10210: RET_CODE.CUSTOM_ROOM_HAS_OFFLINE_PLAYER,
  10211: RET_CODE.CUSTOM_ROOM_CHANGE_POS_WITH_PLAYER_NOT_SUPPORT,
  10212: RET_CODE.CUSTOM_ROOM_VERSION_NOT_MATCH,
  10501: RET_CODE.NOT_IN_GAME,
  10506: RET_CODE.GAME_NOT_IN_PREPARE,
  10507: RET_CODE.GAME_HAS_READY,
  10511: RET_CODE.GAME_NOT_READY,
  10512: RET_CODE.GAME_CANNOT_READY,
  10701: RET_CODE.ROOM_NOT_EXIST,
  10702: RET_CODE.ALREADY_IN_ROOM,
  10703: RET_CODE.ROOM_NUM_LIMIT,
  10704: RET_CODE.ROOM_HAS_START,
  10705: RET_CODE.ROOM_IS_FULL,
  10706: RET_CODE.NOT_IN_ROOM,
  10707: RET_CODE.ROOM_SIDE_FULL,
  10708: RET_CODE.ROOM_POSITION_NOT_EMPTY,
  10709: RET_CODE.ROOM_NOT_CORRECT,
  10710: RET_CODE.ROOM_NOT_CREATOR,
  10711: RET_CODE.ROOM_NOT_READY,
  10712: RET_CODE.ROOM_CANNOT_SET_OTHER_PLAYER,
  10713: RET_CODE.ROOM_CANNOT_ADD_AI,
};

export const enum ACCOUNT_TYPE {
  ACCOUNT_RAW = "ACCOUNT_RAW",
  ACCOUNT_QQ = "ACCOUNT_QQ",
  ACCOUNT_WX = "ACCOUNT_WX",
  ACCOUNT_FACEBOOK = "ACCOUNT_FACEBOOK",
  ACCOUNT_GOOGLE = "ACCOUNT_GOOGLE",
  ACCOUNT_MAIL = "ACCOUNT_MAIL",
  ACCOUNT_APPLEID = "ACCOUNT_APPLEID",
}

export const encodeACCOUNT_TYPE: { [key: string]: number } = {
  ACCOUNT_RAW: 0,
  ACCOUNT_QQ: 1,
  ACCOUNT_WX: 2,
  ACCOUNT_FACEBOOK: 3,
  ACCOUNT_GOOGLE: 4,
  ACCOUNT_MAIL: 5,
  ACCOUNT_APPLEID: 6,
};

export const decodeACCOUNT_TYPE: { [key: number]: ACCOUNT_TYPE } = {
  0: ACCOUNT_TYPE.ACCOUNT_RAW,
  1: ACCOUNT_TYPE.ACCOUNT_QQ,
  2: ACCOUNT_TYPE.ACCOUNT_WX,
  3: ACCOUNT_TYPE.ACCOUNT_FACEBOOK,
  4: ACCOUNT_TYPE.ACCOUNT_GOOGLE,
  5: ACCOUNT_TYPE.ACCOUNT_MAIL,
  6: ACCOUNT_TYPE.ACCOUNT_APPLEID,
};

export const enum AREA_STATUS_TYPE {
  AREA_STATUSCLOSED = "AREA_STATUSCLOSED",
  AREA_STATUSFREE = "AREA_STATUSFREE",
  AREA_STATUSHIGH = "AREA_STATUSHIGH",
  AREA_STATUSFULL = "AREA_STATUSFULL",
}

export const encodeAREA_STATUS_TYPE: { [key: string]: number } = {
  AREA_STATUSCLOSED: 0,
  AREA_STATUSFREE: 1,
  AREA_STATUSHIGH: 2,
  AREA_STATUSFULL: 3,
};

export const decodeAREA_STATUS_TYPE: { [key: number]: AREA_STATUS_TYPE } = {
  0: AREA_STATUS_TYPE.AREA_STATUSCLOSED,
  1: AREA_STATUS_TYPE.AREA_STATUSFREE,
  2: AREA_STATUS_TYPE.AREA_STATUSHIGH,
  3: AREA_STATUS_TYPE.AREA_STATUSFULL,
};

export const enum GENDER_TYPE {
  GENDER_NONE = "GENDER_NONE",
  GENDER_MALE = "GENDER_MALE",
  GENDER_FEMALE = "GENDER_FEMALE",
}

export const encodeGENDER_TYPE: { [key: string]: number } = {
  GENDER_NONE: 0,
  GENDER_MALE: 1,
  GENDER_FEMALE: 2,
};

export const decodeGENDER_TYPE: { [key: number]: GENDER_TYPE } = {
  0: GENDER_TYPE.GENDER_NONE,
  1: GENDER_TYPE.GENDER_MALE,
  2: GENDER_TYPE.GENDER_FEMALE,
};

export const enum GAME_MODE {
  NONE = "NONE",
  P1V1 = "P1V1",
  P2V2 = "P2V2",
  P4V4 = "P4V4",
}

export const encodeGAME_MODE: { [key: string]: number } = {
  NONE: 0,
  P1V1: 1,
  P2V2: 2,
  P4V4: 3,
};

export const decodeGAME_MODE: { [key: number]: GAME_MODE } = {
  0: GAME_MODE.NONE,
  1: GAME_MODE.P1V1,
  2: GAME_MODE.P2V2,
  3: GAME_MODE.P4V4,
};

export const enum CUSTOM_ROOM_STATUS {
  OPEN = "OPEN",
  CREATING = "CREATING",
  FIGHTING = "FIGHTING",
}

export const encodeCUSTOM_ROOM_STATUS: { [key: string]: number } = {
  OPEN: 0,
  CREATING: 2,
  FIGHTING: 3,
};

export const decodeCUSTOM_ROOM_STATUS: { [key: number]: CUSTOM_ROOM_STATUS } = {
  0: CUSTOM_ROOM_STATUS.OPEN,
  2: CUSTOM_ROOM_STATUS.CREATING,
  3: CUSTOM_ROOM_STATUS.FIGHTING,
};

export const enum GAME_RESULT {
  NONE = "NONE",
  RED_WIN = "RED_WIN",
  BLUE_WIN = "BLUE_WIN",
  DRAW = "DRAW",
}

export const encodeGAME_RESULT: { [key: string]: number } = {
  NONE: 0,
  RED_WIN: 1,
  BLUE_WIN: 2,
  DRAW: 3,
};

export const decodeGAME_RESULT: { [key: number]: GAME_RESULT } = {
  0: GAME_RESULT.NONE,
  1: GAME_RESULT.RED_WIN,
  2: GAME_RESULT.BLUE_WIN,
  3: GAME_RESULT.DRAW,
};

export const enum ROLE_GAME_STATUS {
  NONE = "NONE",
  IN_TEAM = "IN_TEAM",
  IN_CUSTOM_ROOM = "IN_CUSTOM_ROOM",
  IN_FIGHTING = "IN_FIGHTING",
  IN_GUILDING = "IN_GUILDING",
}

export const encodeROLE_GAME_STATUS: { [key: string]: number } = {
  NONE: 0,
  IN_TEAM: 1,
  IN_CUSTOM_ROOM: 2,
  IN_FIGHTING: 3,
  IN_GUILDING: 4,
};

export const decodeROLE_GAME_STATUS: { [key: number]: ROLE_GAME_STATUS } = {
  0: ROLE_GAME_STATUS.NONE,
  1: ROLE_GAME_STATUS.IN_TEAM,
  2: ROLE_GAME_STATUS.IN_CUSTOM_ROOM,
  3: ROLE_GAME_STATUS.IN_FIGHTING,
  4: ROLE_GAME_STATUS.IN_GUILDING,
};

export interface RoleBaseAttr {
  uin?: Long;
  openid?: string;
  area_id?: number;
  money?: Long;
  gold?: Long;
  game_money?: Long;
  level?: number;
  xp?: number;
}

export function encodeRoleBaseAttr(message: RoleBaseAttr): Uint8Array {
  let bb = popByteBuffer();
  _encodeRoleBaseAttr(message, bb);
  return toUint8Array(bb);
}

function _encodeRoleBaseAttr(message: RoleBaseAttr, bb: ByteBuffer): void {
  // optional fixed64 uin = 1;
  let $uin = message.uin;
  if ($uin !== undefined) {
    writeVarint32(bb, 9);
    writeInt64(bb, $uin);
  }

  // optional string openid = 2;
  let $openid = message.openid;
  if ($openid !== undefined) {
    writeVarint32(bb, 18);
    writeString(bb, $openid);
  }

  // optional int32 area_id = 3;
  let $area_id = message.area_id;
  if ($area_id !== undefined) {
    writeVarint32(bb, 24);
    writeVarint64(bb, intToLong($area_id));
  }

  // optional int64 money = 4;
  let $money = message.money;
  if ($money !== undefined) {
    writeVarint32(bb, 32);
    writeVarint64(bb, $money);
  }

  // optional int64 gold = 5;
  let $gold = message.gold;
  if ($gold !== undefined) {
    writeVarint32(bb, 40);
    writeVarint64(bb, $gold);
  }

  // optional int64 game_money = 6;
  let $game_money = message.game_money;
  if ($game_money !== undefined) {
    writeVarint32(bb, 48);
    writeVarint64(bb, $game_money);
  }

  // optional int32 level = 7;
  let $level = message.level;
  if ($level !== undefined) {
    writeVarint32(bb, 56);
    writeVarint64(bb, intToLong($level));
  }

  // optional int32 xp = 8;
  let $xp = message.xp;
  if ($xp !== undefined) {
    writeVarint32(bb, 64);
    writeVarint64(bb, intToLong($xp));
  }
}

export function decodeRoleBaseAttr(binary: Uint8Array): RoleBaseAttr {
  return _decodeRoleBaseAttr(wrapByteBuffer(binary));
}

function _decodeRoleBaseAttr(bb: ByteBuffer): RoleBaseAttr {
  let message: RoleBaseAttr = {} as any;

  end_of_message: while (!isAtEnd(bb)) {
    let tag = readVarint32(bb);

    switch (tag >>> 3) {
      case 0:
        break end_of_message;

      // optional fixed64 uin = 1;
      case 1: {
        message.uin = readInt64(bb, /* unsigned */ true);
        break;
      }

      // optional string openid = 2;
      case 2: {
        message.openid = readString(bb, readVarint32(bb));
        break;
      }

      // optional int32 area_id = 3;
      case 3: {
        message.area_id = readVarint32(bb);
        break;
      }

      // optional int64 money = 4;
      case 4: {
        message.money = readVarint64(bb, /* unsigned */ false);
        break;
      }

      // optional int64 gold = 5;
      case 5: {
        message.gold = readVarint64(bb, /* unsigned */ false);
        break;
      }

      // optional int64 game_money = 6;
      case 6: {
        message.game_money = readVarint64(bb, /* unsigned */ false);
        break;
      }

      // optional int32 level = 7;
      case 7: {
        message.level = readVarint32(bb);
        break;
      }

      // optional int32 xp = 8;
      case 8: {
        message.xp = readVarint32(bb);
        break;
      }

      default:
        skipUnknownField(bb, tag & 7);
    }
  }

  return message;
}

export interface RoleExtAttr {
  gender?: GENDER_TYPE;
  nick_name?: string;
  picture?: string;
  client_ver?: number;
  client_ver_str?: string;
  create_time?: number;
  last_login_time?: Long;
  last_login_ip?: string;
  last_logout_time?: number;
  total_online_time?: number;
  bit_flag?: Long;
}

export function encodeRoleExtAttr(message: RoleExtAttr): Uint8Array {
  let bb = popByteBuffer();
  _encodeRoleExtAttr(message, bb);
  return toUint8Array(bb);
}

function _encodeRoleExtAttr(message: RoleExtAttr, bb: ByteBuffer): void {
  // optional GENDER_TYPE gender = 1;
  let $gender = message.gender;
  if ($gender !== undefined) {
    writeVarint32(bb, 8);
    writeVarint32(bb, encodeGENDER_TYPE[$gender]);
  }

  // optional string nick_name = 2;
  let $nick_name = message.nick_name;
  if ($nick_name !== undefined) {
    writeVarint32(bb, 18);
    writeString(bb, $nick_name);
  }

  // optional string picture = 3;
  let $picture = message.picture;
  if ($picture !== undefined) {
    writeVarint32(bb, 26);
    writeString(bb, $picture);
  }

  // optional uint32 client_ver = 4;
  let $client_ver = message.client_ver;
  if ($client_ver !== undefined) {
    writeVarint32(bb, 32);
    writeVarint32(bb, $client_ver);
  }

  // optional string client_ver_str = 5;
  let $client_ver_str = message.client_ver_str;
  if ($client_ver_str !== undefined) {
    writeVarint32(bb, 42);
    writeString(bb, $client_ver_str);
  }

  // optional uint32 create_time = 6;
  let $create_time = message.create_time;
  if ($create_time !== undefined) {
    writeVarint32(bb, 48);
    writeVarint32(bb, $create_time);
  }

  // optional uint64 last_login_time = 7;
  let $last_login_time = message.last_login_time;
  if ($last_login_time !== undefined) {
    writeVarint32(bb, 56);
    writeVarint64(bb, $last_login_time);
  }

  // optional string last_login_ip = 8;
  let $last_login_ip = message.last_login_ip;
  if ($last_login_ip !== undefined) {
    writeVarint32(bb, 66);
    writeString(bb, $last_login_ip);
  }

  // optional uint32 last_logout_time = 9;
  let $last_logout_time = message.last_logout_time;
  if ($last_logout_time !== undefined) {
    writeVarint32(bb, 72);
    writeVarint32(bb, $last_logout_time);
  }

  // optional uint32 total_online_time = 10;
  let $total_online_time = message.total_online_time;
  if ($total_online_time !== undefined) {
    writeVarint32(bb, 80);
    writeVarint32(bb, $total_online_time);
  }

  // optional uint64 bit_flag = 11;
  let $bit_flag = message.bit_flag;
  if ($bit_flag !== undefined) {
    writeVarint32(bb, 88);
    writeVarint64(bb, $bit_flag);
  }
}

export function decodeRoleExtAttr(binary: Uint8Array): RoleExtAttr {
  return _decodeRoleExtAttr(wrapByteBuffer(binary));
}

function _decodeRoleExtAttr(bb: ByteBuffer): RoleExtAttr {
  let message: RoleExtAttr = {} as any;

  end_of_message: while (!isAtEnd(bb)) {
    let tag = readVarint32(bb);

    switch (tag >>> 3) {
      case 0:
        break end_of_message;

      // optional GENDER_TYPE gender = 1;
      case 1: {
        message.gender = decodeGENDER_TYPE[readVarint32(bb)];
        break;
      }

      // optional string nick_name = 2;
      case 2: {
        message.nick_name = readString(bb, readVarint32(bb));
        break;
      }

      // optional string picture = 3;
      case 3: {
        message.picture = readString(bb, readVarint32(bb));
        break;
      }

      // optional uint32 client_ver = 4;
      case 4: {
        message.client_ver = readVarint32(bb) >>> 0;
        break;
      }

      // optional string client_ver_str = 5;
      case 5: {
        message.client_ver_str = readString(bb, readVarint32(bb));
        break;
      }

      // optional uint32 create_time = 6;
      case 6: {
        message.create_time = readVarint32(bb) >>> 0;
        break;
      }

      // optional uint64 last_login_time = 7;
      case 7: {
        message.last_login_time = readVarint64(bb, /* unsigned */ true);
        break;
      }

      // optional string last_login_ip = 8;
      case 8: {
        message.last_login_ip = readString(bb, readVarint32(bb));
        break;
      }

      // optional uint32 last_logout_time = 9;
      case 9: {
        message.last_logout_time = readVarint32(bb) >>> 0;
        break;
      }

      // optional uint32 total_online_time = 10;
      case 10: {
        message.total_online_time = readVarint32(bb) >>> 0;
        break;
      }

      // optional uint64 bit_flag = 11;
      case 11: {
        message.bit_flag = readVarint64(bb, /* unsigned */ true);
        break;
      }

      default:
        skipUnknownField(bb, tag & 7);
    }
  }

  return message;
}

export interface ClientData {
  data?: Uint8Array;
}

export function encodeClientData(message: ClientData): Uint8Array {
  let bb = popByteBuffer();
  _encodeClientData(message, bb);
  return toUint8Array(bb);
}

function _encodeClientData(message: ClientData, bb: ByteBuffer): void {
  // optional bytes data = 1;
  let $data = message.data;
  if ($data !== undefined) {
    writeVarint32(bb, 10);
    writeVarint32(bb, $data.length), writeBytes(bb, $data);
  }
}

export function decodeClientData(binary: Uint8Array): ClientData {
  return _decodeClientData(wrapByteBuffer(binary));
}

function _decodeClientData(bb: ByteBuffer): ClientData {
  let message: ClientData = {} as any;

  end_of_message: while (!isAtEnd(bb)) {
    let tag = readVarint32(bb);

    switch (tag >>> 3) {
      case 0:
        break end_of_message;

      // optional bytes data = 1;
      case 1: {
        message.data = readBytes(bb, readVarint32(bb));
        break;
      }

      default:
        skipUnknownField(bb, tag & 7);
    }
  }

  return message;
}

export interface CardGroupItem {
  card_id?: number;
}

export function encodeCardGroupItem(message: CardGroupItem): Uint8Array {
  let bb = popByteBuffer();
  _encodeCardGroupItem(message, bb);
  return toUint8Array(bb);
}

function _encodeCardGroupItem(message: CardGroupItem, bb: ByteBuffer): void {
  // optional int32 card_id = 1;
  let $card_id = message.card_id;
  if ($card_id !== undefined) {
    writeVarint32(bb, 8);
    writeVarint64(bb, intToLong($card_id));
  }
}

export function decodeCardGroupItem(binary: Uint8Array): CardGroupItem {
  return _decodeCardGroupItem(wrapByteBuffer(binary));
}

function _decodeCardGroupItem(bb: ByteBuffer): CardGroupItem {
  let message: CardGroupItem = {} as any;

  end_of_message: while (!isAtEnd(bb)) {
    let tag = readVarint32(bb);

    switch (tag >>> 3) {
      case 0:
        break end_of_message;

      // optional int32 card_id = 1;
      case 1: {
        message.card_id = readVarint32(bb);
        break;
      }

      default:
        skipUnknownField(bb, tag & 7);
    }
  }

  return message;
}

export interface CardGroupInfo {
  index?: number;
  name?: string;
  force_id?: number;
  cards?: CardGroupItem[];
}

export function encodeCardGroupInfo(message: CardGroupInfo): Uint8Array {
  let bb = popByteBuffer();
  _encodeCardGroupInfo(message, bb);
  return toUint8Array(bb);
}

function _encodeCardGroupInfo(message: CardGroupInfo, bb: ByteBuffer): void {
  // optional int32 index = 1;
  let $index = message.index;
  if ($index !== undefined) {
    writeVarint32(bb, 8);
    writeVarint64(bb, intToLong($index));
  }

  // optional string name = 2;
  let $name = message.name;
  if ($name !== undefined) {
    writeVarint32(bb, 18);
    writeString(bb, $name);
  }

  // optional int32 force_id = 3;
  let $force_id = message.force_id;
  if ($force_id !== undefined) {
    writeVarint32(bb, 24);
    writeVarint64(bb, intToLong($force_id));
  }

  // repeated CardGroupItem cards = 4;
  let array$cards = message.cards;
  if (array$cards !== undefined) {
    for (let value of array$cards) {
      writeVarint32(bb, 34);
      let nested = popByteBuffer();
      _encodeCardGroupItem(value, nested);
      writeVarint32(bb, nested.limit);
      writeByteBuffer(bb, nested);
      pushByteBuffer(nested);
    }
  }
}

export function decodeCardGroupInfo(binary: Uint8Array): CardGroupInfo {
  return _decodeCardGroupInfo(wrapByteBuffer(binary));
}

function _decodeCardGroupInfo(bb: ByteBuffer): CardGroupInfo {
  let message: CardGroupInfo = {} as any;

  end_of_message: while (!isAtEnd(bb)) {
    let tag = readVarint32(bb);

    switch (tag >>> 3) {
      case 0:
        break end_of_message;

      // optional int32 index = 1;
      case 1: {
        message.index = readVarint32(bb);
        break;
      }

      // optional string name = 2;
      case 2: {
        message.name = readString(bb, readVarint32(bb));
        break;
      }

      // optional int32 force_id = 3;
      case 3: {
        message.force_id = readVarint32(bb);
        break;
      }

      // repeated CardGroupItem cards = 4;
      case 4: {
        let limit = pushTemporaryLength(bb);
        let values = message.cards || (message.cards = []);
        values.push(_decodeCardGroupItem(bb));
        bb.limit = limit;
        break;
      }

      default:
        skipUnknownField(bb, tag & 7);
    }
  }

  return message;
}

export interface CardInfo {
  card_id?: number;
  unlock_time?: number;
  expired_time?: number;
  guid?: Long;
  num?: number;
  level?: number;
}

export function encodeCardInfo(message: CardInfo): Uint8Array {
  let bb = popByteBuffer();
  _encodeCardInfo(message, bb);
  return toUint8Array(bb);
}

function _encodeCardInfo(message: CardInfo, bb: ByteBuffer): void {
  // optional int32 card_id = 1;
  let $card_id = message.card_id;
  if ($card_id !== undefined) {
    writeVarint32(bb, 8);
    writeVarint64(bb, intToLong($card_id));
  }

  // optional uint32 unlock_time = 2;
  let $unlock_time = message.unlock_time;
  if ($unlock_time !== undefined) {
    writeVarint32(bb, 16);
    writeVarint32(bb, $unlock_time);
  }

  // optional uint32 expired_time = 3;
  let $expired_time = message.expired_time;
  if ($expired_time !== undefined) {
    writeVarint32(bb, 24);
    writeVarint32(bb, $expired_time);
  }

  // optional fixed64 guid = 4;
  let $guid = message.guid;
  if ($guid !== undefined) {
    writeVarint32(bb, 33);
    writeInt64(bb, $guid);
  }

  // optional int32 num = 5;
  let $num = message.num;
  if ($num !== undefined) {
    writeVarint32(bb, 40);
    writeVarint64(bb, intToLong($num));
  }

  // optional int32 level = 6;
  let $level = message.level;
  if ($level !== undefined) {
    writeVarint32(bb, 48);
    writeVarint64(bb, intToLong($level));
  }
}

export function decodeCardInfo(binary: Uint8Array): CardInfo {
  return _decodeCardInfo(wrapByteBuffer(binary));
}

function _decodeCardInfo(bb: ByteBuffer): CardInfo {
  let message: CardInfo = {} as any;

  end_of_message: while (!isAtEnd(bb)) {
    let tag = readVarint32(bb);

    switch (tag >>> 3) {
      case 0:
        break end_of_message;

      // optional int32 card_id = 1;
      case 1: {
        message.card_id = readVarint32(bb);
        break;
      }

      // optional uint32 unlock_time = 2;
      case 2: {
        message.unlock_time = readVarint32(bb) >>> 0;
        break;
      }

      // optional uint32 expired_time = 3;
      case 3: {
        message.expired_time = readVarint32(bb) >>> 0;
        break;
      }

      // optional fixed64 guid = 4;
      case 4: {
        message.guid = readInt64(bb, /* unsigned */ true);
        break;
      }

      // optional int32 num = 5;
      case 5: {
        message.num = readVarint32(bb);
        break;
      }

      // optional int32 level = 6;
      case 6: {
        message.level = readVarint32(bb);
        break;
      }

      default:
        skipUnknownField(bb, tag & 7);
    }
  }

  return message;
}

export interface HeroInfo {
  hero_id?: number;
  unlock_time?: number;
  expired_time?: number;
}

export function encodeHeroInfo(message: HeroInfo): Uint8Array {
  let bb = popByteBuffer();
  _encodeHeroInfo(message, bb);
  return toUint8Array(bb);
}

function _encodeHeroInfo(message: HeroInfo, bb: ByteBuffer): void {
  // optional int32 hero_id = 1;
  let $hero_id = message.hero_id;
  if ($hero_id !== undefined) {
    writeVarint32(bb, 8);
    writeVarint64(bb, intToLong($hero_id));
  }

  // optional uint32 unlock_time = 2;
  let $unlock_time = message.unlock_time;
  if ($unlock_time !== undefined) {
    writeVarint32(bb, 16);
    writeVarint32(bb, $unlock_time);
  }

  // optional uint32 expired_time = 3;
  let $expired_time = message.expired_time;
  if ($expired_time !== undefined) {
    writeVarint32(bb, 24);
    writeVarint32(bb, $expired_time);
  }
}

export function decodeHeroInfo(binary: Uint8Array): HeroInfo {
  return _decodeHeroInfo(wrapByteBuffer(binary));
}

function _decodeHeroInfo(bb: ByteBuffer): HeroInfo {
  let message: HeroInfo = {} as any;

  end_of_message: while (!isAtEnd(bb)) {
    let tag = readVarint32(bb);

    switch (tag >>> 3) {
      case 0:
        break end_of_message;

      // optional int32 hero_id = 1;
      case 1: {
        message.hero_id = readVarint32(bb);
        break;
      }

      // optional uint32 unlock_time = 2;
      case 2: {
        message.unlock_time = readVarint32(bb) >>> 0;
        break;
      }

      // optional uint32 expired_time = 3;
      case 3: {
        message.expired_time = readVarint32(bb) >>> 0;
        break;
      }

      default:
        skipUnknownField(bb, tag & 7);
    }
  }

  return message;
}

export interface CardGroupList {
  groups?: CardGroupInfo[];
  default_group?: number;
}

export function encodeCardGroupList(message: CardGroupList): Uint8Array {
  let bb = popByteBuffer();
  _encodeCardGroupList(message, bb);
  return toUint8Array(bb);
}

function _encodeCardGroupList(message: CardGroupList, bb: ByteBuffer): void {
  // repeated CardGroupInfo groups = 1;
  let array$groups = message.groups;
  if (array$groups !== undefined) {
    for (let value of array$groups) {
      writeVarint32(bb, 10);
      let nested = popByteBuffer();
      _encodeCardGroupInfo(value, nested);
      writeVarint32(bb, nested.limit);
      writeByteBuffer(bb, nested);
      pushByteBuffer(nested);
    }
  }

  // optional sint32 default_group = 2;
  let $default_group = message.default_group;
  if ($default_group !== undefined) {
    writeVarint32(bb, 16);
    writeVarint32ZigZag(bb, $default_group);
  }
}

export function decodeCardGroupList(binary: Uint8Array): CardGroupList {
  return _decodeCardGroupList(wrapByteBuffer(binary));
}

function _decodeCardGroupList(bb: ByteBuffer): CardGroupList {
  let message: CardGroupList = {} as any;

  end_of_message: while (!isAtEnd(bb)) {
    let tag = readVarint32(bb);

    switch (tag >>> 3) {
      case 0:
        break end_of_message;

      // repeated CardGroupInfo groups = 1;
      case 1: {
        let limit = pushTemporaryLength(bb);
        let values = message.groups || (message.groups = []);
        values.push(_decodeCardGroupInfo(bb));
        bb.limit = limit;
        break;
      }

      // optional sint32 default_group = 2;
      case 2: {
        message.default_group = readVarint32ZigZag(bb);
        break;
      }

      default:
        skipUnknownField(bb, tag & 7);
    }
  }

  return message;
}

export interface GameCardInfo {
  card_id?: number;
  level?: number;
}

export function encodeGameCardInfo(message: GameCardInfo): Uint8Array {
  let bb = popByteBuffer();
  _encodeGameCardInfo(message, bb);
  return toUint8Array(bb);
}

function _encodeGameCardInfo(message: GameCardInfo, bb: ByteBuffer): void {
  // optional int32 card_id = 1;
  let $card_id = message.card_id;
  if ($card_id !== undefined) {
    writeVarint32(bb, 8);
    writeVarint64(bb, intToLong($card_id));
  }

  // optional int32 level = 2;
  let $level = message.level;
  if ($level !== undefined) {
    writeVarint32(bb, 16);
    writeVarint64(bb, intToLong($level));
  }
}

export function decodeGameCardInfo(binary: Uint8Array): GameCardInfo {
  return _decodeGameCardInfo(wrapByteBuffer(binary));
}

function _decodeGameCardInfo(bb: ByteBuffer): GameCardInfo {
  let message: GameCardInfo = {} as any;

  end_of_message: while (!isAtEnd(bb)) {
    let tag = readVarint32(bb);

    switch (tag >>> 3) {
      case 0:
        break end_of_message;

      // optional int32 card_id = 1;
      case 1: {
        message.card_id = readVarint32(bb);
        break;
      }

      // optional int32 level = 2;
      case 2: {
        message.level = readVarint32(bb);
        break;
      }

      default:
        skipUnknownField(bb, tag & 7);
    }
  }

  return message;
}

export interface RoleGameStartInfo {
  game_id?: Long;
  mode?: GAME_MODE;
  map_id?: number;
  gamesvr_id?: number;
  begin_time?: number;
  url?: string;
  session_id?: number;
  session_key?: Uint8Array;
}

export function encodeRoleGameStartInfo(message: RoleGameStartInfo): Uint8Array {
  let bb = popByteBuffer();
  _encodeRoleGameStartInfo(message, bb);
  return toUint8Array(bb);
}

function _encodeRoleGameStartInfo(message: RoleGameStartInfo, bb: ByteBuffer): void {
  // optional fixed64 game_id = 1;
  let $game_id = message.game_id;
  if ($game_id !== undefined) {
    writeVarint32(bb, 9);
    writeInt64(bb, $game_id);
  }

  // optional GAME_MODE mode = 2;
  let $mode = message.mode;
  if ($mode !== undefined) {
    writeVarint32(bb, 16);
    writeVarint32(bb, encodeGAME_MODE[$mode]);
  }

  // optional int32 map_id = 3;
  let $map_id = message.map_id;
  if ($map_id !== undefined) {
    writeVarint32(bb, 24);
    writeVarint64(bb, intToLong($map_id));
  }

  // optional uint32 gamesvr_id = 4;
  let $gamesvr_id = message.gamesvr_id;
  if ($gamesvr_id !== undefined) {
    writeVarint32(bb, 32);
    writeVarint32(bb, $gamesvr_id);
  }

  // optional uint32 begin_time = 5;
  let $begin_time = message.begin_time;
  if ($begin_time !== undefined) {
    writeVarint32(bb, 40);
    writeVarint32(bb, $begin_time);
  }

  // optional string url = 6;
  let $url = message.url;
  if ($url !== undefined) {
    writeVarint32(bb, 50);
    writeString(bb, $url);
  }

  // optional uint32 session_id = 7;
  let $session_id = message.session_id;
  if ($session_id !== undefined) {
    writeVarint32(bb, 56);
    writeVarint32(bb, $session_id);
  }

  // optional bytes session_key = 8;
  let $session_key = message.session_key;
  if ($session_key !== undefined) {
    writeVarint32(bb, 66);
    writeVarint32(bb, $session_key.length), writeBytes(bb, $session_key);
  }
}

export function decodeRoleGameStartInfo(binary: Uint8Array): RoleGameStartInfo {
  return _decodeRoleGameStartInfo(wrapByteBuffer(binary));
}

function _decodeRoleGameStartInfo(bb: ByteBuffer): RoleGameStartInfo {
  let message: RoleGameStartInfo = {} as any;

  end_of_message: while (!isAtEnd(bb)) {
    let tag = readVarint32(bb);

    switch (tag >>> 3) {
      case 0:
        break end_of_message;

      // optional fixed64 game_id = 1;
      case 1: {
        message.game_id = readInt64(bb, /* unsigned */ true);
        break;
      }

      // optional GAME_MODE mode = 2;
      case 2: {
        message.mode = decodeGAME_MODE[readVarint32(bb)];
        break;
      }

      // optional int32 map_id = 3;
      case 3: {
        message.map_id = readVarint32(bb);
        break;
      }

      // optional uint32 gamesvr_id = 4;
      case 4: {
        message.gamesvr_id = readVarint32(bb) >>> 0;
        break;
      }

      // optional uint32 begin_time = 5;
      case 5: {
        message.begin_time = readVarint32(bb) >>> 0;
        break;
      }

      // optional string url = 6;
      case 6: {
        message.url = readString(bb, readVarint32(bb));
        break;
      }

      // optional uint32 session_id = 7;
      case 7: {
        message.session_id = readVarint32(bb) >>> 0;
        break;
      }

      // optional bytes session_key = 8;
      case 8: {
        message.session_key = readBytes(bb, readVarint32(bb));
        break;
      }

      default:
        skipUnknownField(bb, tag & 7);
    }
  }

  return message;
}

export interface RoleGameInfo {
  status?: ROLE_GAME_STATUS;
  status_time?: number;
  start_info?: RoleGameStartInfo;
}

export function encodeRoleGameInfo(message: RoleGameInfo): Uint8Array {
  let bb = popByteBuffer();
  _encodeRoleGameInfo(message, bb);
  return toUint8Array(bb);
}

function _encodeRoleGameInfo(message: RoleGameInfo, bb: ByteBuffer): void {
  // optional ROLE_GAME_STATUS status = 1;
  let $status = message.status;
  if ($status !== undefined) {
    writeVarint32(bb, 8);
    writeVarint32(bb, encodeROLE_GAME_STATUS[$status]);
  }

  // optional uint32 status_time = 2;
  let $status_time = message.status_time;
  if ($status_time !== undefined) {
    writeVarint32(bb, 16);
    writeVarint32(bb, $status_time);
  }

  // optional RoleGameStartInfo start_info = 3;
  let $start_info = message.start_info;
  if ($start_info !== undefined) {
    writeVarint32(bb, 26);
    let nested = popByteBuffer();
    _encodeRoleGameStartInfo($start_info, nested);
    writeVarint32(bb, nested.limit);
    writeByteBuffer(bb, nested);
    pushByteBuffer(nested);
  }
}

export function decodeRoleGameInfo(binary: Uint8Array): RoleGameInfo {
  return _decodeRoleGameInfo(wrapByteBuffer(binary));
}

function _decodeRoleGameInfo(bb: ByteBuffer): RoleGameInfo {
  let message: RoleGameInfo = {} as any;

  end_of_message: while (!isAtEnd(bb)) {
    let tag = readVarint32(bb);

    switch (tag >>> 3) {
      case 0:
        break end_of_message;

      // optional ROLE_GAME_STATUS status = 1;
      case 1: {
        message.status = decodeROLE_GAME_STATUS[readVarint32(bb)];
        break;
      }

      // optional uint32 status_time = 2;
      case 2: {
        message.status_time = readVarint32(bb) >>> 0;
        break;
      }

      // optional RoleGameStartInfo start_info = 3;
      case 3: {
        let limit = pushTemporaryLength(bb);
        message.start_info = _decodeRoleGameStartInfo(bb);
        bb.limit = limit;
        break;
      }

      default:
        skipUnknownField(bb, tag & 7);
    }
  }

  return message;
}

export interface GamePlayer {
  uin?: Long;
  openid?: string;
  name?: string;
  camp_id?: number;
  position?: number;
  is_ai?: boolean;
  hero_id?: number;
  cards?: GameCardInfo[];
  session_id?: number;
}

export function encodeGamePlayer(message: GamePlayer): Uint8Array {
  let bb = popByteBuffer();
  _encodeGamePlayer(message, bb);
  return toUint8Array(bb);
}

function _encodeGamePlayer(message: GamePlayer, bb: ByteBuffer): void {
  // optional fixed64 uin = 1;
  let $uin = message.uin;
  if ($uin !== undefined) {
    writeVarint32(bb, 9);
    writeInt64(bb, $uin);
  }

  // optional string openid = 2;
  let $openid = message.openid;
  if ($openid !== undefined) {
    writeVarint32(bb, 18);
    writeString(bb, $openid);
  }

  // optional string name = 3;
  let $name = message.name;
  if ($name !== undefined) {
    writeVarint32(bb, 26);
    writeString(bb, $name);
  }

  // optional int32 camp_id = 4;
  let $camp_id = message.camp_id;
  if ($camp_id !== undefined) {
    writeVarint32(bb, 32);
    writeVarint64(bb, intToLong($camp_id));
  }

  // optional sint32 position = 5;
  let $position = message.position;
  if ($position !== undefined) {
    writeVarint32(bb, 40);
    writeVarint32ZigZag(bb, $position);
  }

  // optional bool is_ai = 6;
  let $is_ai = message.is_ai;
  if ($is_ai !== undefined) {
    writeVarint32(bb, 48);
    writeByte(bb, $is_ai ? 1 : 0);
  }

  // optional int32 hero_id = 7;
  let $hero_id = message.hero_id;
  if ($hero_id !== undefined) {
    writeVarint32(bb, 56);
    writeVarint64(bb, intToLong($hero_id));
  }

  // repeated GameCardInfo cards = 8;
  let array$cards = message.cards;
  if (array$cards !== undefined) {
    for (let value of array$cards) {
      writeVarint32(bb, 66);
      let nested = popByteBuffer();
      _encodeGameCardInfo(value, nested);
      writeVarint32(bb, nested.limit);
      writeByteBuffer(bb, nested);
      pushByteBuffer(nested);
    }
  }

  // optional uint32 session_id = 9;
  let $session_id = message.session_id;
  if ($session_id !== undefined) {
    writeVarint32(bb, 72);
    writeVarint32(bb, $session_id);
  }
}

export function decodeGamePlayer(binary: Uint8Array): GamePlayer {
  return _decodeGamePlayer(wrapByteBuffer(binary));
}

function _decodeGamePlayer(bb: ByteBuffer): GamePlayer {
  let message: GamePlayer = {} as any;

  end_of_message: while (!isAtEnd(bb)) {
    let tag = readVarint32(bb);

    switch (tag >>> 3) {
      case 0:
        break end_of_message;

      // optional fixed64 uin = 1;
      case 1: {
        message.uin = readInt64(bb, /* unsigned */ true);
        break;
      }

      // optional string openid = 2;
      case 2: {
        message.openid = readString(bb, readVarint32(bb));
        break;
      }

      // optional string name = 3;
      case 3: {
        message.name = readString(bb, readVarint32(bb));
        break;
      }

      // optional int32 camp_id = 4;
      case 4: {
        message.camp_id = readVarint32(bb);
        break;
      }

      // optional sint32 position = 5;
      case 5: {
        message.position = readVarint32ZigZag(bb);
        break;
      }

      // optional bool is_ai = 6;
      case 6: {
        message.is_ai = !!readByte(bb);
        break;
      }

      // optional int32 hero_id = 7;
      case 7: {
        message.hero_id = readVarint32(bb);
        break;
      }

      // repeated GameCardInfo cards = 8;
      case 8: {
        let limit = pushTemporaryLength(bb);
        let values = message.cards || (message.cards = []);
        values.push(_decodeGameCardInfo(bb));
        bb.limit = limit;
        break;
      }

      // optional uint32 session_id = 9;
      case 9: {
        message.session_id = readVarint32(bb) >>> 0;
        break;
      }

      default:
        skipUnknownField(bb, tag & 7);
    }
  }

  return message;
}

export interface GameInfo {
  game_id?: Long;
  begin_time?: number;
  version?: number;
  map_id?: number;
  mode?: GAME_MODE;
  random_num?: number;
  gamesvr_id?: number;
  url?: string;
  players?: GamePlayer[];
}

export function encodeGameInfo(message: GameInfo): Uint8Array {
  let bb = popByteBuffer();
  _encodeGameInfo(message, bb);
  return toUint8Array(bb);
}

function _encodeGameInfo(message: GameInfo, bb: ByteBuffer): void {
  // optional fixed64 game_id = 1;
  let $game_id = message.game_id;
  if ($game_id !== undefined) {
    writeVarint32(bb, 9);
    writeInt64(bb, $game_id);
  }

  // optional uint32 begin_time = 2;
  let $begin_time = message.begin_time;
  if ($begin_time !== undefined) {
    writeVarint32(bb, 16);
    writeVarint32(bb, $begin_time);
  }

  // optional uint32 version = 3;
  let $version = message.version;
  if ($version !== undefined) {
    writeVarint32(bb, 24);
    writeVarint32(bb, $version);
  }

  // optional int32 map_id = 4;
  let $map_id = message.map_id;
  if ($map_id !== undefined) {
    writeVarint32(bb, 32);
    writeVarint64(bb, intToLong($map_id));
  }

  // optional GAME_MODE mode = 5;
  let $mode = message.mode;
  if ($mode !== undefined) {
    writeVarint32(bb, 40);
    writeVarint32(bb, encodeGAME_MODE[$mode]);
  }

  // optional uint32 random_num = 6;
  let $random_num = message.random_num;
  if ($random_num !== undefined) {
    writeVarint32(bb, 48);
    writeVarint32(bb, $random_num);
  }

  // optional uint32 gamesvr_id = 7;
  let $gamesvr_id = message.gamesvr_id;
  if ($gamesvr_id !== undefined) {
    writeVarint32(bb, 56);
    writeVarint32(bb, $gamesvr_id);
  }

  // optional string url = 8;
  let $url = message.url;
  if ($url !== undefined) {
    writeVarint32(bb, 66);
    writeString(bb, $url);
  }

  // repeated GamePlayer players = 9;
  let array$players = message.players;
  if (array$players !== undefined) {
    for (let value of array$players) {
      writeVarint32(bb, 74);
      let nested = popByteBuffer();
      _encodeGamePlayer(value, nested);
      writeVarint32(bb, nested.limit);
      writeByteBuffer(bb, nested);
      pushByteBuffer(nested);
    }
  }
}

export function decodeGameInfo(binary: Uint8Array): GameInfo {
  return _decodeGameInfo(wrapByteBuffer(binary));
}

function _decodeGameInfo(bb: ByteBuffer): GameInfo {
  let message: GameInfo = {} as any;

  end_of_message: while (!isAtEnd(bb)) {
    let tag = readVarint32(bb);

    switch (tag >>> 3) {
      case 0:
        break end_of_message;

      // optional fixed64 game_id = 1;
      case 1: {
        message.game_id = readInt64(bb, /* unsigned */ true);
        break;
      }

      // optional uint32 begin_time = 2;
      case 2: {
        message.begin_time = readVarint32(bb) >>> 0;
        break;
      }

      // optional uint32 version = 3;
      case 3: {
        message.version = readVarint32(bb) >>> 0;
        break;
      }

      // optional int32 map_id = 4;
      case 4: {
        message.map_id = readVarint32(bb);
        break;
      }

      // optional GAME_MODE mode = 5;
      case 5: {
        message.mode = decodeGAME_MODE[readVarint32(bb)];
        break;
      }

      // optional uint32 random_num = 6;
      case 6: {
        message.random_num = readVarint32(bb) >>> 0;
        break;
      }

      // optional uint32 gamesvr_id = 7;
      case 7: {
        message.gamesvr_id = readVarint32(bb) >>> 0;
        break;
      }

      // optional string url = 8;
      case 8: {
        message.url = readString(bb, readVarint32(bb));
        break;
      }

      // repeated GamePlayer players = 9;
      case 9: {
        let limit = pushTemporaryLength(bb);
        let values = message.players || (message.players = []);
        values.push(_decodeGamePlayer(bb));
        bb.limit = limit;
        break;
      }

      default:
        skipUnknownField(bb, tag & 7);
    }
  }

  return message;
}

export interface GamePlayerStat {
  uin?: Long;
  camp_id?: number;
  is_ai?: boolean;
  score?: number;
}

export function encodeGamePlayerStat(message: GamePlayerStat): Uint8Array {
  let bb = popByteBuffer();
  _encodeGamePlayerStat(message, bb);
  return toUint8Array(bb);
}

function _encodeGamePlayerStat(message: GamePlayerStat, bb: ByteBuffer): void {
  // optional fixed64 uin = 1;
  let $uin = message.uin;
  if ($uin !== undefined) {
    writeVarint32(bb, 9);
    writeInt64(bb, $uin);
  }

  // optional int32 camp_id = 2;
  let $camp_id = message.camp_id;
  if ($camp_id !== undefined) {
    writeVarint32(bb, 16);
    writeVarint64(bb, intToLong($camp_id));
  }

  // optional bool is_ai = 3;
  let $is_ai = message.is_ai;
  if ($is_ai !== undefined) {
    writeVarint32(bb, 24);
    writeByte(bb, $is_ai ? 1 : 0);
  }

  // optional int32 score = 4;
  let $score = message.score;
  if ($score !== undefined) {
    writeVarint32(bb, 32);
    writeVarint64(bb, intToLong($score));
  }
}

export function decodeGamePlayerStat(binary: Uint8Array): GamePlayerStat {
  return _decodeGamePlayerStat(wrapByteBuffer(binary));
}

function _decodeGamePlayerStat(bb: ByteBuffer): GamePlayerStat {
  let message: GamePlayerStat = {} as any;

  end_of_message: while (!isAtEnd(bb)) {
    let tag = readVarint32(bb);

    switch (tag >>> 3) {
      case 0:
        break end_of_message;

      // optional fixed64 uin = 1;
      case 1: {
        message.uin = readInt64(bb, /* unsigned */ true);
        break;
      }

      // optional int32 camp_id = 2;
      case 2: {
        message.camp_id = readVarint32(bb);
        break;
      }

      // optional bool is_ai = 3;
      case 3: {
        message.is_ai = !!readByte(bb);
        break;
      }

      // optional int32 score = 4;
      case 4: {
        message.score = readVarint32(bb);
        break;
      }

      default:
        skipUnknownField(bb, tag & 7);
    }
  }

  return message;
}

export interface GameResult {
  game_id?: Long;
  mode?: GAME_MODE;
  map_id?: number;
  start_time?: number;
  end_time?: number;
  duration?: number;
  result?: GAME_RESULT;
  stat?: GamePlayerStat[];
}

export function encodeGameResult(message: GameResult): Uint8Array {
  let bb = popByteBuffer();
  _encodeGameResult(message, bb);
  return toUint8Array(bb);
}

function _encodeGameResult(message: GameResult, bb: ByteBuffer): void {
  // optional fixed64 game_id = 1;
  let $game_id = message.game_id;
  if ($game_id !== undefined) {
    writeVarint32(bb, 9);
    writeInt64(bb, $game_id);
  }

  // optional GAME_MODE mode = 2;
  let $mode = message.mode;
  if ($mode !== undefined) {
    writeVarint32(bb, 16);
    writeVarint32(bb, encodeGAME_MODE[$mode]);
  }

  // optional int32 map_id = 3;
  let $map_id = message.map_id;
  if ($map_id !== undefined) {
    writeVarint32(bb, 24);
    writeVarint64(bb, intToLong($map_id));
  }

  // optional uint32 start_time = 4;
  let $start_time = message.start_time;
  if ($start_time !== undefined) {
    writeVarint32(bb, 32);
    writeVarint32(bb, $start_time);
  }

  // optional uint32 end_time = 5;
  let $end_time = message.end_time;
  if ($end_time !== undefined) {
    writeVarint32(bb, 40);
    writeVarint32(bb, $end_time);
  }

  // optional int32 duration = 6;
  let $duration = message.duration;
  if ($duration !== undefined) {
    writeVarint32(bb, 48);
    writeVarint64(bb, intToLong($duration));
  }

  // optional GAME_RESULT result = 7;
  let $result = message.result;
  if ($result !== undefined) {
    writeVarint32(bb, 56);
    writeVarint32(bb, encodeGAME_RESULT[$result]);
  }

  // repeated GamePlayerStat stat = 8;
  let array$stat = message.stat;
  if (array$stat !== undefined) {
    for (let value of array$stat) {
      writeVarint32(bb, 66);
      let nested = popByteBuffer();
      _encodeGamePlayerStat(value, nested);
      writeVarint32(bb, nested.limit);
      writeByteBuffer(bb, nested);
      pushByteBuffer(nested);
    }
  }
}

export function decodeGameResult(binary: Uint8Array): GameResult {
  return _decodeGameResult(wrapByteBuffer(binary));
}

function _decodeGameResult(bb: ByteBuffer): GameResult {
  let message: GameResult = {} as any;

  end_of_message: while (!isAtEnd(bb)) {
    let tag = readVarint32(bb);

    switch (tag >>> 3) {
      case 0:
        break end_of_message;

      // optional fixed64 game_id = 1;
      case 1: {
        message.game_id = readInt64(bb, /* unsigned */ true);
        break;
      }

      // optional GAME_MODE mode = 2;
      case 2: {
        message.mode = decodeGAME_MODE[readVarint32(bb)];
        break;
      }

      // optional int32 map_id = 3;
      case 3: {
        message.map_id = readVarint32(bb);
        break;
      }

      // optional uint32 start_time = 4;
      case 4: {
        message.start_time = readVarint32(bb) >>> 0;
        break;
      }

      // optional uint32 end_time = 5;
      case 5: {
        message.end_time = readVarint32(bb) >>> 0;
        break;
      }

      // optional int32 duration = 6;
      case 6: {
        message.duration = readVarint32(bb);
        break;
      }

      // optional GAME_RESULT result = 7;
      case 7: {
        message.result = decodeGAME_RESULT[readVarint32(bb)];
        break;
      }

      // repeated GamePlayerStat stat = 8;
      case 8: {
        let limit = pushTemporaryLength(bb);
        let values = message.stat || (message.stat = []);
        values.push(_decodeGamePlayerStat(bb));
        bb.limit = limit;
        break;
      }

      default:
        skipUnknownField(bb, tag & 7);
    }
  }

  return message;
}

export interface RoleSimpleDataOthers {
  gender?: number;
  nick_name?: string;
  picture?: string;
  client_ver?: number;
}

export function encodeRoleSimpleDataOthers(message: RoleSimpleDataOthers): Uint8Array {
  let bb = popByteBuffer();
  _encodeRoleSimpleDataOthers(message, bb);
  return toUint8Array(bb);
}

function _encodeRoleSimpleDataOthers(message: RoleSimpleDataOthers, bb: ByteBuffer): void {
  // optional int32 gender = 2;
  let $gender = message.gender;
  if ($gender !== undefined) {
    writeVarint32(bb, 16);
    writeVarint64(bb, intToLong($gender));
  }

  // optional string nick_name = 3;
  let $nick_name = message.nick_name;
  if ($nick_name !== undefined) {
    writeVarint32(bb, 26);
    writeString(bb, $nick_name);
  }

  // optional string picture = 4;
  let $picture = message.picture;
  if ($picture !== undefined) {
    writeVarint32(bb, 34);
    writeString(bb, $picture);
  }

  // optional uint32 client_ver = 5;
  let $client_ver = message.client_ver;
  if ($client_ver !== undefined) {
    writeVarint32(bb, 40);
    writeVarint32(bb, $client_ver);
  }
}

export function decodeRoleSimpleDataOthers(binary: Uint8Array): RoleSimpleDataOthers {
  return _decodeRoleSimpleDataOthers(wrapByteBuffer(binary));
}

function _decodeRoleSimpleDataOthers(bb: ByteBuffer): RoleSimpleDataOthers {
  let message: RoleSimpleDataOthers = {} as any;

  end_of_message: while (!isAtEnd(bb)) {
    let tag = readVarint32(bb);

    switch (tag >>> 3) {
      case 0:
        break end_of_message;

      // optional int32 gender = 2;
      case 2: {
        message.gender = readVarint32(bb);
        break;
      }

      // optional string nick_name = 3;
      case 3: {
        message.nick_name = readString(bb, readVarint32(bb));
        break;
      }

      // optional string picture = 4;
      case 4: {
        message.picture = readString(bb, readVarint32(bb));
        break;
      }

      // optional uint32 client_ver = 5;
      case 5: {
        message.client_ver = readVarint32(bb) >>> 0;
        break;
      }

      default:
        skipUnknownField(bb, tag & 7);
    }
  }

  return message;
}

export interface GameFrameInputUnit {
  position?: number;
  input?: Uint8Array;
}

export function encodeGameFrameInputUnit(message: GameFrameInputUnit): Uint8Array {
  let bb = popByteBuffer();
  _encodeGameFrameInputUnit(message, bb);
  return toUint8Array(bb);
}

function _encodeGameFrameInputUnit(message: GameFrameInputUnit, bb: ByteBuffer): void {
  // optional sint32 position = 1;
  let $position = message.position;
  if ($position !== undefined) {
    writeVarint32(bb, 8);
    writeVarint32ZigZag(bb, $position);
  }

  // optional bytes input = 2;
  let $input = message.input;
  if ($input !== undefined) {
    writeVarint32(bb, 18);
    writeVarint32(bb, $input.length), writeBytes(bb, $input);
  }
}

export function decodeGameFrameInputUnit(binary: Uint8Array): GameFrameInputUnit {
  return _decodeGameFrameInputUnit(wrapByteBuffer(binary));
}

function _decodeGameFrameInputUnit(bb: ByteBuffer): GameFrameInputUnit {
  let message: GameFrameInputUnit = {} as any;

  end_of_message: while (!isAtEnd(bb)) {
    let tag = readVarint32(bb);

    switch (tag >>> 3) {
      case 0:
        break end_of_message;

      // optional sint32 position = 1;
      case 1: {
        message.position = readVarint32ZigZag(bb);
        break;
      }

      // optional bytes input = 2;
      case 2: {
        message.input = readBytes(bb, readVarint32(bb));
        break;
      }

      default:
        skipUnknownField(bb, tag & 7);
    }
  }

  return message;
}

export interface GameFrameInput {
  frame_seq?: number;
  input?: GameFrameInputUnit;
}

export function encodeGameFrameInput(message: GameFrameInput): Uint8Array {
  let bb = popByteBuffer();
  _encodeGameFrameInput(message, bb);
  return toUint8Array(bb);
}

function _encodeGameFrameInput(message: GameFrameInput, bb: ByteBuffer): void {
  // optional uint32 frame_seq = 1;
  let $frame_seq = message.frame_seq;
  if ($frame_seq !== undefined) {
    writeVarint32(bb, 8);
    writeVarint32(bb, $frame_seq);
  }

  // optional GameFrameInputUnit input = 2;
  let $input = message.input;
  if ($input !== undefined) {
    writeVarint32(bb, 18);
    let nested = popByteBuffer();
    _encodeGameFrameInputUnit($input, nested);
    writeVarint32(bb, nested.limit);
    writeByteBuffer(bb, nested);
    pushByteBuffer(nested);
  }
}

export function decodeGameFrameInput(binary: Uint8Array): GameFrameInput {
  return _decodeGameFrameInput(wrapByteBuffer(binary));
}

function _decodeGameFrameInput(bb: ByteBuffer): GameFrameInput {
  let message: GameFrameInput = {} as any;

  end_of_message: while (!isAtEnd(bb)) {
    let tag = readVarint32(bb);

    switch (tag >>> 3) {
      case 0:
        break end_of_message;

      // optional uint32 frame_seq = 1;
      case 1: {
        message.frame_seq = readVarint32(bb) >>> 0;
        break;
      }

      // optional GameFrameInputUnit input = 2;
      case 2: {
        let limit = pushTemporaryLength(bb);
        message.input = _decodeGameFrameInputUnit(bb);
        bb.limit = limit;
        break;
      }

      default:
        skipUnknownField(bb, tag & 7);
    }
  }

  return message;
}

export interface Long {
  low: number;
  high: number;
  unsigned: boolean;
}

interface ByteBuffer {
  bytes: Uint8Array;
  offset: number;
  limit: number;
}

function pushTemporaryLength(bb: ByteBuffer): number {
  let length = readVarint32(bb);
  let limit = bb.limit;
  bb.limit = bb.offset + length;
  return limit;
}

function skipUnknownField(bb: ByteBuffer, type: number): void {
  switch (type) {
    case 0: while (readByte(bb) & 0x80) { } break;
    case 2: skip(bb, readVarint32(bb)); break;
    case 5: skip(bb, 4); break;
    case 1: skip(bb, 8); break;
    default: throw new Error("Unimplemented type: " + type);
  }
}

function stringToLong(value: string): Long {
  return {
    low: value.charCodeAt(0) | (value.charCodeAt(1) << 16),
    high: value.charCodeAt(2) | (value.charCodeAt(3) << 16),
    unsigned: false,
  };
}

function longToString(value: Long): string {
  let low = value.low;
  let high = value.high;
  return String.fromCharCode(
    low & 0xFFFF,
    low >>> 16,
    high & 0xFFFF,
    high >>> 16);
}

// The code below was modified from https://github.com/protobufjs/bytebuffer.js
// which is under the Apache License 2.0.

let f32 = new Float32Array(1);
let f32_u8 = new Uint8Array(f32.buffer);

let f64 = new Float64Array(1);
let f64_u8 = new Uint8Array(f64.buffer);

function intToLong(value: number): Long {
  value |= 0;
  return {
    low: value,
    high: value >> 31,
    unsigned: value >= 0,
  };
}

let bbStack: ByteBuffer[] = [];

function popByteBuffer(): ByteBuffer {
  const bb = bbStack.pop();
  if (!bb) return { bytes: new Uint8Array(64), offset: 0, limit: 0 };
  bb.offset = bb.limit = 0;
  return bb;
}

function pushByteBuffer(bb: ByteBuffer): void {
  bbStack.push(bb);
}

function wrapByteBuffer(bytes: Uint8Array): ByteBuffer {
  return { bytes, offset: 0, limit: bytes.length };
}

function toUint8Array(bb: ByteBuffer): Uint8Array {
  let bytes = bb.bytes;
  let limit = bb.limit;
  return bytes.length === limit ? bytes : bytes.subarray(0, limit);
}

function skip(bb: ByteBuffer, offset: number): void {
  if (bb.offset + offset > bb.limit) {
    throw new Error('Skip past limit');
  }
  bb.offset += offset;
}

function isAtEnd(bb: ByteBuffer): boolean {
  return bb.offset >= bb.limit;
}

function grow(bb: ByteBuffer, count: number): number {
  let bytes = bb.bytes;
  let offset = bb.offset;
  let limit = bb.limit;
  let finalOffset = offset + count;
  if (finalOffset > bytes.length) {
    let newBytes = new Uint8Array(finalOffset * 2);
    newBytes.set(bytes);
    bb.bytes = newBytes;
  }
  bb.offset = finalOffset;
  if (finalOffset > limit) {
    bb.limit = finalOffset;
  }
  return offset;
}

function advance(bb: ByteBuffer, count: number): number {
  let offset = bb.offset;
  if (offset + count > bb.limit) {
    throw new Error('Read past limit');
  }
  bb.offset += count;
  return offset;
}

function readBytes(bb: ByteBuffer, count: number): Uint8Array {
  let offset = advance(bb, count);
  return bb.bytes.subarray(offset, offset + count);
}

function writeBytes(bb: ByteBuffer, buffer: Uint8Array): void {
  let offset = grow(bb, buffer.length);
  bb.bytes.set(buffer, offset);
}

function readString(bb: ByteBuffer, count: number): string {
  // Sadly a hand-coded UTF8 decoder is much faster than subarray+TextDecoder in V8
  let offset = advance(bb, count);
  let fromCharCode = String.fromCharCode;
  let bytes = bb.bytes;
  let invalid = '\uFFFD';
  let text = '';

  for (let i = 0; i < count; i++) {
    let c1 = bytes[i + offset], c2: number, c3: number, c4: number, c: number;

    // 1 byte
    if ((c1 & 0x80) === 0) {
      text += fromCharCode(c1);
    }

    // 2 bytes
    else if ((c1 & 0xE0) === 0xC0) {
      if (i + 1 >= count) text += invalid;
      else {
        c2 = bytes[i + offset + 1];
        if ((c2 & 0xC0) !== 0x80) text += invalid;
        else {
          c = ((c1 & 0x1F) << 6) | (c2 & 0x3F);
          if (c < 0x80) text += invalid;
          else {
            text += fromCharCode(c);
            i++;
          }
        }
      }
    }

    // 3 bytes
    else if ((c1 & 0xF0) == 0xE0) {
      if (i + 2 >= count) text += invalid;
      else {
        c2 = bytes[i + offset + 1];
        c3 = bytes[i + offset + 2];
        if (((c2 | (c3 << 8)) & 0xC0C0) !== 0x8080) text += invalid;
        else {
          c = ((c1 & 0x0F) << 12) | ((c2 & 0x3F) << 6) | (c3 & 0x3F);
          if (c < 0x0800 || (c >= 0xD800 && c <= 0xDFFF)) text += invalid;
          else {
            text += fromCharCode(c);
            i += 2;
          }
        }
      }
    }

    // 4 bytes
    else if ((c1 & 0xF8) == 0xF0) {
      if (i + 3 >= count) text += invalid;
      else {
        c2 = bytes[i + offset + 1];
        c3 = bytes[i + offset + 2];
        c4 = bytes[i + offset + 3];
        if (((c2 | (c3 << 8) | (c4 << 16)) & 0xC0C0C0) !== 0x808080) text += invalid;
        else {
          c = ((c1 & 0x07) << 0x12) | ((c2 & 0x3F) << 0x0C) | ((c3 & 0x3F) << 0x06) | (c4 & 0x3F);
          if (c < 0x10000 || c > 0x10FFFF) text += invalid;
          else {
            c -= 0x10000;
            text += fromCharCode((c >> 10) + 0xD800, (c & 0x3FF) + 0xDC00);
            i += 3;
          }
        }
      }
    }

    else text += invalid;
  }

  return text;
}

function writeString(bb: ByteBuffer, text: string): void {
  // Sadly a hand-coded UTF8 encoder is much faster than TextEncoder+set in V8
  let n = text.length;
  let byteCount = 0;

  // Write the byte count first
  for (let i = 0; i < n; i++) {
    let c = text.charCodeAt(i);
    if (c >= 0xD800 && c <= 0xDBFF && i + 1 < n) {
      c = (c << 10) + text.charCodeAt(++i) - 0x35FDC00;
    }
    byteCount += c < 0x80 ? 1 : c < 0x800 ? 2 : c < 0x10000 ? 3 : 4;
  }
  writeVarint32(bb, byteCount);

  let offset = grow(bb, byteCount);
  let bytes = bb.bytes;

  // Then write the bytes
  for (let i = 0; i < n; i++) {
    let c = text.charCodeAt(i);
    if (c >= 0xD800 && c <= 0xDBFF && i + 1 < n) {
      c = (c << 10) + text.charCodeAt(++i) - 0x35FDC00;
    }
    if (c < 0x80) {
      bytes[offset++] = c;
    } else {
      if (c < 0x800) {
        bytes[offset++] = ((c >> 6) & 0x1F) | 0xC0;
      } else {
        if (c < 0x10000) {
          bytes[offset++] = ((c >> 12) & 0x0F) | 0xE0;
        } else {
          bytes[offset++] = ((c >> 18) & 0x07) | 0xF0;
          bytes[offset++] = ((c >> 12) & 0x3F) | 0x80;
        }
        bytes[offset++] = ((c >> 6) & 0x3F) | 0x80;
      }
      bytes[offset++] = (c & 0x3F) | 0x80;
    }
  }
}

function writeByteBuffer(bb: ByteBuffer, buffer: ByteBuffer): void {
  let offset = grow(bb, buffer.limit);
  let from = bb.bytes;
  let to = buffer.bytes;

  // This for loop is much faster than subarray+set on V8
  for (let i = 0, n = buffer.limit; i < n; i++) {
    from[i + offset] = to[i];
  }
}

function readByte(bb: ByteBuffer): number {
  return bb.bytes[advance(bb, 1)];
}

function writeByte(bb: ByteBuffer, value: number): void {
  let offset = grow(bb, 1);
  bb.bytes[offset] = value;
}

function readFloat(bb: ByteBuffer): number {
  let offset = advance(bb, 4);
  let bytes = bb.bytes;

  // Manual copying is much faster than subarray+set in V8
  f32_u8[0] = bytes[offset++];
  f32_u8[1] = bytes[offset++];
  f32_u8[2] = bytes[offset++];
  f32_u8[3] = bytes[offset++];
  return f32[0];
}

function writeFloat(bb: ByteBuffer, value: number): void {
  let offset = grow(bb, 4);
  let bytes = bb.bytes;
  f32[0] = value;

  // Manual copying is much faster than subarray+set in V8
  bytes[offset++] = f32_u8[0];
  bytes[offset++] = f32_u8[1];
  bytes[offset++] = f32_u8[2];
  bytes[offset++] = f32_u8[3];
}

function readDouble(bb: ByteBuffer): number {
  let offset = advance(bb, 8);
  let bytes = bb.bytes;

  // Manual copying is much faster than subarray+set in V8
  f64_u8[0] = bytes[offset++];
  f64_u8[1] = bytes[offset++];
  f64_u8[2] = bytes[offset++];
  f64_u8[3] = bytes[offset++];
  f64_u8[4] = bytes[offset++];
  f64_u8[5] = bytes[offset++];
  f64_u8[6] = bytes[offset++];
  f64_u8[7] = bytes[offset++];
  return f64[0];
}

function writeDouble(bb: ByteBuffer, value: number): void {
  let offset = grow(bb, 8);
  let bytes = bb.bytes;
  f64[0] = value;

  // Manual copying is much faster than subarray+set in V8
  bytes[offset++] = f64_u8[0];
  bytes[offset++] = f64_u8[1];
  bytes[offset++] = f64_u8[2];
  bytes[offset++] = f64_u8[3];
  bytes[offset++] = f64_u8[4];
  bytes[offset++] = f64_u8[5];
  bytes[offset++] = f64_u8[6];
  bytes[offset++] = f64_u8[7];
}

function readInt32(bb: ByteBuffer): number {
  let offset = advance(bb, 4);
  let bytes = bb.bytes;
  return (
    bytes[offset] |
    (bytes[offset + 1] << 8) |
    (bytes[offset + 2] << 16) |
    (bytes[offset + 3] << 24)
  );
}

function writeInt32(bb: ByteBuffer, value: number): void {
  let offset = grow(bb, 4);
  let bytes = bb.bytes;
  bytes[offset] = value;
  bytes[offset + 1] = value >> 8;
  bytes[offset + 2] = value >> 16;
  bytes[offset + 3] = value >> 24;
}

function readInt64(bb: ByteBuffer, unsigned: boolean): Long {
  return {
    low: readInt32(bb),
    high: readInt32(bb),
    unsigned,
  };
}

function writeInt64(bb: ByteBuffer, value: Long): void {
  writeInt32(bb, value.low);
  writeInt32(bb, value.high);
}

function readVarint32(bb: ByteBuffer): number {
  let c = 0;
  let value = 0;
  let b: number;
  do {
    b = readByte(bb);
    if (c < 32) value |= (b & 0x7F) << c;
    c += 7;
  } while (b & 0x80);
  return value;
}

function writeVarint32(bb: ByteBuffer, value: number): void {
  value >>>= 0;
  while (value >= 0x80) {
    writeByte(bb, (value & 0x7f) | 0x80);
    value >>>= 7;
  }
  writeByte(bb, value);
}

function readVarint64(bb: ByteBuffer, unsigned: boolean): Long {
  let part0 = 0;
  let part1 = 0;
  let part2 = 0;
  let b: number;

  b = readByte(bb); part0 = (b & 0x7F); if (b & 0x80) {
    b = readByte(bb); part0 |= (b & 0x7F) << 7; if (b & 0x80) {
      b = readByte(bb); part0 |= (b & 0x7F) << 14; if (b & 0x80) {
        b = readByte(bb); part0 |= (b & 0x7F) << 21; if (b & 0x80) {

          b = readByte(bb); part1 = (b & 0x7F); if (b & 0x80) {
            b = readByte(bb); part1 |= (b & 0x7F) << 7; if (b & 0x80) {
              b = readByte(bb); part1 |= (b & 0x7F) << 14; if (b & 0x80) {
                b = readByte(bb); part1 |= (b & 0x7F) << 21; if (b & 0x80) {

                  b = readByte(bb); part2 = (b & 0x7F); if (b & 0x80) {
                    b = readByte(bb); part2 |= (b & 0x7F) << 7;
                  }
                }
              }
            }
          }
        }
      }
    }
  }

  return {
    low: part0 | (part1 << 28),
    high: (part1 >>> 4) | (part2 << 24),
    unsigned,
  };
}

function writeVarint64(bb: ByteBuffer, value: Long): void {
  let part0 = value.low >>> 0;
  let part1 = ((value.low >>> 28) | (value.high << 4)) >>> 0;
  let part2 = value.high >>> 24;

  // ref: src/google/protobuf/io/coded_stream.cc
  let size =
    part2 === 0 ?
      part1 === 0 ?
        part0 < 1 << 14 ?
          part0 < 1 << 7 ? 1 : 2 :
          part0 < 1 << 21 ? 3 : 4 :
        part1 < 1 << 14 ?
          part1 < 1 << 7 ? 5 : 6 :
          part1 < 1 << 21 ? 7 : 8 :
      part2 < 1 << 7 ? 9 : 10;

  let offset = grow(bb, size);
  let bytes = bb.bytes;

  switch (size) {
    case 10: bytes[offset + 9] = (part2 >>> 7) & 0x01;
    case 9: bytes[offset + 8] = size !== 9 ? part2 | 0x80 : part2 & 0x7F;
    case 8: bytes[offset + 7] = size !== 8 ? (part1 >>> 21) | 0x80 : (part1 >>> 21) & 0x7F;
    case 7: bytes[offset + 6] = size !== 7 ? (part1 >>> 14) | 0x80 : (part1 >>> 14) & 0x7F;
    case 6: bytes[offset + 5] = size !== 6 ? (part1 >>> 7) | 0x80 : (part1 >>> 7) & 0x7F;
    case 5: bytes[offset + 4] = size !== 5 ? part1 | 0x80 : part1 & 0x7F;
    case 4: bytes[offset + 3] = size !== 4 ? (part0 >>> 21) | 0x80 : (part0 >>> 21) & 0x7F;
    case 3: bytes[offset + 2] = size !== 3 ? (part0 >>> 14) | 0x80 : (part0 >>> 14) & 0x7F;
    case 2: bytes[offset + 1] = size !== 2 ? (part0 >>> 7) | 0x80 : (part0 >>> 7) & 0x7F;
    case 1: bytes[offset] = size !== 1 ? part0 | 0x80 : part0 & 0x7F;
  }
}

function readVarint32ZigZag(bb: ByteBuffer): number {
  let value = readVarint32(bb);

  // ref: src/google/protobuf/wire_format_lite.h
  return (value >>> 1) ^ -(value & 1);
}

function writeVarint32ZigZag(bb: ByteBuffer, value: number): void {
  // ref: src/google/protobuf/wire_format_lite.h
  writeVarint32(bb, (value << 1) ^ (value >> 31));
}

function readVarint64ZigZag(bb: ByteBuffer): Long {
  let value = readVarint64(bb, /* unsigned */ false);
  let low = value.low;
  let high = value.high;
  let flip = -(low & 1);

  // ref: src/google/protobuf/wire_format_lite.h
  return {
    low: ((low >>> 1) | (high << 31)) ^ flip,
    high: (high >>> 1) ^ flip,
    unsigned: false,
  };
}

function writeVarint64ZigZag(bb: ByteBuffer, value: Long): void {
  let low = value.low;
  let high = value.high;
  let flip = high >> 31;

  // ref: src/google/protobuf/wire_format_lite.h
  writeVarint64(bb, {
    low: (low << 1) ^ flip,
    high: ((high << 1) | (low >>> 31)) ^ flip,
    unsigned: false,
  });
}
