import { _decorator, Component, Node, Vec3, Vec2, Color, Graphics, Camera, EventMouse, Rect } from 'cc';
import { SubLevel, Path, Spawner, MapLayer } from '../core/LevelData';
import { GizmoType, RectUtils } from '../core/Types';

const { ccclass, property } = _decorator;

/**
 * Gizmo handle for interactive editing
 */
interface GizmoHandle {
    id: string;
    type: GizmoType;
    position: Vec3;
    size: number;
    color: Color;
    isSelected: boolean;
    isHovered: boolean;
    data: any; // Associated data (spawner, path point, etc.)
}

/**
 * Editor gizmos for scene view editing
 */
@ccclass('EditorGizmos')
export class EditorGizmos extends Component {
    @property({ type: Graphics })
    public graphics: Graphics | null = null;
    
    @property
    public handleSize: number = 8;
    
    @property
    public lineWidth: number = 2;
    
    @property
    public showLabels: boolean = true;
    
    // Gizmo state
    private gizmoHandles: Map<string, GizmoHandle> = new Map();
    private selectedHandle: GizmoHandle | null = null;
    private hoveredHandle: GizmoHandle | null = null;
    private isDragging: boolean = false;
    private dragOffset: Vec2 = Vec2.ZERO;
    
    // Visual settings
    private colors = {
        [GizmoType.SubLevelBounds]: Color.CYAN,
        [GizmoType.SpawnerPosition]: Color.GREEN,
        [GizmoType.PathPoint]: Color.YELLOW,
        [GizmoType.PathControlPoint]: Color.ORANGE,
        [GizmoType.EntryPoint]: Color.BLUE,
        [GizmoType.ExitPoint]: Color.RED,
        [GizmoType.CameraViewport]: Color.WHITE,
        [GizmoType.ConnectionTrigger]: Color.MAGENTA
    };
    
    // Camera reference
    private camera: Camera | null = null;
    
    // Events
    public onHandleSelected?: (handle: GizmoHandle) => void;
    public onHandleMoved?: (handle: GizmoHandle, newPosition: Vec3) => void;
    public onHandleReleased?: (handle: GizmoHandle) => void;
    
    protected onLoad(): void {
        if (!this.graphics) {
            this.graphics = this.getComponent(Graphics) || this.addComponent(Graphics);
        }
        
        this.camera = Camera.main;
        this.setupMouseEvents();
    }
    
    protected start(): void {
        console.log('EditorGizmos initialized');
    }
    
    /**
     * Setup mouse event handlers
     */
    private setupMouseEvents(): void {
        this.node.on(Node.EventType.MOUSE_DOWN, this.onMouseDown, this);
        this.node.on(Node.EventType.MOUSE_MOVE, this.onMouseMove, this);
        this.node.on(Node.EventType.MOUSE_UP, this.onMouseUp, this);
    }
    
    /**
     * Draw gizmos for a SubLevel
     */
    public drawSubLevelGizmos(subLevel: SubLevel): void {
        this.drawSubLevelBounds(subLevel);
        this.drawEntryExitPoints(subLevel);
        this.drawConnectionTriggers(subLevel);
    }
    
    /**
     * Draw SubLevel bounds
     */
    public drawSubLevelBounds(subLevel: SubLevel): void {
        if (!this.graphics) return;
        
        const bounds = subLevel.bounds;
        const color = this.colors[GizmoType.SubLevelBounds];
        
        // Draw bounds rectangle
        this.graphics.strokeColor = color;
        this.graphics.lineWidth = this.lineWidth;
        this.graphics.rect(bounds.x, bounds.y, bounds.width, bounds.height);
        this.graphics.stroke();
        
        // Add corner handles for resizing
        this.addBoundsHandles(subLevel);
        
        // Draw label
        if (this.showLabels) {
            this.drawLabel(subLevel.name, new Vec3(bounds.x + bounds.width / 2, bounds.y + bounds.height + 20, 0));
        }
    }
    
    /**
     * Draw spawner gizmos
     */
    public drawSpawnerGizmos(spawner: Spawner): void {
        if (!this.graphics) return;
        
        const position = spawner.position;
        const color = spawner.editorData?.gizmoColor || this.colors[GizmoType.SpawnerPosition];
        
        // Draw spawner icon
        this.graphics.fillColor = color;
        this.graphics.circle(position.x, position.y, this.handleSize);
        this.graphics.fill();
        
        // Draw direction indicator if spawner has a path
        if (spawner.pathId) {
            this.drawDirectionIndicator(position, color);
        }
        
        // Add handle
        this.addHandle(
            `spawner_${spawner.id}`,
            GizmoType.SpawnerPosition,
            position,
            color,
            spawner
        );
        
        // Draw label
        if (this.showLabels) {
            this.drawLabel(spawner.name, new Vec3(position.x, position.y + this.handleSize + 15, 0));
        }
    }
    
    /**
     * Draw path gizmos
     */
    public drawPathGizmos(path: Path): void {
        if (!this.graphics || path.points.length < 2) return;
        
        const color = path.editorData?.color || this.colors[GizmoType.PathPoint];
        
        // Draw path curve
        this.drawPathCurve(path, color);
        
        // Draw path points and handles
        path.points.forEach((point, index) => {
            this.drawPathPoint(path, point, index);
        });
        
        // Draw direction indicators
        if (path.editorData?.showDirection) {
            this.drawPathDirections(path);
        }
    }
    
    /**
     * Draw map layer gizmos
     */
    public drawMapLayerGizmos(mapLayer: MapLayer): void {
        if (!this.graphics) return;
        
        const bounds = mapLayer.bounds;
        const color = mapLayer.editorData?.color || Color.WHITE;
        
        // Draw layer bounds with dashed line
        this.drawDashedRect(bounds, color);
        
        // Draw scroll direction indicator
        this.drawScrollIndicator(mapLayer);
        
        // Draw label
        if (this.showLabels) {
            const label = mapLayer.editorData?.name || mapLayer.layerType;
            this.drawLabel(label, new Vec3(bounds.x + 10, bounds.y + 10, 0));
        }
    }
    
    /**
     * Draw camera viewport
     */
    public drawCameraViewport(): void {
        if (!this.graphics || !this.camera) return;
        
        const viewport = this.getCameraViewport();
        const color = this.colors[GizmoType.CameraViewport];
        
        // Draw viewport rectangle
        this.graphics.strokeColor = color;
        this.graphics.lineWidth = this.lineWidth;
        this.graphics.rect(viewport.x, viewport.y, viewport.width, viewport.height);
        this.graphics.stroke();
        
        // Draw center cross
        const centerX = viewport.x + viewport.width / 2;
        const centerY = viewport.y + viewport.height / 2;
        const crossSize = 20;
        
        this.graphics.moveTo(centerX - crossSize, centerY);
        this.graphics.lineTo(centerX + crossSize, centerY);
        this.graphics.moveTo(centerX, centerY - crossSize);
        this.graphics.lineTo(centerX, centerY + crossSize);
        this.graphics.stroke();
    }
    
    /**
     * Clear all gizmos
     */
    public clearGizmos(): void {
        if (this.graphics) {
            this.graphics.clear();
        }
        this.gizmoHandles.clear();
        this.selectedHandle = null;
        this.hoveredHandle = null;
    }
    
    /**
     * Add bounds handles for resizing
     */
    private addBoundsHandles(subLevel: SubLevel): void {
        const bounds = subLevel.bounds;
        const handleSize = this.handleSize;
        const color = this.colors[GizmoType.SubLevelBounds];
        
        // Corner handles
        const corners = [
            { x: bounds.x, y: bounds.y, corner: 'top-left' },
            { x: bounds.x + bounds.width, y: bounds.y, corner: 'top-right' },
            { x: bounds.x, y: bounds.y + bounds.height, corner: 'bottom-left' },
            { x: bounds.x + bounds.width, y: bounds.y + bounds.height, corner: 'bottom-right' }
        ];
        
        corners.forEach(corner => {
            this.addHandle(
                `bounds_${subLevel.id}_${corner.corner}`,
                GizmoType.SubLevelBounds,
                new Vec3(corner.x, corner.y, 0),
                color,
                { subLevel, corner: corner.corner }
            );
        });
    }
    
    /**
     * Draw entry and exit points
     */
    private drawEntryExitPoints(subLevel: SubLevel): void {
        if (!this.graphics) return;
        
        // Entry point
        if (subLevel.entryPoint) {
            const color = this.colors[GizmoType.EntryPoint];
            this.drawArrow(subLevel.entryPoint.position, color, 'entry');
            this.addHandle(
                `entry_${subLevel.id}`,
                GizmoType.EntryPoint,
                subLevel.entryPoint.position,
                color,
                subLevel.entryPoint
            );
        }
        
        // Exit point
        if (subLevel.exitPoint) {
            const color = this.colors[GizmoType.ExitPoint];
            this.drawArrow(subLevel.exitPoint.position, color, 'exit');
            this.addHandle(
                `exit_${subLevel.id}`,
                GizmoType.ExitPoint,
                subLevel.exitPoint.position,
                color,
                subLevel.exitPoint
            );
        }
    }
    
    /**
     * Draw connection triggers
     */
    private drawConnectionTriggers(subLevel: SubLevel): void {
        if (!this.graphics) return;
        
        subLevel.connections.forEach((connection, index) => {
            const color = this.colors[GizmoType.ConnectionTrigger];
            const triggerArea = connection.triggerArea;
            
            // Draw trigger area
            this.graphics.strokeColor = color;
            this.graphics.lineWidth = 1;
            this.graphics.rect(triggerArea.x, triggerArea.y, triggerArea.width, triggerArea.height);
            this.graphics.stroke();
            
            // Draw connection arrow
            const center = RectUtils.getCenter(triggerArea);
            this.drawConnectionArrow(new Vec3(center.x, center.y, 0), connection.targetSubLevelId);
        });
    }
    
    /**
     * Draw path curve
     */
    private drawPathCurve(path: Path, color: Color): void {
        if (!this.graphics) return;
        
        this.graphics.strokeColor = color;
        this.graphics.lineWidth = this.lineWidth;
        
        for (let i = 0; i < path.points.length - 1; i++) {
            const point1 = path.points[i];
            const point2 = path.points[i + 1];
            
            // Draw bezier curve
            const p0 = point1.position;
            const p1 = point1.position.clone().add(point1.controlPoint2);
            const p2 = point2.position.clone().add(point2.controlPoint1);
            const p3 = point2.position;
            
            this.drawBezierCurve(p0, p1, p2, p3);
        }
        
        this.graphics.stroke();
    }
    
    /**
     * Draw a single path point
     */
    private drawPathPoint(path: Path, point: any, index: number): void {
        if (!this.graphics) return;
        
        const color = this.colors[GizmoType.PathPoint];
        const controlColor = this.colors[GizmoType.PathControlPoint];
        
        // Draw main point
        this.graphics.fillColor = color;
        this.graphics.circle(point.position.x, point.position.y, this.handleSize);
        this.graphics.fill();
        
        // Add main point handle
        this.addHandle(
            `path_${path.id}_point_${index}`,
            GizmoType.PathPoint,
            point.position,
            color,
            { path, pointIndex: index, type: 'point' }
        );
        
        // Draw control points if they exist
        if (point.controlPoint1.length() > 0.1) {
            const cp1Pos = point.position.clone().add(point.controlPoint1);
            this.graphics.fillColor = controlColor;
            this.graphics.circle(cp1Pos.x, cp1Pos.y, this.handleSize * 0.7);
            this.graphics.fill();
            
            // Draw control line
            this.graphics.strokeColor = Color.GRAY;
            this.graphics.lineWidth = 1;
            this.graphics.moveTo(point.position.x, point.position.y);
            this.graphics.lineTo(cp1Pos.x, cp1Pos.y);
            this.graphics.stroke();
            
            this.addHandle(
                `path_${path.id}_cp1_${index}`,
                GizmoType.PathControlPoint,
                cp1Pos,
                controlColor,
                { path, pointIndex: index, type: 'controlPoint1' }
            );
        }
        
        if (point.controlPoint2.length() > 0.1) {
            const cp2Pos = point.position.clone().add(point.controlPoint2);
            this.graphics.fillColor = controlColor;
            this.graphics.circle(cp2Pos.x, cp2Pos.y, this.handleSize * 0.7);
            this.graphics.fill();
            
            // Draw control line
            this.graphics.strokeColor = Color.GRAY;
            this.graphics.lineWidth = 1;
            this.graphics.moveTo(point.position.x, point.position.y);
            this.graphics.lineTo(cp2Pos.x, cp2Pos.y);
            this.graphics.stroke();
            
            this.addHandle(
                `path_${path.id}_cp2_${index}`,
                GizmoType.PathControlPoint,
                cp2Pos,
                controlColor,
                { path, pointIndex: index, type: 'controlPoint2' }
            );
        }
    }
    
    /**
     * Add a gizmo handle
     */
    private addHandle(id: string, type: GizmoType, position: Vec3, color: Color, data: any): void {
        const handle: GizmoHandle = {
            id,
            type,
            position: position.clone(),
            size: this.handleSize,
            color,
            isSelected: false,
            isHovered: false,
            data
        };
        
        this.gizmoHandles.set(id, handle);
    }
    
    /**
     * Draw a bezier curve
     */
    private drawBezierCurve(p0: Vec3, p1: Vec3, p2: Vec3, p3: Vec3): void {
        if (!this.graphics) return;
        
        const segments = 20;
        this.graphics.moveTo(p0.x, p0.y);
        
        for (let i = 1; i <= segments; i++) {
            const t = i / segments;
            const u = 1 - t;
            const tt = t * t;
            const uu = u * u;
            const uuu = uu * u;
            const ttt = tt * t;
            
            const x = uuu * p0.x + 3 * uu * t * p1.x + 3 * u * tt * p2.x + ttt * p3.x;
            const y = uuu * p0.y + 3 * uu * t * p1.y + 3 * u * tt * p2.y + ttt * p3.y;
            
            this.graphics.lineTo(x, y);
        }
    }
    
    // Helper methods for drawing various elements
    private drawLabel(text: string, position: Vec3): void {
        // TODO: Implement text rendering for labels
        console.log(`Label: ${text} at ${position.toString()}`);
    }
    
    private drawArrow(position: Vec3, color: Color, type: string): void {
        if (!this.graphics) return;
        
        const size = this.handleSize * 1.5;
        this.graphics.fillColor = color;
        
        // Draw triangle arrow
        this.graphics.moveTo(position.x, position.y - size);
        this.graphics.lineTo(position.x - size * 0.7, position.y + size * 0.5);
        this.graphics.lineTo(position.x + size * 0.7, position.y + size * 0.5);
        this.graphics.close();
        this.graphics.fill();
    }
    
    private drawDirectionIndicator(position: Vec3, color: Color): void {
        if (!this.graphics) return;
        
        const length = this.handleSize * 2;
        this.graphics.strokeColor = color;
        this.graphics.lineWidth = 2;
        this.graphics.moveTo(position.x, position.y);
        this.graphics.lineTo(position.x + length, position.y);
        this.graphics.stroke();
        
        // Arrow head
        this.graphics.moveTo(position.x + length - 5, position.y - 3);
        this.graphics.lineTo(position.x + length, position.y);
        this.graphics.lineTo(position.x + length - 5, position.y + 3);
        this.graphics.stroke();
    }
    
    private drawDashedRect(rect: Rect, color: Color): void {
        // TODO: Implement dashed line drawing
        if (!this.graphics) return;
        
        this.graphics.strokeColor = color;
        this.graphics.lineWidth = 1;
        this.graphics.rect(rect.x, rect.y, rect.width, rect.height);
        this.graphics.stroke();
    }
    
    private drawScrollIndicator(mapLayer: MapLayer): void {
        // TODO: Implement scroll direction indicator
        console.log(`Scroll indicator for layer: ${mapLayer.id}`);
    }
    
    private drawPathDirections(path: Path): void {
        // TODO: Implement path direction indicators
        console.log(`Path directions for: ${path.id}`);
    }
    
    private drawConnectionArrow(position: Vec3, targetId: string): void {
        // TODO: Implement connection arrow
        console.log(`Connection arrow to: ${targetId}`);
    }
    
    private getCameraViewport(): Rect {
        // TODO: Get actual camera viewport
        return new Rect(-960, -540, 1920, 1080);
    }
    
    // Mouse event handlers
    private onMouseDown(event: EventMouse): void {
        // TODO: Implement mouse interaction
    }
    
    private onMouseMove(event: EventMouse): void {
        // TODO: Implement mouse interaction
    }
    
    private onMouseUp(event: EventMouse): void {
        // TODO: Implement mouse interaction
    }
}
