/**
 * CrazyStorm 2.0 Integration for Cocos Creator
 * MultiEmitter Component Implementation
 *
 * This component emits standard particles (without trails),
 * commonly used for bullet patterns, explosions, and effects.
 */

import { _decorator, Vec2, instantiate, log, warn } from 'cc';
import { EmitterComponent } from './EmitterComponent';
import { Particle } from '../physics/ParticlePhysics';
import {
    MultiEmitterComponent as MultiEmitterData,
    ParticleType,
    ComponentType
} from '../core/CrazyStormTypes';

const { ccclass, property } = _decorator;

/**
 * MultiEmitter component for Cocos Creator
 * Extends EmitterComponent to provide standard particle functionality
 */
@ccclass('MultiEmitterComponent')
export class MultiEmitterComponent extends EmitterComponent {
    // ============================================================================
    // ABSTRACT METHOD IMPLEMENTATIONS
    // ============================================================================

    /**
     * Create a new standard particle instance
     */
    protected createParticle(): Particle {
        const particleId = Math.floor(Math.random() * 10000);
        const defaultParticleType: ParticleType = {
            id: 1000,
            name: 'DefaultParticle',
            imageId: 1000,
            radius: 3,
            textureRect: { x: 0, y: 0, width: 16, height: 16 }
        };

        const particle = new Particle(
            particleId,
            this.particleType || defaultParticleType,
            this.componentId,
            0 // layerId
        );

        if (this.particleBaseData) {
            // Initialize particle with base data
            particle.maxLife = this.particleBaseData.maxLife;
            particle.mass = this.particleBaseData.mass;
            particle.opacity = this.particleBaseData.opacity;
            particle.collision = this.particleBaseData.collision;
            particle.killOutside = this.particleBaseData.killOutside;
            particle.fadeEffect = this.particleBaseData.fadeEffect;
            particle.fogEffect = this.particleBaseData.fogEffect;
        }

        return particle;
    }

    /**
     * Initialize a standard particle with position and angle
     */
    protected initializeParticle(particle: Particle, position: Vec2, angle: number): void {
        // Reset particle state
        particle.alive = true;
        particle.currentFrame = 0;
        particle.position.set(position.x, position.y);

        // Set velocity from particle base data
        if (this.particleBaseData) {
            const speed = this.particleBaseData.pspeed;
            const speedAngle = angle + this.particleBaseData.pspeedAngle;
            const radians = speedAngle * Math.PI / 180;

            particle.velocity.set(
                speed * Math.cos(radians),
                speed * Math.sin(radians)
            );

            // Set acceleration
            const acSpeed = this.particleBaseData.pacspeed;
            const acAngle = angle + this.particleBaseData.pacspeedAngle;
            const acRadians = acAngle * Math.PI / 180;

            particle.acceleration.set(
                acSpeed * Math.cos(acRadians),
                acSpeed * Math.sin(acRadians)
            );

            // Set rotation
            particle.rotation = this.particleBaseData.protation;
        }

        // Create or reuse particle node
        if (!particle.node || !particle.node.isValid) {
            if (this.particlePrefab) {
                particle.node = instantiate(this.particlePrefab);
                this.node.addChild(particle.node);
                this.particleNodes.push(particle.node);
            }
        }

        if (particle.node) {
            particle.node.active = true;
            particle.node.setPosition(position.x, position.y, 0);
        }
    }

    /**
     * Called when CrazyStorm data is initialized
     */
    protected onDataInitialized(): void {
        if (!this.crazyStormData || this.crazyStormData.specificType !== ComponentType.MultiEmitter) {
            warn('MultiEmitterComponent: Invalid CrazyStorm data');
            return;
        }

        const data = this.crazyStormData as MultiEmitterData;

        // Initialize base emitter data
        this.initializeEmitterData(
            data.emitter.emitterData,
            data.emitter.particleBase.particleBaseData,
            data.emitter.particleEventGroups
        );

        // Create particle type
        this.particleType = {
            id: data.emitter.particleBase.type,
            name: `Particle_${this.componentId}`,
            imageId: data.emitter.particleBase.type,
            radius: 3, // Default radius for standard particles
            textureRect: { x: 0, y: 0, width: 16, height: 16 }
        };

        log(`MultiEmitterComponent: Initialized with ${this.emitCount} particles per cycle`);
    }
}
