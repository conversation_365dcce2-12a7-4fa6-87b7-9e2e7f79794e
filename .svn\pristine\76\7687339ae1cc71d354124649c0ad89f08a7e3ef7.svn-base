/**
 * CrazyStorm 2.0 Integration for Cocos Creator
 * CurveEmitter Component Implementation
 *
 * This component emits particles that create trails/curves,
 * commonly used for bullet patterns and visual effects.
 */

import { _decorator, Vec2, instantiate, log, warn } from 'cc';
import { EmitterComponent } from './EmitterComponent';
import { CurveParticle } from '../physics/ParticlePhysics';
import {
    CurveEmitterComponent as CurveEmitterData,
    CurveParticleData,
    ParticleType,
    ComponentType
} from '../core/CrazyStormTypes';

const { ccclass, property } = _decorator;

/**
 * CurveEmitter component for Cocos Creator
 * Extends EmitterComponent to provide curve particle functionality
 */
@ccclass('CurveEmitterComponent')
export class CurveEmitterComponent extends EmitterComponent {
    @property({ displayName: "Curve Length" })
    curveLength: number = 10;

    // Curve-specific data
    private curveParticleData: CurveParticleData | null = null;

    // Override particles array to use CurveParticle type
    protected particles: CurveParticle[] = [];
    protected particlePool: CurveParticle[] = [];

    // ============================================================================
    // ABSTRACT METHOD IMPLEMENTATIONS
    // ============================================================================

    /**
     * Create a new curve particle instance
     */
    protected createParticle(): CurveParticle {
        const particleId = Math.floor(Math.random() * 10000);
        const defaultParticleType: ParticleType = {
            id: 1000,
            name: 'DefaultCurveParticle',
            imageId: 1000,
            radius: 3,
            textureRect: { x: 0, y: 0, width: 16, height: 16 }
        };

        const particle = new CurveParticle(
            particleId,
            this.particleType || defaultParticleType,
            this.componentId,
            0, // layerId
            this.curveParticleData?.length || this.curveLength
        );

        if (this.particleBaseData) {
            // Initialize particle with base data
            particle.maxLife = this.particleBaseData.maxLife;
            particle.mass = this.particleBaseData.mass;
            particle.opacity = this.particleBaseData.opacity;
            particle.collision = this.particleBaseData.collision;
            particle.killOutside = this.particleBaseData.killOutside;
            particle.fadeEffect = this.particleBaseData.fadeEffect;
            particle.fogEffect = this.particleBaseData.fogEffect;
        }

        return particle;
    }

    /**
     * Initialize a curve particle with position and angle
     */
    protected initializeParticle(particle: CurveParticle, position: Vec2, angle: number): void {
        // Reset particle state
        particle.alive = true;
        particle.currentFrame = 0;
        particle.position.set(position.x, position.y);

        // Set velocity from particle base data
        if (this.particleBaseData) {
            const speed = this.particleBaseData.pspeed;
            const speedAngle = angle + this.particleBaseData.pspeedAngle;
            const radians = speedAngle * Math.PI / 180;

            particle.velocity.set(
                speed * Math.cos(radians),
                speed * Math.sin(radians)
            );

            // Set acceleration
            const acSpeed = this.particleBaseData.pacspeed;
            const acAngle = angle + this.particleBaseData.pacspeedAngle;
            const acRadians = acAngle * Math.PI / 180;

            particle.acceleration.set(
                acSpeed * Math.cos(acRadians),
                acSpeed * Math.sin(acRadians)
            );

            // Set rotation
            particle.rotation = this.particleBaseData.protation;
        }

        // Initialize curve trail
        particle.trail = [position.clone()];

        // Create or reuse particle node
        if (!particle.node || !particle.node.isValid) {
            if (this.particlePrefab) {
                particle.node = instantiate(this.particlePrefab);
                this.node.addChild(particle.node);
                this.particleNodes.push(particle.node);
            }
        }

        if (particle.node) {
            particle.node.active = true;
            particle.node.setPosition(position.x, position.y, 0);
        }
    }

    /**
     * Called when CrazyStorm data is initialized
     */
    protected onDataInitialized(): void {
        if (!this.crazyStormData || this.crazyStormData.specificType !== ComponentType.CurveEmitter) {
            warn('CurveEmitterComponent: Invalid CrazyStorm data');
            return;
        }

        const data = this.crazyStormData as CurveEmitterData;

        // Store curve-specific data
        this.curveParticleData = data.emitter.particleBase.curveParticle?.curveParticleData || { length: 10 };
        this.curveLength = this.curveParticleData.length;

        // Initialize base emitter data
        this.initializeEmitterData(
            data.emitter.emitterData,
            data.emitter.particleBase.particleBaseData,
            data.emitter.particleEventGroups
        );

        // Create particle type
        this.particleType = {
            id: data.emitter.particleBase.type,
            name: `CurveParticle_${this.componentId}`,
            imageId: data.emitter.particleBase.type,
            radius: 3, // Default radius for curve particles
            textureRect: { x: 0, y: 0, width: 16, height: 16 }
        };

        log(`CurveEmitterComponent: Initialized with ${this.emitCount} curve particles per cycle`);
    }

    // ============================================================================
    // CURVE-SPECIFIC OVERRIDES
    // ============================================================================

    /**
     * Override particle update to handle curve trail updates
     */
    protected updateParticle(particle: CurveParticle, deltaTime: number): void {
        // Call base particle update
        super.updateParticle(particle, deltaTime);

        // Update curve trail (call public method if available)
        if (typeof (particle as any).updateTrail === 'function') {
            (particle as any).updateTrail();
        }
    }
}
