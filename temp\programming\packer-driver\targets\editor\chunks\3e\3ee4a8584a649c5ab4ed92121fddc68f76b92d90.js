System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, instantiate, Global, PersistNode, PlayerBullet, GameFactory, PlayerBulletFactory, _crd, ccclass, property;

  function _reportPossibleCrUseOfGlobal(extras) {
    _reporterNs.report("Global", "../Global", _context.meta, extras);
  }

  function _reportPossibleCrUseOfPersistNode(extras) {
    _reporterNs.report("PersistNode", "../PersistNode", _context.meta, extras);
  }

  function _reportPossibleCrUseOfPlayerBullet(extras) {
    _reporterNs.report("PlayerBullet", "../PlayerBullet", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameFactory(extras) {
    _reporterNs.report("GameFactory", "./GameFactory", _context.meta, extras);
  }

  _export("PlayerBulletFactory", void 0);

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      instantiate = _cc.instantiate;
    }, function (_unresolved_2) {
      Global = _unresolved_2.Global;
    }, function (_unresolved_3) {
      PersistNode = _unresolved_3.PersistNode;
    }, function (_unresolved_4) {
      PlayerBullet = _unresolved_4.PlayerBullet;
    }, function (_unresolved_5) {
      GameFactory = _unresolved_5.GameFactory;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "73373aDY59JjqjAhxODJXy+", "PlayerBulletFactory", undefined);

      __checkObsolete__(['_decorator', 'Component', 'Node', 'instantiate']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("PlayerBulletFactory", PlayerBulletFactory = class PlayerBulletFactory extends (_crd && GameFactory === void 0 ? (_reportPossibleCrUseOfGameFactory({
        error: Error()
      }), GameFactory) : GameFactory) {
        createProduct(productType) {
          let playBulletTemp = null;

          if (this.productPool.size() > 0) {
            playBulletTemp = this.productPool.get(); //如果池里有子弹，就直接拿来用
          } else {
            playBulletTemp = instantiate(this.persistNode.getComponent(_crd && PersistNode === void 0 ? (_reportPossibleCrUseOfPersistNode({
              error: Error()
            }), PersistNode) : PersistNode).playerBulletPreb); //从常驻节点拿到预制体原料
          }

          switch (productType) {
            case (_crd && Global === void 0 ? (_reportPossibleCrUseOfGlobal({
              error: Error()
            }), Global) : Global).NORMAL_BULLET:
              playBulletTemp.getComponent(_crd && PlayerBullet === void 0 ? (_reportPossibleCrUseOfPlayerBullet({
                error: Error()
              }), PlayerBullet) : PlayerBullet).init(productType, this.persistNode.getComponent(_crd && PersistNode === void 0 ? (_reportPossibleCrUseOfPersistNode({
                error: Error()
              }), PersistNode) : PersistNode).normalBullet); //通过调用PlayerBullet的init方法，来创建子弹

              break;

            case (_crd && Global === void 0 ? (_reportPossibleCrUseOfGlobal({
              error: Error()
            }), Global) : Global).LIGHT_BULLET:
              playBulletTemp.getComponent(_crd && PlayerBullet === void 0 ? (_reportPossibleCrUseOfPlayerBullet({
                error: Error()
              }), PlayerBullet) : PlayerBullet).init(productType, this.persistNode.getComponent(_crd && PersistNode === void 0 ? (_reportPossibleCrUseOfPersistNode({
                error: Error()
              }), PersistNode) : PersistNode).lightBullet);
              break;

            case (_crd && Global === void 0 ? (_reportPossibleCrUseOfGlobal({
              error: Error()
            }), Global) : Global).MISSILE_BULLET:
              playBulletTemp.getComponent(_crd && PlayerBullet === void 0 ? (_reportPossibleCrUseOfPlayerBullet({
                error: Error()
              }), PlayerBullet) : PlayerBullet).init(productType, this.persistNode.getComponent(_crd && PersistNode === void 0 ? (_reportPossibleCrUseOfPersistNode({
                error: Error()
              }), PersistNode) : PersistNode).missileBullet);
              break;
          }

          return playBulletTemp;
        }

      });

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=3ee4a8584a649c5ab4ed92121fddc68f76b92d90.js.map