System.register(["cc", "cc/env"], function (_export, _context) {
  "use strict";

  var _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Component, director, LabelComponent, ProgressBar, ccenum, WECHAT, ResLoadingTips, _dec, _dec2, _dec3, _dec4, _dec5, _dec6, _class2, _class3, _descriptor, _descriptor2, _descriptor3, _descriptor4, _descriptor5, _crd, ccclass, property, warnCustom, groupCustom, GameLogLevel, ResUpdate;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  return {
    setters: [function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Component = _cc.Component;
      director = _cc.director;
      LabelComponent = _cc.LabelComponent;
      ProgressBar = _cc.ProgressBar;
      ccenum = _cc.ccenum;
    }, function (_ccEnv) {
      WECHAT = _ccEnv.WECHAT;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "30734ypWehPU5ZyhV2WDXQn", "ResUpdate", undefined);

      __checkObsolete__(['_decorator', 'Component', 'Prefab', 'Node', 'loader', 'director', 'LabelComponent', 'ProgressBar', 'JsonAsset', 'math', 'Enum', 'CCString', 'ccenum', 'resources']);

      ({
        ccclass,
        property
      } = _decorator);
      ResLoadingTips = class ResLoadingTips {
        constructor() {
          this.Progress = void 0;
          this.Context = void 0;
        }

      };

      if (WECHAT) {
        warnCustom = console.warn;

        console.warn = function (res) {
          if (typeof res == "string" && res.indexOf("文件路径在真机上可能无法读取") > -1) {
            return;
          } else {
            warnCustom(res);
          }
        };

        groupCustom = console.group;

        console.group = function (res) {
          if (typeof res == "string" && res.indexOf("读取文件/文件夹警告") > -1) {
            return;
          } else {
            groupCustom(res);
          }
        };
      }

      GameLogLevel = /*#__PURE__*/function (GameLogLevel) {
        GameLogLevel[GameLogLevel["TRACE"] = 0] = "TRACE";
        GameLogLevel[GameLogLevel["DEBUG"] = 1] = "DEBUG";
        GameLogLevel[GameLogLevel["LOG"] = 2] = "LOG";
        GameLogLevel[GameLogLevel["INFO"] = 3] = "INFO";
        GameLogLevel[GameLogLevel["WARN"] = 4] = "WARN";
        GameLogLevel[GameLogLevel["ERROR"] = 5] = "ERROR";
        return GameLogLevel;
      }(GameLogLevel || {});

      ccenum(GameLogLevel);

      _export("ResUpdate", ResUpdate = (_dec = ccclass("ResUpdate"), _dec2 = property(LabelComponent), _dec3 = property(LabelComponent), _dec4 = property(LabelComponent), _dec5 = property(ProgressBar), _dec6 = property({
        type: GameLogLevel
      }), _dec(_class2 = (_class3 = class ResUpdate extends Component {
        constructor() {
          super(...arguments);

          /* class member could be defined like this */
          // dummy = '';
          _initializerDefineProperty(this, "countLabel", _descriptor, this);

          _initializerDefineProperty(this, "perLabel", _descriptor2, this);

          _initializerDefineProperty(this, "versionLabel", _descriptor3, this);

          _initializerDefineProperty(this, "loadingBar", _descriptor4, this);

          //private isMainSceneLoaded = false;
          _initializerDefineProperty(this, "logLevel", _descriptor5, this);
        }

        start() {
          this.SetLogLevel(); // Your initialization goes here.

          var THIS = this;
          cc.loader.onProgress = this.OnLoadProgress.bind(this);
          console.log('ybgg start load main scene');
          director.loadScene("Main", () => {
            console.log("load main scene success");
          }); //director.preloadScene("MainScene", this.OnLoadProgress.bind(this), () => {
          //this.isMainSceneLoaded = true;
          //console.log("ybgg load main scene success")
          //})
        }

        SetLogLevel() {
          switch (this.logLevel) {
            case GameLogLevel.ERROR:
              console.warn = () => {};

            case GameLogLevel.WARN:
              console.info = () => {};

            case GameLogLevel.INFO:
              console.log = () => {};

            case GameLogLevel.LOG:
              console.debug = () => {};

            case GameLogLevel.DEBUG:
              console.trace = () => {};

            case GameLogLevel.TRACE:
          }
        }

        OnLoadProgress(completedCount, totalCount, item) {
          var progress = completedCount / totalCount;
          console.log('ybgg load main scene', completedCount, totalCount, (progress * 100).toFixed(2), item.id);

          if (this.node == null) {
            return;
          }

          this.perLabel.string = (progress * 100).toFixed(2) + "%";
          this.countLabel.string = '加载中...(' + completedCount + '/' + totalCount + ')';
          this.loadingBar.progress = progress;
        }

        onLoad() {}

        onDestroy() {
          cc.loader.onProgress = null;
        }

        update(deltaTime) {//if (this.isMainSceneLoaded) //&& window.GameInstance && window.GameInstance.getLogin().isLogined)
          //{
          //    director.loadScene("MainScene")
          //}
          // Your update function goes here.
        }

      }, (_descriptor = _applyDecoratedDescriptor(_class3.prototype, "countLabel", [_dec2], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor2 = _applyDecoratedDescriptor(_class3.prototype, "perLabel", [_dec3], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor3 = _applyDecoratedDescriptor(_class3.prototype, "versionLabel", [_dec4], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor4 = _applyDecoratedDescriptor(_class3.prototype, "loadingBar", [_dec5], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor5 = _applyDecoratedDescriptor(_class3.prototype, "logLevel", [_dec6], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return GameLogLevel.TRACE;
        }
      })), _class3)) || _class2));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=b3433f15e11500991d9cadb4ca4daa00193597a3.js.map