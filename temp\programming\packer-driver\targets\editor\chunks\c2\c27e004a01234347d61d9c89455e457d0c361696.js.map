{"version": 3, "sources": ["file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/gizmos/EmitterGizmo.ts"], "names": ["EmitterGizmo", "Color", "GizmoDrawer", "Emitter", "componentType", "drawerName", "showDirections", "showCenter", "directionColor", "RED", "centerColor", "WHITE", "speedScale", "arrowSize", "centerSize", "drawGizmos", "emitter", "graphics", "node", "worldPos", "worldPosition", "worldRot", "worldRotation", "worldScale", "save", "translate", "x", "y", "rotate", "z", "scale", "drawCenter", "count", "drawDirections", "restore", "drawCross", "baseLength", "speedFactor", "speedMultiplier", "<PERSON><PERSON><PERSON><PERSON>", "Math", "max", "i", "dirX", "dirY", "startX", "startY", "endX", "endY", "drawArrow", "getPriority", "configure", "options", "undefined"], "mappings": ";;;4GAQaA,Y;;;;;;;;;;;;;;;;;;;AARkBC,MAAAA,K,OAAAA,K;;AACtBC,MAAAA,W,iBAAAA,W;;AACAC,MAAAA,O,iBAAAA,O;;;;;;;;;AAET;AACA;AACA;AACA;8BACaH,Y,GAAN,MAAMA,YAAN;AAAA;AAAA,sCAAgD;AAAA;AAAA;AAAA,eAEnCI,aAFmC;AAAA;AAAA;AAAA,eAGnCC,UAHmC,GAGtB,cAHsB;AAKnD;AALmD,eAM5CC,cAN4C,GAMlB,IANkB;AAAA,eAO5CC,UAP4C,GAOtB,IAPsB;AASnD;AATmD,eAU5CC,cAV4C,GAUpBP,KAAK,CAACQ,GAVc;AAAA,eAW5CC,WAX4C,GAWvBT,KAAK,CAACU,KAXiB;AAanD;AAbmD,eAc5CC,UAd4C,GAcvB,GAduB;AAAA,eAe5CC,SAf4C,GAexB,CAfwB;AAAA,eAgB5CC,UAhB4C,GAgBvB,CAhBuB;AAAA;;AAkB5CC,QAAAA,UAAU,CAACC,OAAD,EAAmBC,QAAnB,EAAuCC,IAAvC,EAAyD;AACtE;AACA,gBAAMC,QAAQ,GAAGD,IAAI,CAACE,aAAtB;AACA,gBAAMC,QAAQ,GAAGH,IAAI,CAACI,aAAtB;AACA,gBAAMC,UAAU,GAAGL,IAAI,CAACK,UAAxB,CAJsE,CAMtE;;AACAN,UAAAA,QAAQ,CAACO,IAAT,GAPsE,CAStE;;AACAP,UAAAA,QAAQ,CAACQ,SAAT,CAAmBN,QAAQ,CAACO,CAA5B,EAA+BP,QAAQ,CAACQ,CAAxC;AACAV,UAAAA,QAAQ,CAACW,MAAT,CAAgBP,QAAQ,CAACQ,CAAzB;AACAZ,UAAAA,QAAQ,CAACa,KAAT,CAAeP,UAAU,CAACG,CAA1B,EAA6BH,UAAU,CAACI,CAAxC,EAZsE,CActE;;AACA,cAAI,KAAKpB,UAAT,EAAqB;AACjB,iBAAKwB,UAAL,CAAgBd,QAAhB;AACH,WAjBqE,CAmBtE;;;AACA,cAAI,KAAKX,cAAL,IAAuBU,OAAO,CAACgB,KAAR,GAAgB,CAA3C,EAA8C;AAC1C,iBAAKC,cAAL,CAAoBhB,QAApB,EAA8BD,OAA9B;AACH,WAtBqE,CAwBtE;;;AACAC,UAAAA,QAAQ,CAACiB,OAAT;AACH;;AAEOH,QAAAA,UAAU,CAACd,QAAD,EAA2B;AACzC,eAAKkB,SAAL,CAAelB,QAAf,EAAyB,CAAzB,EAA4B,CAA5B,EAA+B,KAAKH,UAApC,EAAgD,KAAKJ,WAArD;AACH;;AAEOuB,QAAAA,cAAc,CAAChB,QAAD,EAAqBD,OAArB,EAA6C;AAC/D,gBAAMoB,UAAU,GAAG,EAAnB;AACA,gBAAMC,WAAW,GAAGrB,OAAO,CAACsB,eAAR,IAA2B,CAA/C;AACA,gBAAMC,WAAW,GAAGC,IAAI,CAACC,GAAL,CAASL,UAAT,EAAqBA,UAAU,GAAGC,WAAb,GAA2B,KAAKzB,UAArD,CAApB;;AAEA,eAAK,IAAI8B,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG1B,OAAO,CAACgB,KAA5B,EAAmCU,CAAC,EAApC,EAAwC;AACpC;AACA,kBAAMC,IAAI,GAAG,CAAb;AACA,kBAAMC,IAAI,GAAG,CAAb;AAEA,kBAAMC,MAAM,GAAG,CAAf;AACA,kBAAMC,MAAM,GAAG,CAAf;AACA,kBAAMC,IAAI,GAAGJ,IAAI,GAAGJ,WAApB;AACA,kBAAMS,IAAI,GAAGJ,IAAI,GAAGL,WAApB,CARoC,CAUpC;;AACA,iBAAKU,SAAL,CAAehC,QAAf,EAAyB4B,MAAzB,EAAiCC,MAAjC,EAAyCC,IAAzC,EAA+CC,IAA/C,EAAqD,KAAKxC,cAA1D,EAA0E,KAAKK,SAA/E;AACH;AACJ;;AAEMqC,QAAAA,WAAW,GAAW;AACzB,iBAAO,CAAP,CADyB,CACf;AACb;AAED;AACJ;AACA;;;AACWC,QAAAA,SAAS,CAACC,OAAD,EAQP;AACL,cAAIA,OAAO,CAAC9C,cAAR,KAA2B+C,SAA/B,EAA0C,KAAK/C,cAAL,GAAsB8C,OAAO,CAAC9C,cAA9B;AAC1C,cAAI8C,OAAO,CAAC7C,UAAR,KAAuB8C,SAA3B,EAAsC,KAAK9C,UAAL,GAAkB6C,OAAO,CAAC7C,UAA1B;AACtC,cAAI6C,OAAO,CAAC5C,cAAR,KAA2B6C,SAA/B,EAA0C,KAAK7C,cAAL,GAAsB4C,OAAO,CAAC5C,cAA9B;AAC1C,cAAI4C,OAAO,CAAC1C,WAAR,KAAwB2C,SAA5B,EAAuC,KAAK3C,WAAL,GAAmB0C,OAAO,CAAC1C,WAA3B;AACvC,cAAI0C,OAAO,CAACxC,UAAR,KAAuByC,SAA3B,EAAsC,KAAKzC,UAAL,GAAkBwC,OAAO,CAACxC,UAA1B;AACtC,cAAIwC,OAAO,CAACvC,SAAR,KAAsBwC,SAA1B,EAAqC,KAAKxC,SAAL,GAAiBuC,OAAO,CAACvC,SAAzB;AACrC,cAAIuC,OAAO,CAACtC,UAAR,KAAuBuC,SAA3B,EAAsC,KAAKvC,UAAL,GAAkBsC,OAAO,CAACtC,UAA1B;AACzC;;AA7FkD,O", "sourcesContent": ["import { _decorator, Graphics, Color, Node } from 'cc';\nimport { GizmoDrawer } from './GizmoDrawer';\nimport { Emitter } from '../world/bullet/Emitter';\n\n/**\n * Gizmo drawer for base Emitter components\n * Draws visual debugging information for basic bullet emitters\n */\nexport class EmitterGizmo extends GizmoDrawer<Emitter> {\n    \n    public readonly componentType = Emitter;\n    public readonly drawerName = \"EmitterGizmo\";\n    \n    // Gizmo display options\n    public showDirections: boolean = true;\n    public showCenter: boolean = true;\n    \n    // Colors\n    public directionColor: Color = Color.RED;\n    public centerColor: Color = Color.WHITE;\n    \n    // Display settings\n    public speedScale: number = 1.0;\n    public arrowSize: number = 8;\n    public centerSize: number = 8;\n    \n    public drawGizmos(emitter: Emitter, graphics: Graphics, node: Node): void {\n        // Set transform to match the emitter's node\n        const worldPos = node.worldPosition;\n        const worldRot = node.worldRotation;\n        const worldScale = node.worldScale;\n        \n        // Save current transform\n        graphics.save();\n        \n        // Apply node transform\n        graphics.translate(worldPos.x, worldPos.y);\n        graphics.rotate(worldRot.z);\n        graphics.scale(worldScale.x, worldScale.y);\n        \n        // Draw center point\n        if (this.showCenter) {\n            this.drawCenter(graphics);\n        }\n        \n        // Draw direction arrows\n        if (this.showDirections && emitter.count > 0) {\n            this.drawDirections(graphics, emitter);\n        }\n        \n        // Restore transform\n        graphics.restore();\n    }\n    \n    private drawCenter(graphics: Graphics): void {\n        this.drawCross(graphics, 0, 0, this.centerSize, this.centerColor);\n    }\n    \n    private drawDirections(graphics: Graphics, emitter: Emitter): void {\n        const baseLength = 30;\n        const speedFactor = emitter.speedMultiplier || 1;\n        const arrowLength = Math.max(baseLength, baseLength * speedFactor * this.speedScale);\n        \n        for (let i = 0; i < emitter.count; i++) {\n            // For basic emitters, just draw simple forward direction (up)\n            const dirX = 0;\n            const dirY = 1;\n            \n            const startX = 0;\n            const startY = 0;\n            const endX = dirX * arrowLength;\n            const endY = dirY * arrowLength;\n            \n            // Draw arrow\n            this.drawArrow(graphics, startX, startY, endX, endY, this.directionColor, this.arrowSize);\n        }\n    }\n    \n    public getPriority(): number {\n        return 5; // Draw base emitter gizmos with lower priority than EmitterArc\n    }\n    \n    /**\n     * Configure display options\n     */\n    public configure(options: {\n        showDirections?: boolean;\n        showCenter?: boolean;\n        directionColor?: Color;\n        centerColor?: Color;\n        speedScale?: number;\n        arrowSize?: number;\n        centerSize?: number;\n    }): void {\n        if (options.showDirections !== undefined) this.showDirections = options.showDirections;\n        if (options.showCenter !== undefined) this.showCenter = options.showCenter;\n        if (options.directionColor !== undefined) this.directionColor = options.directionColor;\n        if (options.centerColor !== undefined) this.centerColor = options.centerColor;\n        if (options.speedScale !== undefined) this.speedScale = options.speedScale;\n        if (options.arrowSize !== undefined) this.arrowSize = options.arrowSize;\n        if (options.centerSize !== undefined) this.centerSize = options.centerSize;\n    }\n}\n"]}