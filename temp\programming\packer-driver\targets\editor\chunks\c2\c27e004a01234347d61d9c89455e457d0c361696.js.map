{"version": 3, "sources": ["file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/gizmos/EmitterGizmo.ts"], "names": ["EmitterGizmo", "Color", "GizmoDrawer", "Emitter", "componentType", "drawerName", "showDirections", "showCenter", "directionColor", "RED", "centerColor", "WHITE", "speedScale", "arrowSize", "centerSize", "drawGizmos", "emitter", "graphics", "node", "worldPos", "worldPosition", "drawCenter", "x", "y", "count", "drawDirections", "worldX", "worldY", "drawCross", "baseLength", "speedFactor", "speedMultiplier", "<PERSON><PERSON><PERSON><PERSON>", "Math", "max", "i", "dirX", "dirY", "startX", "startY", "endX", "endY", "drawArrow", "getPriority", "configure", "options", "undefined"], "mappings": ";;;4GASaA,Y;;;;;;;;;;;;;;;;;;;AATkBC,MAAAA,K,OAAAA,K;;AACtBC,MAAAA,W,iBAAAA,W;;AAEAC,MAAAA,O,iBAAAA,O;;;;;;;;;AAET;AACA;AACA;AACA;8BACaH,Y,GAAN,MAAMA,YAAN;AAAA;AAAA,sCAAgD;AAAA;AAAA;AAAA,eAEnCI,aAFmC;AAAA;AAAA;AAAA,eAGnCC,UAHmC,GAGtB,cAHsB;AAKnD;AALmD,eAM5CC,cAN4C,GAMlB,IANkB;AAAA,eAO5CC,UAP4C,GAOtB,IAPsB;AASnD;AATmD,eAU5CC,cAV4C,GAUpBP,KAAK,CAACQ,GAVc;AAAA,eAW5CC,WAX4C,GAWvBT,KAAK,CAACU,KAXiB;AAanD;AAbmD,eAc5CC,UAd4C,GAcvB,GAduB;AAAA,eAe5CC,SAf4C,GAexB,CAfwB;AAAA,eAgB5CC,UAhB4C,GAgBvB,CAhBuB;AAAA;;AAkB5CC,QAAAA,UAAU,CAACC,OAAD,EAAmBC,QAAnB,EAAuCC,IAAvC,EAAyD;AACtE;AACA,gBAAMC,QAAQ,GAAGD,IAAI,CAACE,aAAtB,CAFsE,CAItE;;AACA,cAAI,KAAKb,UAAT,EAAqB;AACjB,iBAAKc,UAAL,CAAgBJ,QAAhB,EAA0BE,QAAQ,CAACG,CAAnC,EAAsCH,QAAQ,CAACI,CAA/C;AACH,WAPqE,CAStE;;;AACA,cAAI,KAAKjB,cAAL,IAAuBU,OAAO,CAACQ,KAAR,GAAgB,CAA3C,EAA8C;AAC1C,iBAAKC,cAAL,CAAoBR,QAApB,EAA8BD,OAA9B,EAAuCG,QAAQ,CAACG,CAAhD,EAAmDH,QAAQ,CAACI,CAA5D;AACH;AACJ;;AAEOF,QAAAA,UAAU,CAACJ,QAAD,EAAqBS,MAArB,EAAqCC,MAArC,EAA2D;AACzE,eAAKC,SAAL,CAAeX,QAAf,EAAyBS,MAAzB,EAAiCC,MAAjC,EAAyC,KAAKb,UAA9C,EAA0D,KAAKJ,WAA/D;AACH;;AAEOe,QAAAA,cAAc,CAACR,QAAD,EAAqBD,OAArB,EAAuCU,MAAvC,EAAuDC,MAAvD,EAA6E;AAC/F,gBAAME,UAAU,GAAG,EAAnB;AACA,gBAAMC,WAAW,GAAGd,OAAO,CAACe,eAAR,IAA2B,CAA/C;AACA,gBAAMC,WAAW,GAAGC,IAAI,CAACC,GAAL,CAASL,UAAT,EAAqBA,UAAU,GAAGC,WAAb,GAA2B,KAAKlB,UAArD,CAApB;;AAEA,eAAK,IAAIuB,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGnB,OAAO,CAACQ,KAA5B,EAAmCW,CAAC,EAApC,EAAwC;AACpC;AACA,kBAAMC,IAAI,GAAG,CAAb;AACA,kBAAMC,IAAI,GAAG,CAAb;AAEA,kBAAMC,MAAM,GAAGZ,MAAf;AACA,kBAAMa,MAAM,GAAGZ,MAAf;AACA,kBAAMa,IAAI,GAAGd,MAAM,GAAGU,IAAI,GAAGJ,WAA7B;AACA,kBAAMS,IAAI,GAAGd,MAAM,GAAGU,IAAI,GAAGL,WAA7B,CARoC,CAUpC;;AACA,iBAAKU,SAAL,CAAezB,QAAf,EAAyBqB,MAAzB,EAAiCC,MAAjC,EAAyCC,IAAzC,EAA+CC,IAA/C,EAAqD,KAAKjC,cAA1D,EAA0E,KAAKK,SAA/E;AACH;AACJ;;AAEM8B,QAAAA,WAAW,GAAW;AACzB,iBAAO,CAAP,CADyB,CACf;AACb;AAED;AACJ;AACA;;;AACWC,QAAAA,SAAS,CAACC,OAAD,EAQP;AACL,cAAIA,OAAO,CAACvC,cAAR,KAA2BwC,SAA/B,EAA0C,KAAKxC,cAAL,GAAsBuC,OAAO,CAACvC,cAA9B;AAC1C,cAAIuC,OAAO,CAACtC,UAAR,KAAuBuC,SAA3B,EAAsC,KAAKvC,UAAL,GAAkBsC,OAAO,CAACtC,UAA1B;AACtC,cAAIsC,OAAO,CAACrC,cAAR,KAA2BsC,SAA/B,EAA0C,KAAKtC,cAAL,GAAsBqC,OAAO,CAACrC,cAA9B;AAC1C,cAAIqC,OAAO,CAACnC,WAAR,KAAwBoC,SAA5B,EAAuC,KAAKpC,WAAL,GAAmBmC,OAAO,CAACnC,WAA3B;AACvC,cAAImC,OAAO,CAACjC,UAAR,KAAuBkC,SAA3B,EAAsC,KAAKlC,UAAL,GAAkBiC,OAAO,CAACjC,UAA1B;AACtC,cAAIiC,OAAO,CAAChC,SAAR,KAAsBiC,SAA1B,EAAqC,KAAKjC,SAAL,GAAiBgC,OAAO,CAAChC,SAAzB;AACrC,cAAIgC,OAAO,CAAC/B,UAAR,KAAuBgC,SAA3B,EAAsC,KAAKhC,UAAL,GAAkB+B,OAAO,CAAC/B,UAA1B;AACzC;;AAhFkD,O", "sourcesContent": ["import { _decorator, Graphics, Color, Node } from 'cc';\nimport { GizmoDrawer } from './GizmoDrawer';\nimport { GizmoUtils } from './GizmoUtils';\nimport { Emitter } from '../world/bullet/Emitter';\n\n/**\n * Gizmo drawer for base Emitter components\n * Draws visual debugging information for basic bullet emitters\n */\nexport class EmitterGizmo extends GizmoDrawer<Emitter> {\n    \n    public readonly componentType = Emitter;\n    public readonly drawerName = \"EmitterGizmo\";\n    \n    // Gizmo display options\n    public showDirections: boolean = true;\n    public showCenter: boolean = true;\n    \n    // Colors\n    public directionColor: Color = Color.RED;\n    public centerColor: Color = Color.WHITE;\n    \n    // Display settings\n    public speedScale: number = 1.0;\n    public arrowSize: number = 8;\n    public centerSize: number = 8;\n    \n    public drawGizmos(emitter: Emitter, graphics: Graphics, node: Node): void {\n        // Get world position for drawing\n        const worldPos = node.worldPosition;\n\n        // Draw center point\n        if (this.showCenter) {\n            this.drawCenter(graphics, worldPos.x, worldPos.y);\n        }\n\n        // Draw direction arrows\n        if (this.showDirections && emitter.count > 0) {\n            this.drawDirections(graphics, emitter, worldPos.x, worldPos.y);\n        }\n    }\n    \n    private drawCenter(graphics: Graphics, worldX: number, worldY: number): void {\n        this.drawCross(graphics, worldX, worldY, this.centerSize, this.centerColor);\n    }\n\n    private drawDirections(graphics: Graphics, emitter: Emitter, worldX: number, worldY: number): void {\n        const baseLength = 30;\n        const speedFactor = emitter.speedMultiplier || 1;\n        const arrowLength = Math.max(baseLength, baseLength * speedFactor * this.speedScale);\n\n        for (let i = 0; i < emitter.count; i++) {\n            // For basic emitters, just draw simple forward direction (up)\n            const dirX = 0;\n            const dirY = 1;\n\n            const startX = worldX;\n            const startY = worldY;\n            const endX = worldX + dirX * arrowLength;\n            const endY = worldY + dirY * arrowLength;\n\n            // Draw arrow\n            this.drawArrow(graphics, startX, startY, endX, endY, this.directionColor, this.arrowSize);\n        }\n    }\n    \n    public getPriority(): number {\n        return 5; // Draw base emitter gizmos with lower priority than EmitterArc\n    }\n    \n    /**\n     * Configure display options\n     */\n    public configure(options: {\n        showDirections?: boolean;\n        showCenter?: boolean;\n        directionColor?: Color;\n        centerColor?: Color;\n        speedScale?: number;\n        arrowSize?: number;\n        centerSize?: number;\n    }): void {\n        if (options.showDirections !== undefined) this.showDirections = options.showDirections;\n        if (options.showCenter !== undefined) this.showCenter = options.showCenter;\n        if (options.directionColor !== undefined) this.directionColor = options.directionColor;\n        if (options.centerColor !== undefined) this.centerColor = options.centerColor;\n        if (options.speedScale !== undefined) this.speedScale = options.speedScale;\n        if (options.arrowSize !== undefined) this.arrowSize = options.arrowSize;\n        if (options.centerSize !== undefined) this.centerSize = options.centerSize;\n    }\n}\n"]}