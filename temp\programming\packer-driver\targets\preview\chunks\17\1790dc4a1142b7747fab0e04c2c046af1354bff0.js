System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Component, Sprite, Vec3, find, ProgressBar, Collider2D, Contact2DType, Label, director, AudioSource, Animation, Global, PersistNode, Player, _dec, _class, _crd, ccclass, property, Enemy;

  function _reportPossibleCrUseOfGameFactory(extras) {
    _reporterNs.report("GameFactory", "./factroy/GameFactory", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGlobal(extras) {
    _reporterNs.report("Global", "./Global", _context.meta, extras);
  }

  function _reportPossibleCrUseOfPersistNode(extras) {
    _reporterNs.report("PersistNode", "./PersistNode", _context.meta, extras);
  }

  function _reportPossibleCrUseOfPlayer(extras) {
    _reporterNs.report("Player", "./Player", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Component = _cc.Component;
      Sprite = _cc.Sprite;
      Vec3 = _cc.Vec3;
      find = _cc.find;
      ProgressBar = _cc.ProgressBar;
      Collider2D = _cc.Collider2D;
      Contact2DType = _cc.Contact2DType;
      Label = _cc.Label;
      director = _cc.director;
      AudioSource = _cc.AudioSource;
      Animation = _cc.Animation;
    }, function (_unresolved_2) {
      Global = _unresolved_2.Global;
    }, function (_unresolved_3) {
      PersistNode = _unresolved_3.PersistNode;
    }, function (_unresolved_4) {
      Player = _unresolved_4.Player;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "c5c206FZUJMOI8E90OG1eyf", "Enemy", undefined);

      __checkObsolete__(['_decorator', 'Component', 'Node', 'SpriteFrame', 'Sprite', 'Vec3', 'find', 'ProgressBar', 'Collider2D', 'Contact2DType', 'IPhysics2DContact', 'Label', 'log', 'director', 'AudioSource', 'Animation']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("Enemy", Enemy = (_dec = ccclass('Enemy'), _dec(_class = class Enemy extends Component {
        constructor() {
          super(...arguments);
          this.enemyType = null;
          this.curPos = null;
          this.enemyFactory = null;
          this.enemyBulletFactory = null;
          this.persistNode = null;
          this.enemy1MoveSpeed = 0;
          //敌机1的移动速度
          this.enemy2MoveSpeed = 0;
          //敌机1的移动速度
          this.enemy1ShootTimer = 0;
          //敌机1发射子弹计时器
          this.enemy2ShootTimer = 0;
          //敌机2发射子弹计时器
          this.enemy1ShootSpeed = 0;
          //敌机1发射子弹时间间隔
          this.enemy2ShootSpeed = 0;
          //敌机2发射子弹时间间隔
          this.enemyTotalBlood = 0;
          //敌机总血量
          this.enemyBlood = 0;
          //敌机当前血量
          this.enemyContactPlayerReduce = 0;
        }

        //敌机碰到玩家，玩家掉多少血
        onLoad() {
          this.persistNode = find("PersistNode");
          this.enemyFactory = this.persistNode.getComponent(_crd && PersistNode === void 0 ? (_reportPossibleCrUseOfPersistNode({
            error: Error()
          }), PersistNode) : PersistNode).enemyFactory;
          this.enemyBulletFactory = this.persistNode.getComponent(_crd && PersistNode === void 0 ? (_reportPossibleCrUseOfPersistNode({
            error: Error()
          }), PersistNode) : PersistNode).enemyBulletFactory;
          this.enemy1MoveSpeed = this.persistNode.getComponent(_crd && PersistNode === void 0 ? (_reportPossibleCrUseOfPersistNode({
            error: Error()
          }), PersistNode) : PersistNode).enemy1MoveSpeed;
          this.enemy2MoveSpeed = this.persistNode.getComponent(_crd && PersistNode === void 0 ? (_reportPossibleCrUseOfPersistNode({
            error: Error()
          }), PersistNode) : PersistNode).enemy2MoveSpeed;
          this.enemy1ShootSpeed = this.persistNode.getComponent(_crd && PersistNode === void 0 ? (_reportPossibleCrUseOfPersistNode({
            error: Error()
          }), PersistNode) : PersistNode).enemy1ShootSpeed;
          this.enemy2ShootSpeed = this.persistNode.getComponent(_crd && PersistNode === void 0 ? (_reportPossibleCrUseOfPersistNode({
            error: Error()
          }), PersistNode) : PersistNode).enemy2ShootSpeed;
          this.enemyTotalBlood = this.persistNode.getComponent(_crd && PersistNode === void 0 ? (_reportPossibleCrUseOfPersistNode({
            error: Error()
          }), PersistNode) : PersistNode).enemyTotalBlood;
          this.enemyBlood = this.enemyTotalBlood;
          this.enemyContactPlayerReduce = this.persistNode.getComponent(_crd && PersistNode === void 0 ? (_reportPossibleCrUseOfPersistNode({
            error: Error()
          }), PersistNode) : PersistNode).enemyContactPlayerReduce;
          var collider = this.node.getComponent(Collider2D);

          if (collider) {
            collider.on(Contact2DType.BEGIN_CONTACT, this.onBeginContact, this);
          }
        }

        onBeginContact(selfCollider, otherCollider, contact) {
          if (otherCollider.tag == 1) {
            otherCollider.getComponent(_crd && Player === void 0 ? (_reportPossibleCrUseOfPlayer({
              error: Error()
            }), Player) : Player).planeBlood -= this.enemyContactPlayerReduce;
            otherCollider.node.getChildByName("Blood").getComponent(ProgressBar).progress = otherCollider.getComponent(_crd && Player === void 0 ? (_reportPossibleCrUseOfPlayer({
              error: Error()
            }), Player) : Player).planeBlood / otherCollider.getComponent(_crd && Player === void 0 ? (_reportPossibleCrUseOfPlayer({
              error: Error()
            }), Player) : Player).planeTotalBlood;

            if (this.enemyType == (_crd && Global === void 0 ? (_reportPossibleCrUseOfGlobal({
              error: Error()
            }), Global) : Global).ENEMY_1) {
              (_crd && Global === void 0 ? (_reportPossibleCrUseOfGlobal({
                error: Error()
              }), Global) : Global).SCORE += 20;
            } else {
              (_crd && Global === void 0 ? (_reportPossibleCrUseOfGlobal({
                error: Error()
              }), Global) : Global).SCORE += 40;
            }

            find("Canvas/Score").getComponent(Label).string = "Score:" + (_crd && Global === void 0 ? (_reportPossibleCrUseOfGlobal({
              error: Error()
            }), Global) : Global).SCORE.toString(); //添加动画节点

            var anim = this.persistNode.getComponent(_crd && PersistNode === void 0 ? (_reportPossibleCrUseOfPersistNode({
              error: Error()
            }), PersistNode) : PersistNode).animFactory.createAnim();
            anim.setPosition(this.node.getPosition());
            find("Canvas").addChild(anim);
            anim.getComponent(Animation).play(); //播放动画

            this.enemyFactory.recycleProduct(this.node); //敌机消失

            this.persistNode.getComponent(AudioSource).playOneShot(this.persistNode.getComponent(_crd && PersistNode === void 0 ? (_reportPossibleCrUseOfPersistNode({
              error: Error()
            }), PersistNode) : PersistNode).boomAudioClip);

            if (otherCollider.getComponent(_crd && Player === void 0 ? (_reportPossibleCrUseOfPlayer({
              error: Error()
            }), Player) : Player).planeBlood <= 0) {
              director.loadScene("Main");
              this.persistNode.getComponent(AudioSource).playOneShot(this.persistNode.getComponent(_crd && PersistNode === void 0 ? (_reportPossibleCrUseOfPersistNode({
                error: Error()
              }), PersistNode) : PersistNode).gameOverAudioClip);
            }
          }
        }
        /**
         * 敌机初始化函数
         */


        init(enemyType, spriteFrame) {
          this.enemyType = enemyType;
          this.node.getComponent(Sprite).spriteFrame = spriteFrame;
          this.enemyBlood = this.enemyTotalBlood; //敌机满血复活

          this.node.getChildByName("EnemyBlood").getComponent(ProgressBar).progress = 1;
        }

        update(deltaTime) {
          if (this.enemyType == (_crd && Global === void 0 ? (_reportPossibleCrUseOfGlobal({
            error: Error()
          }), Global) : Global).ENEMY_1) {
            //敌机1相关操作
            this.enem1Move(deltaTime); //敌机1移动
            //敌机1发射子弹

            this.enemy1ShootTimer += deltaTime;

            if (this.enemy1ShootTimer > this.enemy1ShootSpeed) {
              this.enemy1Shoot();
              this.enemy1ShootTimer = 0;
            }
          } else if (this.enemyType == (_crd && Global === void 0 ? (_reportPossibleCrUseOfGlobal({
            error: Error()
          }), Global) : Global).ENEMY_2) {
            //敌机2相关操作
            this.enem2Move(deltaTime); //敌机2发射子弹

            this.enemy2ShootTimer += deltaTime;

            if (this.enemy2ShootTimer > this.enemy2ShootSpeed) {
              this.enemy2Shoot();
              this.enemy2ShootTimer = 0;
            }
          }
        }
        /**
         * 敌机2发射子弹
         */


        enemy2Shoot() {
          var posBegin = new Vec3(); //定义子弹开始的位置

          var enemyBullet = null;
          enemyBullet = this.enemyBulletFactory.createProduct((_crd && Global === void 0 ? (_reportPossibleCrUseOfGlobal({
            error: Error()
          }), Global) : Global).ENEMY_BULLET_2); //制作子弹

          this.node.parent.addChild(enemyBullet); //添加节点到画布

          this.curPos = this.node.getPosition(); //得到敌机机当前位置

          posBegin.x = this.curPos.x;
          posBegin.y = this.curPos.y - 50; //设置到机头位置

          enemyBullet.setPosition(posBegin);
        }
        /**
         * 敌机1发射子弹
         */


        enemy1Shoot() {
          var posBegin = new Vec3(); //定义子弹开始的位置

          var enemyBullet = null;
          enemyBullet = this.enemyBulletFactory.createProduct((_crd && Global === void 0 ? (_reportPossibleCrUseOfGlobal({
            error: Error()
          }), Global) : Global).ENEMY_BULLET_1); //制作子弹

          this.node.parent.addChild(enemyBullet); //添加节点到画布

          this.curPos = this.node.getPosition(); //得到敌机机当前位置

          posBegin.x = this.curPos.x;
          posBegin.y = this.curPos.y - 50; //设置到机头位置

          enemyBullet.setPosition(posBegin);
        }
        /**
         * 敌机1移动
         */


        enem1Move(deltaTime) {
          this.curPos = this.node.getPosition();
          this.curPos.y -= this.enemy1MoveSpeed * deltaTime;
          this.node.setPosition(this.curPos);

          if (this.curPos.y < -(_crd && Global === void 0 ? (_reportPossibleCrUseOfGlobal({
            error: Error()
          }), Global) : Global).HEIGHT / 2) {
            this.enemyFactory.recycleProduct(this.node);
          }
        }
        /**
         * 敌机2移动
         */


        enem2Move(deltaTime) {
          this.curPos = this.node.getPosition();
          this.curPos.y -= this.enemy2MoveSpeed * deltaTime;
          this.node.setPosition(this.curPos);

          if (this.curPos.y < -(_crd && Global === void 0 ? (_reportPossibleCrUseOfGlobal({
            error: Error()
          }), Global) : Global).HEIGHT / 2) {
            this.enemyFactory.recycleProduct(this.node);
          }
        }

      }) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=1790dc4a1142b7747fab0e04c2c046af1354bff0.js.map