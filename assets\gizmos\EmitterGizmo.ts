/**
 * Emitter Gizmo Component
 * This component provides visual debugging for Emitter components in the scene view
 * It should be added to the same node as the Emitter component
 */

import { _decorator, Component, Color, Graphics } from 'cc';
import { EDITOR } from 'cc/env';
import { Emitter } from '../assets/scripts/Game/world/bullet/Emitter';
import { EmitterArc } from '../assets/scripts/Game/world/bullet/EmitterArc';
const { ccclass, property, executeInEditMode } = _decorator;

@ccclass('EmitterGizmo')
@executeInEditMode(true)
export class EmitterGizmo extends Component {

    @property
    public showRadius: boolean = true;

    @property
    public showDirections: boolean = true;

    @property
    public showCenter: boolean = true;

    @property
    public radiusColor: Color = Color.GRAY;

    @property
    public directionColor: Color = Color.RED;

    @property
    public centerColor: Color = Color.WHITE;

    @property
    public speedScale: number = 1.0;

    private graphics: Graphics | null = null;
    private emitter: Emitter | null = null;

    protected onLoad(): void {
        if (!EDITOR) return;

        // Get or create Graphics component
        this.graphics = this.getComponent(Graphics) || this.addComponent(Graphics);

        // Get Emitter component (try EmitterArc first, then base Emitter)
        this.emitter = this.getComponent(EmitterArc) || this.getComponent(Emitter);

        if (!this.emitter) {
            console.warn('EmitterGizmo: No Emitter component found on this node');
        }
    }

    protected update(): void {
        if (!EDITOR || !this.graphics || !this.emitter) return;

        this.drawGizmos();
    }

    private drawGizmos(): void {
        if (!this.graphics) return;

        // Clear previous drawings
        this.graphics.clear();

        // Draw center point
        if (this.showCenter) {
            this.drawCenter();
        }

        // Draw radius circle (only for EmitterArc)
        if (this.showRadius && this.isEmitterArc() && (this.emitter as EmitterArc).radius > 0) {
            this.drawRadius();
        }

        // Draw direction arrows
        if (this.showDirections) {
            this.drawDirections();
        }
    }

    private drawCenter(): void {
        if (!this.graphics) return;

        this.graphics.strokeColor = this.centerColor;
        this.graphics.lineWidth = 2;

        const centerSize = 8;

        // Draw cross at center
        this.graphics.moveTo(-centerSize, 0);
        this.graphics.lineTo(centerSize, 0);
        this.graphics.moveTo(0, -centerSize);
        this.graphics.lineTo(0, centerSize);
        this.graphics.stroke();
    }

    private drawRadius(): void {
        if (!this.graphics || !this.isEmitterArc()) return;

        const emitterArc = this.emitter as EmitterArc;
        this.graphics.strokeColor = this.radiusColor;
        this.graphics.lineWidth = 1;

        // Draw radius circle
        this.graphics.circle(0, 0, emitterArc.radius);
        this.graphics.stroke();
    }

    private drawDirections(): void {
        if (!this.graphics || !this.emitter || this.emitter.count <= 0) return;

        this.graphics.strokeColor = this.directionColor;
        this.graphics.lineWidth = 2;

        // Check if this is an EmitterArc to access arc and angle properties
        if (this.isEmitterArc()) {
            this.drawArcDirections();
        } else {
            this.drawBasicDirections();
        }

        this.graphics.stroke();
    }

    private drawArcDirections(): void {
        if (!this.graphics || !this.isEmitterArc()) return;

        const emitterArc = this.emitter as EmitterArc;

        // Use arc property for spread calculation, angle for direction
        const baseDirection = emitterArc.angle || 0; // Base direction from angle property
        const totalArc = emitterArc.arc || 0; // Total arc to spread bullets across

        // Calculate angle per bullet based on arc and count
        const anglePerBullet = emitterArc.count > 1 ? totalArc / (emitterArc.count - 1) : 0;
        const startAngle = baseDirection - totalArc / 2; // Start from base direction minus half arc

        for (let i = 0; i < emitterArc.count; i++) {
            let bulletAngle: number;

            if (emitterArc.count === 1) {
                bulletAngle = baseDirection; // Single bullet goes in base direction
            } else {
                bulletAngle = startAngle + (anglePerBullet * i);
            }

            // Convert angle to radians (0 degrees = up in Cocos Creator)
            const angleRad = (bulletAngle + 90) * Math.PI / 180;

            // Calculate direction vector
            const dirX = Math.cos(angleRad);
            const dirY = Math.sin(angleRad);

            // Start position (at radius distance from center)
            const startX = dirX * emitterArc.radius;
            const startY = dirY * emitterArc.radius;

            // Calculate arrow length based on speed multiplier
            // Base length of 30 pixels, scaled by speed multiplier and speedScale property
            const baseLength = 30;
            const speedFactor = emitterArc.speedMultiplier || 1; // Default to 1 if speedMultiplier is 0 or undefined
            const arrowLength = Math.max(baseLength, baseLength * speedFactor * this.speedScale);

            const endX = startX + dirX * arrowLength;
            const endY = startY + dirY * arrowLength;

            // Draw arrow line
            this.graphics.moveTo(startX, startY);
            this.graphics.lineTo(endX, endY);

            // Draw arrow head
            this.drawArrowHead(endX, endY, dirX, dirY);
        }
    }

    private drawBasicDirections(): void {
        if (!this.graphics || !this.emitter) return;

        // For basic emitters, just draw a simple forward direction
        const baseLength = 30;
        const speedFactor = this.emitter.speedMultiplier || 1;
        const arrowLength = Math.max(baseLength, baseLength * speedFactor * this.speedScale);

        for (let i = 0; i < this.emitter.count; i++) {
            // Simple forward direction (up)
            const dirX = 0;
            const dirY = 1;

            const startX = 0;
            const startY = 0;
            const endX = dirX * arrowLength;
            const endY = dirY * arrowLength;

            // Draw arrow line
            this.graphics.moveTo(startX, startY);
            this.graphics.lineTo(endX, endY);

            // Draw arrow head
            this.drawArrowHead(endX, endY, dirX, dirY);
        }
    }

    /**
     * Check if the current emitter is an EmitterArc
     */
    private isEmitterArc(): boolean {
        return this.emitter instanceof EmitterArc;
    }

    private drawArrowHead(endX: number, endY: number, dirX: number, dirY: number): void {
        if (!this.graphics) return;

        const arrowSize = 8;

        // Calculate arrow head points
        const arrowAngle = Math.PI / 6; // 30 degrees

        // Left arrow point
        const leftX = endX - arrowSize * (dirX * Math.cos(arrowAngle) - dirY * Math.sin(arrowAngle));
        const leftY = endY - arrowSize * (dirY * Math.cos(arrowAngle) + dirX * Math.sin(arrowAngle));

        // Right arrow point
        const rightX = endX - arrowSize * (dirX * Math.cos(-arrowAngle) - dirY * Math.sin(-arrowAngle));
        const rightY = endY - arrowSize * (dirY * Math.cos(-arrowAngle) + dirX * Math.sin(-arrowAngle));

        // Draw arrow head lines
        this.graphics.moveTo(endX, endY);
        this.graphics.lineTo(leftX, leftY);
        this.graphics.moveTo(endX, endY);
        this.graphics.lineTo(rightX, rightY);
    }
}
