/**
 * CrazyStorm 2.0 Integration for Cocos Creator
 * Component Factory
 * 
 * This factory creates the appropriate CrazyStorm component instances
 * based on the component type specified in the data.
 */

import { Node, log, warn, error } from 'cc';
import { ComponentType, Component as CrazyStormComponentData } from '../core/CrazyStormTypes';
import { CrazyStormComponent } from './CrazyStormComponent';
import { CurveEmitterComponent } from './CurveEmitterComponent';
import { MultiEmitterComponent } from './MultiEmitterComponent';
import { ForceFieldComponent } from './ForceFieldComponent';
import { EventFieldComponent } from './EventFieldComponent';
import { RebounderComponent } from './RebounderComponent';

/**
 * Factory for creating CrazyStorm components
 */
export class ComponentFactory {
    /**
     * Create a component of the specified type
     */
    public static createComponent(
        componentData: CrazyStormComponentData,
        parentNode: Node,
        globals: Map<string, number>
    ): CrazyStormComponent | null {
        try {
            const componentNode = new Node(componentData.name);
            parentNode.addChild(componentNode);

            let component: CrazyStormComponent | null = null;

            switch (componentData.specificType) {
                case ComponentType.CurveEmitter:
                    component = componentNode.addComponent(CurveEmitterComponent);
                    break;

                case ComponentType.MultiEmitter:
                    component = componentNode.addComponent(MultiEmitterComponent);
                    break;

                case ComponentType.ForceField:
                    component = componentNode.addComponent(ForceFieldComponent);
                    break;

                case ComponentType.EventField:
                    component = componentNode.addComponent(EventFieldComponent);
                    break;

                case ComponentType.Rebounder:
                    component = componentNode.addComponent(RebounderComponent);
                    break;

                default:
                    error(`ComponentFactory: Unknown component type: ${componentData.specificType}`);
                    componentNode.destroy();
                    return null;
            }

            if (component) {
                // Initialize the component with CrazyStorm data
                component.initializeWithData(componentData, globals);
                log(`ComponentFactory: Created ${componentData.specificType} component: ${componentData.name}`);
            }

            return component;

        } catch (err) {
            error('ComponentFactory: Failed to create component:', err);
            return null;
        }
    }

    /**
     * Create multiple components from an array of component data
     */
    public static createComponents(
        componentsData: CrazyStormComponentData[],
        parentNode: Node,
        globals: Map<string, number>
    ): CrazyStormComponent[] {
        const components: CrazyStormComponent[] = [];

        componentsData.forEach((componentData, index) => {
            const component = this.createComponent(componentData, parentNode, globals);
            if (component) {
                components.push(component);
            } else {
                warn(`ComponentFactory: Failed to create component at index ${index}`);
            }
        });

        // Set up parent-child relationships
        this.setupComponentHierarchy(components, componentsData);

        return components;
    }

    /**
     * Set up parent-child relationships between components
     */
    private static setupComponentHierarchy(
        components: CrazyStormComponent[],
        componentsData: CrazyStormComponentData[]
    ): void {
        // Create a map of component ID to component instance
        const componentMap = new Map<number, CrazyStormComponent>();
        components.forEach((component, index) => {
            const data = componentsData[index];
            componentMap.set(data.id, component);
        });

        // Set up parent-child relationships
        components.forEach((component, index) => {
            const data = componentsData[index];
            
            // Set parent if specified
            if (data.parentId !== undefined && data.parentId >= 0) {
                const parent = componentMap.get(data.parentId);
                if (parent) {
                    parent.addChild(component);
                }
            }

            // Set binding target if specified
            if (data.bindingTargetId !== undefined && data.bindingTargetId >= 0) {
                const bindingTarget = componentMap.get(data.bindingTargetId);
                if (bindingTarget) {
                    component.setBindingTarget(bindingTarget);
                }
            }
        });
    }

    /**
     * Get component type name for display
     */
    public static getComponentTypeName(type: ComponentType): string {
        switch (type) {
            case ComponentType.CurveEmitter:
                return 'Curve Emitter';
            case ComponentType.MultiEmitter:
                return 'Multi Emitter';
            case ComponentType.ForceField:
                return 'Force Field';
            case ComponentType.EventField:
                return 'Event Field';
            case ComponentType.Rebounder:
                return 'Rebounder';
            default:
                return 'Unknown';
        }
    }

    /**
     * Check if a component type is an emitter
     */
    public static isEmitterType(type: ComponentType): boolean {
        return type === ComponentType.CurveEmitter || type === ComponentType.MultiEmitter;
    }

    /**
     * Check if a component type is a field
     */
    public static isFieldType(type: ComponentType): boolean {
        return type === ComponentType.ForceField || 
               type === ComponentType.EventField || 
               type === ComponentType.Rebounder;
    }

    /**
     * Get default component data for a specific type
     */
    public static getDefaultComponentData(type: ComponentType): Partial<CrazyStormComponentData> {
        const baseData = {
            specificType: type,
            id: 0,
            name: this.getComponentTypeName(type),
            properties: {},
            componentData: {
                layerFrame: 0,
                currentFrame: 0,
                beginFrame: 0,
                totalFrame: 200,
                position: { x: 0, y: 0 },
                speed: 0,
                speedAngle: 0,
                acspeed: 0,
                acspeedAngle: 0,
                visibility: true
            },
            variables: [],
            componentEventGroups: [],
            children: []
        };

        switch (type) {
            case ComponentType.CurveEmitter:
            case ComponentType.MultiEmitter:
                return {
                    ...baseData,
                    properties: {
                        'EmitPosition': { expression: true, value: 'Position' }
                    }
                };

            case ComponentType.ForceField:
                return {
                    ...baseData,
                    properties: {
                        'HalfWidth': { expression: false, value: 50 },
                        'HalfHeight': { expression: false, value: 50 },
                        'Force': { expression: false, value: 1 },
                        'Direction': { expression: false, value: 0 }
                    }
                };

            case ComponentType.EventField:
                return {
                    ...baseData,
                    properties: {
                        'HalfWidth': { expression: false, value: 50 },
                        'HalfHeight': { expression: false, value: 50 }
                    }
                };

            case ComponentType.Rebounder:
                return {
                    ...baseData,
                    properties: {
                        'HalfWidth': { expression: false, value: 50 },
                        'HalfHeight': { expression: false, value: 50 },
                        'ReboundFactor': { expression: false, value: 1 }
                    }
                };

            default:
                return baseData;
        }
    }

    /**
     * Validate component data
     */
    public static validateComponentData(data: CrazyStormComponentData): boolean {
        // Check required fields
        if (!data.specificType || data.id === undefined || !data.name) {
            warn('ComponentFactory: Component data missing required fields');
            return false;
        }

        // Check if component type is supported
        if (!Object.values(ComponentType).includes(data.specificType)) {
            warn(`ComponentFactory: Unsupported component type: ${data.specificType}`);
            return false;
        }

        // Check component data
        if (!data.componentData) {
            warn('ComponentFactory: Component data missing componentData');
            return false;
        }

        return true;
    }

    /**
     * Clone component data
     */
    public static cloneComponentData(data: CrazyStormComponentData): CrazyStormComponentData {
        return JSON.parse(JSON.stringify(data));
    }

    /**
     * Get component statistics
     */
    public static getComponentStats(components: CrazyStormComponent[]): {
        total: number;
        byType: { [key: string]: number };
        emitters: number;
        fields: number;
    } {
        const stats = {
            total: components.length,
            byType: {} as { [key: string]: number },
            emitters: 0,
            fields: 0
        };

        // Initialize type counters
        Object.values(ComponentType).forEach(type => {
            stats.byType[type] = 0;
        });

        // Count components by type
        components.forEach(component => {
            const data = (component as any).crazyStormData;
            if (data && data.specificType) {
                stats.byType[data.specificType]++;
                
                if (this.isEmitterType(data.specificType)) {
                    stats.emitters++;
                } else if (this.isFieldType(data.specificType)) {
                    stats.fields++;
                }
            }
        });

        return stats;
    }
}

/**
 * Component registry for managing component types
 */
export class ComponentRegistry {
    private static registeredTypes: Map<ComponentType, any> = new Map();

    /**
     * Register a component class for a specific type
     */
    public static registerComponent(type: ComponentType, componentClass: any): void {
        this.registeredTypes.set(type, componentClass);
        log(`ComponentRegistry: Registered component type ${type}`);
    }

    /**
     * Get registered component class for a type
     */
    public static getComponentClass(type: ComponentType): any {
        return this.registeredTypes.get(type);
    }

    /**
     * Check if a component type is registered
     */
    public static isRegistered(type: ComponentType): boolean {
        return this.registeredTypes.has(type);
    }

    /**
     * Get all registered component types
     */
    public static getRegisteredTypes(): ComponentType[] {
        return Array.from(this.registeredTypes.keys());
    }

    /**
     * Initialize default component registrations
     */
    public static initializeDefaults(): void {
        this.registerComponent(ComponentType.CurveEmitter, CurveEmitterComponent);
        this.registerComponent(ComponentType.MultiEmitter, MultiEmitterComponent);
        this.registerComponent(ComponentType.ForceField, ForceFieldComponent);
        this.registerComponent(ComponentType.EventField, EventFieldComponent);
        this.registerComponent(ComponentType.Rebounder, RebounderComponent);
    }
}

// Initialize default registrations
ComponentRegistry.initializeDefaults();
