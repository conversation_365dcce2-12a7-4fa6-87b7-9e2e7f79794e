System.register(["__unresolved_0", "cc", "__unresolved_1"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, IMgr, _dec, _class, _crd, ccclass, property, NetMgr;

  function _reportPossibleCrUseOfIMgr(extras) {
    _reporterNs.report("IMgr", "../IMgr", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
    }, function (_unresolved_2) {
      IMgr = _unresolved_2.IMgr;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "d7f90yKiiBA+ZpKmPMVdOF6", "NetMgr", undefined);

      __checkObsolete__(['_decorator', 'Component', 'Node', 'JsonAsset', 'AssetManager', 'resources']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("NetMgr", NetMgr = (_dec = ccclass("NetMgr"), _dec(_class = class NetMgr extends (_crd && IMgr === void 0 ? (_reportPossibleCrUseOfIMgr({
        error: Error()
      }), IMgr) : IMgr) {
        init() {
          super.init();
        }

      }) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=e6ee09e0a836659daeca4b7a85e86f7ac5a5f093.js.map