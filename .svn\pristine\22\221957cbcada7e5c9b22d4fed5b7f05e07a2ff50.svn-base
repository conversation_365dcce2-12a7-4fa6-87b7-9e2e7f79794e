import { _decorator, Component, Node, Vec3 } from 'cc';
import { Global } from './Global';
const { ccclass, property } = _decorator;

@ccclass('Background')
export class Background extends Component {
    
    @property
    bgSpeed: number = 50;

    curPos: Vec3 = null;

    update(deltaTime: number) {
        this.curPos = this.node.getPosition();
        this.curPos.y -= this.bgSpeed * deltaTime;

        if (this.curPos.y < -Global.HEIGHT) {
            this.curPos.y = 0;
        }

        this.node.setPosition(this.curPos);
    }
}

