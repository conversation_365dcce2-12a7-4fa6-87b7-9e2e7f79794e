/**
 * CrazyStorm 2.0 Integration for Cocos Creator
 * Runtime Manager
 * 
 * This is the main runtime manager that coordinates all CrazyStorm systems,
 * including particle systems, layers, components, and resource management.
 */

import { _decorator, Component, Node, log, warn, error } from 'cc';
import { CrazyStormFile, ParticleSystem, Layer } from '../core/CrazyStormTypes';
import { getCrazyStormLoader } from '../core/CrazyStormLoader';
import { ParticleSystemManager } from './ParticleSystemManager';
import { LayerManager } from './LayerManager';
import { ResourceManager } from './ResourceManager';
import { GlobalVariableManager } from './GlobalVariableManager';

const { ccclass, property } = _decorator;

/**
 * Runtime configuration for CrazyStorm manager
 */
export interface RuntimeConfig {
    autoPlay: boolean;
    loop: boolean;
    frameRate: number;
    maxParticles: number;
    enableCollision: boolean;
    enableDebugDraw: boolean;
    bounds: {
        left: number;
        right: number;
        top: number;
        bottom: number;
    };
}

/**
 * Main CrazyStorm runtime manager component
 */
@ccclass('CrazyStormManager')
export class CrazyStormManager extends Component {
    @property({ displayName: "Auto Play" })
    autoPlay: boolean = true;

    @property({ displayName: "Loop" })
    loop: boolean = true;

    @property({ displayName: "Frame Rate" })
    frameRate: number = 60;

    @property({ displayName: "Max Particles" })
    maxParticles: number = 5000;

    @property({ displayName: "Enable Collision" })
    enableCollision: boolean = true;

    @property({ displayName: "Enable Debug Draw" })
    enableDebugDraw: boolean = false;

    // Runtime managers
    private particleSystemManager: ParticleSystemManager;
    private layerManager: LayerManager;
    private resourceManager: ResourceManager;
    private globalVariableManager: GlobalVariableManager;

    // Runtime state
    private crazyStormFile: CrazyStormFile | null = null;
    private isPlaying: boolean = false;
    private isPaused: boolean = false;
    private currentFrame: number = 0;
    private deltaTimeAccumulator: number = 0;
    private frameInterval: number = 1 / 60; // Default 60 FPS

    // Configuration
    private config: RuntimeConfig;

    onLoad() {
        // Initialize configuration
        this.config = {
            autoPlay: this.autoPlay,
            loop: this.loop,
            frameRate: this.frameRate,
            maxParticles: this.maxParticles,
            enableCollision: this.enableCollision,
            enableDebugDraw: this.enableDebugDraw,
            bounds: {
                left: -1000,
                right: 1000,
                top: -1000,
                bottom: 1000
            }
        };

        this.frameInterval = 1 / this.config.frameRate;

        // Initialize managers
        this.initializeManagers();

        log('CrazyStormManager: Initialized');
    }

    start() {
        if (this.config.autoPlay) {
            this.play();
        }
    }

    update(deltaTime: number) {
        if (!this.isPlaying || this.isPaused || !this.crazyStormFile) {
            return;
        }

        // Accumulate delta time for frame-based updates
        this.deltaTimeAccumulator += deltaTime;

        // Update at specified frame rate
        while (this.deltaTimeAccumulator >= this.frameInterval) {
            this.updateFrame();
            this.deltaTimeAccumulator -= this.frameInterval;
        }
    }

    /**
     * Load a CrazyStorm file and initialize the runtime
     */
    public async loadFile(filePath: string): Promise<boolean> {
        try {
            const loader = getCrazyStormLoader();
            this.crazyStormFile = await loader.loadFile(filePath, {
                useCache: true,
                parserConfig: {
                    validateStructure: true,
                    optimizeForMobile: true,
                    debugMode: this.config.enableDebugDraw
                },
                preloadResources: true
            });

            if (!this.crazyStormFile) {
                error(`CrazyStormManager: Failed to load file: ${filePath}`);
                return false;
            }

            // Initialize with loaded file
            await this.initializeWithFile(this.crazyStormFile);
            
            log(`CrazyStormManager: Successfully loaded file: ${filePath}`);
            return true;

        } catch (err) {
            error('CrazyStormManager: Error loading file:', err);
            return false;
        }
    }

    /**
     * Load a specific particle system by name
     */
    public async loadParticleSystem(filePath: string, systemName: string): Promise<boolean> {
        try {
            const loader = getCrazyStormLoader();
            const system = await loader.loadParticleSystem(filePath, systemName);

            if (!system) {
                error(`CrazyStormManager: Failed to load particle system: ${systemName}`);
                return false;
            }

            // Create a minimal file structure with just this system
            this.crazyStormFile = {
                version: '0.92',
                fileResourceIndex: 0,
                particleIndex: 0,
                particleSystems: [system],
                images: [],
                sounds: [],
                globals: [
                    { label: 'cx', value: 0 },
                    { label: 'cy', value: 0 }
                ]
            };

            await this.initializeWithFile(this.crazyStormFile);
            
            log(`CrazyStormManager: Successfully loaded particle system: ${systemName}`);
            return true;

        } catch (err) {
            error('CrazyStormManager: Error loading particle system:', err);
            return false;
        }
    }

    /**
     * Play the loaded CrazyStorm content
     */
    public play(): void {
        if (!this.crazyStormFile) {
            warn('CrazyStormManager: No file loaded');
            return;
        }

        this.isPlaying = true;
        this.isPaused = false;
        
        // Start all particle systems
        this.particleSystemManager.playAll();
        
        log('CrazyStormManager: Started playing');
    }

    /**
     * Pause playback
     */
    public pause(): void {
        this.isPaused = true;
        log('CrazyStormManager: Paused');
    }

    /**
     * Resume playback
     */
    public resume(): void {
        this.isPaused = false;
        log('CrazyStormManager: Resumed');
    }

    /**
     * Stop playback and reset
     */
    public stop(): void {
        this.isPlaying = false;
        this.isPaused = false;
        this.currentFrame = 0;
        this.deltaTimeAccumulator = 0;

        // Stop all particle systems
        this.particleSystemManager.stopAll();
        
        log('CrazyStormManager: Stopped');
    }

    /**
     * Reset to beginning
     */
    public reset(): void {
        this.currentFrame = 0;
        this.deltaTimeAccumulator = 0;

        // Reset all particle systems
        this.particleSystemManager.resetAll();
        
        log('CrazyStormManager: Reset');
    }

    /**
     * Set global variable value
     */
    public setGlobal(label: string, value: number): void {
        this.globalVariableManager.setVariable(label, value);
    }

    /**
     * Get global variable value
     */
    public getGlobal(label: string): number {
        return this.globalVariableManager.getVariable(label);
    }

    /**
     * Set player position (updates cx, cy globals)
     */
    public setPlayerPosition(x: number, y: number): void {
        this.setGlobal('cx', x);
        this.setGlobal('cy', y);
    }

    /**
     * Get runtime statistics
     */
    public getStats(): {
        isPlaying: boolean;
        currentFrame: number;
        particleSystems: number;
        activeParticles: number;
        totalLayers: number;
        activeLayers: number;
    } {
        return {
            isPlaying: this.isPlaying,
            currentFrame: this.currentFrame,
            particleSystems: this.particleSystemManager.getSystemCount(),
            activeParticles: this.particleSystemManager.getActiveParticleCount(),
            totalLayers: this.layerManager.getTotalLayerCount(),
            activeLayers: this.layerManager.getActiveLayerCount()
        };
    }

    /**
     * Set runtime configuration
     */
    public setConfig(config: Partial<RuntimeConfig>): void {
        this.config = { ...this.config, ...config };
        this.frameInterval = 1 / this.config.frameRate;
        
        // Update managers with new config
        this.particleSystemManager.setMaxParticles(this.config.maxParticles);
        this.particleSystemManager.setCollisionEnabled(this.config.enableCollision);
    }

    /**
     * Get current configuration
     */
    public getConfig(): RuntimeConfig {
        return { ...this.config };
    }

    // Private methods

    /**
     * Initialize all runtime managers
     */
    private initializeManagers(): void {
        this.globalVariableManager = new GlobalVariableManager();
        this.resourceManager = new ResourceManager();
        this.layerManager = new LayerManager(this.globalVariableManager);
        this.particleSystemManager = new ParticleSystemManager(
            this.node,
            this.layerManager,
            this.resourceManager,
            this.globalVariableManager,
            this.config
        );
    }

    /**
     * Initialize runtime with loaded file
     */
    private async initializeWithFile(file: CrazyStormFile): Promise<void> {
        // Initialize global variables
        this.globalVariableManager.initializeFromFile(file);

        // Initialize resources
        await this.resourceManager.initializeFromFile(file);

        // Initialize particle systems
        this.particleSystemManager.initializeFromFile(file);

        log(`CrazyStormManager: Initialized with ${file.particleSystems.length} particle systems`);
    }

    /**
     * Update one frame
     */
    private updateFrame(): void {
        // Update global variables (in case they're driven by expressions)
        this.globalVariableManager.update();

        // Update all particle systems
        this.particleSystemManager.update(this.currentFrame);

        // Update layers
        this.layerManager.update(this.currentFrame);

        // Increment frame counter
        this.currentFrame++;

        // Check for loop
        if (this.config.loop && this.shouldLoop()) {
            this.reset();
        }
    }

    /**
     * Check if we should loop back to the beginning
     */
    private shouldLoop(): boolean {
        if (!this.crazyStormFile) {
            return false;
        }

        // Find the maximum total frame across all particle systems
        let maxTotalFrame = 0;
        this.crazyStormFile.particleSystems.forEach(system => {
            system.layers.forEach(layer => {
                maxTotalFrame = Math.max(maxTotalFrame, layer.totalFrame);
            });
        });

        return this.currentFrame >= maxTotalFrame;
    }

    onDestroy() {
        this.stop();
        
        // Clean up managers
        if (this.particleSystemManager) {
            this.particleSystemManager.destroy();
        }
        if (this.layerManager) {
            this.layerManager.destroy();
        }
        if (this.resourceManager) {
            this.resourceManager.destroy();
        }
        if (this.globalVariableManager) {
            this.globalVariableManager.destroy();
        }
    }
}
