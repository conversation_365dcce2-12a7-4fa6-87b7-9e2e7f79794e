{"__type__": "cc.EffectAsset", "_name": "internal/editor/light-probe-visualization", "_objFlags": 0, "__editorExtras__": {}, "_native": "", "techniques": [{"passes": [{"rasterizerState": {"cullMode": 0}, "blendState": {"targets": [{"blend": true, "blendSrc": 2, "blendDst": 4, "blendDstAlpha": 4}]}, "program": "internal/editor/light-probe-visualization|gizmo-vs:vert|gizmo-fs:front", "priority": 245, "depthStencilState": {"depthTest": false, "depthWrite": false}}, {"rasterizerState": {"cullMode": 0}, "blendState": {"targets": [{"blend": true, "blendSrc": 2, "blendDst": 4, "blendDstAlpha": 4}]}, "program": "internal/editor/light-probe-visualization|gizmo-vs:vert|gizmo-fs:back", "priority": 245, "depthStencilState": {"depthTest": false, "depthWrite": false}}]}], "shaders": [{"blocks": [{"name": "Constant", "members": [{"name": "mainColor", "type": 16, "count": 1}, {"name": "cc_sh_linear_const_r", "type": 16, "count": 1}, {"name": "cc_sh_linear_const_g", "type": 16, "count": 1}, {"name": "cc_sh_linear_const_b", "type": 16, "count": 1}, {"name": "cc_sh_quadratic_r", "type": 16, "count": 1}, {"name": "cc_sh_quadratic_g", "type": 16, "count": 1}, {"name": "cc_sh_quadratic_b", "type": 16, "count": 1}, {"name": "cc_sh_quadratic_a", "type": 16, "count": 1}], "defines": [], "stageFlags": 16, "binding": 0}], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": [], "attributes": [{"name": "a_position", "defines": [], "format": 32, "location": 0}, {"name": "a_normal", "defines": [], "format": 32, "location": 1}], "varyings": [{"name": "normal_w", "type": 15, "count": 1, "defines": [], "stageFlags": 17, "location": 0}, {"name": "pos_w", "type": 15, "count": 1, "defines": [], "stageFlags": 17, "location": 1}, {"name": "pos_l", "type": 15, "count": 1, "defines": [], "stageFlags": 17, "location": 2}, {"name": "right", "type": 15, "count": 1, "defines": [], "stageFlags": 17, "location": 3}, {"name": "up", "type": 15, "count": 1, "defines": [], "stageFlags": 17, "location": 4}, {"name": "forward", "type": 15, "count": 1, "defines": [], "stageFlags": 17, "location": 5}], "fragColors": [{"name": "cc_FragColor", "typename": "vec4", "type": 16, "count": 1, "defines": [], "stageFlags": 16, "location": 0}], "descriptors": [{"rate": 0, "blocks": [{"tags": {"builtin": "local"}, "name": "CCLocal", "members": [{"name": "cc_matWorld", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matWorldIT", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_lightingMapUVParam", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_localShadowBias", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_reflectionProbeData1", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_reflectionProbeData2", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_reflectionProbeBlendData1", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_reflectionProbeBlendData2", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}], "defines": [], "stageFlags": 1}], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}, {"rate": 1, "blocks": [{"name": "Constant", "members": [{"name": "mainColor", "type": 16, "count": 1}, {"name": "cc_sh_linear_const_r", "type": 16, "count": 1}, {"name": "cc_sh_linear_const_g", "type": 16, "count": 1}, {"name": "cc_sh_linear_const_b", "type": 16, "count": 1}, {"name": "cc_sh_quadratic_r", "type": 16, "count": 1}, {"name": "cc_sh_quadratic_g", "type": 16, "count": 1}, {"name": "cc_sh_quadratic_b", "type": 16, "count": 1}, {"name": "cc_sh_quadratic_a", "type": 16, "count": 1}], "defines": [], "stageFlags": 16, "binding": 0}], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}, {"rate": 2, "blocks": [], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}, {"rate": 3, "blocks": [{"tags": {"builtin": "global"}, "name": "CCGlobal", "members": [{"name": "cc_time", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_screenSize", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_nativeSize", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_probeInfo", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_debug_view_mode", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}], "defines": [], "stageFlags": 17}, {"tags": {"builtin": "global"}, "name": "CCCamera", "members": [{"name": "cc_matView", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matViewInv", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matProj", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matProjInv", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matViewProj", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matViewProjInv", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_cameraPos", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_surfaceTransform", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_screenScale", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_exposure", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_mainLitDir", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_mainLitColor", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_ambientSky", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_ambientGround", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_fogColor", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_fogBase", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_fogAdd", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_nearFar", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_viewPort", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}], "defines": [], "stageFlags": 17}], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}], "hash": 4208657580, "glsl4": {"vert": "\nprecision mediump float;\nlayout(set = 2, binding = 0) uniform CCLocal {\n  highp mat4 cc_matWorld;\n  highp mat4 cc_matWorldIT;\n  highp vec4 cc_lightingMapUVParam;\n  highp vec4 cc_localShadowBias;\n  highp vec4 cc_reflectionProbeData1;\n  highp vec4 cc_reflectionProbeData2;\n  highp vec4 cc_reflectionProbeBlendData1;\n  highp vec4 cc_reflectionProbeBlendData2;\n};\nlayout(set = 0, binding = 0) uniform CCGlobal {\n  highp   vec4 cc_time;\n  mediump vec4 cc_screenSize;\n  mediump vec4 cc_nativeSize;\n  mediump vec4 cc_probeInfo;\n  mediump vec4 cc_debug_view_mode;\n};\nlayout(set = 0, binding = 1) uniform CCCamera {\n  highp   mat4 cc_matView;\n  highp   mat4 cc_matViewInv;\n  highp   mat4 cc_matProj;\n  highp   mat4 cc_matProjInv;\n  highp   mat4 cc_matViewProj;\n  highp   mat4 cc_matViewProjInv;\n  highp   vec4 cc_cameraPos;\n  mediump vec4 cc_surfaceTransform;\n  mediump vec4 cc_screenScale;\n  mediump vec4 cc_exposure;\n  mediump vec4 cc_mainLitDir;\n  mediump vec4 cc_mainLitColor;\n  mediump vec4 cc_ambientSky;\n  mediump vec4 cc_ambientGround;\n  mediump vec4 cc_fogColor;\n  mediump vec4 cc_fogBase;\n  mediump vec4 cc_fogAdd;\n  mediump vec4 cc_nearFar;\n  mediump vec4 cc_viewPort;\n};\nlayout(location = 0) in vec3 a_position;\nlayout(location = 1) in vec3 a_normal;\nlayout(location = 0) out vec3 normal_w;\nlayout(location = 1) out vec3 pos_w;\nlayout(location = 2) out vec3 pos_l;\nlayout(location = 3) out vec3 right;\nlayout(location = 4) out vec3 up;\nlayout(location = 5) out vec3 forward;\nvec4 vert () {\n  vec4 pos = vec4(a_position, 1);\n  vec4 normal = vec4(a_normal, 0);\n  pos_l = a_position;\n  pos_w = (cc_matWorld * pos).xyz;\n  normal_w = (cc_matWorldIT * normal).xyz;\n  right = vec3(cc_matView[0][0], cc_matView[1][0], cc_matView[2][0]);\n  up = vec3(cc_matView[0][1], cc_matView[1][1], cc_matView[2][1]);\n  forward = vec3(cc_matView[0][2], cc_matView[1][2], cc_matView[2][2]);\n  return cc_matProj * (cc_matView * cc_matWorld) * pos;\n}\nvoid main() { gl_Position = vert(); }", "frag": "\nprecision mediump float;\nmat3 transposeMat3 (mat3 v) {\n  mat3 tmp;\n  tmp[0] = vec3(v[0].x, v[1].x, v[2].x);\n  tmp[1] = vec3(v[0].y, v[1].y, v[2].y);\n  tmp[2] = vec3(v[0].z, v[1].z, v[2].z);\n  return tmp;\n}\nvoid ClipQuadToHorizon (inout vec3 L[5], out int n) {\n  int config = 0;\n  if (L[0].z > 0.0) config += 1;\n  if (L[1].z > 0.0) config += 2;\n  if (L[2].z > 0.0) config += 4;\n  if (L[3].z > 0.0) config += 8;\n  config = 15;\n  n = 0;\n  if (config == 0)\n  {\n  }\n  else if (config == 1) {\n    n = 3;\n    L[1] = -L[1].z * L[0] + L[0].z * L[1];\n    L[2] = -L[3].z * L[0] + L[0].z * L[3];\n  }\n  else if (config == 2) {\n    n = 3;\n    L[0] = -L[0].z * L[1] + L[1].z * L[0];\n    L[2] = -L[2].z * L[1] + L[1].z * L[2];\n  }\n  else if (config == 3) {\n    n = 4;\n    L[2] = -L[2].z * L[1] + L[1].z * L[2];\n    L[3] = -L[3].z * L[0] + L[0].z * L[3];\n  }\n  else if (config == 4) {\n    n = 3;\n    L[0] = -L[3].z * L[2] + L[2].z * L[3];\n    L[1] = -L[1].z * L[2] + L[2].z * L[1];\n  }\n  else if (config == 5) {\n    n = 0;\n  }\n  else if (config == 6) {\n    n = 4;\n    L[0] = -L[0].z * L[1] + L[1].z * L[0];\n    L[3] = -L[3].z * L[2] + L[2].z * L[3];\n  }\n  else if (config == 7) {\n    n = 5;\n    L[4] = -L[3].z * L[0] + L[0].z * L[3];\n    L[3] = -L[3].z * L[2] + L[2].z * L[3];\n  }\n  else if (config == 8) {\n    n = 3;\n    L[0] = -L[0].z * L[3] + L[3].z * L[0];\n    L[1] = -L[2].z * L[3] + L[3].z * L[2];\n    L[2] =  L[3];\n  }\n  else if (config == 9) {\n    n = 4;\n    L[1] = -L[1].z * L[0] + L[0].z * L[1];\n    L[2] = -L[2].z * L[3] + L[3].z * L[2];\n  }\n  else if (config == 10) {\n    n = 0;\n  }\n  else if (config == 11) {\n    n = 5;\n    L[4] = L[3];\n    L[3] = -L[2].z * L[3] + L[3].z * L[2];\n    L[2] = -L[2].z * L[1] + L[1].z * L[2];\n  }\n  else if (config == 12) {\n    n = 4;\n    L[1] = -L[1].z * L[2] + L[2].z * L[1];\n    L[0] = -L[0].z * L[3] + L[3].z * L[0];\n  }\n  else if (config == 13) {\n    n = 5;\n    L[4] = L[3];\n    L[3] = L[2];\n    L[2] = -L[1].z * L[2] + L[2].z * L[1];\n    L[1] = -L[1].z * L[0] + L[0].z * L[1];\n  }\n  else if (config == 14) {\n    n = 5;\n    L[4] = -L[0].z * L[3] + L[3].z * L[0];\n    L[0] = -L[0].z * L[1] + L[1].z * L[0];\n  }\n  else if (config == 15) {\n    n = 4;\n  }\n  if (n == 3) L[3] = L[0];\n  if (n == 4) L[4] = L[0];\n}\nfloat IntegrateEdge (vec3 v1, vec3 v2) {\n  float cosTheta = dot(v1, v2);\n  float theta = acos(cosTheta);\n  return cross(v1, v2).z * ((theta > 0.001) ? theta/sin(theta) : 4.0);\n}\nvec3 LTC_Evaluate (vec3 N, vec3 V, vec3 P, mat3 Minv, vec3 points[4]) {\n  vec3 T1, T2;\n  T1 = normalize(V - N*dot(V, N));\n  T2 = cross(N, T1);\n  Minv = Minv * transposeMat3(mat3(T1, T2, N));\n  vec3 L[5];\n  L[0] = Minv * (points[0] - P);\n  L[1] = Minv * (points[1] - P);\n  L[2] = Minv * (points[2] - P);\n  L[3] = Minv * (points[3] - P);\n  int n;\n  ClipQuadToHorizon(L, n);\n  if (n == 0) return vec3(0, 0, 0);\n  L[0] = normalize(L[0]);\n  L[1] = normalize(L[1]);\n  L[2] = normalize(L[2]);\n  L[3] = normalize(L[3]);\n  L[4] = normalize(L[4]);\n  float sum = 0.0;\n  sum += IntegrateEdge(L[0], L[1]);\n  sum += IntegrateEdge(L[1], L[2]);\n  sum += IntegrateEdge(L[2], L[3]);\n  if (n >= 4) sum += IntegrateEdge(L[3], L[4]);\n  if (n == 5) sum += IntegrateEdge(L[4], L[0]);\n  sum = max(0.0, sum);\n  vec3 Lo_i = vec3(sum, sum, sum);\n  return Lo_i;\n}\nlayout(set = 0, binding = 0) uniform CCGlobal {\n  highp   vec4 cc_time;\n  mediump vec4 cc_screenSize;\n  mediump vec4 cc_nativeSize;\n  mediump vec4 cc_probeInfo;\n  mediump vec4 cc_debug_view_mode;\n};\nlayout(set = 0, binding = 1) uniform CCCamera {\n  highp   mat4 cc_matView;\n  highp   mat4 cc_matViewInv;\n  highp   mat4 cc_matProj;\n  highp   mat4 cc_matProjInv;\n  highp   mat4 cc_matViewProj;\n  highp   mat4 cc_matViewProjInv;\n  highp   vec4 cc_cameraPos;\n  mediump vec4 cc_surfaceTransform;\n  mediump vec4 cc_screenScale;\n  mediump vec4 cc_exposure;\n  mediump vec4 cc_mainLitDir;\n  mediump vec4 cc_mainLitColor;\n  mediump vec4 cc_ambientSky;\n  mediump vec4 cc_ambientGround;\n  mediump vec4 cc_fogColor;\n  mediump vec4 cc_fogBase;\n  mediump vec4 cc_fogAdd;\n  mediump vec4 cc_nearFar;\n  mediump vec4 cc_viewPort;\n};\nvec3 LinearToSRGB(vec3 linear) {\n#ifdef CC_USE_SURFACE_SHADER\n  #if CC_USE_DEBUG_VIEW == CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC && CC_SURFACES_ENABLE_DEBUG_VIEW\n    if (!IS_DEBUG_VIEW_COMPOSITE_ENABLE_GAMMA_CORRECTION) {\n      return linear;\n    }\n  #endif\n#endif\n  return sqrt(linear);\n}\n#if CC_USE_LIGHT_PROBE\n  vec3 ACESToneMap (vec3 color) {\n    color = min(color, vec3(8.0));\n    const float A = 2.51;\n    const float B = 0.03;\n    const float C = 2.43;\n    const float D = 0.59;\n    const float E = 0.14;\n    return (color * (A * color + B)) / (color * (C * color + D) + E);\n  }\n  vec4 packRGBE (vec3 rgb) {\n    highp float maxComp = max(max(rgb.r, rgb.g), rgb.b);\n    highp float e = 128.0;\n    if (maxComp > 0.0001) {\n      e = log(maxComp) / log(1.1);\n      e = ceil(e);\n      e = clamp(e + 128.0, 0.0, 255.0);\n    }\n    highp float sc = 1.0 / pow(1.1, e - 128.0);\n    vec3 encode = clamp(rgb * sc, vec3(0.0), vec3(1.0)) * 255.0;\n    vec3 encode_rounded = floor(encode) + step(encode - floor(encode), vec3(0.5));\n    return vec4(encode_rounded, e) / 255.0;\n  }\n  vec4 CCFragOutput (vec4 color) {\n    #if CC_USE_RGBE_OUTPUT\n      color = packRGBE(color.rgb);\n    #elif !CC_USE_FLOAT_OUTPUT\n      #if CC_USE_HDR && CC_TONE_MAPPING_TYPE == HDR_TONE_MAPPING_ACES\n        color.rgb = ACESToneMap(color.rgb);\n      #endif\n      color.rgb = LinearToSRGB(color.rgb);\n    #endif\n    return color;\n  }\n#else\n  vec4 CCFragOutput (vec4 color) {\n    return color;\n  }\n#endif\nlayout(location = 0) in vec3 normal_w;\nlayout(location = 1) in vec3 pos_w;\nlayout(location = 2) in vec3 pos_l;\nlayout(location = 3) in vec3 right;\nlayout(location = 4) in vec3 up;\nlayout(location = 5) in vec3 forward;\nlayout(set = 1, binding = 0) uniform Constant {\n  vec4 mainColor;\n  vec4 cc_sh_linear_const_r;\n  vec4 cc_sh_linear_const_g;\n  vec4 cc_sh_linear_const_b;\n  vec4 cc_sh_quadratic_r;\n  vec4 cc_sh_quadratic_g;\n  vec4 cc_sh_quadratic_b;\n  vec4 cc_sh_quadratic_a;\n};\n#if CC_USE_LIGHT_PROBE\n  #if CC_USE_LIGHT_PROBE\n  vec3 SHEvaluate(vec3 normal)\n  {\n      vec3 result;\n  #if USE_INSTANCING\n      vec4 normal4 = vec4(normal, 1.0);\n      result.r = dot(v_sh_linear_const_r, normal4);\n      result.g = dot(v_sh_linear_const_g, normal4);\n      result.b = dot(v_sh_linear_const_b, normal4);\n  #else\n      vec4 normal4 = vec4(normal, 1.0);\n      result.r = dot(cc_sh_linear_const_r, normal4);\n      result.g = dot(cc_sh_linear_const_g, normal4);\n      result.b = dot(cc_sh_linear_const_b, normal4);\n      vec4 n14 = normal.xyzz * normal.yzzx;\n      float n5 = normal.x * normal.x - normal.y * normal.y;\n      result.r += dot(cc_sh_quadratic_r, n14);\n      result.g += dot(cc_sh_quadratic_g, n14);\n      result.b += dot(cc_sh_quadratic_b, n14);\n      result += (cc_sh_quadratic_a.rgb * n5);\n  #endif\n    #if CC_USE_HDR\n      result *= cc_exposure.w * cc_exposure.x;\n    #endif\n    return result;\n  }\n  #endif\n#endif\nvec4 gizmo_fs (float alpha) {\n  #if CC_USE_LIGHT_PROBE\n    vec3 N = normalize(normal_w) * (float(gl_FrontFacing) * 2.0 - 1.0);\n    vec3 diffuse = SHEvaluate(N);\n    #if USE_FORWARD_PIPELINE\n      return CCFragOutput(vec4(diffuse, mainColor.a * alpha));\n    #else\n      return vec4(diffuse, mainColor.a * alpha);\n    #endif\n  #else\n    vec3 N = normalize(normal_w) * (float(gl_FrontFacing) * 2.0 - 1.0);\n    vec3 V = normalize(cc_cameraPos.xyz - pos_w);\n    vec3 points [4];\n    points[0] = (forward * 3.0 + right + up) * 40.0;\n    points[1] = (forward * 3.0 - right + up) * 40.0;\n    points[2] = (forward * 3.0 - right - up) * 40.0;\n    points[3] = (forward * 3.0 + right - up) * 40.0;\n    vec3 diffuse = LinearToSRGB(mainColor.rgb * LTC_Evaluate(N, V, pos_l, mat3(1), points));\n    #if USE_FORWARD_PIPELINE\n      return CCFragOutput(vec4(diffuse, mainColor.a * alpha));\n    #else\n      return vec4(diffuse, mainColor.a * alpha);\n    #endif\n  #endif\n}\nvec4 front () {\n  return gizmo_fs(1.0);\n}\nlayout(location = 0) out vec4 cc_FragColor;\nvoid main() { cc_FragColor = front(); }"}, "glsl3": {"vert": "\nprecision mediump float;\nlayout(std140) uniform CCLocal {\n  highp mat4 cc_matWorld;\n  highp mat4 cc_matWorldIT;\n  highp vec4 cc_lightingMapUVParam;\n  highp vec4 cc_localShadowBias;\n  highp vec4 cc_reflectionProbeData1;\n  highp vec4 cc_reflectionProbeData2;\n  highp vec4 cc_reflectionProbeBlendData1;\n  highp vec4 cc_reflectionProbeBlendData2;\n};\nlayout(std140) uniform CCGlobal {\n  highp   vec4 cc_time;\n  mediump vec4 cc_screenSize;\n  mediump vec4 cc_nativeSize;\n  mediump vec4 cc_probeInfo;\n  mediump vec4 cc_debug_view_mode;\n};\nlayout(std140) uniform CCCamera {\n  highp   mat4 cc_matView;\n  highp   mat4 cc_matViewInv;\n  highp   mat4 cc_matProj;\n  highp   mat4 cc_matProjInv;\n  highp   mat4 cc_matViewProj;\n  highp   mat4 cc_matViewProjInv;\n  highp   vec4 cc_cameraPos;\n  mediump vec4 cc_surfaceTransform;\n  mediump vec4 cc_screenScale;\n  mediump vec4 cc_exposure;\n  mediump vec4 cc_mainLitDir;\n  mediump vec4 cc_mainLitColor;\n  mediump vec4 cc_ambientSky;\n  mediump vec4 cc_ambientGround;\n  mediump vec4 cc_fogColor;\n  mediump vec4 cc_fogBase;\n  mediump vec4 cc_fogAdd;\n  mediump vec4 cc_nearFar;\n  mediump vec4 cc_viewPort;\n};\nin vec3 a_position;\nin vec3 a_normal;\nout vec3 normal_w;\nout vec3 pos_w;\nout vec3 pos_l;\nout vec3 right;\nout vec3 up;\nout vec3 forward;\nvec4 vert () {\n  vec4 pos = vec4(a_position, 1);\n  vec4 normal = vec4(a_normal, 0);\n  pos_l = a_position;\n  pos_w = (cc_matWorld * pos).xyz;\n  normal_w = (cc_matWorldIT * normal).xyz;\n  right = vec3(cc_matView[0][0], cc_matView[1][0], cc_matView[2][0]);\n  up = vec3(cc_matView[0][1], cc_matView[1][1], cc_matView[2][1]);\n  forward = vec3(cc_matView[0][2], cc_matView[1][2], cc_matView[2][2]);\n  return cc_matProj * (cc_matView * cc_matWorld) * pos;\n}\nvoid main() { gl_Position = vert(); }", "frag": "\nprecision mediump float;\nmat3 transposeMat3 (mat3 v) {\n  mat3 tmp;\n  tmp[0] = vec3(v[0].x, v[1].x, v[2].x);\n  tmp[1] = vec3(v[0].y, v[1].y, v[2].y);\n  tmp[2] = vec3(v[0].z, v[1].z, v[2].z);\n  return tmp;\n}\nvoid ClipQuadToHorizon (inout vec3 L[5], out int n) {\n  int config = 0;\n  if (L[0].z > 0.0) config += 1;\n  if (L[1].z > 0.0) config += 2;\n  if (L[2].z > 0.0) config += 4;\n  if (L[3].z > 0.0) config += 8;\n  config = 15;\n  n = 0;\n  if (config == 0)\n  {\n  }\n  else if (config == 1) {\n    n = 3;\n    L[1] = -L[1].z * L[0] + L[0].z * L[1];\n    L[2] = -L[3].z * L[0] + L[0].z * L[3];\n  }\n  else if (config == 2) {\n    n = 3;\n    L[0] = -L[0].z * L[1] + L[1].z * L[0];\n    L[2] = -L[2].z * L[1] + L[1].z * L[2];\n  }\n  else if (config == 3) {\n    n = 4;\n    L[2] = -L[2].z * L[1] + L[1].z * L[2];\n    L[3] = -L[3].z * L[0] + L[0].z * L[3];\n  }\n  else if (config == 4) {\n    n = 3;\n    L[0] = -L[3].z * L[2] + L[2].z * L[3];\n    L[1] = -L[1].z * L[2] + L[2].z * L[1];\n  }\n  else if (config == 5) {\n    n = 0;\n  }\n  else if (config == 6) {\n    n = 4;\n    L[0] = -L[0].z * L[1] + L[1].z * L[0];\n    L[3] = -L[3].z * L[2] + L[2].z * L[3];\n  }\n  else if (config == 7) {\n    n = 5;\n    L[4] = -L[3].z * L[0] + L[0].z * L[3];\n    L[3] = -L[3].z * L[2] + L[2].z * L[3];\n  }\n  else if (config == 8) {\n    n = 3;\n    L[0] = -L[0].z * L[3] + L[3].z * L[0];\n    L[1] = -L[2].z * L[3] + L[3].z * L[2];\n    L[2] =  L[3];\n  }\n  else if (config == 9) {\n    n = 4;\n    L[1] = -L[1].z * L[0] + L[0].z * L[1];\n    L[2] = -L[2].z * L[3] + L[3].z * L[2];\n  }\n  else if (config == 10) {\n    n = 0;\n  }\n  else if (config == 11) {\n    n = 5;\n    L[4] = L[3];\n    L[3] = -L[2].z * L[3] + L[3].z * L[2];\n    L[2] = -L[2].z * L[1] + L[1].z * L[2];\n  }\n  else if (config == 12) {\n    n = 4;\n    L[1] = -L[1].z * L[2] + L[2].z * L[1];\n    L[0] = -L[0].z * L[3] + L[3].z * L[0];\n  }\n  else if (config == 13) {\n    n = 5;\n    L[4] = L[3];\n    L[3] = L[2];\n    L[2] = -L[1].z * L[2] + L[2].z * L[1];\n    L[1] = -L[1].z * L[0] + L[0].z * L[1];\n  }\n  else if (config == 14) {\n    n = 5;\n    L[4] = -L[0].z * L[3] + L[3].z * L[0];\n    L[0] = -L[0].z * L[1] + L[1].z * L[0];\n  }\n  else if (config == 15) {\n    n = 4;\n  }\n  if (n == 3) L[3] = L[0];\n  if (n == 4) L[4] = L[0];\n}\nfloat IntegrateEdge (vec3 v1, vec3 v2) {\n  float cosTheta = dot(v1, v2);\n  float theta = acos(cosTheta);\n  return cross(v1, v2).z * ((theta > 0.001) ? theta/sin(theta) : 4.0);\n}\nvec3 LTC_Evaluate (vec3 N, vec3 V, vec3 P, mat3 Minv, vec3 points[4]) {\n  vec3 T1, T2;\n  T1 = normalize(V - N*dot(V, N));\n  T2 = cross(N, T1);\n  Minv = Minv * transposeMat3(mat3(T1, T2, N));\n  vec3 L[5];\n  L[0] = Minv * (points[0] - P);\n  L[1] = Minv * (points[1] - P);\n  L[2] = Minv * (points[2] - P);\n  L[3] = Minv * (points[3] - P);\n  int n;\n  ClipQuadToHorizon(L, n);\n  if (n == 0) return vec3(0, 0, 0);\n  L[0] = normalize(L[0]);\n  L[1] = normalize(L[1]);\n  L[2] = normalize(L[2]);\n  L[3] = normalize(L[3]);\n  L[4] = normalize(L[4]);\n  float sum = 0.0;\n  sum += IntegrateEdge(L[0], L[1]);\n  sum += IntegrateEdge(L[1], L[2]);\n  sum += IntegrateEdge(L[2], L[3]);\n  if (n >= 4) sum += IntegrateEdge(L[3], L[4]);\n  if (n == 5) sum += IntegrateEdge(L[4], L[0]);\n  sum = max(0.0, sum);\n  vec3 Lo_i = vec3(sum, sum, sum);\n  return Lo_i;\n}\nlayout(std140) uniform CCGlobal {\n  highp   vec4 cc_time;\n  mediump vec4 cc_screenSize;\n  mediump vec4 cc_nativeSize;\n  mediump vec4 cc_probeInfo;\n  mediump vec4 cc_debug_view_mode;\n};\nlayout(std140) uniform CCCamera {\n  highp   mat4 cc_matView;\n  highp   mat4 cc_matViewInv;\n  highp   mat4 cc_matProj;\n  highp   mat4 cc_matProjInv;\n  highp   mat4 cc_matViewProj;\n  highp   mat4 cc_matViewProjInv;\n  highp   vec4 cc_cameraPos;\n  mediump vec4 cc_surfaceTransform;\n  mediump vec4 cc_screenScale;\n  mediump vec4 cc_exposure;\n  mediump vec4 cc_mainLitDir;\n  mediump vec4 cc_mainLitColor;\n  mediump vec4 cc_ambientSky;\n  mediump vec4 cc_ambientGround;\n  mediump vec4 cc_fogColor;\n  mediump vec4 cc_fogBase;\n  mediump vec4 cc_fogAdd;\n  mediump vec4 cc_nearFar;\n  mediump vec4 cc_viewPort;\n};\nvec3 LinearToSRGB(vec3 linear) {\n#ifdef CC_USE_SURFACE_SHADER\n  #if CC_USE_DEBUG_VIEW == CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC && CC_SURFACES_ENABLE_DEBUG_VIEW\n    if (!IS_DEBUG_VIEW_COMPOSITE_ENABLE_GAMMA_CORRECTION) {\n      return linear;\n    }\n  #endif\n#endif\n  return sqrt(linear);\n}\n#if CC_USE_LIGHT_PROBE\n  vec3 ACESToneMap (vec3 color) {\n    color = min(color, vec3(8.0));\n    const float A = 2.51;\n    const float B = 0.03;\n    const float C = 2.43;\n    const float D = 0.59;\n    const float E = 0.14;\n    return (color * (A * color + B)) / (color * (C * color + D) + E);\n  }\n  vec4 packRGBE (vec3 rgb) {\n    highp float maxComp = max(max(rgb.r, rgb.g), rgb.b);\n    highp float e = 128.0;\n    if (maxComp > 0.0001) {\n      e = log(maxComp) / log(1.1);\n      e = ceil(e);\n      e = clamp(e + 128.0, 0.0, 255.0);\n    }\n    highp float sc = 1.0 / pow(1.1, e - 128.0);\n    vec3 encode = clamp(rgb * sc, vec3(0.0), vec3(1.0)) * 255.0;\n    vec3 encode_rounded = floor(encode) + step(encode - floor(encode), vec3(0.5));\n    return vec4(encode_rounded, e) / 255.0;\n  }\n  vec4 CCFragOutput (vec4 color) {\n    #if CC_USE_RGBE_OUTPUT\n      color = packRGBE(color.rgb);\n    #elif !CC_USE_FLOAT_OUTPUT\n      #if CC_USE_HDR && CC_TONE_MAPPING_TYPE == HDR_TONE_MAPPING_ACES\n        color.rgb = ACESToneMap(color.rgb);\n      #endif\n      color.rgb = LinearToSRGB(color.rgb);\n    #endif\n    return color;\n  }\n#else\n  vec4 CCFragOutput (vec4 color) {\n    return color;\n  }\n#endif\nin vec3 normal_w;\nin vec3 pos_w;\nin vec3 pos_l;\nin vec3 right;\nin vec3 up;\nin vec3 forward;\nlayout(std140) uniform Constant {\n  vec4 mainColor;\n  vec4 cc_sh_linear_const_r;\n  vec4 cc_sh_linear_const_g;\n  vec4 cc_sh_linear_const_b;\n  vec4 cc_sh_quadratic_r;\n  vec4 cc_sh_quadratic_g;\n  vec4 cc_sh_quadratic_b;\n  vec4 cc_sh_quadratic_a;\n};\n#if CC_USE_LIGHT_PROBE\n  #if CC_USE_LIGHT_PROBE\n  vec3 SHEvaluate(vec3 normal)\n  {\n      vec3 result;\n  #if USE_INSTANCING\n      vec4 normal4 = vec4(normal, 1.0);\n      result.r = dot(v_sh_linear_const_r, normal4);\n      result.g = dot(v_sh_linear_const_g, normal4);\n      result.b = dot(v_sh_linear_const_b, normal4);\n  #else\n      vec4 normal4 = vec4(normal, 1.0);\n      result.r = dot(cc_sh_linear_const_r, normal4);\n      result.g = dot(cc_sh_linear_const_g, normal4);\n      result.b = dot(cc_sh_linear_const_b, normal4);\n      vec4 n14 = normal.xyzz * normal.yzzx;\n      float n5 = normal.x * normal.x - normal.y * normal.y;\n      result.r += dot(cc_sh_quadratic_r, n14);\n      result.g += dot(cc_sh_quadratic_g, n14);\n      result.b += dot(cc_sh_quadratic_b, n14);\n      result += (cc_sh_quadratic_a.rgb * n5);\n  #endif\n    #if CC_USE_HDR\n      result *= cc_exposure.w * cc_exposure.x;\n    #endif\n    return result;\n  }\n  #endif\n#endif\nvec4 gizmo_fs (float alpha) {\n  #if CC_USE_LIGHT_PROBE\n    vec3 N = normalize(normal_w) * (float(gl_FrontFacing) * 2.0 - 1.0);\n    vec3 diffuse = SHEvaluate(N);\n    #if USE_FORWARD_PIPELINE\n      return CCFragOutput(vec4(diffuse, mainColor.a * alpha));\n    #else\n      return vec4(diffuse, mainColor.a * alpha);\n    #endif\n  #else\n    vec3 N = normalize(normal_w) * (float(gl_FrontFacing) * 2.0 - 1.0);\n    vec3 V = normalize(cc_cameraPos.xyz - pos_w);\n    vec3 points [4];\n    points[0] = (forward * 3.0 + right + up) * 40.0;\n    points[1] = (forward * 3.0 - right + up) * 40.0;\n    points[2] = (forward * 3.0 - right - up) * 40.0;\n    points[3] = (forward * 3.0 + right - up) * 40.0;\n    vec3 diffuse = LinearToSRGB(mainColor.rgb * LTC_Evaluate(N, V, pos_l, mat3(1), points));\n    #if USE_FORWARD_PIPELINE\n      return CCFragOutput(vec4(diffuse, mainColor.a * alpha));\n    #else\n      return vec4(diffuse, mainColor.a * alpha);\n    #endif\n  #endif\n}\nvec4 front () {\n  return gizmo_fs(1.0);\n}\nlayout(location = 0) out vec4 cc_FragColor;\nvoid main() { cc_FragColor = front(); }"}, "glsl1": {"vert": "\nprecision mediump float;\nuniform highp mat4 cc_matWorld;\n  uniform highp mat4 cc_matWorldIT;\nuniform highp mat4 cc_matView;\n  uniform highp mat4 cc_matProj;\nattribute vec3 a_position;\nattribute vec3 a_normal;\nvarying vec3 normal_w;\nvarying vec3 pos_w;\nvarying vec3 pos_l;\nvarying vec3 right;\nvarying vec3 up;\nvarying vec3 forward;\nvec4 vert () {\n  vec4 pos = vec4(a_position, 1);\n  vec4 normal = vec4(a_normal, 0);\n  pos_l = a_position;\n  pos_w = (cc_matWorld * pos).xyz;\n  normal_w = (cc_matWorldIT * normal).xyz;\n  right = vec3(cc_matView[0][0], cc_matView[1][0], cc_matView[2][0]);\n  up = vec3(cc_matView[0][1], cc_matView[1][1], cc_matView[2][1]);\n  forward = vec3(cc_matView[0][2], cc_matView[1][2], cc_matView[2][2]);\n  return cc_matProj * (cc_matView * cc_matWorld) * pos;\n}\nvoid main() { gl_Position = vert(); }", "frag": "\nprecision mediump float;\nmat3 transposeMat3 (mat3 v) {\n  mat3 tmp;\n  tmp[0] = vec3(v[0].x, v[1].x, v[2].x);\n  tmp[1] = vec3(v[0].y, v[1].y, v[2].y);\n  tmp[2] = vec3(v[0].z, v[1].z, v[2].z);\n  return tmp;\n}\nvoid ClipQuadToHorizon (inout vec3 L[5], out int n) {\n  int config = 0;\n  if (L[0].z > 0.0) config += 1;\n  if (L[1].z > 0.0) config += 2;\n  if (L[2].z > 0.0) config += 4;\n  if (L[3].z > 0.0) config += 8;\n  config = 15;\n  n = 0;\n  if (config == 0)\n  {\n  }\n  else if (config == 1) {\n    n = 3;\n    L[1] = -L[1].z * L[0] + L[0].z * L[1];\n    L[2] = -L[3].z * L[0] + L[0].z * L[3];\n  }\n  else if (config == 2) {\n    n = 3;\n    L[0] = -L[0].z * L[1] + L[1].z * L[0];\n    L[2] = -L[2].z * L[1] + L[1].z * L[2];\n  }\n  else if (config == 3) {\n    n = 4;\n    L[2] = -L[2].z * L[1] + L[1].z * L[2];\n    L[3] = -L[3].z * L[0] + L[0].z * L[3];\n  }\n  else if (config == 4) {\n    n = 3;\n    L[0] = -L[3].z * L[2] + L[2].z * L[3];\n    L[1] = -L[1].z * L[2] + L[2].z * L[1];\n  }\n  else if (config == 5) {\n    n = 0;\n  }\n  else if (config == 6) {\n    n = 4;\n    L[0] = -L[0].z * L[1] + L[1].z * L[0];\n    L[3] = -L[3].z * L[2] + L[2].z * L[3];\n  }\n  else if (config == 7) {\n    n = 5;\n    L[4] = -L[3].z * L[0] + L[0].z * L[3];\n    L[3] = -L[3].z * L[2] + L[2].z * L[3];\n  }\n  else if (config == 8) {\n    n = 3;\n    L[0] = -L[0].z * L[3] + L[3].z * L[0];\n    L[1] = -L[2].z * L[3] + L[3].z * L[2];\n    L[2] =  L[3];\n  }\n  else if (config == 9) {\n    n = 4;\n    L[1] = -L[1].z * L[0] + L[0].z * L[1];\n    L[2] = -L[2].z * L[3] + L[3].z * L[2];\n  }\n  else if (config == 10) {\n    n = 0;\n  }\n  else if (config == 11) {\n    n = 5;\n    L[4] = L[3];\n    L[3] = -L[2].z * L[3] + L[3].z * L[2];\n    L[2] = -L[2].z * L[1] + L[1].z * L[2];\n  }\n  else if (config == 12) {\n    n = 4;\n    L[1] = -L[1].z * L[2] + L[2].z * L[1];\n    L[0] = -L[0].z * L[3] + L[3].z * L[0];\n  }\n  else if (config == 13) {\n    n = 5;\n    L[4] = L[3];\n    L[3] = L[2];\n    L[2] = -L[1].z * L[2] + L[2].z * L[1];\n    L[1] = -L[1].z * L[0] + L[0].z * L[1];\n  }\n  else if (config == 14) {\n    n = 5;\n    L[4] = -L[0].z * L[3] + L[3].z * L[0];\n    L[0] = -L[0].z * L[1] + L[1].z * L[0];\n  }\n  else if (config == 15) {\n    n = 4;\n  }\n  if (n == 3) L[3] = L[0];\n  if (n == 4) L[4] = L[0];\n}\nfloat IntegrateEdge (vec3 v1, vec3 v2) {\n  float cosTheta = dot(v1, v2);\n  float theta = acos(cosTheta);\n  return cross(v1, v2).z * ((theta > 0.001) ? theta/sin(theta) : 4.0);\n}\nvec3 LTC_Evaluate (vec3 N, vec3 V, vec3 P, mat3 Minv, vec3 points[4]) {\n  vec3 T1, T2;\n  T1 = normalize(V - N*dot(V, N));\n  T2 = cross(N, T1);\n  Minv = Minv * transposeMat3(mat3(T1, T2, N));\n  vec3 L[5];\n  L[0] = Minv * (points[0] - P);\n  L[1] = Minv * (points[1] - P);\n  L[2] = Minv * (points[2] - P);\n  L[3] = Minv * (points[3] - P);\n  int n;\n  ClipQuadToHorizon(L, n);\n  if (n == 0) return vec3(0, 0, 0);\n  L[0] = normalize(L[0]);\n  L[1] = normalize(L[1]);\n  L[2] = normalize(L[2]);\n  L[3] = normalize(L[3]);\n  L[4] = normalize(L[4]);\n  float sum = 0.0;\n  sum += IntegrateEdge(L[0], L[1]);\n  sum += IntegrateEdge(L[1], L[2]);\n  sum += IntegrateEdge(L[2], L[3]);\n  if (n >= 4) sum += IntegrateEdge(L[3], L[4]);\n  if (n == 5) sum += IntegrateEdge(L[4], L[0]);\n  sum = max(0.0, sum);\n  vec3 Lo_i = vec3(sum, sum, sum);\n  return Lo_i;\n}\nuniform highp vec4 cc_cameraPos;\n  uniform mediump vec4 cc_exposure;\nvec3 LinearToSRGB(vec3 linear) {\n#ifdef CC_USE_SURFACE_SHADER\n  #if CC_USE_DEBUG_VIEW == CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC && CC_SURFACES_ENABLE_DEBUG_VIEW\n    if (!IS_DEBUG_VIEW_COMPOSITE_ENABLE_GAMMA_CORRECTION) {\n      return linear;\n    }\n  #endif\n#endif\n  return sqrt(linear);\n}\n#if CC_USE_LIGHT_PROBE\n  vec3 ACESToneMap (vec3 color) {\n    color = min(color, vec3(8.0));\n    const float A = 2.51;\n    const float B = 0.03;\n    const float C = 2.43;\n    const float D = 0.59;\n    const float E = 0.14;\n    return (color * (A * color + B)) / (color * (C * color + D) + E);\n  }\n  vec4 packRGBE (vec3 rgb) {\n    highp float maxComp = max(max(rgb.r, rgb.g), rgb.b);\n    highp float e = 128.0;\n    if (maxComp > 0.0001) {\n      e = log(maxComp) / log(1.1);\n      e = ceil(e);\n      e = clamp(e + 128.0, 0.0, 255.0);\n    }\n    highp float sc = 1.0 / pow(1.1, e - 128.0);\n    vec3 encode = clamp(rgb * sc, vec3(0.0), vec3(1.0)) * 255.0;\n    vec3 encode_rounded = floor(encode) + step(encode - floor(encode), vec3(0.5));\n    return vec4(encode_rounded, e) / 255.0;\n  }\n  vec4 CCFragOutput (vec4 color) {\n    #if CC_USE_RGBE_OUTPUT\n      color = packRGBE(color.rgb);\n    #elif !CC_USE_FLOAT_OUTPUT\n      #if CC_USE_HDR && CC_TONE_MAPPING_TYPE == HDR_TONE_MAPPING_ACES\n        color.rgb = ACESToneMap(color.rgb);\n      #endif\n      color.rgb = LinearToSRGB(color.rgb);\n    #endif\n    return color;\n  }\n#else\n  vec4 CCFragOutput (vec4 color) {\n    return color;\n  }\n#endif\nvarying vec3 normal_w;\nvarying vec3 pos_w;\nvarying vec3 pos_l;\nvarying vec3 right;\nvarying vec3 up;\nvarying vec3 forward;\n   uniform vec4 mainColor;\n   uniform vec4 cc_sh_linear_const_r;\n   uniform vec4 cc_sh_linear_const_g;\n   uniform vec4 cc_sh_linear_const_b;\n   uniform vec4 cc_sh_quadratic_r;\n   uniform vec4 cc_sh_quadratic_g;\n   uniform vec4 cc_sh_quadratic_b;\n   uniform vec4 cc_sh_quadratic_a;\n#if CC_USE_LIGHT_PROBE\n  #if CC_USE_LIGHT_PROBE\n  vec3 SHEvaluate(vec3 normal)\n  {\n      vec3 result;\n  #if USE_INSTANCING\n      vec4 normal4 = vec4(normal, 1.0);\n      result.r = dot(v_sh_linear_const_r, normal4);\n      result.g = dot(v_sh_linear_const_g, normal4);\n      result.b = dot(v_sh_linear_const_b, normal4);\n  #else\n      vec4 normal4 = vec4(normal, 1.0);\n      result.r = dot(cc_sh_linear_const_r, normal4);\n      result.g = dot(cc_sh_linear_const_g, normal4);\n      result.b = dot(cc_sh_linear_const_b, normal4);\n      vec4 n14 = normal.xyzz * normal.yzzx;\n      float n5 = normal.x * normal.x - normal.y * normal.y;\n      result.r += dot(cc_sh_quadratic_r, n14);\n      result.g += dot(cc_sh_quadratic_g, n14);\n      result.b += dot(cc_sh_quadratic_b, n14);\n      result += (cc_sh_quadratic_a.rgb * n5);\n  #endif\n    #if CC_USE_HDR\n      result *= cc_exposure.w * cc_exposure.x;\n    #endif\n    return result;\n  }\n  #endif\n#endif\nvec4 gizmo_fs (float alpha) {\n  #if CC_USE_LIGHT_PROBE\n    vec3 N = normalize(normal_w) * (float(gl_FrontFacing) * 2.0 - 1.0);\n    vec3 diffuse = SHEvaluate(N);\n    #if USE_FORWARD_PIPELINE\n      return CCFragOutput(vec4(diffuse, mainColor.a * alpha));\n    #else\n      return vec4(diffuse, mainColor.a * alpha);\n    #endif\n  #else\n    vec3 N = normalize(normal_w) * (float(gl_FrontFacing) * 2.0 - 1.0);\n    vec3 V = normalize(cc_cameraPos.xyz - pos_w);\n    vec3 points [4];\n    points[0] = (forward * 3.0 + right + up) * 40.0;\n    points[1] = (forward * 3.0 - right + up) * 40.0;\n    points[2] = (forward * 3.0 - right - up) * 40.0;\n    points[3] = (forward * 3.0 + right - up) * 40.0;\n    vec3 diffuse = LinearToSRGB(mainColor.rgb * LTC_Evaluate(N, V, pos_l, mat3(1), points));\n    #if USE_FORWARD_PIPELINE\n      return CCFragOutput(vec4(diffuse, mainColor.a * alpha));\n    #else\n      return vec4(diffuse, mainColor.a * alpha);\n    #endif\n  #endif\n}\nvec4 front () {\n  return gizmo_fs(1.0);\n}\nvoid main() { gl_FragColor = front(); }"}, "builtins": {"globals": {"blocks": [{"name": "CCGlobal", "defines": []}, {"name": "CCCamera", "defines": []}], "samplerTextures": [], "buffers": [], "images": []}, "locals": {"blocks": [{"name": "CCLocal", "defines": []}], "samplerTextures": [], "buffers": [], "images": []}, "statistics": {"CC_EFFECT_USED_VERTEX_UNIFORM_VECTORS": 56, "CC_EFFECT_USED_FRAGMENT_UNIFORM_VECTORS": 50}}, "defines": [{"name": "CC_USE_DEBUG_VIEW", "type": "number", "defines": [], "range": [0, 3]}, {"name": "CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC", "type": "boolean", "defines": ["CC_USE_DEBUG_VIEW"]}, {"name": "CC_SURFACES_ENABLE_DEBUG_VIEW", "type": "boolean", "defines": ["CC_USE_DEBUG_VIEW", "CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC"]}, {"name": "CC_USE_LIGHT_PROBE", "type": "boolean", "defines": [], "default": 0}, {"name": "CC_USE_RGBE_OUTPUT", "type": "boolean", "defines": ["CC_USE_LIGHT_PROBE"]}, {"name": "CC_USE_FLOAT_OUTPUT", "type": "boolean", "defines": ["CC_USE_LIGHT_PROBE", "!CC_USE_RGBE_OUTPUT"]}, {"name": "CC_USE_HDR", "type": "boolean", "defines": ["CC_USE_LIGHT_PROBE", "CC_USE_LIGHT_PROBE"]}, {"name": "CC_TONE_MAPPING_TYPE", "type": "number", "defines": ["CC_USE_HDR", "CC_USE_LIGHT_PROBE", "!CC_USE_RGBE_OUTPUT", "!CC_USE_FLOAT_OUTPUT"], "range": [0, 3]}, {"name": "HDR_TONE_MAPPING_ACES", "type": "boolean", "defines": ["CC_USE_HDR", "CC_TONE_MAPPING_TYPE", "CC_USE_LIGHT_PROBE", "!CC_USE_RGBE_OUTPUT", "!CC_USE_FLOAT_OUTPUT"]}, {"name": "USE_INSTANCING", "type": "boolean", "defines": ["CC_USE_LIGHT_PROBE", "CC_USE_LIGHT_PROBE"]}, {"name": "USE_FORWARD_PIPELINE", "type": "boolean", "defines": ["CC_USE_LIGHT_PROBE"]}], "name": "internal/editor/light-probe-visualization|gizmo-vs:vert|gizmo-fs:front"}, {"blocks": [{"name": "Constant", "members": [{"name": "mainColor", "type": 16, "count": 1}, {"name": "cc_sh_linear_const_r", "type": 16, "count": 1}, {"name": "cc_sh_linear_const_g", "type": 16, "count": 1}, {"name": "cc_sh_linear_const_b", "type": 16, "count": 1}, {"name": "cc_sh_quadratic_r", "type": 16, "count": 1}, {"name": "cc_sh_quadratic_g", "type": 16, "count": 1}, {"name": "cc_sh_quadratic_b", "type": 16, "count": 1}, {"name": "cc_sh_quadratic_a", "type": 16, "count": 1}], "defines": [], "stageFlags": 16, "binding": 0}], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": [], "attributes": [{"name": "a_position", "defines": [], "format": 32, "location": 0}, {"name": "a_normal", "defines": [], "format": 32, "location": 1}], "varyings": [{"name": "normal_w", "type": 15, "count": 1, "defines": [], "stageFlags": 17, "location": 0}, {"name": "pos_w", "type": 15, "count": 1, "defines": [], "stageFlags": 17, "location": 1}, {"name": "pos_l", "type": 15, "count": 1, "defines": [], "stageFlags": 17, "location": 2}, {"name": "right", "type": 15, "count": 1, "defines": [], "stageFlags": 17, "location": 3}, {"name": "up", "type": 15, "count": 1, "defines": [], "stageFlags": 17, "location": 4}, {"name": "forward", "type": 15, "count": 1, "defines": [], "stageFlags": 17, "location": 5}], "fragColors": [{"name": "cc_FragColor", "typename": "vec4", "type": 16, "count": 1, "defines": [], "stageFlags": 16, "location": 0}], "descriptors": [{"rate": 0, "blocks": [{"tags": {"builtin": "local"}, "name": "CCLocal", "members": [{"name": "cc_matWorld", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matWorldIT", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_lightingMapUVParam", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_localShadowBias", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_reflectionProbeData1", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_reflectionProbeData2", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_reflectionProbeBlendData1", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_reflectionProbeBlendData2", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}], "defines": [], "stageFlags": 1}], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}, {"rate": 1, "blocks": [{"name": "Constant", "members": [{"name": "mainColor", "type": 16, "count": 1}, {"name": "cc_sh_linear_const_r", "type": 16, "count": 1}, {"name": "cc_sh_linear_const_g", "type": 16, "count": 1}, {"name": "cc_sh_linear_const_b", "type": 16, "count": 1}, {"name": "cc_sh_quadratic_r", "type": 16, "count": 1}, {"name": "cc_sh_quadratic_g", "type": 16, "count": 1}, {"name": "cc_sh_quadratic_b", "type": 16, "count": 1}, {"name": "cc_sh_quadratic_a", "type": 16, "count": 1}], "defines": [], "stageFlags": 16, "binding": 0}], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}, {"rate": 2, "blocks": [], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}, {"rate": 3, "blocks": [{"tags": {"builtin": "global"}, "name": "CCGlobal", "members": [{"name": "cc_time", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_screenSize", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_nativeSize", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_probeInfo", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_debug_view_mode", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}], "defines": [], "stageFlags": 17}, {"tags": {"builtin": "global"}, "name": "CCCamera", "members": [{"name": "cc_matView", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matViewInv", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matProj", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matProjInv", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matViewProj", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matViewProjInv", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_cameraPos", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_surfaceTransform", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_screenScale", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_exposure", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_mainLitDir", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_mainLitColor", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_ambientSky", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_ambientGround", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_fogColor", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_fogBase", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_fogAdd", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_nearFar", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_viewPort", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}], "defines": [], "stageFlags": 17}], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}], "hash": 2568219391, "glsl4": {"vert": "\nprecision mediump float;\nlayout(set = 2, binding = 0) uniform CCLocal {\n  highp mat4 cc_matWorld;\n  highp mat4 cc_matWorldIT;\n  highp vec4 cc_lightingMapUVParam;\n  highp vec4 cc_localShadowBias;\n  highp vec4 cc_reflectionProbeData1;\n  highp vec4 cc_reflectionProbeData2;\n  highp vec4 cc_reflectionProbeBlendData1;\n  highp vec4 cc_reflectionProbeBlendData2;\n};\nlayout(set = 0, binding = 0) uniform CCGlobal {\n  highp   vec4 cc_time;\n  mediump vec4 cc_screenSize;\n  mediump vec4 cc_nativeSize;\n  mediump vec4 cc_probeInfo;\n  mediump vec4 cc_debug_view_mode;\n};\nlayout(set = 0, binding = 1) uniform CCCamera {\n  highp   mat4 cc_matView;\n  highp   mat4 cc_matViewInv;\n  highp   mat4 cc_matProj;\n  highp   mat4 cc_matProjInv;\n  highp   mat4 cc_matViewProj;\n  highp   mat4 cc_matViewProjInv;\n  highp   vec4 cc_cameraPos;\n  mediump vec4 cc_surfaceTransform;\n  mediump vec4 cc_screenScale;\n  mediump vec4 cc_exposure;\n  mediump vec4 cc_mainLitDir;\n  mediump vec4 cc_mainLitColor;\n  mediump vec4 cc_ambientSky;\n  mediump vec4 cc_ambientGround;\n  mediump vec4 cc_fogColor;\n  mediump vec4 cc_fogBase;\n  mediump vec4 cc_fogAdd;\n  mediump vec4 cc_nearFar;\n  mediump vec4 cc_viewPort;\n};\nlayout(location = 0) in vec3 a_position;\nlayout(location = 1) in vec3 a_normal;\nlayout(location = 0) out vec3 normal_w;\nlayout(location = 1) out vec3 pos_w;\nlayout(location = 2) out vec3 pos_l;\nlayout(location = 3) out vec3 right;\nlayout(location = 4) out vec3 up;\nlayout(location = 5) out vec3 forward;\nvec4 vert () {\n  vec4 pos = vec4(a_position, 1);\n  vec4 normal = vec4(a_normal, 0);\n  pos_l = a_position;\n  pos_w = (cc_matWorld * pos).xyz;\n  normal_w = (cc_matWorldIT * normal).xyz;\n  right = vec3(cc_matView[0][0], cc_matView[1][0], cc_matView[2][0]);\n  up = vec3(cc_matView[0][1], cc_matView[1][1], cc_matView[2][1]);\n  forward = vec3(cc_matView[0][2], cc_matView[1][2], cc_matView[2][2]);\n  return cc_matProj * (cc_matView * cc_matWorld) * pos;\n}\nvoid main() { gl_Position = vert(); }", "frag": "\nprecision mediump float;\nmat3 transposeMat3 (mat3 v) {\n  mat3 tmp;\n  tmp[0] = vec3(v[0].x, v[1].x, v[2].x);\n  tmp[1] = vec3(v[0].y, v[1].y, v[2].y);\n  tmp[2] = vec3(v[0].z, v[1].z, v[2].z);\n  return tmp;\n}\nvoid ClipQuadToHorizon (inout vec3 L[5], out int n) {\n  int config = 0;\n  if (L[0].z > 0.0) config += 1;\n  if (L[1].z > 0.0) config += 2;\n  if (L[2].z > 0.0) config += 4;\n  if (L[3].z > 0.0) config += 8;\n  config = 15;\n  n = 0;\n  if (config == 0)\n  {\n  }\n  else if (config == 1) {\n    n = 3;\n    L[1] = -L[1].z * L[0] + L[0].z * L[1];\n    L[2] = -L[3].z * L[0] + L[0].z * L[3];\n  }\n  else if (config == 2) {\n    n = 3;\n    L[0] = -L[0].z * L[1] + L[1].z * L[0];\n    L[2] = -L[2].z * L[1] + L[1].z * L[2];\n  }\n  else if (config == 3) {\n    n = 4;\n    L[2] = -L[2].z * L[1] + L[1].z * L[2];\n    L[3] = -L[3].z * L[0] + L[0].z * L[3];\n  }\n  else if (config == 4) {\n    n = 3;\n    L[0] = -L[3].z * L[2] + L[2].z * L[3];\n    L[1] = -L[1].z * L[2] + L[2].z * L[1];\n  }\n  else if (config == 5) {\n    n = 0;\n  }\n  else if (config == 6) {\n    n = 4;\n    L[0] = -L[0].z * L[1] + L[1].z * L[0];\n    L[3] = -L[3].z * L[2] + L[2].z * L[3];\n  }\n  else if (config == 7) {\n    n = 5;\n    L[4] = -L[3].z * L[0] + L[0].z * L[3];\n    L[3] = -L[3].z * L[2] + L[2].z * L[3];\n  }\n  else if (config == 8) {\n    n = 3;\n    L[0] = -L[0].z * L[3] + L[3].z * L[0];\n    L[1] = -L[2].z * L[3] + L[3].z * L[2];\n    L[2] =  L[3];\n  }\n  else if (config == 9) {\n    n = 4;\n    L[1] = -L[1].z * L[0] + L[0].z * L[1];\n    L[2] = -L[2].z * L[3] + L[3].z * L[2];\n  }\n  else if (config == 10) {\n    n = 0;\n  }\n  else if (config == 11) {\n    n = 5;\n    L[4] = L[3];\n    L[3] = -L[2].z * L[3] + L[3].z * L[2];\n    L[2] = -L[2].z * L[1] + L[1].z * L[2];\n  }\n  else if (config == 12) {\n    n = 4;\n    L[1] = -L[1].z * L[2] + L[2].z * L[1];\n    L[0] = -L[0].z * L[3] + L[3].z * L[0];\n  }\n  else if (config == 13) {\n    n = 5;\n    L[4] = L[3];\n    L[3] = L[2];\n    L[2] = -L[1].z * L[2] + L[2].z * L[1];\n    L[1] = -L[1].z * L[0] + L[0].z * L[1];\n  }\n  else if (config == 14) {\n    n = 5;\n    L[4] = -L[0].z * L[3] + L[3].z * L[0];\n    L[0] = -L[0].z * L[1] + L[1].z * L[0];\n  }\n  else if (config == 15) {\n    n = 4;\n  }\n  if (n == 3) L[3] = L[0];\n  if (n == 4) L[4] = L[0];\n}\nfloat IntegrateEdge (vec3 v1, vec3 v2) {\n  float cosTheta = dot(v1, v2);\n  float theta = acos(cosTheta);\n  return cross(v1, v2).z * ((theta > 0.001) ? theta/sin(theta) : 4.0);\n}\nvec3 LTC_Evaluate (vec3 N, vec3 V, vec3 P, mat3 Minv, vec3 points[4]) {\n  vec3 T1, T2;\n  T1 = normalize(V - N*dot(V, N));\n  T2 = cross(N, T1);\n  Minv = Minv * transposeMat3(mat3(T1, T2, N));\n  vec3 L[5];\n  L[0] = Minv * (points[0] - P);\n  L[1] = Minv * (points[1] - P);\n  L[2] = Minv * (points[2] - P);\n  L[3] = Minv * (points[3] - P);\n  int n;\n  ClipQuadToHorizon(L, n);\n  if (n == 0) return vec3(0, 0, 0);\n  L[0] = normalize(L[0]);\n  L[1] = normalize(L[1]);\n  L[2] = normalize(L[2]);\n  L[3] = normalize(L[3]);\n  L[4] = normalize(L[4]);\n  float sum = 0.0;\n  sum += IntegrateEdge(L[0], L[1]);\n  sum += IntegrateEdge(L[1], L[2]);\n  sum += IntegrateEdge(L[2], L[3]);\n  if (n >= 4) sum += IntegrateEdge(L[3], L[4]);\n  if (n == 5) sum += IntegrateEdge(L[4], L[0]);\n  sum = max(0.0, sum);\n  vec3 Lo_i = vec3(sum, sum, sum);\n  return Lo_i;\n}\nlayout(set = 0, binding = 0) uniform CCGlobal {\n  highp   vec4 cc_time;\n  mediump vec4 cc_screenSize;\n  mediump vec4 cc_nativeSize;\n  mediump vec4 cc_probeInfo;\n  mediump vec4 cc_debug_view_mode;\n};\nlayout(set = 0, binding = 1) uniform CCCamera {\n  highp   mat4 cc_matView;\n  highp   mat4 cc_matViewInv;\n  highp   mat4 cc_matProj;\n  highp   mat4 cc_matProjInv;\n  highp   mat4 cc_matViewProj;\n  highp   mat4 cc_matViewProjInv;\n  highp   vec4 cc_cameraPos;\n  mediump vec4 cc_surfaceTransform;\n  mediump vec4 cc_screenScale;\n  mediump vec4 cc_exposure;\n  mediump vec4 cc_mainLitDir;\n  mediump vec4 cc_mainLitColor;\n  mediump vec4 cc_ambientSky;\n  mediump vec4 cc_ambientGround;\n  mediump vec4 cc_fogColor;\n  mediump vec4 cc_fogBase;\n  mediump vec4 cc_fogAdd;\n  mediump vec4 cc_nearFar;\n  mediump vec4 cc_viewPort;\n};\nvec3 LinearToSRGB(vec3 linear) {\n#ifdef CC_USE_SURFACE_SHADER\n  #if CC_USE_DEBUG_VIEW == CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC && CC_SURFACES_ENABLE_DEBUG_VIEW\n    if (!IS_DEBUG_VIEW_COMPOSITE_ENABLE_GAMMA_CORRECTION) {\n      return linear;\n    }\n  #endif\n#endif\n  return sqrt(linear);\n}\n#if CC_USE_LIGHT_PROBE\n  vec3 ACESToneMap (vec3 color) {\n    color = min(color, vec3(8.0));\n    const float A = 2.51;\n    const float B = 0.03;\n    const float C = 2.43;\n    const float D = 0.59;\n    const float E = 0.14;\n    return (color * (A * color + B)) / (color * (C * color + D) + E);\n  }\n  vec4 packRGBE (vec3 rgb) {\n    highp float maxComp = max(max(rgb.r, rgb.g), rgb.b);\n    highp float e = 128.0;\n    if (maxComp > 0.0001) {\n      e = log(maxComp) / log(1.1);\n      e = ceil(e);\n      e = clamp(e + 128.0, 0.0, 255.0);\n    }\n    highp float sc = 1.0 / pow(1.1, e - 128.0);\n    vec3 encode = clamp(rgb * sc, vec3(0.0), vec3(1.0)) * 255.0;\n    vec3 encode_rounded = floor(encode) + step(encode - floor(encode), vec3(0.5));\n    return vec4(encode_rounded, e) / 255.0;\n  }\n  vec4 CCFragOutput (vec4 color) {\n    #if CC_USE_RGBE_OUTPUT\n      color = packRGBE(color.rgb);\n    #elif !CC_USE_FLOAT_OUTPUT\n      #if CC_USE_HDR && CC_TONE_MAPPING_TYPE == HDR_TONE_MAPPING_ACES\n        color.rgb = ACESToneMap(color.rgb);\n      #endif\n      color.rgb = LinearToSRGB(color.rgb);\n    #endif\n    return color;\n  }\n#else\n  vec4 CCFragOutput (vec4 color) {\n    return color;\n  }\n#endif\nlayout(location = 0) in vec3 normal_w;\nlayout(location = 1) in vec3 pos_w;\nlayout(location = 2) in vec3 pos_l;\nlayout(location = 3) in vec3 right;\nlayout(location = 4) in vec3 up;\nlayout(location = 5) in vec3 forward;\nlayout(set = 1, binding = 0) uniform Constant {\n  vec4 mainColor;\n  vec4 cc_sh_linear_const_r;\n  vec4 cc_sh_linear_const_g;\n  vec4 cc_sh_linear_const_b;\n  vec4 cc_sh_quadratic_r;\n  vec4 cc_sh_quadratic_g;\n  vec4 cc_sh_quadratic_b;\n  vec4 cc_sh_quadratic_a;\n};\n#if CC_USE_LIGHT_PROBE\n  #if CC_USE_LIGHT_PROBE\n  vec3 SHEvaluate(vec3 normal)\n  {\n      vec3 result;\n  #if USE_INSTANCING\n      vec4 normal4 = vec4(normal, 1.0);\n      result.r = dot(v_sh_linear_const_r, normal4);\n      result.g = dot(v_sh_linear_const_g, normal4);\n      result.b = dot(v_sh_linear_const_b, normal4);\n  #else\n      vec4 normal4 = vec4(normal, 1.0);\n      result.r = dot(cc_sh_linear_const_r, normal4);\n      result.g = dot(cc_sh_linear_const_g, normal4);\n      result.b = dot(cc_sh_linear_const_b, normal4);\n      vec4 n14 = normal.xyzz * normal.yzzx;\n      float n5 = normal.x * normal.x - normal.y * normal.y;\n      result.r += dot(cc_sh_quadratic_r, n14);\n      result.g += dot(cc_sh_quadratic_g, n14);\n      result.b += dot(cc_sh_quadratic_b, n14);\n      result += (cc_sh_quadratic_a.rgb * n5);\n  #endif\n    #if CC_USE_HDR\n      result *= cc_exposure.w * cc_exposure.x;\n    #endif\n    return result;\n  }\n  #endif\n#endif\nvec4 gizmo_fs (float alpha) {\n  #if CC_USE_LIGHT_PROBE\n    vec3 N = normalize(normal_w) * (float(gl_FrontFacing) * 2.0 - 1.0);\n    vec3 diffuse = SHEvaluate(N);\n    #if USE_FORWARD_PIPELINE\n      return CCFragOutput(vec4(diffuse, mainColor.a * alpha));\n    #else\n      return vec4(diffuse, mainColor.a * alpha);\n    #endif\n  #else\n    vec3 N = normalize(normal_w) * (float(gl_FrontFacing) * 2.0 - 1.0);\n    vec3 V = normalize(cc_cameraPos.xyz - pos_w);\n    vec3 points [4];\n    points[0] = (forward * 3.0 + right + up) * 40.0;\n    points[1] = (forward * 3.0 - right + up) * 40.0;\n    points[2] = (forward * 3.0 - right - up) * 40.0;\n    points[3] = (forward * 3.0 + right - up) * 40.0;\n    vec3 diffuse = LinearToSRGB(mainColor.rgb * LTC_Evaluate(N, V, pos_l, mat3(1), points));\n    #if USE_FORWARD_PIPELINE\n      return CCFragOutput(vec4(diffuse, mainColor.a * alpha));\n    #else\n      return vec4(diffuse, mainColor.a * alpha);\n    #endif\n  #endif\n}\nvec4 back () {\n  return gizmo_fs(0.2);\n}\nlayout(location = 0) out vec4 cc_FragColor;\nvoid main() { cc_FragColor = back(); }"}, "glsl3": {"vert": "\nprecision mediump float;\nlayout(std140) uniform CCLocal {\n  highp mat4 cc_matWorld;\n  highp mat4 cc_matWorldIT;\n  highp vec4 cc_lightingMapUVParam;\n  highp vec4 cc_localShadowBias;\n  highp vec4 cc_reflectionProbeData1;\n  highp vec4 cc_reflectionProbeData2;\n  highp vec4 cc_reflectionProbeBlendData1;\n  highp vec4 cc_reflectionProbeBlendData2;\n};\nlayout(std140) uniform CCGlobal {\n  highp   vec4 cc_time;\n  mediump vec4 cc_screenSize;\n  mediump vec4 cc_nativeSize;\n  mediump vec4 cc_probeInfo;\n  mediump vec4 cc_debug_view_mode;\n};\nlayout(std140) uniform CCCamera {\n  highp   mat4 cc_matView;\n  highp   mat4 cc_matViewInv;\n  highp   mat4 cc_matProj;\n  highp   mat4 cc_matProjInv;\n  highp   mat4 cc_matViewProj;\n  highp   mat4 cc_matViewProjInv;\n  highp   vec4 cc_cameraPos;\n  mediump vec4 cc_surfaceTransform;\n  mediump vec4 cc_screenScale;\n  mediump vec4 cc_exposure;\n  mediump vec4 cc_mainLitDir;\n  mediump vec4 cc_mainLitColor;\n  mediump vec4 cc_ambientSky;\n  mediump vec4 cc_ambientGround;\n  mediump vec4 cc_fogColor;\n  mediump vec4 cc_fogBase;\n  mediump vec4 cc_fogAdd;\n  mediump vec4 cc_nearFar;\n  mediump vec4 cc_viewPort;\n};\nin vec3 a_position;\nin vec3 a_normal;\nout vec3 normal_w;\nout vec3 pos_w;\nout vec3 pos_l;\nout vec3 right;\nout vec3 up;\nout vec3 forward;\nvec4 vert () {\n  vec4 pos = vec4(a_position, 1);\n  vec4 normal = vec4(a_normal, 0);\n  pos_l = a_position;\n  pos_w = (cc_matWorld * pos).xyz;\n  normal_w = (cc_matWorldIT * normal).xyz;\n  right = vec3(cc_matView[0][0], cc_matView[1][0], cc_matView[2][0]);\n  up = vec3(cc_matView[0][1], cc_matView[1][1], cc_matView[2][1]);\n  forward = vec3(cc_matView[0][2], cc_matView[1][2], cc_matView[2][2]);\n  return cc_matProj * (cc_matView * cc_matWorld) * pos;\n}\nvoid main() { gl_Position = vert(); }", "frag": "\nprecision mediump float;\nmat3 transposeMat3 (mat3 v) {\n  mat3 tmp;\n  tmp[0] = vec3(v[0].x, v[1].x, v[2].x);\n  tmp[1] = vec3(v[0].y, v[1].y, v[2].y);\n  tmp[2] = vec3(v[0].z, v[1].z, v[2].z);\n  return tmp;\n}\nvoid ClipQuadToHorizon (inout vec3 L[5], out int n) {\n  int config = 0;\n  if (L[0].z > 0.0) config += 1;\n  if (L[1].z > 0.0) config += 2;\n  if (L[2].z > 0.0) config += 4;\n  if (L[3].z > 0.0) config += 8;\n  config = 15;\n  n = 0;\n  if (config == 0)\n  {\n  }\n  else if (config == 1) {\n    n = 3;\n    L[1] = -L[1].z * L[0] + L[0].z * L[1];\n    L[2] = -L[3].z * L[0] + L[0].z * L[3];\n  }\n  else if (config == 2) {\n    n = 3;\n    L[0] = -L[0].z * L[1] + L[1].z * L[0];\n    L[2] = -L[2].z * L[1] + L[1].z * L[2];\n  }\n  else if (config == 3) {\n    n = 4;\n    L[2] = -L[2].z * L[1] + L[1].z * L[2];\n    L[3] = -L[3].z * L[0] + L[0].z * L[3];\n  }\n  else if (config == 4) {\n    n = 3;\n    L[0] = -L[3].z * L[2] + L[2].z * L[3];\n    L[1] = -L[1].z * L[2] + L[2].z * L[1];\n  }\n  else if (config == 5) {\n    n = 0;\n  }\n  else if (config == 6) {\n    n = 4;\n    L[0] = -L[0].z * L[1] + L[1].z * L[0];\n    L[3] = -L[3].z * L[2] + L[2].z * L[3];\n  }\n  else if (config == 7) {\n    n = 5;\n    L[4] = -L[3].z * L[0] + L[0].z * L[3];\n    L[3] = -L[3].z * L[2] + L[2].z * L[3];\n  }\n  else if (config == 8) {\n    n = 3;\n    L[0] = -L[0].z * L[3] + L[3].z * L[0];\n    L[1] = -L[2].z * L[3] + L[3].z * L[2];\n    L[2] =  L[3];\n  }\n  else if (config == 9) {\n    n = 4;\n    L[1] = -L[1].z * L[0] + L[0].z * L[1];\n    L[2] = -L[2].z * L[3] + L[3].z * L[2];\n  }\n  else if (config == 10) {\n    n = 0;\n  }\n  else if (config == 11) {\n    n = 5;\n    L[4] = L[3];\n    L[3] = -L[2].z * L[3] + L[3].z * L[2];\n    L[2] = -L[2].z * L[1] + L[1].z * L[2];\n  }\n  else if (config == 12) {\n    n = 4;\n    L[1] = -L[1].z * L[2] + L[2].z * L[1];\n    L[0] = -L[0].z * L[3] + L[3].z * L[0];\n  }\n  else if (config == 13) {\n    n = 5;\n    L[4] = L[3];\n    L[3] = L[2];\n    L[2] = -L[1].z * L[2] + L[2].z * L[1];\n    L[1] = -L[1].z * L[0] + L[0].z * L[1];\n  }\n  else if (config == 14) {\n    n = 5;\n    L[4] = -L[0].z * L[3] + L[3].z * L[0];\n    L[0] = -L[0].z * L[1] + L[1].z * L[0];\n  }\n  else if (config == 15) {\n    n = 4;\n  }\n  if (n == 3) L[3] = L[0];\n  if (n == 4) L[4] = L[0];\n}\nfloat IntegrateEdge (vec3 v1, vec3 v2) {\n  float cosTheta = dot(v1, v2);\n  float theta = acos(cosTheta);\n  return cross(v1, v2).z * ((theta > 0.001) ? theta/sin(theta) : 4.0);\n}\nvec3 LTC_Evaluate (vec3 N, vec3 V, vec3 P, mat3 Minv, vec3 points[4]) {\n  vec3 T1, T2;\n  T1 = normalize(V - N*dot(V, N));\n  T2 = cross(N, T1);\n  Minv = Minv * transposeMat3(mat3(T1, T2, N));\n  vec3 L[5];\n  L[0] = Minv * (points[0] - P);\n  L[1] = Minv * (points[1] - P);\n  L[2] = Minv * (points[2] - P);\n  L[3] = Minv * (points[3] - P);\n  int n;\n  ClipQuadToHorizon(L, n);\n  if (n == 0) return vec3(0, 0, 0);\n  L[0] = normalize(L[0]);\n  L[1] = normalize(L[1]);\n  L[2] = normalize(L[2]);\n  L[3] = normalize(L[3]);\n  L[4] = normalize(L[4]);\n  float sum = 0.0;\n  sum += IntegrateEdge(L[0], L[1]);\n  sum += IntegrateEdge(L[1], L[2]);\n  sum += IntegrateEdge(L[2], L[3]);\n  if (n >= 4) sum += IntegrateEdge(L[3], L[4]);\n  if (n == 5) sum += IntegrateEdge(L[4], L[0]);\n  sum = max(0.0, sum);\n  vec3 Lo_i = vec3(sum, sum, sum);\n  return Lo_i;\n}\nlayout(std140) uniform CCGlobal {\n  highp   vec4 cc_time;\n  mediump vec4 cc_screenSize;\n  mediump vec4 cc_nativeSize;\n  mediump vec4 cc_probeInfo;\n  mediump vec4 cc_debug_view_mode;\n};\nlayout(std140) uniform CCCamera {\n  highp   mat4 cc_matView;\n  highp   mat4 cc_matViewInv;\n  highp   mat4 cc_matProj;\n  highp   mat4 cc_matProjInv;\n  highp   mat4 cc_matViewProj;\n  highp   mat4 cc_matViewProjInv;\n  highp   vec4 cc_cameraPos;\n  mediump vec4 cc_surfaceTransform;\n  mediump vec4 cc_screenScale;\n  mediump vec4 cc_exposure;\n  mediump vec4 cc_mainLitDir;\n  mediump vec4 cc_mainLitColor;\n  mediump vec4 cc_ambientSky;\n  mediump vec4 cc_ambientGround;\n  mediump vec4 cc_fogColor;\n  mediump vec4 cc_fogBase;\n  mediump vec4 cc_fogAdd;\n  mediump vec4 cc_nearFar;\n  mediump vec4 cc_viewPort;\n};\nvec3 LinearToSRGB(vec3 linear) {\n#ifdef CC_USE_SURFACE_SHADER\n  #if CC_USE_DEBUG_VIEW == CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC && CC_SURFACES_ENABLE_DEBUG_VIEW\n    if (!IS_DEBUG_VIEW_COMPOSITE_ENABLE_GAMMA_CORRECTION) {\n      return linear;\n    }\n  #endif\n#endif\n  return sqrt(linear);\n}\n#if CC_USE_LIGHT_PROBE\n  vec3 ACESToneMap (vec3 color) {\n    color = min(color, vec3(8.0));\n    const float A = 2.51;\n    const float B = 0.03;\n    const float C = 2.43;\n    const float D = 0.59;\n    const float E = 0.14;\n    return (color * (A * color + B)) / (color * (C * color + D) + E);\n  }\n  vec4 packRGBE (vec3 rgb) {\n    highp float maxComp = max(max(rgb.r, rgb.g), rgb.b);\n    highp float e = 128.0;\n    if (maxComp > 0.0001) {\n      e = log(maxComp) / log(1.1);\n      e = ceil(e);\n      e = clamp(e + 128.0, 0.0, 255.0);\n    }\n    highp float sc = 1.0 / pow(1.1, e - 128.0);\n    vec3 encode = clamp(rgb * sc, vec3(0.0), vec3(1.0)) * 255.0;\n    vec3 encode_rounded = floor(encode) + step(encode - floor(encode), vec3(0.5));\n    return vec4(encode_rounded, e) / 255.0;\n  }\n  vec4 CCFragOutput (vec4 color) {\n    #if CC_USE_RGBE_OUTPUT\n      color = packRGBE(color.rgb);\n    #elif !CC_USE_FLOAT_OUTPUT\n      #if CC_USE_HDR && CC_TONE_MAPPING_TYPE == HDR_TONE_MAPPING_ACES\n        color.rgb = ACESToneMap(color.rgb);\n      #endif\n      color.rgb = LinearToSRGB(color.rgb);\n    #endif\n    return color;\n  }\n#else\n  vec4 CCFragOutput (vec4 color) {\n    return color;\n  }\n#endif\nin vec3 normal_w;\nin vec3 pos_w;\nin vec3 pos_l;\nin vec3 right;\nin vec3 up;\nin vec3 forward;\nlayout(std140) uniform Constant {\n  vec4 mainColor;\n  vec4 cc_sh_linear_const_r;\n  vec4 cc_sh_linear_const_g;\n  vec4 cc_sh_linear_const_b;\n  vec4 cc_sh_quadratic_r;\n  vec4 cc_sh_quadratic_g;\n  vec4 cc_sh_quadratic_b;\n  vec4 cc_sh_quadratic_a;\n};\n#if CC_USE_LIGHT_PROBE\n  #if CC_USE_LIGHT_PROBE\n  vec3 SHEvaluate(vec3 normal)\n  {\n      vec3 result;\n  #if USE_INSTANCING\n      vec4 normal4 = vec4(normal, 1.0);\n      result.r = dot(v_sh_linear_const_r, normal4);\n      result.g = dot(v_sh_linear_const_g, normal4);\n      result.b = dot(v_sh_linear_const_b, normal4);\n  #else\n      vec4 normal4 = vec4(normal, 1.0);\n      result.r = dot(cc_sh_linear_const_r, normal4);\n      result.g = dot(cc_sh_linear_const_g, normal4);\n      result.b = dot(cc_sh_linear_const_b, normal4);\n      vec4 n14 = normal.xyzz * normal.yzzx;\n      float n5 = normal.x * normal.x - normal.y * normal.y;\n      result.r += dot(cc_sh_quadratic_r, n14);\n      result.g += dot(cc_sh_quadratic_g, n14);\n      result.b += dot(cc_sh_quadratic_b, n14);\n      result += (cc_sh_quadratic_a.rgb * n5);\n  #endif\n    #if CC_USE_HDR\n      result *= cc_exposure.w * cc_exposure.x;\n    #endif\n    return result;\n  }\n  #endif\n#endif\nvec4 gizmo_fs (float alpha) {\n  #if CC_USE_LIGHT_PROBE\n    vec3 N = normalize(normal_w) * (float(gl_FrontFacing) * 2.0 - 1.0);\n    vec3 diffuse = SHEvaluate(N);\n    #if USE_FORWARD_PIPELINE\n      return CCFragOutput(vec4(diffuse, mainColor.a * alpha));\n    #else\n      return vec4(diffuse, mainColor.a * alpha);\n    #endif\n  #else\n    vec3 N = normalize(normal_w) * (float(gl_FrontFacing) * 2.0 - 1.0);\n    vec3 V = normalize(cc_cameraPos.xyz - pos_w);\n    vec3 points [4];\n    points[0] = (forward * 3.0 + right + up) * 40.0;\n    points[1] = (forward * 3.0 - right + up) * 40.0;\n    points[2] = (forward * 3.0 - right - up) * 40.0;\n    points[3] = (forward * 3.0 + right - up) * 40.0;\n    vec3 diffuse = LinearToSRGB(mainColor.rgb * LTC_Evaluate(N, V, pos_l, mat3(1), points));\n    #if USE_FORWARD_PIPELINE\n      return CCFragOutput(vec4(diffuse, mainColor.a * alpha));\n    #else\n      return vec4(diffuse, mainColor.a * alpha);\n    #endif\n  #endif\n}\nvec4 back () {\n  return gizmo_fs(0.2);\n}\nlayout(location = 0) out vec4 cc_FragColor;\nvoid main() { cc_FragColor = back(); }"}, "glsl1": {"vert": "\nprecision mediump float;\nuniform highp mat4 cc_matWorld;\n  uniform highp mat4 cc_matWorldIT;\nuniform highp mat4 cc_matView;\n  uniform highp mat4 cc_matProj;\nattribute vec3 a_position;\nattribute vec3 a_normal;\nvarying vec3 normal_w;\nvarying vec3 pos_w;\nvarying vec3 pos_l;\nvarying vec3 right;\nvarying vec3 up;\nvarying vec3 forward;\nvec4 vert () {\n  vec4 pos = vec4(a_position, 1);\n  vec4 normal = vec4(a_normal, 0);\n  pos_l = a_position;\n  pos_w = (cc_matWorld * pos).xyz;\n  normal_w = (cc_matWorldIT * normal).xyz;\n  right = vec3(cc_matView[0][0], cc_matView[1][0], cc_matView[2][0]);\n  up = vec3(cc_matView[0][1], cc_matView[1][1], cc_matView[2][1]);\n  forward = vec3(cc_matView[0][2], cc_matView[1][2], cc_matView[2][2]);\n  return cc_matProj * (cc_matView * cc_matWorld) * pos;\n}\nvoid main() { gl_Position = vert(); }", "frag": "\nprecision mediump float;\nmat3 transposeMat3 (mat3 v) {\n  mat3 tmp;\n  tmp[0] = vec3(v[0].x, v[1].x, v[2].x);\n  tmp[1] = vec3(v[0].y, v[1].y, v[2].y);\n  tmp[2] = vec3(v[0].z, v[1].z, v[2].z);\n  return tmp;\n}\nvoid ClipQuadToHorizon (inout vec3 L[5], out int n) {\n  int config = 0;\n  if (L[0].z > 0.0) config += 1;\n  if (L[1].z > 0.0) config += 2;\n  if (L[2].z > 0.0) config += 4;\n  if (L[3].z > 0.0) config += 8;\n  config = 15;\n  n = 0;\n  if (config == 0)\n  {\n  }\n  else if (config == 1) {\n    n = 3;\n    L[1] = -L[1].z * L[0] + L[0].z * L[1];\n    L[2] = -L[3].z * L[0] + L[0].z * L[3];\n  }\n  else if (config == 2) {\n    n = 3;\n    L[0] = -L[0].z * L[1] + L[1].z * L[0];\n    L[2] = -L[2].z * L[1] + L[1].z * L[2];\n  }\n  else if (config == 3) {\n    n = 4;\n    L[2] = -L[2].z * L[1] + L[1].z * L[2];\n    L[3] = -L[3].z * L[0] + L[0].z * L[3];\n  }\n  else if (config == 4) {\n    n = 3;\n    L[0] = -L[3].z * L[2] + L[2].z * L[3];\n    L[1] = -L[1].z * L[2] + L[2].z * L[1];\n  }\n  else if (config == 5) {\n    n = 0;\n  }\n  else if (config == 6) {\n    n = 4;\n    L[0] = -L[0].z * L[1] + L[1].z * L[0];\n    L[3] = -L[3].z * L[2] + L[2].z * L[3];\n  }\n  else if (config == 7) {\n    n = 5;\n    L[4] = -L[3].z * L[0] + L[0].z * L[3];\n    L[3] = -L[3].z * L[2] + L[2].z * L[3];\n  }\n  else if (config == 8) {\n    n = 3;\n    L[0] = -L[0].z * L[3] + L[3].z * L[0];\n    L[1] = -L[2].z * L[3] + L[3].z * L[2];\n    L[2] =  L[3];\n  }\n  else if (config == 9) {\n    n = 4;\n    L[1] = -L[1].z * L[0] + L[0].z * L[1];\n    L[2] = -L[2].z * L[3] + L[3].z * L[2];\n  }\n  else if (config == 10) {\n    n = 0;\n  }\n  else if (config == 11) {\n    n = 5;\n    L[4] = L[3];\n    L[3] = -L[2].z * L[3] + L[3].z * L[2];\n    L[2] = -L[2].z * L[1] + L[1].z * L[2];\n  }\n  else if (config == 12) {\n    n = 4;\n    L[1] = -L[1].z * L[2] + L[2].z * L[1];\n    L[0] = -L[0].z * L[3] + L[3].z * L[0];\n  }\n  else if (config == 13) {\n    n = 5;\n    L[4] = L[3];\n    L[3] = L[2];\n    L[2] = -L[1].z * L[2] + L[2].z * L[1];\n    L[1] = -L[1].z * L[0] + L[0].z * L[1];\n  }\n  else if (config == 14) {\n    n = 5;\n    L[4] = -L[0].z * L[3] + L[3].z * L[0];\n    L[0] = -L[0].z * L[1] + L[1].z * L[0];\n  }\n  else if (config == 15) {\n    n = 4;\n  }\n  if (n == 3) L[3] = L[0];\n  if (n == 4) L[4] = L[0];\n}\nfloat IntegrateEdge (vec3 v1, vec3 v2) {\n  float cosTheta = dot(v1, v2);\n  float theta = acos(cosTheta);\n  return cross(v1, v2).z * ((theta > 0.001) ? theta/sin(theta) : 4.0);\n}\nvec3 LTC_Evaluate (vec3 N, vec3 V, vec3 P, mat3 Minv, vec3 points[4]) {\n  vec3 T1, T2;\n  T1 = normalize(V - N*dot(V, N));\n  T2 = cross(N, T1);\n  Minv = Minv * transposeMat3(mat3(T1, T2, N));\n  vec3 L[5];\n  L[0] = Minv * (points[0] - P);\n  L[1] = Minv * (points[1] - P);\n  L[2] = Minv * (points[2] - P);\n  L[3] = Minv * (points[3] - P);\n  int n;\n  ClipQuadToHorizon(L, n);\n  if (n == 0) return vec3(0, 0, 0);\n  L[0] = normalize(L[0]);\n  L[1] = normalize(L[1]);\n  L[2] = normalize(L[2]);\n  L[3] = normalize(L[3]);\n  L[4] = normalize(L[4]);\n  float sum = 0.0;\n  sum += IntegrateEdge(L[0], L[1]);\n  sum += IntegrateEdge(L[1], L[2]);\n  sum += IntegrateEdge(L[2], L[3]);\n  if (n >= 4) sum += IntegrateEdge(L[3], L[4]);\n  if (n == 5) sum += IntegrateEdge(L[4], L[0]);\n  sum = max(0.0, sum);\n  vec3 Lo_i = vec3(sum, sum, sum);\n  return Lo_i;\n}\nuniform highp vec4 cc_cameraPos;\n  uniform mediump vec4 cc_exposure;\nvec3 LinearToSRGB(vec3 linear) {\n#ifdef CC_USE_SURFACE_SHADER\n  #if CC_USE_DEBUG_VIEW == CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC && CC_SURFACES_ENABLE_DEBUG_VIEW\n    if (!IS_DEBUG_VIEW_COMPOSITE_ENABLE_GAMMA_CORRECTION) {\n      return linear;\n    }\n  #endif\n#endif\n  return sqrt(linear);\n}\n#if CC_USE_LIGHT_PROBE\n  vec3 ACESToneMap (vec3 color) {\n    color = min(color, vec3(8.0));\n    const float A = 2.51;\n    const float B = 0.03;\n    const float C = 2.43;\n    const float D = 0.59;\n    const float E = 0.14;\n    return (color * (A * color + B)) / (color * (C * color + D) + E);\n  }\n  vec4 packRGBE (vec3 rgb) {\n    highp float maxComp = max(max(rgb.r, rgb.g), rgb.b);\n    highp float e = 128.0;\n    if (maxComp > 0.0001) {\n      e = log(maxComp) / log(1.1);\n      e = ceil(e);\n      e = clamp(e + 128.0, 0.0, 255.0);\n    }\n    highp float sc = 1.0 / pow(1.1, e - 128.0);\n    vec3 encode = clamp(rgb * sc, vec3(0.0), vec3(1.0)) * 255.0;\n    vec3 encode_rounded = floor(encode) + step(encode - floor(encode), vec3(0.5));\n    return vec4(encode_rounded, e) / 255.0;\n  }\n  vec4 CCFragOutput (vec4 color) {\n    #if CC_USE_RGBE_OUTPUT\n      color = packRGBE(color.rgb);\n    #elif !CC_USE_FLOAT_OUTPUT\n      #if CC_USE_HDR && CC_TONE_MAPPING_TYPE == HDR_TONE_MAPPING_ACES\n        color.rgb = ACESToneMap(color.rgb);\n      #endif\n      color.rgb = LinearToSRGB(color.rgb);\n    #endif\n    return color;\n  }\n#else\n  vec4 CCFragOutput (vec4 color) {\n    return color;\n  }\n#endif\nvarying vec3 normal_w;\nvarying vec3 pos_w;\nvarying vec3 pos_l;\nvarying vec3 right;\nvarying vec3 up;\nvarying vec3 forward;\n   uniform vec4 mainColor;\n   uniform vec4 cc_sh_linear_const_r;\n   uniform vec4 cc_sh_linear_const_g;\n   uniform vec4 cc_sh_linear_const_b;\n   uniform vec4 cc_sh_quadratic_r;\n   uniform vec4 cc_sh_quadratic_g;\n   uniform vec4 cc_sh_quadratic_b;\n   uniform vec4 cc_sh_quadratic_a;\n#if CC_USE_LIGHT_PROBE\n  #if CC_USE_LIGHT_PROBE\n  vec3 SHEvaluate(vec3 normal)\n  {\n      vec3 result;\n  #if USE_INSTANCING\n      vec4 normal4 = vec4(normal, 1.0);\n      result.r = dot(v_sh_linear_const_r, normal4);\n      result.g = dot(v_sh_linear_const_g, normal4);\n      result.b = dot(v_sh_linear_const_b, normal4);\n  #else\n      vec4 normal4 = vec4(normal, 1.0);\n      result.r = dot(cc_sh_linear_const_r, normal4);\n      result.g = dot(cc_sh_linear_const_g, normal4);\n      result.b = dot(cc_sh_linear_const_b, normal4);\n      vec4 n14 = normal.xyzz * normal.yzzx;\n      float n5 = normal.x * normal.x - normal.y * normal.y;\n      result.r += dot(cc_sh_quadratic_r, n14);\n      result.g += dot(cc_sh_quadratic_g, n14);\n      result.b += dot(cc_sh_quadratic_b, n14);\n      result += (cc_sh_quadratic_a.rgb * n5);\n  #endif\n    #if CC_USE_HDR\n      result *= cc_exposure.w * cc_exposure.x;\n    #endif\n    return result;\n  }\n  #endif\n#endif\nvec4 gizmo_fs (float alpha) {\n  #if CC_USE_LIGHT_PROBE\n    vec3 N = normalize(normal_w) * (float(gl_FrontFacing) * 2.0 - 1.0);\n    vec3 diffuse = SHEvaluate(N);\n    #if USE_FORWARD_PIPELINE\n      return CCFragOutput(vec4(diffuse, mainColor.a * alpha));\n    #else\n      return vec4(diffuse, mainColor.a * alpha);\n    #endif\n  #else\n    vec3 N = normalize(normal_w) * (float(gl_FrontFacing) * 2.0 - 1.0);\n    vec3 V = normalize(cc_cameraPos.xyz - pos_w);\n    vec3 points [4];\n    points[0] = (forward * 3.0 + right + up) * 40.0;\n    points[1] = (forward * 3.0 - right + up) * 40.0;\n    points[2] = (forward * 3.0 - right - up) * 40.0;\n    points[3] = (forward * 3.0 + right - up) * 40.0;\n    vec3 diffuse = LinearToSRGB(mainColor.rgb * LTC_Evaluate(N, V, pos_l, mat3(1), points));\n    #if USE_FORWARD_PIPELINE\n      return CCFragOutput(vec4(diffuse, mainColor.a * alpha));\n    #else\n      return vec4(diffuse, mainColor.a * alpha);\n    #endif\n  #endif\n}\nvec4 back () {\n  return gizmo_fs(0.2);\n}\nvoid main() { gl_FragColor = back(); }"}, "builtins": {"globals": {"blocks": [{"name": "CCGlobal", "defines": []}, {"name": "CCCamera", "defines": []}], "samplerTextures": [], "buffers": [], "images": []}, "locals": {"blocks": [{"name": "CCLocal", "defines": []}], "samplerTextures": [], "buffers": [], "images": []}, "statistics": {"CC_EFFECT_USED_VERTEX_UNIFORM_VECTORS": 56, "CC_EFFECT_USED_FRAGMENT_UNIFORM_VECTORS": 50}}, "defines": [{"name": "CC_USE_DEBUG_VIEW", "type": "number", "defines": [], "range": [0, 3]}, {"name": "CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC", "type": "boolean", "defines": ["CC_USE_DEBUG_VIEW"]}, {"name": "CC_SURFACES_ENABLE_DEBUG_VIEW", "type": "boolean", "defines": ["CC_USE_DEBUG_VIEW", "CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC"]}, {"name": "CC_USE_LIGHT_PROBE", "type": "boolean", "defines": [], "default": 0}, {"name": "CC_USE_RGBE_OUTPUT", "type": "boolean", "defines": ["CC_USE_LIGHT_PROBE"]}, {"name": "CC_USE_FLOAT_OUTPUT", "type": "boolean", "defines": ["CC_USE_LIGHT_PROBE", "!CC_USE_RGBE_OUTPUT"]}, {"name": "CC_USE_HDR", "type": "boolean", "defines": ["CC_USE_LIGHT_PROBE", "CC_USE_LIGHT_PROBE"]}, {"name": "CC_TONE_MAPPING_TYPE", "type": "number", "defines": ["CC_USE_HDR", "CC_USE_LIGHT_PROBE", "!CC_USE_RGBE_OUTPUT", "!CC_USE_FLOAT_OUTPUT"], "range": [0, 3]}, {"name": "HDR_TONE_MAPPING_ACES", "type": "boolean", "defines": ["CC_USE_HDR", "CC_TONE_MAPPING_TYPE", "CC_USE_LIGHT_PROBE", "!CC_USE_RGBE_OUTPUT", "!CC_USE_FLOAT_OUTPUT"]}, {"name": "USE_INSTANCING", "type": "boolean", "defines": ["CC_USE_LIGHT_PROBE", "CC_USE_LIGHT_PROBE"]}, {"name": "USE_FORWARD_PIPELINE", "type": "boolean", "defines": ["CC_USE_LIGHT_PROBE"]}], "name": "internal/editor/light-probe-visualization|gizmo-vs:vert|gizmo-fs:back"}], "combinations": [], "hideInEditor": true}