[{"__type__": "cc.Prefab", "_name": "Particle System", "_objFlags": 0, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "asyncLoadAssets": false, "persistent": false}, {"__type__": "cc.Node", "_name": "Particle System", "_objFlags": 0, "_parent": null, "_children": [], "_active": true, "_components": [{"__id__": 2}], "_prefab": {"__id__": 53}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0.7071067811865475, "y": 0, "z": 0, "w": 0.7071067811865476}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 90, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.ParticleSystem", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "_materials": [{"__uuid__": "ea7478b0-408d-4052-b703-f0d2355e095f"}], "_visFlags": 0, "startColor": {"__id__": 3}, "scaleSpace": 1, "startSize3D": false, "startSizeX": {"__id__": 4}, "startSize": {"__id__": 4}, "startSizeY": {"__id__": 5}, "startSizeZ": {"__id__": 6}, "startSpeed": {"__id__": 7}, "startRotation3D": false, "startRotationX": {"__id__": 8}, "startRotationY": {"__id__": 9}, "startRotationZ": {"__id__": 10}, "startRotation": {"__id__": 10}, "startDelay": {"__id__": 11}, "startLifetime": {"__id__": 12}, "duration": 5, "loop": true, "simulationSpeed": 1, "playOnAwake": true, "gravityModifier": {"__id__": 13}, "rateOverTime": {"__id__": 14}, "rateOverDistance": {"__id__": 15}, "bursts": [], "_colorOverLifetimeModule": {"__id__": 16}, "_shapeModule": {"__id__": 18}, "_sizeOvertimeModule": {"__id__": 20}, "_velocityOvertimeModule": {"__id__": 25}, "_forceOvertimeModule": {"__id__": 30}, "_limitVelocityOvertimeModule": {"__id__": 34}, "_rotationOvertimeModule": {"__id__": 39}, "_textureAnimationModule": {"__id__": 43}, "_trailModule": {"__id__": 46}, "renderer": {"__id__": 51}, "enableCulling": false, "_prewarm": false, "_capacity": 100, "_simulationSpace": 1, "_id": "", "__prefab": {"__id__": 52}}, {"__type__": "cc.GradientRange", "_mode": 0, "color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}}, {"__type__": "cc.CurveRange", "mode": 0, "constant": 1, "multiplier": 1}, {"__type__": "cc.CurveRange", "mode": 0, "constant": 0, "multiplier": 1}, {"__type__": "cc.CurveRange", "mode": 0, "constant": 0, "multiplier": 1}, {"__type__": "cc.CurveRange", "mode": 0, "constant": 5, "multiplier": 1}, {"__type__": "cc.CurveRange", "mode": 0, "constant": 0, "multiplier": 1}, {"__type__": "cc.CurveRange", "mode": 0, "constant": 0, "multiplier": 1}, {"__type__": "cc.CurveRange", "mode": 0, "constant": 0, "multiplier": 1}, {"__type__": "cc.CurveRange", "mode": 0, "constant": 0, "multiplier": 1}, {"__type__": "cc.CurveRange", "mode": 0, "constant": 5, "multiplier": 1}, {"__type__": "cc.CurveRange", "mode": 0, "constant": 0, "multiplier": 1}, {"__type__": "cc.CurveRange", "mode": 0, "constant": 10, "multiplier": 1}, {"__type__": "cc.CurveRange", "mode": 0, "constant": 0, "multiplier": 1}, {"__type__": "cc.ColorOvertimeModule", "_enable": false, "color": {"__id__": 17}}, {"__type__": "cc.GradientRange", "_mode": 0, "color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}}, {"__type__": "cc.ShapeModule", "_enable": true, "_shapeType": 2, "shapeType": 2, "emitFrom": 0, "alignToDirection": false, "randomDirectionAmount": 0, "sphericalDirectionAmount": 0, "randomPositionAmount": 0, "radius": 1, "radiusThickness": 1, "arcMode": 0, "arcSpread": 0, "arcSpeed": {"__id__": 19}, "length": 5, "boxThickness": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_position": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_rotation": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_scale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_arc": 6.283185307179586, "_angle": 0.4363323129985824}, {"__type__": "cc.CurveRange", "mode": 0, "constant": 1, "multiplier": 1}, {"__type__": "cc.SizeOvertimeModule", "_enable": false, "separateAxes": false, "size": {"__id__": 21}, "x": {"__id__": 22}, "y": {"__id__": 23}, "z": {"__id__": 24}}, {"__type__": "cc.CurveRange", "mode": 0, "constant": 0, "multiplier": 1}, {"__type__": "cc.CurveRange", "mode": 0, "constant": 0, "multiplier": 1}, {"__type__": "cc.CurveRange", "mode": 0, "constant": 0, "multiplier": 1}, {"__type__": "cc.CurveRange", "mode": 0, "constant": 0, "multiplier": 1}, {"__type__": "cc.VelocityOvertimeModule", "_enable": false, "x": {"__id__": 26}, "y": {"__id__": 27}, "z": {"__id__": 28}, "speedModifier": {"__id__": 29}, "space": 1}, {"__type__": "cc.CurveRange", "mode": 0, "constant": 0, "multiplier": 1}, {"__type__": "cc.CurveRange", "mode": 0, "constant": 0, "multiplier": 1}, {"__type__": "cc.CurveRange", "mode": 0, "constant": 0, "multiplier": 1}, {"__type__": "cc.CurveRange", "mode": 0, "constant": 1, "multiplier": 1}, {"__type__": "cc.ForceOvertimeModule", "_enable": false, "x": {"__id__": 31}, "y": {"__id__": 32}, "z": {"__id__": 33}, "space": 1}, {"__type__": "cc.CurveRange", "mode": 0, "constant": 0, "multiplier": 1}, {"__type__": "cc.CurveRange", "mode": 0, "constant": 0, "multiplier": 1}, {"__type__": "cc.CurveRange", "mode": 0, "constant": 0, "multiplier": 1}, {"__type__": "cc.LimitVelocityOvertimeModule", "_enable": false, "limitX": {"__id__": 35}, "limitY": {"__id__": 36}, "limitZ": {"__id__": 37}, "limit": {"__id__": 38}, "dampen": 3, "separateAxes": false, "space": 1}, {"__type__": "cc.CurveRange", "mode": 0, "constant": 0, "multiplier": 1}, {"__type__": "cc.CurveRange", "mode": 0, "constant": 0, "multiplier": 1}, {"__type__": "cc.CurveRange", "mode": 0, "constant": 0, "multiplier": 1}, {"__type__": "cc.CurveRange", "mode": 0, "constant": 0, "multiplier": 1}, {"__type__": "cc.RotationOvertimeModule", "_enable": false, "_separateAxes": false, "x": {"__id__": 40}, "y": {"__id__": 41}, "z": {"__id__": 42}}, {"__type__": "cc.CurveRange", "mode": 0, "constant": 0, "multiplier": 1}, {"__type__": "cc.CurveRange", "mode": 0, "constant": 0, "multiplier": 1}, {"__type__": "cc.CurveRange", "mode": 0, "constant": 0, "multiplier": 1}, {"__type__": "cc.TextureAnimationModule", "_enable": false, "_numTilesX": 0, "numTilesX": 0, "_numTilesY": 0, "numTilesY": 0, "_mode": 0, "animation": 0, "frameOverTime": {"__id__": 44}, "startFrame": {"__id__": 45}, "cycleCount": 0, "_flipU": 0, "_flipV": 0, "_uvChannelMask": -1, "randomRow": false, "rowIndex": 0}, {"__type__": "cc.CurveRange", "mode": 1, "constant": 0, "multiplier": 1}, {"__type__": "cc.CurveRange", "mode": 0, "constant": 0, "multiplier": 1}, {"__type__": "cc.TrailModule", "_enable": false, "mode": 0, "lifeTime": {"__id__": 47}, "_minParticleDistance": 0.1, "existWithParticles": true, "textureMode": 0, "widthFromParticle": true, "widthRatio": {"__id__": 48}, "colorFromParticle": false, "colorOverTrail": {"__id__": 49}, "colorOvertime": {"__id__": 50}, "_space": 0, "_particleSystem": {"__id__": 2}}, {"__type__": "cc.CurveRange", "mode": 0, "constant": 1, "multiplier": 1}, {"__type__": "cc.CurveRange", "mode": 0, "constant": 0, "multiplier": 1}, {"__type__": "cc.GradientRange", "_mode": 0, "color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}}, {"__type__": "cc.GradientRange", "_mode": 0, "color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}}, {"__type__": "cc.ParticleSystemRenderer", "_renderMode": 0, "_velocityScale": 1, "_lengthScale": 1, "_mesh": null, "_mainTexture": {"__uuid__": "b5b27ab1-e740-4398-b407-848fc2b2c897@6c48a"}, "_useGPU": false}, {"__type__": "cc.CompPrefabInfo", "fileId": "0b7a7v3FVOCpzmec7amMlB"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "d8TRONhORDRpCXbQBSi+LM"}]