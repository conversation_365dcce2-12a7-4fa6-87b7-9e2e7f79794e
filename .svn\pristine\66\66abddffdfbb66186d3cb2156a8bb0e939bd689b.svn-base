import { _decorator, Component, Node, instantiate } from 'cc';
import { EnemyBullet } from '../EnemyBullet';
import { Global } from '../Global';
import { PersistNode } from '../PersistNode';
import { GameFactory } from './GameFactory';
const { ccclass, property } = _decorator;


export class EnemyBulletFactory extends GameFactory {
    
    public createProduct(productType: string): Node {
        let enemyBulletTemp: Node = null;

        if (this.productPool.size() > 0) {
            enemyBulletTemp = this.productPool.get();  //如果池里有敌机子弹，就直接拿来用
        } else {
            enemyBulletTemp = instantiate(this.persistNode.getComponent(PersistNode).enemyBulletPreb);  //从常驻节点拿到预制体原料
        }
        
        switch(productType) {
            case Global.ENEMY_BULLET_1:
                enemyBulletTemp.getComponent(EnemyBullet).init(productType, this.persistNode.getComponent(PersistNode).enemybullet1);  //通过调用EnemyBullet的init方法，来创建子弹
                break;
            case Global.ENEMY_BULLET_2:
                enemyBulletTemp.getComponent(EnemyBullet).init(productType, this.persistNode.getComponent(PersistNode).enemybullet2);
                break;
        }

        return enemyBulletTemp;
    }

}

