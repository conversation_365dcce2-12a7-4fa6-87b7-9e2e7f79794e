/**
 * CrazyStorm 2.0 Integration for Cocos Creator
 * Performance Profiler
 * 
 * This system provides detailed performance profiling for debugging
 * and optimization of CrazyStorm systems.
 */

import { log, warn } from 'cc';

/**
 * Profile entry for timing measurements
 */
interface ProfileEntry {
    name: string;
    startTime: number;
    endTime: number;
    duration: number;
    callCount: number;
    totalTime: number;
    averageTime: number;
    minTime: number;
    maxTime: number;
}

/**
 * Profile session data
 */
interface ProfileSession {
    name: string;
    startTime: number;
    endTime: number;
    entries: Map<string, ProfileEntry>;
    frameCount: number;
}

/**
 * Performance profiler class
 */
export class Profiler {
    private static instance: Profiler;
    
    private isEnabled: boolean = false;
    private currentSession: ProfileSession | null = null;
    private sessions: ProfileSession[] = [];
    private maxSessions: number = 10;
    
    // Active timers
    private activeTimers: Map<string, number> = new Map();
    
    // Frame timing
    private frameStartTime: number = 0;
    private frameCount: number = 0;

    private constructor() {}

    /**
     * Get singleton instance
     */
    public static getInstance(): Profiler {
        if (!Profiler.instance) {
            Profiler.instance = new Profiler();
        }
        return Profiler.instance;
    }

    /**
     * Enable profiling
     */
    public enable(): void {
        this.isEnabled = true;
        log('Profiler: Enabled');
    }

    /**
     * Disable profiling
     */
    public disable(): void {
        this.isEnabled = false;
        this.stopSession();
        log('Profiler: Disabled');
    }

    /**
     * Start a new profiling session
     */
    public startSession(name: string = 'Default'): void {
        if (!this.isEnabled) {
            return;
        }

        this.stopSession(); // Stop current session if any

        this.currentSession = {
            name,
            startTime: performance.now(),
            endTime: 0,
            entries: new Map(),
            frameCount: 0
        };

        this.frameCount = 0;
        log(`Profiler: Started session '${name}'`);
    }

    /**
     * Stop current profiling session
     */
    public stopSession(): void {
        if (!this.currentSession) {
            return;
        }

        this.currentSession.endTime = performance.now();
        this.currentSession.frameCount = this.frameCount;

        // Add to sessions history
        this.sessions.push(this.currentSession);
        if (this.sessions.length > this.maxSessions) {
            this.sessions.shift();
        }

        log(`Profiler: Stopped session '${this.currentSession.name}' (${this.frameCount} frames)`);
        this.currentSession = null;
    }

    /**
     * Start timing a specific operation
     */
    public startTimer(name: string): void {
        if (!this.isEnabled || !this.currentSession) {
            return;
        }

        this.activeTimers.set(name, performance.now());
    }

    /**
     * End timing a specific operation
     */
    public endTimer(name: string): number {
        if (!this.isEnabled || !this.currentSession) {
            return 0;
        }

        const startTime = this.activeTimers.get(name);
        if (startTime === undefined) {
            warn(`Profiler: Timer '${name}' was not started`);
            return 0;
        }

        const endTime = performance.now();
        const duration = endTime - startTime;

        this.activeTimers.delete(name);
        this.recordEntry(name, startTime, endTime, duration);

        return duration;
    }

    /**
     * Time a function execution
     */
    public time<T>(name: string, func: () => T): T {
        this.startTimer(name);
        const result = func();
        this.endTimer(name);
        return result;
    }

    /**
     * Time an async function execution
     */
    public async timeAsync<T>(name: string, func: () => Promise<T>): Promise<T> {
        this.startTimer(name);
        const result = await func();
        this.endTimer(name);
        return result;
    }

    /**
     * Mark the start of a frame
     */
    public startFrame(): void {
        if (!this.isEnabled) {
            return;
        }

        this.frameStartTime = performance.now();
    }

    /**
     * Mark the end of a frame
     */
    public endFrame(): void {
        if (!this.isEnabled || !this.currentSession) {
            return;
        }

        const frameTime = performance.now() - this.frameStartTime;
        this.recordEntry('Frame', this.frameStartTime, performance.now(), frameTime);
        this.frameCount++;
    }

    /**
     * Get current session data
     */
    public getCurrentSession(): ProfileSession | null {
        return this.currentSession;
    }

    /**
     * Get all session data
     */
    public getAllSessions(): ProfileSession[] {
        return [...this.sessions];
    }

    /**
     * Get session by name
     */
    public getSession(name: string): ProfileSession | null {
        return this.sessions.find(s => s.name === name) || null;
    }

    /**
     * Get profiling report for current session
     */
    public getReport(): string {
        if (!this.currentSession) {
            return 'No active profiling session';
        }

        const session = this.currentSession;
        const sessionDuration = (session.endTime || performance.now()) - session.startTime;
        
        let report = `\n=== Profiling Report: ${session.name} ===\n`;
        report += `Session Duration: ${sessionDuration.toFixed(2)}ms\n`;
        report += `Frame Count: ${session.frameCount}\n`;
        report += `Average FPS: ${session.frameCount > 0 ? (session.frameCount / (sessionDuration / 1000)).toFixed(1) : 'N/A'}\n\n`;

        // Sort entries by total time
        const sortedEntries = Array.from(session.entries.values())
            .sort((a, b) => b.totalTime - a.totalTime);

        report += 'Performance Breakdown:\n';
        report += '┌─────────────────────────┬──────────┬──────────┬──────────┬──────────┬──────────┐\n';
        report += '│ Operation               │ Calls    │ Total    │ Average  │ Min      │ Max      │\n';
        report += '├─────────────────────────┼──────────┼──────────┼──────────┼──────────┼──────────┤\n';

        sortedEntries.forEach(entry => {
            const name = entry.name.padEnd(23);
            const calls = entry.callCount.toString().padStart(8);
            const total = `${entry.totalTime.toFixed(2)}ms`.padStart(8);
            const average = `${entry.averageTime.toFixed(2)}ms`.padStart(8);
            const min = `${entry.minTime.toFixed(2)}ms`.padStart(8);
            const max = `${entry.maxTime.toFixed(2)}ms`.padStart(8);
            
            report += `│ ${name} │ ${calls} │ ${total} │ ${average} │ ${min} │ ${max} │\n`;
        });

        report += '└─────────────────────────┴──────────┴──────────┴──────────┴──────────┴──────────┘\n';

        return report;
    }

    /**
     * Export profiling data as JSON
     */
    public exportData(): any {
        return {
            isEnabled: this.isEnabled,
            currentSession: this.currentSession,
            sessions: this.sessions.map(session => ({
                ...session,
                entries: Array.from(session.entries.entries()).map(([name, entry]) => ({
                    name,
                    ...entry
                }))
            }))
        };
    }

    /**
     * Clear all profiling data
     */
    public clear(): void {
        this.sessions = [];
        this.currentSession = null;
        this.activeTimers.clear();
        this.frameCount = 0;
        log('Profiler: Cleared all data');
    }

    /**
     * Get top performance bottlenecks
     */
    public getBottlenecks(count: number = 5): Array<{name: string; totalTime: number; percentage: number}> {
        if (!this.currentSession) {
            return [];
        }

        const entries = Array.from(this.currentSession.entries.values());
        const totalTime = entries.reduce((sum, entry) => sum + entry.totalTime, 0);

        return entries
            .sort((a, b) => b.totalTime - a.totalTime)
            .slice(0, count)
            .map(entry => ({
                name: entry.name,
                totalTime: entry.totalTime,
                percentage: (entry.totalTime / totalTime) * 100
            }));
    }

    /**
     * Get memory usage estimate
     */
    public getMemoryUsage(): number {
        let size = 0;
        
        // Estimate size of sessions
        this.sessions.forEach(session => {
            size += 200; // Base session size
            size += session.entries.size * 150; // Entry size
        });

        // Current session
        if (this.currentSession) {
            size += 200;
            size += this.currentSession.entries.size * 150;
        }

        return size; // Bytes
    }

    // Private methods

    /**
     * Record a profiling entry
     */
    private recordEntry(name: string, startTime: number, endTime: number, duration: number): void {
        if (!this.currentSession) {
            return;
        }

        let entry = this.currentSession.entries.get(name);
        
        if (!entry) {
            entry = {
                name,
                startTime,
                endTime,
                duration,
                callCount: 0,
                totalTime: 0,
                averageTime: 0,
                minTime: Infinity,
                maxTime: 0
            };
            this.currentSession.entries.set(name, entry);
        }

        // Update entry statistics
        entry.callCount++;
        entry.totalTime += duration;
        entry.averageTime = entry.totalTime / entry.callCount;
        entry.minTime = Math.min(entry.minTime, duration);
        entry.maxTime = Math.max(entry.maxTime, duration);
        entry.endTime = endTime; // Keep track of last end time
    }
}

/**
 * Convenience functions for profiling
 */

/**
 * Get the global profiler instance
 */
export function getProfiler(): Profiler {
    return Profiler.getInstance();
}

/**
 * Decorator for profiling method calls
 */
export function profile(name?: string) {
    return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
        const originalMethod = descriptor.value;
        const profileName = name || `${target.constructor.name}.${propertyKey}`;

        descriptor.value = function (...args: any[]) {
            const profiler = getProfiler();
            profiler.startTimer(profileName);
            const result = originalMethod.apply(this, args);
            profiler.endTimer(profileName);
            return result;
        };

        return descriptor;
    };
}

/**
 * Decorator for profiling async method calls
 */
export function profileAsync(name?: string) {
    return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
        const originalMethod = descriptor.value;
        const profileName = name || `${target.constructor.name}.${propertyKey}`;

        descriptor.value = async function (...args: any[]) {
            const profiler = getProfiler();
            profiler.startTimer(profileName);
            const result = await originalMethod.apply(this, args);
            profiler.endTimer(profileName);
            return result;
        };

        return descriptor;
    };
}
