/**
 * CrazyStorm 2.0 Integration for Cocos Creator
 * Global Particle Manager
 * 
 * Manages particle allocation, global limits, and system-wide coordination.
 * Based on the C# ParticleManager.cs implementation.
 */

import { Vec2, Node, log, warn } from 'cc';
import { Particle, CurveParticle } from '../physics/ParticlePhysics';
import { ParticleType } from '../core/CrazyStormTypes';

/**
 * Particle allocation statistics
 */
export interface ParticleStats {
    totalParticles: number;
    activeParticles: number;
    pooledParticles: number;
    curveParticles: number;
    standardParticles: number;
    memoryUsage: number; // Estimated memory usage in bytes
}

/**
 * Particle search bounds for spatial queries
 */
export interface SearchBounds {
    left: number;
    right: number;
    top: number;
    bottom: number;
}

/**
 * Global particle manager for efficient particle allocation and management
 */
export class ParticleManager {
    private static instance: ParticleManager | null = null;
    
    // Global particle pools
    private standardParticlePool: Particle[] = [];
    private curveParticlePool: CurveParticle[] = [];
    
    // Active particle tracking
    private activeParticles: Set<Particle> = new Set();
    private particlesByLayer: Map<number, Particle[]> = new Map();
    
    // Configuration
    private maxTotalParticles: number = 10000;
    private maxParticlesPerLayer: number = 2000;
    private poolPreallocationSize: number = 1000;
    
    // Performance tracking
    private stats: ParticleStats = {
        totalParticles: 0,
        activeParticles: 0,
        pooledParticles: 0,
        curveParticles: 0,
        standardParticles: 0,
        memoryUsage: 0
    };

    private constructor() {
        this.initializePools();
    }

    /**
     * Get singleton instance
     */
    public static getInstance(): ParticleManager {
        if (!this.instance) {
            this.instance = new ParticleManager();
        }
        return this.instance;
    }

    /**
     * Initialize particle pools
     */
    private initializePools(): void {
        // Pre-allocate standard particles
        for (let i = 0; i < this.poolPreallocationSize; i++) {
            const particle = this.createStandardParticle();
            particle.alive = false;
            this.standardParticlePool.push(particle);
        }

        // Pre-allocate curve particles
        for (let i = 0; i < this.poolPreallocationSize / 2; i++) {
            const particle = this.createCurveParticle();
            particle.alive = false;
            this.curveParticlePool.push(particle);
        }

        this.updateStats();
        log(`ParticleManager: Initialized with ${this.poolPreallocationSize} standard and ${this.poolPreallocationSize / 2} curve particles`);
    }

    /**
     * Get a standard particle from the global pool
     */
    public getParticle(layerId: number, particleType: ParticleType, componentId: number): Particle | null {
        // Check global limits
        if (this.activeParticles.size >= this.maxTotalParticles) {
            warn('ParticleManager: Global particle limit reached');
            return this.recycleOldestParticle(layerId, false) as Particle;
        }

        // Check layer limits
        const layerParticles = this.particlesByLayer.get(layerId) || [];
        if (layerParticles.length >= this.maxParticlesPerLayer) {
            warn(`ParticleManager: Layer ${layerId} particle limit reached`);
            return this.recycleOldestParticle(layerId, false) as Particle;
        }

        // Get particle from pool or create new one
        let particle: Particle;
        if (this.standardParticlePool.length > 0) {
            particle = this.standardParticlePool.pop()!;
            this.resetParticle(particle);
        } else {
            particle = this.createStandardParticle();
        }

        // Initialize particle
        particle.particleType = particleType;
        particle.componentId = componentId;
        particle.layerId = layerId;
        particle.alive = true;

        // Track particle
        this.activeParticles.add(particle);
        if (!this.particlesByLayer.has(layerId)) {
            this.particlesByLayer.set(layerId, []);
        }
        this.particlesByLayer.get(layerId)!.push(particle);

        this.updateStats();
        return particle;
    }

    /**
     * Get a curve particle from the global pool
     */
    public getCurveParticle(layerId: number, particleType: ParticleType, componentId: number, curveLength: number): CurveParticle | null {
        // Check global limits
        if (this.activeParticles.size >= this.maxTotalParticles) {
            warn('ParticleManager: Global particle limit reached');
            return this.recycleOldestParticle(layerId, true) as CurveParticle;
        }

        // Check layer limits
        const layerParticles = this.particlesByLayer.get(layerId) || [];
        if (layerParticles.length >= this.maxParticlesPerLayer) {
            warn(`ParticleManager: Layer ${layerId} particle limit reached`);
            return this.recycleOldestParticle(layerId, true) as CurveParticle;
        }

        // Get particle from pool or create new one
        let particle: CurveParticle;
        if (this.curveParticlePool.length > 0) {
            particle = this.curveParticlePool.pop()!;
            this.resetParticle(particle);
        } else {
            particle = this.createCurveParticle();
        }

        // Initialize particle
        particle.particleType = particleType;
        particle.componentId = componentId;
        particle.layerId = layerId;
        particle.length = curveLength;
        particle.alive = true;

        // Track particle
        this.activeParticles.add(particle);
        if (!this.particlesByLayer.has(layerId)) {
            this.particlesByLayer.set(layerId, []);
        }
        this.particlesByLayer.get(layerId)!.push(particle);

        this.updateStats();
        return particle;
    }

    /**
     * Return a particle to the global pool
     */
    public returnParticle(particle: Particle): void {
        if (!this.activeParticles.has(particle)) {
            return; // Particle not managed by this manager
        }

        // Remove from tracking
        this.activeParticles.delete(particle);
        const layerParticles = this.particlesByLayer.get(particle.layerId);
        if (layerParticles) {
            const index = layerParticles.indexOf(particle);
            if (index >= 0) {
                layerParticles.splice(index, 1);
            }
        }

        // Reset and return to pool
        this.resetParticle(particle);
        particle.alive = false;

        if (particle instanceof CurveParticle) {
            this.curveParticlePool.push(particle);
        } else {
            this.standardParticlePool.push(particle);
        }

        this.updateStats();
    }

    /**
     * Search for particles within bounds
     */
    public searchByRect(bounds: SearchBounds): Particle[] {
        const results: Particle[] = [];
        
        for (const particle of this.activeParticles) {
            if (particle.alive &&
                particle.position.x >= bounds.left &&
                particle.position.x <= bounds.right &&
                particle.position.y >= bounds.bottom &&
                particle.position.y <= bounds.top) {
                results.push(particle);
            }
        }
        
        return results;
    }

    /**
     * Search for particles within radius of a point
     */
    public searchByRadius(center: Vec2, radius: number): Particle[] {
        const results: Particle[] = [];
        const radiusSquared = radius * radius;
        
        for (const particle of this.activeParticles) {
            if (particle.alive) {
                const dx = particle.position.x - center.x;
                const dy = particle.position.y - center.y;
                const distanceSquared = dx * dx + dy * dy;
                
                if (distanceSquared <= radiusSquared) {
                    results.push(particle);
                }
            }
        }
        
        return results;
    }

    /**
     * Get particles by layer
     */
    public getParticlesByLayer(layerId: number): Particle[] {
        return this.particlesByLayer.get(layerId) || [];
    }

    /**
     * Get particles by component
     */
    public getParticlesByComponent(componentId: number): Particle[] {
        const results: Particle[] = [];
        
        for (const particle of this.activeParticles) {
            if (particle.componentId === componentId) {
                results.push(particle);
            }
        }
        
        return results;
    }

    /**
     * Clear all particles
     */
    public clearAllParticles(): void {
        for (const particle of this.activeParticles) {
            this.resetParticle(particle);
            particle.alive = false;
            
            if (particle instanceof CurveParticle) {
                this.curveParticlePool.push(particle);
            } else {
                this.standardParticlePool.push(particle);
            }
        }
        
        this.activeParticles.clear();
        this.particlesByLayer.clear();
        this.updateStats();
        
        log('ParticleManager: Cleared all particles');
    }

    /**
     * Clear particles by layer
     */
    public clearParticlesByLayer(layerId: number): void {
        const layerParticles = this.particlesByLayer.get(layerId) || [];
        
        for (const particle of layerParticles) {
            this.returnParticle(particle);
        }
        
        log(`ParticleManager: Cleared ${layerParticles.length} particles from layer ${layerId}`);
    }

    /**
     * Clear particles by component
     */
    public clearParticlesByComponent(componentId: number): void {
        const particlesToClear: Particle[] = [];
        
        for (const particle of this.activeParticles) {
            if (particle.componentId === componentId) {
                particlesToClear.push(particle);
            }
        }
        
        for (const particle of particlesToClear) {
            this.returnParticle(particle);
        }
        
        log(`ParticleManager: Cleared ${particlesToClear.length} particles from component ${componentId}`);
    }

    /**
     * Get current statistics
     */
    public getStats(): ParticleStats {
        this.updateStats();
        return { ...this.stats };
    }

    /**
     * Configure particle limits
     */
    public configure(options: {
        maxTotalParticles?: number;
        maxParticlesPerLayer?: number;
        poolPreallocationSize?: number;
    }): void {
        if (options.maxTotalParticles !== undefined) {
            this.maxTotalParticles = options.maxTotalParticles;
        }
        if (options.maxParticlesPerLayer !== undefined) {
            this.maxParticlesPerLayer = options.maxParticlesPerLayer;
        }
        if (options.poolPreallocationSize !== undefined) {
            this.poolPreallocationSize = options.poolPreallocationSize;
        }
        
        log(`ParticleManager: Configured - Max Total: ${this.maxTotalParticles}, Max Per Layer: ${this.maxParticlesPerLayer}`);
    }

    // ============================================================================
    // PRIVATE HELPER METHODS
    // ============================================================================

    /**
     * Create a new standard particle
     */
    private createStandardParticle(): Particle {
        const particleId = Math.floor(Math.random() * 1000000);
        const defaultType: ParticleType = {
            id: 0,
            name: 'DefaultParticle',
            imageId: 0,
            radius: 3,
            textureRect: { x: 0, y: 0, width: 16, height: 16 }
        };

        return new Particle(particleId, defaultType, 0, 0);
    }

    /**
     * Create a new curve particle
     */
    private createCurveParticle(): CurveParticle {
        const particleId = Math.floor(Math.random() * 1000000);
        const defaultType: ParticleType = {
            id: 0,
            name: 'DefaultCurveParticle',
            imageId: 0,
            radius: 3,
            textureRect: { x: 0, y: 0, width: 16, height: 16 }
        };

        return new CurveParticle(particleId, defaultType, 0, 0, 10);
    }

    /**
     * Reset particle to default state
     */
    private resetParticle(particle: Particle): void {
        particle.reset();

        // Hide particle node if it exists
        if (particle.node && particle.node.isValid) {
            particle.node.active = false;
        }
    }

    /**
     * Recycle the oldest particle when limits are reached
     */
    private recycleOldestParticle(layerId: number, needsCurveParticle: boolean): Particle | null {
        // Try to find an old particle from the same layer first
        const layerParticles = this.particlesByLayer.get(layerId) || [];

        for (const particle of layerParticles) {
            const isCurveParticle = particle instanceof CurveParticle;
            if (isCurveParticle === needsCurveParticle) {
                this.returnParticle(particle);

                // Return the recycled particle
                if (needsCurveParticle) {
                    return this.getCurveParticle(layerId, particle.particleType, particle.componentId, (particle as CurveParticle).length);
                } else {
                    return this.getParticle(layerId, particle.particleType, particle.componentId);
                }
            }
        }

        // If no suitable particle found in layer, try globally
        for (const particle of this.activeParticles) {
            const isCurveParticle = particle instanceof CurveParticle;
            if (isCurveParticle === needsCurveParticle) {
                this.returnParticle(particle);

                // Return the recycled particle
                if (needsCurveParticle) {
                    return this.getCurveParticle(layerId, particle.particleType, particle.componentId, (particle as CurveParticle).length);
                } else {
                    return this.getParticle(layerId, particle.particleType, particle.componentId);
                }
            }
        }

        warn('ParticleManager: Could not recycle suitable particle');
        return null;
    }

    /**
     * Update statistics
     */
    private updateStats(): void {
        this.stats.activeParticles = this.activeParticles.size;
        this.stats.pooledParticles = this.standardParticlePool.length + this.curveParticlePool.length;
        this.stats.totalParticles = this.stats.activeParticles + this.stats.pooledParticles;

        // Count curve vs standard particles
        this.stats.curveParticles = 0;
        this.stats.standardParticles = 0;

        for (const particle of this.activeParticles) {
            if (particle instanceof CurveParticle) {
                this.stats.curveParticles++;
            } else {
                this.stats.standardParticles++;
            }
        }

        // Estimate memory usage (rough calculation)
        const avgParticleSize = 200; // bytes per particle (rough estimate)
        this.stats.memoryUsage = this.stats.totalParticles * avgParticleSize;
    }

    /**
     * Cleanup and destroy the manager
     */
    public destroy(): void {
        this.clearAllParticles();
        this.standardParticlePool = [];
        this.curveParticlePool = [];
        ParticleManager.instance = null;
        log('ParticleManager: Destroyed');
    }
}
