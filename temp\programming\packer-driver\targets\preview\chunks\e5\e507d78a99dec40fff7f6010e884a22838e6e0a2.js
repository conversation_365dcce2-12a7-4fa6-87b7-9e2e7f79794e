System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Component, LubanMgr, NetMgr, _dec, _class, _class2, _crd, ccclass, GameInstance;

  function _reportPossibleCrUseOfIMgr(extras) {
    _reporterNs.report("IMgr", "./IMgr", _context.meta, extras);
  }

  function _reportPossibleCrUseOfFnOnUpdate(extras) {
    _reporterNs.report("FnOnUpdate", "./IMgr", _context.meta, extras);
  }

  function _reportPossibleCrUseOfFnOnLateUpdate(extras) {
    _reporterNs.report("FnOnLateUpdate", "./IMgr", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLubanMgr(extras) {
    _reporterNs.report("LubanMgr", "./Luban/LubanMgr", _context.meta, extras);
  }

  function _reportPossibleCrUseOfNetMgr(extras) {
    _reporterNs.report("NetMgr", "./Network/NetMgr", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Component = _cc.Component;
    }, function (_unresolved_2) {
      LubanMgr = _unresolved_2.LubanMgr;
    }, function (_unresolved_3) {
      NetMgr = _unresolved_3.NetMgr;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "815bcg6mBVI2oWeCmHI13ZP", "GameInstance", undefined);

      __checkObsolete__(['_decorator', 'Component']);

      ({
        ccclass
      } = _decorator);

      _export("GameInstance", GameInstance = (_dec = ccclass("GameInstance"), _dec(_class = (_class2 = class GameInstance extends Component {
        constructor() {
          super(...arguments);
          this.ManagerPool = [];
          this._updateContainer = [];
          this._lateUpdateContainer = [];
          this._lubanMgr = null;
          this._netMgr = null;
        }

        static GetInstance() {
          return GameInstance._instance;
        }

        onLoad() {
          GameInstance._instance = this;
          this._lubanMgr = new (_crd && LubanMgr === void 0 ? (_reportPossibleCrUseOfLubanMgr({
            error: Error()
          }), LubanMgr) : LubanMgr)();
          this.ManagerPool.push(this._lubanMgr);
          this._netMgr = new (_crd && NetMgr === void 0 ? (_reportPossibleCrUseOfNetMgr({
            error: Error()
          }), NetMgr) : NetMgr)();
          this.ManagerPool.push(this._netMgr);
          this.ManagerPool.forEach(manager => {
            manager.init();

            this._updateContainer.push(manager.onUpdate.bind(manager));

            this._lateUpdateContainer.push(manager.onLateUpdate.bind(manager));
          });
        }

        update(deltaTime) {
          for (var i = 0; i < this._updateContainer.length; i++) {
            this._updateContainer[i](deltaTime);
          }
        }

        lateUpdate() {
          for (var i = 0; i < this._lateUpdateContainer.length; i++) {
            this._lateUpdateContainer[i]();
          }
        }

      }, _class2._instance = null, _class2)) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=e507d78a99dec40fff7f6010e884a22838e6e0a2.js.map