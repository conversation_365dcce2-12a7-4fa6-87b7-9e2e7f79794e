# CrazyStorm 2.0 Integration for Cocos Creator

A comprehensive TypeScript integration system that brings CrazyStorm 2.0 bullet pattern editor capabilities to Cocos Creator, specifically optimized for mobile air-fighter games.

## 🚀 Features

- **Dual Format Support**: Parse both .bgp (XML) and .bg (binary) files from CrazyStorm 2.0 editor
- **5 Component Types**: CurveEmitter, MultiEmitter, ForceField, EventField, Rebounder
- **Mobile Optimized**: LOD system, object pooling, adaptive quality
- **Performance Monitoring**: Real-time FPS tracking and optimization recommendations
- **Collision System**: Spatial partitioning for efficient collision detection
- **Game Integration**: Enemy patterns, wave management, difficulty scaling
- **TypeScript Native**: Full type safety and IntelliSense support

## 📁 Project Structure

```
assets/scripts/crazy-storm/
├── core/                    # Core parsing and loading
│   ├── CrazyStormTypes.ts   # Type definitions
│   ├── CrazyStormParser.ts  # XML parser (.bgp files)
│   ├── BinaryParser.ts      # Binary parser (.bg files)
│   └── CrazyStormLoader.ts  # File loader with caching
├── components/              # CrazyStorm components
│   ├── CrazyStormComponent.ts      # Base component
│   ├── CurveEmitterComponent.ts    # Curve particle emitter
│   ├── MultiEmitterComponent.ts    # Standard particle emitter
│   ├── ForceFieldComponent.ts      # Force field effects
│   ├── EventFieldComponent.ts      # Event triggers
│   ├── RebounderComponent.ts       # Particle bouncing
│   └── ComponentFactory.ts         # Component creation
├── physics/                 # Particle physics
│   └── ParticlePhysics.ts   # Particle simulation
├── runtime/                 # Runtime management
│   ├── CrazyStormManager.ts        # Main runtime manager
│   ├── ParticleSystemManager.ts    # Particle system coordination
│   ├── LayerManager.ts             # Layer timing and visibility
│   ├── ResourceManager.ts          # Asset loading and caching
│   └── GlobalVariableManager.ts    # Global variable system
├── performance/             # Performance optimization
│   ├── ObjectPool.ts        # Object pooling system
│   ├── PerformanceMonitor.ts       # Performance tracking
│   ├── MobileOptimizer.ts          # Mobile-specific optimizations
│   └── Profiler.ts                 # Development profiling
├── game/                    # Game integration
│   ├── GameIntegration.ts          # Main integration component
│   ├── CollisionManager.ts         # Collision detection
│   ├── EnemyPatternController.ts   # Enemy pattern management
│   └── GameStateManager.ts         # Game state synchronization
├── tests/                   # Unit tests
├── examples/                # Usage examples
└── index.ts                 # Main export
```

## 🎯 Quick Start

### 1. Basic Setup

```typescript
import { initializeCrazyStorm } from './crazy-storm';

// Initialize the system
initializeCrazyStorm();
```

### 2. Add Game Integration Component

```typescript
import { GameIntegration } from './crazy-storm/game';

@ccclass('GameController')
export class GameController extends Component {
    @property(GameIntegration)
    crazyStormIntegration: GameIntegration = null!;

    start() {
        // Set up player collision
        this.crazyStormIntegration.setGameEvents({
            onPlayerHit: (damage, position) => {
                this.handlePlayerDamage(damage);
            },
            onEnemyDestroyed: (enemyId, position) => {
                this.addScore(100);
            }
        });
    }

    update(deltaTime: number) {
        // Update player position for global variables (cx, cy)
        this.crazyStormIntegration.setPlayerPosition(this.playerPosition);
    }
}
```

### 3. Spawn Enemy Patterns

```typescript
// Spawn a basic enemy pattern
await this.crazyStormIntegration.spawnEnemyPattern(
    'basic_spread',     // Pattern name
    enemyPosition,      // Spawn position
    'enemy_001'         // Enemy ID
);

// Spawn with configuration
await this.crazyStormIntegration.spawnEnemyPattern(
    'boss_pattern_1',
    bossPosition,
    'boss_001',
    {
        duration: 15,   // 15 seconds
        looping: true,  // Loop the pattern
        scale: 1.2      // 120% scale
    }
);
```

## 🎮 Game Integration Examples

### Enemy Wave System

```typescript
const gameState = this.crazyStormIntegration.getGameStateManager();

// Set up wave progression
gameState.onWaveChange((wave) => {
    wave.enemies.forEach(async (enemy) => {
        for (let i = 0; i < enemy.count; i++) {
            const position = this.getEnemySpawnPosition();
            const enemyId = `${enemy.type}_${i}`;
            
            // Spawn enemy with patterns
            for (const pattern of enemy.patterns) {
                await this.crazyStormIntegration.spawnEnemyPattern(
                    pattern, position, enemyId
                );
            }
            
            // Delay between spawns
            await this.delay(enemy.spawnDelay);
        }
    });
});
```

### Boss Battle Integration

```typescript
class BossController {
    private async startBossPhase1() {
        // Phase 1: Spread pattern
        await this.crazyStormIntegration.spawnEnemyPattern(
            'boss_spread', this.bossPosition, 'boss', 
            { duration: 10 }
        );
    }

    private async startBossPhase2() {
        // Phase 2: Spiral + aimed shots
        await Promise.all([
            this.crazyStormIntegration.spawnEnemyPattern(
                'boss_spiral', this.bossPosition, 'boss'
            ),
            this.crazyStormIntegration.spawnEnemyPattern(
                'boss_aimed', this.bossPosition, 'boss'
            )
        ]);
    }

    private onBossDefeated() {
        // Stop all boss patterns
        this.crazyStormIntegration.stopPatternsByEnemy('boss');
    }
}
```

## 📱 Mobile Optimization

The system automatically optimizes for mobile devices:

```typescript
import { getMobileOptimizer, getPerformanceMonitor } from './crazy-storm/performance';

// Configure for mobile
const optimizer = getMobileOptimizer();
optimizer.setCameraPosition(this.cameraPosition);
optimizer.setViewportSize(this.screenSize);

// Monitor performance
const monitor = getPerformanceMonitor();
monitor.setPerformanceIssueCallback((recommendations) => {
    recommendations.forEach(rec => {
        if (rec.severity === 'high') {
            // Automatically reduce quality
            optimizer.adjustQuality('down');
        }
    });
});
```

## 🔧 Configuration

### Performance Settings

```typescript
// Mobile configuration
const mobileConfig = {
    enableCollision: true,
    enablePerformanceMonitoring: true,
    playerCollisionRadius: 12,
    bulletCollisionRadius: 4,
    maxConcurrentPatterns: 3
};

// Desktop configuration  
const desktopConfig = {
    enableCollision: true,
    enablePerformanceMonitoring: false,
    playerCollisionRadius: 15,
    bulletCollisionRadius: 5,
    maxConcurrentPatterns: 8
};
```

### Pattern Registration

```typescript
const patternController = this.crazyStormIntegration.getEnemyPatternController();

// Register custom patterns
patternController.registerPattern('my_pattern', 'patterns/my_pattern');
patternController.registerPattern('boss_special', 'patterns/bosses/special_attack');
```

## 🎨 Creating Patterns

1. **Use CrazyStorm 2.0 Editor** to create your bullet patterns
2. **Export as .bgp (XML) or .bg (binary) files**
   - **.bgp files**: Human-readable, easier to debug, larger file size
   - **.bg files**: Compact binary format, faster loading, smaller file size
3. **Place in your resources folder** under `crazy-storm/patterns/`
4. **Register patterns** in your game code
5. **Spawn patterns** using the integration system

### Pattern File Structure

```
resources/crazy-storm/patterns/
├── basic_spread.bgp         # XML format
├── basic_spread.bg          # Binary format (same pattern)
├── spiral.bgp
├── aimed_burst.bg           # Mix and match formats
├── boss_patterns/
│   ├── boss1.bg             # Binary for faster loading
│   └── boss2.bgp            # XML for easier debugging
└── enemy_patterns/
    ├── enemy1.bg
    └── enemy2.bg
```

## 🚀 Performance Tips

1. **Use Object Pooling**: Automatically handled by the system
2. **Enable LOD**: Reduces particles at distance
3. **Spatial Culling**: Particles outside view are culled
4. **Adaptive Quality**: Automatically adjusts based on performance
5. **Batch Operations**: Group pattern spawns when possible

## 🧪 Testing

```typescript
import { CrazyStormParserTest } from './crazy-storm/tests';

// Run parser tests
const testSuite = new CrazyStormParserTest();
const allPassed = testSuite.runAllTests();
console.log(`Tests ${allPassed ? 'PASSED' : 'FAILED'}`);
```

## 📊 Monitoring

```typescript
// Get comprehensive performance report
import { getPerformanceReport } from './crazy-storm/performance';

const report = getPerformanceReport();
console.log('Performance Report:', report);

// Get game statistics
const stats = this.crazyStormIntegration.getGameStats();
console.log('Game Stats:', stats);
```

## 🤝 Contributing

This integration system is designed to be extensible. You can:

- Add new component types
- Extend the parser for custom properties
- Create custom optimization strategies
- Add new collision detection methods

## 📄 License

This integration system is provided as-is for educational and development purposes. CrazyStorm 2.0 is a separate product with its own licensing terms.

---

**Ready to create amazing bullet hell patterns in your Cocos Creator air-fighter game!** 🎮✈️
