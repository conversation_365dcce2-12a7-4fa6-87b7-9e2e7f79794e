[{"__type__": "cc.Prefab", "_name": "EditBox", "_objFlags": 0, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "asyncLoadAssets": false, "persistent": false}, {"__type__": "cc.Node", "_name": "EditBox", "_objFlags": 0, "_parent": null, "_children": [{"__id__": 2}, {"__id__": 8}], "_active": true, "_level": 2, "_components": [{"__id__": 14}, {"__id__": 16}, {"__id__": 18}], "_prefab": {"__id__": 20}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "TEXT_LABEL", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [], "_active": false, "_level": 2, "_components": [{"__id__": 3}, {"__id__": 5}], "_prefab": {"__id__": 7}, "_lpos": {"__type__": "cc.Vec3", "x": -78, "y": 20, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "_contentSize": {"__type__": "cc.Size", "width": 158, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 1}, "_id": "", "_priority": 0, "__prefab": {"__id__": 4}}, {"__type__": "cc.CompPrefabInfo", "fileId": "779kAXGTtMZKXfYlOg0Tfd"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_sharedMaterial": null, "_useOriginalSize": true, "_string": "", "_horizontalAlign": 0, "_verticalAlign": 1, "_actualFontSize": 40, "_fontSize": 20, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 1, "_enableWrapText": false, "_font": null, "_isSystemFontUsed": true, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_cacheMode": 0, "_id": "", "__prefab": {"__id__": 6}}, {"__type__": "cc.CompPrefabInfo", "fileId": "ddIY+NJvlDTIQAg7PLVrGo"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "e02jnMmZxAGZlJs5fq9NJo"}, {"__type__": "cc.Node", "_name": "PLACEHOLDER_LABEL", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_level": 2, "_components": [{"__id__": 9}, {"__id__": 11}], "_prefab": {"__id__": 13}, "_lpos": {"__type__": "cc.Vec3", "x": -78, "y": 20, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 8}, "_enabled": true, "_contentSize": {"__type__": "cc.Size", "width": 158, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 1}, "_id": "", "_priority": 0, "__prefab": {"__id__": 10}}, {"__type__": "cc.CompPrefabInfo", "fileId": "d07wQj4whCUqYGJH1lEpVp"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 8}, "_enabled": true, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 187, "g": 187, "b": 187, "a": 255}, "_sharedMaterial": null, "_useOriginalSize": true, "_string": "Enter text here...", "_horizontalAlign": 0, "_verticalAlign": 1, "_actualFontSize": 20, "_fontSize": 20, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 1, "_enableWrapText": false, "_font": null, "_isSystemFontUsed": true, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_cacheMode": 0, "_id": "", "__prefab": {"__id__": 12}}, {"__type__": "cc.CompPrefabInfo", "fileId": "8fhi7qRLFJbK0abIJuXmCW"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c4xCQrIDRL0pjg6mrIks7k"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "_contentSize": {"__type__": "cc.Size", "width": 160, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "", "_priority": 0, "__prefab": {"__id__": 15}}, {"__type__": "cc.CompPrefabInfo", "fileId": "1fhJOVuOVAGYSYZoiE25Uz"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_sharedMaterial": null, "_spriteFrame": {"__uuid__": "bd1bcaba-bd7d-4a71-b143-997c882383e4@f9941"}, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "", "__prefab": {"__id__": 17}}, {"__type__": "cc.CompPrefabInfo", "fileId": "43qH95z3VGeYelCElKd6FW"}, {"__type__": "cc.EditBox", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "editingDidBegan": [], "textChanged": [], "editingDidEnded": [], "editingReturn": [], "_returnType": 0, "_useOriginalSize": true, "_string": "", "_tabIndex": 0, "_backgroundImage": {"__uuid__": "bd1bcaba-bd7d-4a71-b143-997c882383e4@f9941"}, "_inputFlag": 5, "_inputMode": 6, "_fontSize": 20, "_lineHeight": 40, "_maxLength": 8, "_fontColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_placeholder": "Enter text here...", "_placeholderFontSize": 20, "_placeholderFontColor": {"__type__": "cc.Color", "r": 187, "g": 187, "b": 187, "a": 255}, "_stayOnTop": false, "_id": "", "__prefab": {"__id__": 19}}, {"__type__": "cc.CompPrefabInfo", "fileId": "1bCHrwPGZOPrbmPh93kwpe"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "028Ule0PtAPqB/Osb42WnZ"}]