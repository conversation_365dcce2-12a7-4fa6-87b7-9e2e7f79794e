{"__type__": "cc.SpriteFrame", "content": {"name": "light", "atlas": "", "rect": {"x": 5, "y": 0, "width": 24, "height": 125}, "offset": {"x": 1, "y": 1.5}, "originalSize": {"width": 32, "height": 128}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-12, -62.5, 0, 12, -62.5, 0, -12, 62.5, 0, 12, 62.5, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [5, 128, 29, 128, 5, 3, 29, 3], "nuv": [0.15625, 0.0234375, 0.90625, 0.0234375, 0.15625, 1, 0.90625, 1], "minPos": {"x": -12, "y": -62.5, "z": 0}, "maxPos": {"x": 12, "y": 62.5, "z": 0}}, "texture": "d81124f5-56a2-44ea-b84f-423fbb443a31@6c48a", "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}}