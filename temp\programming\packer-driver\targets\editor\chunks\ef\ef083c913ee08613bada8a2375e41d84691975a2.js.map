{"version": 3, "sources": ["file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/gizmos/EmitterGizmo.ts"], "names": ["_decorator", "Component", "Color", "Graphics", "EDITOR", "ccclass", "property", "executeInEditMode", "EmitterGizmo", "graphics", "emitter", "onLoad", "getComponent", "addComponent", "console", "warn", "update", "drawGizmos", "clear", "showCenter", "drawCenter", "showRadius", "radius", "drawRadius", "showDirections", "drawDirections", "strokeColor", "centerColor", "lineWidth", "centerSize", "moveTo", "lineTo", "stroke", "radiusColor", "circle", "count", "directionColor", "baseDirection", "angle", "totalArc", "arc", "anglePerBullet", "startAngle", "i", "bulletAngle", "angleRad", "Math", "PI", "dirX", "cos", "dirY", "sin", "startX", "startY", "baseLength", "speedFactor", "speed", "<PERSON><PERSON><PERSON><PERSON>", "max", "speedScale", "endX", "endY", "drawArrowHead", "arrowSize", "arrowAngle", "leftX", "leftY", "rightX", "rightY", "GRAY", "RED", "WHITE"], "mappings": ";;;;;;;;;;;;;;;;AAMSA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,Q,OAAAA,Q;;AAC9BC,MAAAA,M,UAAAA,M;;;;;;AAPT;AACA;AACA;AACA;AACA;;;;;OAIM;AAAEC,QAAAA,OAAF;AAAWC,QAAAA,QAAX;AAAqBC,QAAAA;AAArB,O,GAA2CP,U;;8BAIpCQ,Y,WAFZH,OAAO,CAAC,cAAD,C,UACPE,iBAAiB,CAAC,IAAD,C,0CADlB,MAEaC,YAFb,SAEkCP,SAFlC,CAE4C;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA,eAuBhCQ,QAvBgC,GAuBJ,IAvBI;AAAA,eAwBhCC,OAxBgC,GAwBjB,IAxBiB;AAAA;;AA0B9BC,QAAAA,MAAM,GAAS;AACrB,cAAI,CAACP,MAAL,EAAa,OADQ,CAGrB;;AACA,eAAKK,QAAL,GAAgB,KAAKG,YAAL,CAAkBT,QAAlB,KAA+B,KAAKU,YAAL,CAAkBV,QAAlB,CAA/C,CAJqB,CAMrB;;AACA,eAAKO,OAAL,GAAe,KAAKE,YAAL,CAAkB,SAAlB,CAAf;;AAEA,cAAI,CAAC,KAAKF,OAAV,EAAmB;AACfI,YAAAA,OAAO,CAACC,IAAR,CAAa,uDAAb;AACH;AACJ;;AAESC,QAAAA,MAAM,GAAS;AACrB,cAAI,CAACZ,MAAD,IAAW,CAAC,KAAKK,QAAjB,IAA6B,CAAC,KAAKC,OAAvC,EAAgD;AAEhD,eAAKO,UAAL;AACH;;AAEOA,QAAAA,UAAU,GAAS;AACvB,cAAI,CAAC,KAAKR,QAAV,EAAoB,OADG,CAGvB;;AACA,eAAKA,QAAL,CAAcS,KAAd,GAJuB,CAMvB;;AACA,cAAI,KAAKC,UAAT,EAAqB;AACjB,iBAAKC,UAAL;AACH,WATsB,CAWvB;;;AACA,cAAI,KAAKC,UAAL,IAAmB,KAAKX,OAAL,CAAaY,MAAb,GAAsB,CAA7C,EAAgD;AAC5C,iBAAKC,UAAL;AACH,WAdsB,CAgBvB;;;AACA,cAAI,KAAKC,cAAT,EAAyB;AACrB,iBAAKC,cAAL;AACH;AACJ;;AAEOL,QAAAA,UAAU,GAAS;AACvB,cAAI,CAAC,KAAKX,QAAV,EAAoB;AAEpB,eAAKA,QAAL,CAAciB,WAAd,GAA4B,KAAKC,WAAjC;AACA,eAAKlB,QAAL,CAAcmB,SAAd,GAA0B,CAA1B;AAEA,gBAAMC,UAAU,GAAG,CAAnB,CANuB,CAQvB;;AACA,eAAKpB,QAAL,CAAcqB,MAAd,CAAqB,CAACD,UAAtB,EAAkC,CAAlC;AACA,eAAKpB,QAAL,CAAcsB,MAAd,CAAqBF,UAArB,EAAiC,CAAjC;AACA,eAAKpB,QAAL,CAAcqB,MAAd,CAAqB,CAArB,EAAwB,CAACD,UAAzB;AACA,eAAKpB,QAAL,CAAcsB,MAAd,CAAqB,CAArB,EAAwBF,UAAxB;AACA,eAAKpB,QAAL,CAAcuB,MAAd;AACH;;AAEOT,QAAAA,UAAU,GAAS;AACvB,cAAI,CAAC,KAAKd,QAAV,EAAoB;AAEpB,eAAKA,QAAL,CAAciB,WAAd,GAA4B,KAAKO,WAAjC;AACA,eAAKxB,QAAL,CAAcmB,SAAd,GAA0B,CAA1B,CAJuB,CAMvB;;AACA,eAAKnB,QAAL,CAAcyB,MAAd,CAAqB,CAArB,EAAwB,CAAxB,EAA2B,KAAKxB,OAAL,CAAaY,MAAxC;AACA,eAAKb,QAAL,CAAcuB,MAAd;AACH;;AAEOP,QAAAA,cAAc,GAAS;AAC3B,cAAI,CAAC,KAAKhB,QAAN,IAAkB,KAAKC,OAAL,CAAayB,KAAb,IAAsB,CAA5C,EAA+C;AAE/C,eAAK1B,QAAL,CAAciB,WAAd,GAA4B,KAAKU,cAAjC;AACA,eAAK3B,QAAL,CAAcmB,SAAd,GAA0B,CAA1B,CAJ2B,CAM3B;;AACA,gBAAMS,aAAa,GAAG,KAAK3B,OAAL,CAAa4B,KAAb,IAAsB,CAA5C,CAP2B,CAOoB;;AAC/C,gBAAMC,QAAQ,GAAG,KAAK7B,OAAL,CAAa8B,GAAb,IAAoB,CAArC,CAR2B,CAQa;AAExC;;AACA,gBAAMC,cAAc,GAAG,KAAK/B,OAAL,CAAayB,KAAb,GAAqB,CAArB,GAAyBI,QAAQ,IAAI,KAAK7B,OAAL,CAAayB,KAAb,GAAqB,CAAzB,CAAjC,GAA+D,CAAtF;AACA,gBAAMO,UAAU,GAAGL,aAAa,GAAGE,QAAQ,GAAG,CAA9C,CAZ2B,CAYsB;;AAEjD,eAAK,IAAII,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKjC,OAAL,CAAayB,KAAjC,EAAwCQ,CAAC,EAAzC,EAA6C;AACzC,gBAAIC,WAAJ;;AAEA,gBAAI,KAAKlC,OAAL,CAAayB,KAAb,KAAuB,CAA3B,EAA8B;AAC1BS,cAAAA,WAAW,GAAGP,aAAd,CAD0B,CACG;AAChC,aAFD,MAEO;AACHO,cAAAA,WAAW,GAAGF,UAAU,GAAID,cAAc,GAAGE,CAA7C;AACH,aAPwC,CASzC;;;AACA,kBAAME,QAAQ,GAAG,CAACD,WAAW,GAAG,EAAf,IAAqBE,IAAI,CAACC,EAA1B,GAA+B,GAAhD,CAVyC,CAYzC;;AACA,kBAAMC,IAAI,GAAGF,IAAI,CAACG,GAAL,CAASJ,QAAT,CAAb;AACA,kBAAMK,IAAI,GAAGJ,IAAI,CAACK,GAAL,CAASN,QAAT,CAAb,CAdyC,CAgBzC;;AACA,kBAAMO,MAAM,GAAGJ,IAAI,GAAG,KAAKtC,OAAL,CAAaY,MAAnC;AACA,kBAAM+B,MAAM,GAAGH,IAAI,GAAG,KAAKxC,OAAL,CAAaY,MAAnC,CAlByC,CAoBzC;AACA;;AACA,kBAAMgC,UAAU,GAAG,EAAnB;AACA,kBAAMC,WAAW,GAAG,KAAK7C,OAAL,CAAa8C,KAAb,IAAsB,CAA1C,CAvByC,CAuBI;;AAC7C,kBAAMC,WAAW,GAAGX,IAAI,CAACY,GAAL,CAASJ,UAAT,EAAqBA,UAAU,GAAGC,WAAb,GAA2B,KAAKI,UAArD,CAApB;AAEA,kBAAMC,IAAI,GAAGR,MAAM,GAAGJ,IAAI,GAAGS,WAA7B;AACA,kBAAMI,IAAI,GAAGR,MAAM,GAAGH,IAAI,GAAGO,WAA7B,CA3ByC,CA6BzC;;AACA,iBAAKhD,QAAL,CAAcqB,MAAd,CAAqBsB,MAArB,EAA6BC,MAA7B;AACA,iBAAK5C,QAAL,CAAcsB,MAAd,CAAqB6B,IAArB,EAA2BC,IAA3B,EA/ByC,CAiCzC;;AACA,iBAAKC,aAAL,CAAmBF,IAAnB,EAAyBC,IAAzB,EAA+Bb,IAA/B,EAAqCE,IAArC;AACH;;AAED,eAAKzC,QAAL,CAAcuB,MAAd;AACH;;AAEO8B,QAAAA,aAAa,CAACF,IAAD,EAAeC,IAAf,EAA6Bb,IAA7B,EAA2CE,IAA3C,EAA+D;AAChF,cAAI,CAAC,KAAKzC,QAAV,EAAoB;AAEpB,gBAAMsD,SAAS,GAAG,CAAlB,CAHgF,CAKhF;;AACA,gBAAMC,UAAU,GAAGlB,IAAI,CAACC,EAAL,GAAU,CAA7B,CANgF,CAMhD;AAEhC;;AACA,gBAAMkB,KAAK,GAAGL,IAAI,GAAGG,SAAS,IAAIf,IAAI,GAAGF,IAAI,CAACG,GAAL,CAASe,UAAT,CAAP,GAA8Bd,IAAI,GAAGJ,IAAI,CAACK,GAAL,CAASa,UAAT,CAAzC,CAA9B;AACA,gBAAME,KAAK,GAAGL,IAAI,GAAGE,SAAS,IAAIb,IAAI,GAAGJ,IAAI,CAACG,GAAL,CAASe,UAAT,CAAP,GAA8BhB,IAAI,GAAGF,IAAI,CAACK,GAAL,CAASa,UAAT,CAAzC,CAA9B,CAVgF,CAYhF;;AACA,gBAAMG,MAAM,GAAGP,IAAI,GAAGG,SAAS,IAAIf,IAAI,GAAGF,IAAI,CAACG,GAAL,CAAS,CAACe,UAAV,CAAP,GAA+Bd,IAAI,GAAGJ,IAAI,CAACK,GAAL,CAAS,CAACa,UAAV,CAA1C,CAA/B;AACA,gBAAMI,MAAM,GAAGP,IAAI,GAAGE,SAAS,IAAIb,IAAI,GAAGJ,IAAI,CAACG,GAAL,CAAS,CAACe,UAAV,CAAP,GAA+BhB,IAAI,GAAGF,IAAI,CAACK,GAAL,CAAS,CAACa,UAAV,CAA1C,CAA/B,CAdgF,CAgBhF;;AACA,eAAKvD,QAAL,CAAcqB,MAAd,CAAqB8B,IAArB,EAA2BC,IAA3B;AACA,eAAKpD,QAAL,CAAcsB,MAAd,CAAqBkC,KAArB,EAA4BC,KAA5B;AACA,eAAKzD,QAAL,CAAcqB,MAAd,CAAqB8B,IAArB,EAA2BC,IAA3B;AACA,eAAKpD,QAAL,CAAcsB,MAAd,CAAqBoC,MAArB,EAA6BC,MAA7B;AACH;;AA1KuC,O,6EAEvC9D,Q;;;;;iBAC4B,I;;yFAE5BA,Q;;;;;iBACgC,I;;qFAEhCA,Q;;;;;iBAC4B,I;;sFAE5BA,Q;;;;;iBAC2BJ,KAAK,CAACmE,I;;yFAEjC/D,Q;;;;;iBAC8BJ,KAAK,CAACoE,G;;sFAEpChE,Q;;;;;iBAC2BJ,KAAK,CAACqE,K;;qFAEjCjE,Q;;;;;iBAC2B,G", "sourcesContent": ["/**\n * Emitter Gizmo Component\n * This component provides visual debugging for Emitter components in the scene view\n * It should be added to the same node as the Emitter component\n */\n\nimport { _decorator, Component, Color, Graphics } from 'cc';\nimport { EDITOR } from 'cc/env';\nconst { ccclass, property, executeInEditMode } = _decorator;\n\n@ccclass('EmitterGizmo')\n@executeInEditMode(true)\nexport class EmitterGizmo extends Component {\n\n    @property\n    public showRadius: boolean = true;\n\n    @property\n    public showDirections: boolean = true;\n\n    @property\n    public showCenter: boolean = true;\n\n    @property\n    public radiusColor: Color = Color.GRAY;\n\n    @property\n    public directionColor: Color = Color.RED;\n\n    @property\n    public centerColor: Color = Color.WHITE;\n\n    @property\n    public speedScale: number = 1.0;\n\n    private graphics: Graphics | null = null;\n    private emitter: any = null;\n\n    protected onLoad(): void {\n        if (!EDITOR) return;\n\n        // Get or create Graphics component\n        this.graphics = this.getComponent(Graphics) || this.addComponent(Graphics);\n\n        // Get Emitter component\n        this.emitter = this.getComponent('Emitter');\n\n        if (!this.emitter) {\n            console.warn('EmitterGizmo: No Emitter component found on this node');\n        }\n    }\n\n    protected update(): void {\n        if (!EDITOR || !this.graphics || !this.emitter) return;\n\n        this.drawGizmos();\n    }\n\n    private drawGizmos(): void {\n        if (!this.graphics) return;\n\n        // Clear previous drawings\n        this.graphics.clear();\n\n        // Draw center point\n        if (this.showCenter) {\n            this.drawCenter();\n        }\n\n        // Draw radius circle\n        if (this.showRadius && this.emitter.radius > 0) {\n            this.drawRadius();\n        }\n\n        // Draw direction arrows\n        if (this.showDirections) {\n            this.drawDirections();\n        }\n    }\n\n    private drawCenter(): void {\n        if (!this.graphics) return;\n\n        this.graphics.strokeColor = this.centerColor;\n        this.graphics.lineWidth = 2;\n\n        const centerSize = 8;\n\n        // Draw cross at center\n        this.graphics.moveTo(-centerSize, 0);\n        this.graphics.lineTo(centerSize, 0);\n        this.graphics.moveTo(0, -centerSize);\n        this.graphics.lineTo(0, centerSize);\n        this.graphics.stroke();\n    }\n\n    private drawRadius(): void {\n        if (!this.graphics) return;\n\n        this.graphics.strokeColor = this.radiusColor;\n        this.graphics.lineWidth = 1;\n\n        // Draw radius circle\n        this.graphics.circle(0, 0, this.emitter.radius);\n        this.graphics.stroke();\n    }\n\n    private drawDirections(): void {\n        if (!this.graphics || this.emitter.count <= 0) return;\n\n        this.graphics.strokeColor = this.directionColor;\n        this.graphics.lineWidth = 2;\n\n        // Use arc property for spread calculation, angle for direction\n        const baseDirection = this.emitter.angle || 0; // Base direction from angle property\n        const totalArc = this.emitter.arc || 0; // Total arc to spread bullets across\n\n        // Calculate angle per bullet based on arc and count\n        const anglePerBullet = this.emitter.count > 1 ? totalArc / (this.emitter.count - 1) : 0;\n        const startAngle = baseDirection - totalArc / 2; // Start from base direction minus half arc\n\n        for (let i = 0; i < this.emitter.count; i++) {\n            let bulletAngle: number;\n\n            if (this.emitter.count === 1) {\n                bulletAngle = baseDirection; // Single bullet goes in base direction\n            } else {\n                bulletAngle = startAngle + (anglePerBullet * i);\n            }\n\n            // Convert angle to radians (0 degrees = up in Cocos Creator)\n            const angleRad = (bulletAngle + 90) * Math.PI / 180;\n\n            // Calculate direction vector\n            const dirX = Math.cos(angleRad);\n            const dirY = Math.sin(angleRad);\n\n            // Start position (at radius distance from center)\n            const startX = dirX * this.emitter.radius;\n            const startY = dirY * this.emitter.radius;\n\n            // Calculate arrow length based on speed factor\n            // Base length of 30 pixels, scaled by speed factor and speedScale property\n            const baseLength = 30;\n            const speedFactor = this.emitter.speed || 1; // Default to 1 if speed is 0 or undefined\n            const arrowLength = Math.max(baseLength, baseLength * speedFactor * this.speedScale);\n\n            const endX = startX + dirX * arrowLength;\n            const endY = startY + dirY * arrowLength;\n\n            // Draw arrow line\n            this.graphics.moveTo(startX, startY);\n            this.graphics.lineTo(endX, endY);\n\n            // Draw arrow head\n            this.drawArrowHead(endX, endY, dirX, dirY);\n        }\n\n        this.graphics.stroke();\n    }\n\n    private drawArrowHead(endX: number, endY: number, dirX: number, dirY: number): void {\n        if (!this.graphics) return;\n\n        const arrowSize = 8;\n\n        // Calculate arrow head points\n        const arrowAngle = Math.PI / 6; // 30 degrees\n\n        // Left arrow point\n        const leftX = endX - arrowSize * (dirX * Math.cos(arrowAngle) - dirY * Math.sin(arrowAngle));\n        const leftY = endY - arrowSize * (dirY * Math.cos(arrowAngle) + dirX * Math.sin(arrowAngle));\n\n        // Right arrow point\n        const rightX = endX - arrowSize * (dirX * Math.cos(-arrowAngle) - dirY * Math.sin(-arrowAngle));\n        const rightY = endY - arrowSize * (dirY * Math.cos(-arrowAngle) + dirX * Math.sin(-arrowAngle));\n\n        // Draw arrow head lines\n        this.graphics.moveTo(endX, endY);\n        this.graphics.lineTo(leftX, leftY);\n        this.graphics.moveTo(endX, endY);\n        this.graphics.lineTo(rightX, rightY);\n    }\n}\n"]}