import { Vec2, Vec3, Rect, Size, Color } from 'cc';

/**
 * Core level data structure containing all level information
 */
export interface ILevelData {
    metadata: LevelMetadata;
    subLevels: SubLevel[];
    globalEvents: EventGraph;
    cameraSettings: CameraSettings;
}

/**
 * Level metadata information
 */
export interface LevelMetadata {
    name: string;
    version: string;
    duration: number;
    difficulty: number;
    description?: string;
    author?: string;
    createdAt: string;
    modifiedAt: string;
}

/**
 * SubLevel represents a section of the level with its own content
 */
export interface SubLevel {
    id: string;
    name: string;
    bounds: Rect;  // World space bounds of this sublevel
    maps: MapLayer[];  // Background/foreground layers
    spawners: Spawner[];
    paths: Path[];
    events: EventGraph;
    entryPoint?: EntryPoint;
    exitPoint?: ExitPoint;
    connections: SubLevelConnection[];
    
    // Runtime state (not serialized in final data)
    isActive?: boolean;
    isLoaded?: boolean;
    loadPriority?: number;
}

/**
 * Connection between sublevels
 */
export interface SubLevelConnection {
    targetSubLevelId: string;
    connectionType: 'seamless' | 'transition' | 'teleport';
    triggerArea: Rect;  // Area that triggers the connection
    transitionData?: TransitionData;
}

/**
 * Transition data for sublevel connections
 */
export interface TransitionData {
    duration: number;
    fadeType: 'none' | 'fade' | 'slide';
    direction?: Vec2;
    easing?: string;
}

/**
 * Entry point configuration for sublevels
 */
export interface EntryPoint {
    position: Vec3;
    entryType: 'smooth' | 'fade' | 'instant';
    showCardSelection?: boolean;
    entryAnimation?: string;
    entryDelay?: number;
}

/**
 * Exit point configuration for sublevels
 */
export interface ExitPoint {
    position: Vec3;
    exitType: 'smooth' | 'fade' | 'instant';
    showCardSelection?: boolean;
    exitAnimation?: string;
    exitDelay?: number;
}

/**
 * Predefined map layer types with depth ordering
 */
export enum MapLayerType {
    BG_VeryFar = 'BG_VeryFar',
    BG_Far = 'BG_Far',
    BG_Mid = 'BG_Mid',
    BG_Close = 'BG_Close',
    BG_VeryClose = 'BG_VeryClose',
    Player = 'Player',
    FG_VeryClose = 'FG_VeryClose',
    FG_Close = 'FG_Close'
}

/**
 * Map layer configuration (renamed from Background)
 */
export interface MapLayer {
    id: string;
    prefabPath: string;
    layerType: MapLayerType;
    depth: number;
    scrollSpeed: Vec2;
    repeatMode: 'none' | 'vertical' | 'horizontal' | 'both';
    offset: Vec2;
    bounds: Rect;  // Which part of the sublevel this map covers
    isVisible: boolean;
    
    // Editor properties (not serialized in runtime)
    editorData?: {
        name: string;
        color: Color;
        locked: boolean;
    };
}

/**
 * Spawner configuration for entities
 */
export interface Spawner {
    id: string;
    name: string;
    position: Vec3;
    prefabPath: string;
    spawnPattern: SpawnPattern;
    pathId?: string;  // Optional path for spawned entities
    isActive: boolean;
    waves: Wave[];
    
    // Editor properties
    editorData?: {
        gizmoColor: Color;
        showPreview: boolean;
        notes: string;
    };
}

/**
 * Spawn pattern configuration
 */
export interface SpawnPattern {
    type: SpawnType;
    entities: SpawnableEntity[];
    count: number;
    interval: number;
    delay: number;
}

/**
 * Spawn types
 */
export enum SpawnType {
    Sequential = 'sequential',
    Random = 'random',
    Static = 'static'
}

/**
 * Entity that can be spawned
 */
export interface SpawnableEntity {
    prefabPath: string;
    weight: number;  // For random spawning
    pathId?: string;  // Override spawner's default path
}

/**
 * Wave configuration containing multiple spawners
 */
export interface Wave {
    id: string;
    name: string;
    startTime: number;
    endTime: number;
    spawnerConfigs: WaveSpawnerConfig[];
    isActive: boolean;
}

/**
 * Spawner configuration within a wave
 */
export interface WaveSpawnerConfig {
    spawnerId: string;
    spawnTime: number;
    delay?: number;
    interval?: number;
    overridePattern?: SpawnPattern;
}

/**
 * Path for entity movement
 */
export interface Path {
    id: string;
    name: string;
    points: BezierPoint[];
    isLoop: boolean;
    totalLength: number;
    
    // Editor properties
    editorData?: {
        color: Color;
        showDirection: boolean;
        showSpeed: boolean;
        notes: string;
    };
}

/**
 * Bezier curve point with control handles
 */
export interface BezierPoint {
    position: Vec3;
    controlPoint1: Vec3;
    controlPoint2: Vec3;
    speed: number;  // Speed when entity passes through this point
    rotation: number;  // Rotation for entity at this point
}

/**
 * Camera settings for the level
 */
export interface CameraSettings {
    viewportSize: Size;
    scrollSpeed: number;
    followTarget?: string;  // Entity ID to follow
    bounds?: Rect;  // Camera movement constraints
    smoothing: number;  // Camera movement smoothing
}

/**
 * Event graph containing event nodes and connections
 */
export interface EventGraph {
    nodes: Map<string, IEventNode>;
    connections: EventConnection[];
    triggers: EventTrigger[];
}

/**
 * Base interface for event nodes
 */
export interface IEventNode {
    // Runtime properties (serialized)
    id: string;
    type: string;
    properties: { [key: string]: any };
    inputs: EventPort[];
    outputs: EventPort[];
    
    // Editor properties (not serialized)
    editorData?: {
        position: Vec2;
        name: string;
        color: Color;
        collapsed: boolean;
        notes: string;
    };
}

/**
 * Event node input/output port
 */
export interface EventPort {
    name: string;
    type: string;
    isConnected: boolean;
    connections: EventConnection[];
}

/**
 * Connection between event nodes
 */
export interface EventConnection {
    id: string;
    sourceNodeId: string;
    sourcePortName: string;
    targetNodeId: string;
    targetPortName: string;
}

/**
 * Event trigger configuration
 */
export interface EventTrigger {
    id: string;
    type: 'time' | 'condition' | 'manual';
    condition: any;  // Specific to trigger type
    targetNodeId: string;
    isActive: boolean;
}

/**
 * Event execution context
 */
export interface EventContext {
    deltaTime: number;
    currentTime: number;
    level: ILevelData;
    subLevel?: SubLevel;
    triggerData?: any;
}
