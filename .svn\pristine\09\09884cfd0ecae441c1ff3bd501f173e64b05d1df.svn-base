import { _decorator, Component, Node, instantiate } from 'cc';
import { PersistNode } from '../PersistNode';
import { GameFactory } from './GameFactory';
const { ccclass, property } = _decorator;


export class AnimFactory extends GameFactory {
    public createAnim(): Node {
        let animTemp: Node = null;

        if(this.productPool.size() > 0) {
            animTemp = this.productPool.get();  //如果池里有敌机，就直接拿来用
        } else {
            animTemp = instantiate(this.persistNode.getComponent(PersistNode).animPreb);  //从常驻节点拿到预制体原料
        }

        return animTemp;
    } 
}

