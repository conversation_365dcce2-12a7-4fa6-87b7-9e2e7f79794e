{"version": 3, "sources": ["file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/base/SystemContainer.ts"], "names": ["_decorator", "TypeID", "TypeIDUtils", "ccclass", "SystemContainer", "_systems", "_systemRegistry", "TypedRegistry", "_updateContainer", "_lateUpdateC<PERSON>r", "_isInitialized", "registerSystem", "system", "systemName", "getSystemName", "typeId", "getTypeId", "has", "constructor", "console", "warn", "push", "register", "_initializeSystem", "log", "unregisterSystem", "systemConstructor", "get", "typeName", "getTypeName", "unInit", "index", "indexOf", "splice", "remove", "getSystem", "hasSystem", "getAllSystems", "getSystemCount", "length", "init", "for<PERSON>ach", "i", "clear", "update", "deltaTime", "lateUpdate", "setSystemEnabled", "enabled", "setEnabled", "isInitialized", "bind", "error"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;;AAEAC,MAAAA,M,iBAAAA,M;AAAQC,MAAAA,W,iBAAAA,W;;;;;;;;;OACX;AAAEC,QAAAA;AAAF,O,GAAcH,U;AAEpB;AACA;AACA;;AAIA;AACA;AACA;AACA;iCAEaI,e,WADZD,OAAO,CAAC,iBAAD,C,gBAAR,MACaC,eADb,CAC6B;AAAA;AAAA,eAEjBC,QAFiB,GAEI,EAFJ;AAAA,eAGjBC,eAHiB,GAGoC,IAAI;AAAA;AAAA,0CAAYC,aAAhB,EAHpC;AAAA,eAIjBC,gBAJiB,GAIoB,EAJpB;AAAA,eAKjBC,oBALiB,GAK4B,EAL5B;AAAA,eAMjBC,cANiB,GAMS,KANT;AAAA;;AAQzB;AACJ;AACA;AACA;AACA;AACWC,QAAAA,cAAc,CAACC,MAAD,EAA0B;AAC3C,cAAMC,UAAU,GAAGD,MAAM,CAACE,aAAP,EAAnB;AACA,cAAMC,MAAM,GAAGH,MAAM,CAACI,SAAP,EAAf;;AAEA,cAAI,KAAKV,eAAL,CAAqBW,GAArB,CAAyBL,MAAM,CAACM,WAAhC,CAAJ,EAAoF;AAChFC,YAAAA,OAAO,CAACC,IAAR,8BAAwCP,UAAxC,kBAA+DE,MAA/D;AACA,mBAAO,KAAP;AACH;;AAED,eAAKV,QAAL,CAAcgB,IAAd,CAAmBT,MAAnB;;AACA,eAAKN,eAAL,CAAqBgB,QAArB,CAA8BV,MAA9B,EAV2C,CAY3C;;;AACA,cAAI,KAAKF,cAAT,EAAyB;AACrB,iBAAKa,iBAAL,CAAuBX,MAAvB;AACH;;AAEDO,UAAAA,OAAO,CAACK,GAAR,yCAAkDX,UAAlD,kBAAyEE,MAAzE;AACA,iBAAO,IAAP;AACH;AAED;AACJ;AACA;AACA;AACA;;;AACWU,QAAAA,gBAAgB,CAAmBC,iBAAnB,EAA0E;AAC7F,cAAMd,MAAM,GAAG,KAAKN,eAAL,CAAqBqB,GAArB,CAAyBD,iBAAzB,CAAf;;AACA,cAAI,CAACd,MAAL,EAAa;AACT,gBAAMgB,QAAQ,GAAG;AAAA;AAAA,kCAAOC,WAAP,CAAmB;AAAA;AAAA,kCAAOF,GAAP,CAAWD,iBAAX,CAAnB,CAAjB;AACAP,YAAAA,OAAO,CAACC,IAAR,8BAAwCQ,QAAxC;AACA,mBAAO,KAAP;AACH;;AAED,cAAMf,UAAU,GAAGD,MAAM,CAACE,aAAP,EAAnB,CAR6F,CAU7F;;AACAF,UAAAA,MAAM,CAACkB,MAAP,GAX6F,CAa7F;;AACA,cAAMC,KAAK,GAAG,KAAK1B,QAAL,CAAc2B,OAAd,CAAsBpB,MAAtB,CAAd;;AACA,cAAImB,KAAK,IAAI,CAAb,EAAgB;AACZ,iBAAK1B,QAAL,CAAc4B,MAAd,CAAqBF,KAArB,EAA4B,CAA5B;;AACA,iBAAKvB,gBAAL,CAAsByB,MAAtB,CAA6BF,KAA7B,EAAoC,CAApC;;AACA,iBAAKtB,oBAAL,CAA0BwB,MAA1B,CAAiCF,KAAjC,EAAwC,CAAxC;AACH;;AAED,eAAKzB,eAAL,CAAqB4B,MAArB,CAA4BR,iBAA5B;;AAEAP,UAAAA,OAAO,CAACK,GAAR,2CAAoDX,UAApD;AACA,iBAAO,IAAP;AACH;AAED;AACJ;AACA;AACA;AACA;;;AACWsB,QAAAA,SAAS,CAAmBT,iBAAnB,EAA2E;AACvF,iBAAO,KAAKpB,eAAL,CAAqBqB,GAArB,CAAyBD,iBAAzB,CAAP;AACH;AAED;AACJ;AACA;AACA;AACA;;;AACWU,QAAAA,SAAS,CAAmBV,iBAAnB,EAA0E;AACtF,iBAAO,KAAKpB,eAAL,CAAqBW,GAArB,CAAyBS,iBAAzB,CAAP;AACH;AAED;AACJ;AACA;AACA;;;AACWW,QAAAA,aAAa,GAAa;AAC7B,iBAAO,CAAC,GAAG,KAAKhC,QAAT,CAAP;AACH;AAED;AACJ;AACA;AACA;;;AACWiC,QAAAA,cAAc,GAAW;AAC5B,iBAAO,KAAKjC,QAAL,CAAckC,MAArB;AACH;AAED;AACJ;AACA;;;AACWC,QAAAA,IAAI,GAAS;AAChB,cAAI,KAAK9B,cAAT,EAAyB;AACrBS,YAAAA,OAAO,CAACC,IAAR,CAAa,sCAAb;AACA;AACH;;AAEDD,UAAAA,OAAO,CAACK,GAAR,oCAA6C,KAAKnB,QAAL,CAAckC,MAA3D,eANgB,CAQhB;;AACA,eAAK/B,gBAAL,CAAsB+B,MAAtB,GAA+B,CAA/B;AACA,eAAK9B,oBAAL,CAA0B8B,MAA1B,GAAmC,CAAnC,CAVgB,CAYhB;;AACA,eAAKlC,QAAL,CAAcoC,OAAd,CAAsB7B,MAAM,IAAI;AAC5B,iBAAKW,iBAAL,CAAuBX,MAAvB;AACH,WAFD;;AAIA,eAAKF,cAAL,GAAsB,IAAtB;AACAS,UAAAA,OAAO,CAACK,GAAR,CAAY,0CAAZ;AACH;AAED;AACJ;AACA;;;AACWM,QAAAA,MAAM,GAAS;AAClB,cAAI,CAAC,KAAKpB,cAAV,EAA0B;AACtB;AACH;;AAEDS,UAAAA,OAAO,CAACK,GAAR,CAAY,sCAAZ,EALkB,CAOlB;;AACA,eAAK,IAAIkB,CAAC,GAAG,KAAKrC,QAAL,CAAckC,MAAd,GAAuB,CAApC,EAAuCG,CAAC,IAAI,CAA5C,EAA+CA,CAAC,EAAhD,EAAoD;AAChD,iBAAKrC,QAAL,CAAcqC,CAAd,EAAiBZ,MAAjB;AACH,WAViB,CAYlB;;;AACA,eAAKzB,QAAL,CAAckC,MAAd,GAAuB,CAAvB;;AACA,eAAKjC,eAAL,CAAqBqC,KAArB;;AACA,eAAKnC,gBAAL,CAAsB+B,MAAtB,GAA+B,CAA/B;AACA,eAAK9B,oBAAL,CAA0B8B,MAA1B,GAAmC,CAAnC;AAEA,eAAK7B,cAAL,GAAsB,KAAtB;AACAS,UAAAA,OAAO,CAACK,GAAR,CAAY,mCAAZ;AACH;AAED;AACJ;AACA;AACA;;;AACWoB,QAAAA,MAAM,CAACC,SAAD,EAA0B;AACnC,cAAI,CAAC,KAAKnC,cAAV,EAA0B;AACtB;AACH;;AAED,eAAK,IAAIgC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKlC,gBAAL,CAAsB+B,MAA1C,EAAkDG,CAAC,EAAnD,EAAuD;AACnD,iBAAKlC,gBAAL,CAAsBkC,CAAtB,EAAyBG,SAAzB;AACH;AACJ;AAED;AACJ;AACA;AACA;;;AACWC,QAAAA,UAAU,CAACD,SAAD,EAA0B;AACvC,cAAI,CAAC,KAAKnC,cAAV,EAA0B;AACtB;AACH;;AAED,eAAK,IAAIgC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKjC,oBAAL,CAA0B8B,MAA9C,EAAsDG,CAAC,EAAvD,EAA2D;AACvD,iBAAKjC,oBAAL,CAA0BiC,CAA1B,EAA6BG,SAA7B;AACH;AACJ;AAED;AACJ;AACA;AACA;AACA;AACA;;;AACWE,QAAAA,gBAAgB,CAAmBrB,iBAAnB,EAAiEsB,OAAjE,EAA4F;AAC/G,cAAMpC,MAAM,GAAG,KAAKN,eAAL,CAAqBqB,GAArB,CAAyBD,iBAAzB,CAAf;;AACA,cAAI,CAACd,MAAL,EAAa;AACT,gBAAMgB,QAAQ,GAAG;AAAA;AAAA,kCAAOC,WAAP,CAAmB;AAAA;AAAA,kCAAOF,GAAP,CAAWD,iBAAX,CAAnB,CAAjB;AACAP,YAAAA,OAAO,CAACC,IAAR,8BAAwCQ,QAAxC;AACA,mBAAO,KAAP;AACH;;AAED,cAAMf,UAAU,GAAGD,MAAM,CAACE,aAAP,EAAnB;AACAF,UAAAA,MAAM,CAACqC,UAAP,CAAkBD,OAAlB;AACA7B,UAAAA,OAAO,CAACK,GAAR,8BAAuCX,UAAvC,UAAqDmC,OAAO,GAAG,SAAH,GAAe,UAA3E;AACA,iBAAO,IAAP;AACH;AAED;AACJ;AACA;;;AACWE,QAAAA,aAAa,GAAY;AAC5B,iBAAO,KAAKxC,cAAZ;AACH;AAED;AACJ;AACA;AACA;;;AACYa,QAAAA,iBAAiB,CAACX,MAAD,EAAuB;AAC5C,cAAI;AACAA,YAAAA,MAAM,CAAC4B,IAAP;;AACA,iBAAKhC,gBAAL,CAAsBa,IAAtB,CAA2BT,MAAM,CAACgC,MAAP,CAAcO,IAAd,CAAmBvC,MAAnB,CAA3B;;AACA,iBAAKH,oBAAL,CAA0BY,IAA1B,CAA+BT,MAAM,CAACkC,UAAP,CAAkBK,IAAlB,CAAuBvC,MAAvB,CAA/B;;AACAO,YAAAA,OAAO,CAACK,GAAR,0CAAmDZ,MAAM,CAACE,aAAP,EAAnD;AACH,WALD,CAKE,OAAOsC,KAAP,EAAc;AACZjC,YAAAA,OAAO,CAACiC,KAAR,mDAA8DxC,MAAM,CAACE,aAAP,EAA9D,QAAyFsC,KAAzF;AACH;AACJ;;AAzNwB,O", "sourcesContent": ["import { _decorator } from \"cc\";\nimport { System } from \"./System\";\nimport { TypeID, TypeIDUtils } from \"./TypeID\";\nconst { ccclass } = _decorator;\n\n/**\n * Type definition for system update functions\n */\nexport type SystemUpdateFn = (deltaTime: number) => void;\nexport type SystemLateUpdateFn = (deltaTime: number) => void;\n\n/**\n * SystemContainer manages a collection of game systems\n * Similar to GameInstance's ManagerPool but specifically for game world systems\n */\n@ccclass(\"SystemContainer\")\nexport class SystemContainer {\n\n    private _systems: System[] = [];\n    private _systemRegistry: TypeIDUtils.TypedRegistry<System> = new TypeIDUtils.TypedRegistry<System>();\n    private _updateContainer: SystemUpdateFn[] = [];\n    private _lateUpdateContainer: SystemLateUpdateFn[] = [];\n    private _isInitialized: boolean = false;\n    \n    /**\n     * Register a system to the container\n     * @param system The system to register\n     * @returns true if registration was successful, false if system already exists\n     */\n    public registerSystem(system: System): boolean {\n        const systemName = system.getSystemName();\n        const typeId = system.getTypeId();\n\n        if (this._systemRegistry.has(system.constructor as new (...args: any[]) => System)) {\n            console.warn(`SystemContainer: System ${systemName} (TypeID: ${typeId}) is already registered`);\n            return false;\n        }\n\n        this._systems.push(system);\n        this._systemRegistry.register(system);\n\n        // If container is already initialized, initialize the new system immediately\n        if (this._isInitialized) {\n            this._initializeSystem(system);\n        }\n\n        console.log(`SystemContainer: Registered system ${systemName} (TypeID: ${typeId})`);\n        return true;\n    }\n    \n    /**\n     * Unregister a system from the container\n     * @param systemConstructor The constructor of the system to unregister\n     * @returns true if unregistration was successful, false if system not found\n     */\n    public unregisterSystem<T extends System>(systemConstructor: new (...args: any[]) => T): boolean {\n        const system = this._systemRegistry.get(systemConstructor);\n        if (!system) {\n            const typeName = TypeID.getTypeName(TypeID.get(systemConstructor));\n            console.warn(`SystemContainer: System ${typeName} not found`);\n            return false;\n        }\n\n        const systemName = system.getSystemName();\n\n        // Cleanup the system\n        system.unInit();\n\n        // Remove from collections\n        const index = this._systems.indexOf(system);\n        if (index >= 0) {\n            this._systems.splice(index, 1);\n            this._updateContainer.splice(index, 1);\n            this._lateUpdateContainer.splice(index, 1);\n        }\n\n        this._systemRegistry.remove(systemConstructor);\n\n        console.log(`SystemContainer: Unregistered system ${systemName}`);\n        return true;\n    }\n    \n    /**\n     * Get a system by type\n     * @param systemConstructor The constructor of the system to get\n     * @returns The system instance or null if not found\n     */\n    public getSystem<T extends System>(systemConstructor: new (...args: any[]) => T): T | null {\n        return this._systemRegistry.get(systemConstructor);\n    }\n\n    /**\n     * Check if a system is registered\n     * @param systemConstructor The constructor of the system to check\n     * @returns true if the system is registered\n     */\n    public hasSystem<T extends System>(systemConstructor: new (...args: any[]) => T): boolean {\n        return this._systemRegistry.has(systemConstructor);\n    }\n    \n    /**\n     * Get all registered systems\n     * @returns Array of all registered systems\n     */\n    public getAllSystems(): System[] {\n        return [...this._systems];\n    }\n    \n    /**\n     * Get the number of registered systems\n     * @returns The number of registered systems\n     */\n    public getSystemCount(): number {\n        return this._systems.length;\n    }\n    \n    /**\n     * Initialize all registered systems\n     */\n    public init(): void {\n        if (this._isInitialized) {\n            console.warn(\"SystemContainer: Already initialized\");\n            return;\n        }\n        \n        console.log(`SystemContainer: Initializing ${this._systems.length} systems`);\n        \n        // Clear update containers\n        this._updateContainer.length = 0;\n        this._lateUpdateContainer.length = 0;\n        \n        // Initialize all systems\n        this._systems.forEach(system => {\n            this._initializeSystem(system);\n        });\n        \n        this._isInitialized = true;\n        console.log(\"SystemContainer: Initialization complete\");\n    }\n    \n    /**\n     * Cleanup all systems\n     */\n    public unInit(): void {\n        if (!this._isInitialized) {\n            return;\n        }\n        \n        console.log(\"SystemContainer: Cleaning up systems\");\n        \n        // Cleanup all systems in reverse order\n        for (let i = this._systems.length - 1; i >= 0; i--) {\n            this._systems[i].unInit();\n        }\n        \n        // Clear all containers\n        this._systems.length = 0;\n        this._systemRegistry.clear();\n        this._updateContainer.length = 0;\n        this._lateUpdateContainer.length = 0;\n        \n        this._isInitialized = false;\n        console.log(\"SystemContainer: Cleanup complete\");\n    }\n    \n    /**\n     * Update all systems\n     * @param deltaTime Time elapsed since last frame in seconds\n     */\n    public update(deltaTime: number): void {\n        if (!this._isInitialized) {\n            return;\n        }\n        \n        for (let i = 0; i < this._updateContainer.length; i++) {\n            this._updateContainer[i](deltaTime);\n        }\n    }\n    \n    /**\n     * Late update all systems\n     * @param deltaTime Time elapsed since last frame in seconds\n     */\n    public lateUpdate(deltaTime: number): void {\n        if (!this._isInitialized) {\n            return;\n        }\n        \n        for (let i = 0; i < this._lateUpdateContainer.length; i++) {\n            this._lateUpdateContainer[i](deltaTime);\n        }\n    }\n    \n    /**\n     * Enable or disable a specific system\n     * @param systemConstructor The constructor of the system to enable/disable\n     * @param enabled Whether to enable or disable the system\n     * @returns true if the operation was successful\n     */\n    public setSystemEnabled<T extends System>(systemConstructor: new (...args: any[]) => T, enabled: boolean): boolean {\n        const system = this._systemRegistry.get(systemConstructor);\n        if (!system) {\n            const typeName = TypeID.getTypeName(TypeID.get(systemConstructor));\n            console.warn(`SystemContainer: System ${typeName} not found`);\n            return false;\n        }\n\n        const systemName = system.getSystemName();\n        system.setEnabled(enabled);\n        console.log(`SystemContainer: System ${systemName} ${enabled ? 'enabled' : 'disabled'}`);\n        return true;\n    }\n    \n    /**\n     * Check if the container is initialized\n     */\n    public isInitialized(): boolean {\n        return this._isInitialized;\n    }\n    \n    /**\n     * Initialize a single system and add it to update containers\n     * @param system The system to initialize\n     */\n    private _initializeSystem(system: System): void {\n        try {\n            system.init();\n            this._updateContainer.push(system.update.bind(system));\n            this._lateUpdateContainer.push(system.lateUpdate.bind(system));\n            console.log(`SystemContainer: Initialized system ${system.getSystemName()}`);\n        } catch (error) {\n            console.error(`SystemContainer: Failed to initialize system ${system.getSystemName()}:`, error);\n        }\n    }\n}\n"]}