{"version": 3, "sources": ["file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/bullet/BulletSystem.ts"], "names": ["_decorator", "Vec3", "System", "RegisterTypeID", "ccclass", "BulletSystem", "_bullets", "Map", "_bulletIdCounter", "_maxBullets", "_bulletPool", "getSystemName", "onInit", "console", "log", "_preallocateBulletPool", "onUnInit", "for<PERSON>ach", "bullet", "_destroyBullet", "clear", "length", "onUpdate", "deltaTime", "bulletsToRemove", "id", "position", "add", "multiplyScalar", "velocity", "lifetime", "maxLifetime", "push", "node", "setPosition", "removeBullet", "onLateUpdate", "_deltaTime", "createBullet", "direction", "config", "ownerId", "size", "warn", "_getBulletFromPool", "_generateBulletId", "set", "normalize", "speed", "damage", "bulletType", "bulletId", "get", "delete", "_returnBulletToPool", "getBullet", "getAllBullets", "Array", "from", "values", "getBulletsByOwner", "filter", "getBulletCount", "setMaxBullets", "maxBullets", "Math", "max", "clearAllBullets", "poolSize", "min", "i", "_createEmptyBullet", "pop", "undefined", "<PERSON><PERSON><PERSON><PERSON>", "destroy"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,I,OAAAA,I;;AACZC,MAAAA,M,iBAAAA,M;;AACAC,MAAAA,c,iBAAAA,c;;;;;;;;;OACH;AAAEC,QAAAA;AAAF,O,GAAcJ,U;AAEpB;AACA;AACA;;AAaA;AACA;AACA;;AASA;AACA;AACA;AACA;8BAGaK,Y,WAFZD,OAAO,CAAC,cAAD,C;;yEAAR,MAEaC,YAFb;AAAA;AAAA,4BAEyC;AAAA;AAAA;AAAA,eAE7BC,QAF6B,GAEO,IAAIC,GAAJ,EAFP;AAAA,eAG7BC,gBAH6B,GAGF,CAHE;AAAA,eAI7BC,WAJ6B,GAIP,IAJO;AAAA,eAK7BC,WAL6B,GAKD,EALC;AAAA;;AAOrC;AACJ;AACA;AACWC,QAAAA,aAAa,GAAW;AAC3B,iBAAO,cAAP;AACH;AAED;AACJ;AACA;;;AACcC,QAAAA,MAAM,GAAS;AACrBC,UAAAA,OAAO,CAACC,GAAR,CAAY,0CAAZ,EADqB,CAGrB;;AACA,eAAKC,sBAAL;;AAEAF,UAAAA,OAAO,CAACC,GAAR,CAAa,+CAA8C,KAAKL,WAAY,EAA5E;AACH;AAED;AACJ;AACA;;;AACcO,QAAAA,QAAQ,GAAS;AACvBH,UAAAA,OAAO,CAACC,GAAR,CAAY,yCAAZ,EADuB,CAGvB;;AACA,eAAKR,QAAL,CAAcW,OAAd,CAAsBC,MAAM,IAAI;AAC5B,iBAAKC,cAAL,CAAoBD,MAApB;AACH,WAFD;;AAIA,eAAKZ,QAAL,CAAcc,KAAd;;AACA,eAAKV,WAAL,CAAiBW,MAAjB,GAA0B,CAA1B;AACA,eAAKb,gBAAL,GAAwB,CAAxB;AAEAK,UAAAA,OAAO,CAACC,GAAR,CAAY,gCAAZ;AACH;AAED;AACJ;AACA;;;AACcQ,QAAAA,QAAQ,CAACC,SAAD,EAA0B;AACxC;AACA,gBAAMC,eAAyB,GAAG,EAAlC;;AAEA,eAAKlB,QAAL,CAAcW,OAAd,CAAsB,CAACC,MAAD,EAASO,EAAT,KAAgB;AAClC;AACAP,YAAAA,MAAM,CAACQ,QAAP,CAAgBC,GAAhB,CAAoB1B,IAAI,CAAC2B,cAAL,CAAoB,IAAI3B,IAAJ,EAApB,EAAgCiB,MAAM,CAACW,QAAvC,EAAiDN,SAAjD,CAApB,EAFkC,CAIlC;;AACAL,YAAAA,MAAM,CAACY,QAAP,IAAmBP,SAAnB,CALkC,CAOlC;;AACA,gBAAIL,MAAM,CAACY,QAAP,IAAmBZ,MAAM,CAACa,WAA9B,EAA2C;AACvCP,cAAAA,eAAe,CAACQ,IAAhB,CAAqBP,EAArB;AACH,aAViC,CAYlC;;;AACA,gBAAIP,MAAM,CAACe,IAAX,EAAiB;AACbf,cAAAA,MAAM,CAACe,IAAP,CAAYC,WAAZ,CAAwBhB,MAAM,CAACQ,QAA/B;AACH;AACJ,WAhBD,EAJwC,CAsBxC;;;AACAF,UAAAA,eAAe,CAACP,OAAhB,CAAwBQ,EAAE,IAAI;AAC1B,iBAAKU,YAAL,CAAkBV,EAAlB;AACH,WAFD;AAGH;AAED;AACJ;AACA;;;AACcW,QAAAA,YAAY,CAACC,UAAD,EAA2B,CAC7C;AACH;AAED;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;;;AACWC,QAAAA,YAAY,CAACZ,QAAD,EAAiBa,SAAjB,EAAkCC,MAAlC,EAAwDC,OAAxD,EAAwF;AACvG,cAAI,KAAKnC,QAAL,CAAcoC,IAAd,IAAsB,KAAKjC,WAA/B,EAA4C;AACxCI,YAAAA,OAAO,CAAC8B,IAAR,CAAa,0DAAb;AACA,mBAAO,IAAP;AACH,WAJsG,CAMvG;;;AACA,gBAAMzB,MAAM,GAAG,KAAK0B,kBAAL,EAAf,CAPuG,CASvG;;;AACA1B,UAAAA,MAAM,CAACO,EAAP,GAAY,KAAKoB,iBAAL,EAAZ;AACA3B,UAAAA,MAAM,CAACQ,QAAP,CAAgBoB,GAAhB,CAAoBpB,QAApB;AACAR,UAAAA,MAAM,CAACW,QAAP,GAAkB5B,IAAI,CAAC2B,cAAL,CAAoB,IAAI3B,IAAJ,EAApB,EAAgCsC,SAAS,CAACQ,SAAV,EAAhC,EAAuDP,MAAM,CAACQ,KAA9D,CAAlB;AACA9B,UAAAA,MAAM,CAAC+B,MAAP,GAAgBT,MAAM,CAACS,MAAvB;AACA/B,UAAAA,MAAM,CAACY,QAAP,GAAkB,CAAlB;AACAZ,UAAAA,MAAM,CAACa,WAAP,GAAqBS,MAAM,CAACV,QAA5B;AACAZ,UAAAA,MAAM,CAACgC,UAAP,GAAoBV,MAAM,CAACU,UAA3B;AACAhC,UAAAA,MAAM,CAACuB,OAAP,GAAiBA,OAAjB,CAjBuG,CAmBvG;;AACA,eAAKnC,QAAL,CAAcwC,GAAd,CAAkB5B,MAAM,CAACO,EAAzB,EAA6BP,MAA7B;;AAEAL,UAAAA,OAAO,CAACC,GAAR,CAAa,gCAA+BI,MAAM,CAACO,EAAG,cAAagB,OAAQ,EAA3E;AACA,iBAAOvB,MAAM,CAACO,EAAd;AACH;AAED;AACJ;AACA;AACA;AACA;;;AACWU,QAAAA,YAAY,CAACgB,QAAD,EAA4B;AAC3C,gBAAMjC,MAAM,GAAG,KAAKZ,QAAL,CAAc8C,GAAd,CAAkBD,QAAlB,CAAf;;AACA,cAAI,CAACjC,MAAL,EAAa;AACT,mBAAO,KAAP;AACH;;AAED,eAAKC,cAAL,CAAoBD,MAApB;;AACA,eAAKZ,QAAL,CAAc+C,MAAd,CAAqBF,QAArB;;AACA,eAAKG,mBAAL,CAAyBpC,MAAzB;;AAEA,iBAAO,IAAP;AACH;AAED;AACJ;AACA;AACA;AACA;;;AACWqC,QAAAA,SAAS,CAACJ,QAAD,EAAsC;AAClD,iBAAO,KAAK7C,QAAL,CAAc8C,GAAd,CAAkBD,QAAlB,KAA+B,IAAtC;AACH;AAED;AACJ;AACA;AACA;;;AACWK,QAAAA,aAAa,GAAiB;AACjC,iBAAOC,KAAK,CAACC,IAAN,CAAW,KAAKpD,QAAL,CAAcqD,MAAd,EAAX,CAAP;AACH;AAED;AACJ;AACA;AACA;AACA;;;AACWC,QAAAA,iBAAiB,CAACnB,OAAD,EAAgC;AACpD,iBAAOgB,KAAK,CAACC,IAAN,CAAW,KAAKpD,QAAL,CAAcqD,MAAd,EAAX,EAAmCE,MAAnC,CAA0C3C,MAAM,IAAIA,MAAM,CAACuB,OAAP,KAAmBA,OAAvE,CAAP;AACH;AAED;AACJ;AACA;AACA;;;AACWqB,QAAAA,cAAc,GAAW;AAC5B,iBAAO,KAAKxD,QAAL,CAAcoC,IAArB;AACH;AAED;AACJ;AACA;AACA;;;AACWqB,QAAAA,aAAa,CAACC,UAAD,EAA2B;AAC3C,eAAKvD,WAAL,GAAmBwD,IAAI,CAACC,GAAL,CAAS,CAAT,EAAYF,UAAZ,CAAnB;AACAnD,UAAAA,OAAO,CAACC,GAAR,CAAa,oCAAmC,KAAKL,WAAY,EAAjE;AACH;AAED;AACJ;AACA;;;AACW0D,QAAAA,eAAe,GAAS;AAC3B,eAAK7D,QAAL,CAAcW,OAAd,CAAsBC,MAAM,IAAI;AAC5B,iBAAKC,cAAL,CAAoBD,MAApB;;AACA,iBAAKoC,mBAAL,CAAyBpC,MAAzB;AACH,WAHD;;AAKA,eAAKZ,QAAL,CAAcc,KAAd;;AACAP,UAAAA,OAAO,CAACC,GAAR,CAAY,mCAAZ;AACH;AAED;AACJ;AACA;;;AACYC,QAAAA,sBAAsB,GAAS;AACnC,gBAAMqD,QAAQ,GAAGH,IAAI,CAACI,GAAL,CAAS,GAAT,EAAc,KAAK5D,WAAnB,CAAjB;;AAEA,eAAK,IAAI6D,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGF,QAApB,EAA8BE,CAAC,EAA/B,EAAmC;AAC/B,iBAAK5D,WAAL,CAAiBsB,IAAjB,CAAsB,KAAKuC,kBAAL,EAAtB;AACH;;AAED1D,UAAAA,OAAO,CAACC,GAAR,CAAa,+BAA8BsD,QAAS,kBAApD;AACH;AAED;AACJ;AACA;;;AACYxB,QAAAA,kBAAkB,GAAe;AACrC,iBAAO,KAAKlC,WAAL,CAAiB8D,GAAjB,MAA0B,KAAKD,kBAAL,EAAjC;AACH;AAED;AACJ;AACA;;;AACYjB,QAAAA,mBAAmB,CAACpC,MAAD,EAA2B;AAClD;AACAA,UAAAA,MAAM,CAACO,EAAP,GAAY,EAAZ;AACAP,UAAAA,MAAM,CAACQ,QAAP,CAAgBoB,GAAhB,CAAoB,CAApB,EAAuB,CAAvB,EAA0B,CAA1B;AACA5B,UAAAA,MAAM,CAACW,QAAP,CAAgBiB,GAAhB,CAAoB,CAApB,EAAuB,CAAvB,EAA0B,CAA1B;AACA5B,UAAAA,MAAM,CAAC+B,MAAP,GAAgB,CAAhB;AACA/B,UAAAA,MAAM,CAACY,QAAP,GAAkB,CAAlB;AACAZ,UAAAA,MAAM,CAACa,WAAP,GAAqB,CAArB;AACAb,UAAAA,MAAM,CAACgC,UAAP,GAAoB,EAApB;AACAhC,UAAAA,MAAM,CAACuB,OAAP,GAAiB,CAAjB;AACAvB,UAAAA,MAAM,CAACe,IAAP,GAAcwC,SAAd,CAVkD,CAYlD;;AACA,cAAI,KAAK/D,WAAL,CAAiBW,MAAjB,GAA0B,GAA9B,EAAmC;AAC/B,iBAAKX,WAAL,CAAiBsB,IAAjB,CAAsBd,MAAtB;AACH;AACJ;AAED;AACJ;AACA;;;AACYqD,QAAAA,kBAAkB,GAAe;AACrC,iBAAO;AACH9C,YAAAA,EAAE,EAAE,EADD;AAEHC,YAAAA,QAAQ,EAAE,IAAIzB,IAAJ,EAFP;AAGH4B,YAAAA,QAAQ,EAAE,IAAI5B,IAAJ,EAHP;AAIHgD,YAAAA,MAAM,EAAE,CAJL;AAKHnB,YAAAA,QAAQ,EAAE,CALP;AAMHC,YAAAA,WAAW,EAAE,CANV;AAOHmB,YAAAA,UAAU,EAAE,EAPT;AAQHT,YAAAA,OAAO,EAAE,CARN;AASHR,YAAAA,IAAI,EAAEwC;AATH,WAAP;AAWH;AAED;AACJ;AACA;;;AACY5B,QAAAA,iBAAiB,GAAW;AAChC,iBAAQ,UAAS,EAAE,KAAKrC,gBAAiB,EAAzC;AACH;AAED;AACJ;AACA;;;AACYW,QAAAA,cAAc,CAACD,MAAD,EAA2B;AAC7C,cAAIA,MAAM,CAACe,IAAP,IAAef,MAAM,CAACe,IAAP,CAAYyC,OAA/B,EAAwC;AACpCxD,YAAAA,MAAM,CAACe,IAAP,CAAY0C,OAAZ;AACAzD,YAAAA,MAAM,CAACe,IAAP,GAAcwC,SAAd;AACH;AACJ;;AAvQoC,O", "sourcesContent": ["import { _decorator, Vec3, Node } from \"cc\";\nimport { System } from \"../base/System\";\nimport { RegisterTypeID } from \"../base/TypeID\";\nconst { ccclass } = _decorator;\n\n/**\n * Bullet data structure\n */\nexport interface BulletData {\n    id: string;\n    position: Vec3;\n    velocity: Vec3;\n    damage: number;\n    lifetime: number;\n    maxLifetime: number;\n    bulletType: string;\n    ownerId: number;\n    node?: Node;\n}\n\n/**\n * Bullet configuration\n */\nexport interface BulletConfig {\n    speed: number;\n    damage: number;\n    lifetime: number;\n    bulletType: string;\n    size: number;\n}\n\n/**\n * BulletSystem - manages all bullets in the game world\n * Handles bullet creation, movement, collision, and cleanup\n */\n@ccclass(\"BulletSystem\")\n@RegisterTypeID\nexport class BulletSystem extends System {\n    \n    private _bullets: Map<string, BulletData> = new Map();\n    private _bulletIdCounter: number = 0;\n    private _maxBullets: number = 1000;\n    private _bulletPool: BulletData[] = [];\n    \n    /**\n     * Get the system name\n     */\n    public getSystemName(): string {\n        return \"BulletSystem\";\n    }\n    \n    /**\n     * Initialize the bullet system\n     */\n    protected onInit(): void {\n        console.log(\"BulletSystem: Initializing bullet system\");\n        \n        // Pre-allocate bullet pool for performance\n        this._preallocateBulletPool();\n        \n        console.log(`BulletSystem: Initialized with max bullets: ${this._maxBullets}`);\n    }\n    \n    /**\n     * Cleanup the bullet system\n     */\n    protected onUnInit(): void {\n        console.log(\"BulletSystem: Cleaning up bullet system\");\n        \n        // Destroy all bullets\n        this._bullets.forEach(bullet => {\n            this._destroyBullet(bullet);\n        });\n        \n        this._bullets.clear();\n        this._bulletPool.length = 0;\n        this._bulletIdCounter = 0;\n        \n        console.log(\"BulletSystem: Cleanup complete\");\n    }\n    \n    /**\n     * Update all bullets\n     */\n    protected onUpdate(deltaTime: number): void {\n        // Update bullet positions and lifetimes\n        const bulletsToRemove: string[] = [];\n        \n        this._bullets.forEach((bullet, id) => {\n            // Update position\n            bullet.position.add(Vec3.multiplyScalar(new Vec3(), bullet.velocity, deltaTime));\n            \n            // Update lifetime\n            bullet.lifetime += deltaTime;\n            \n            // Check if bullet should be removed\n            if (bullet.lifetime >= bullet.maxLifetime) {\n                bulletsToRemove.push(id);\n            }\n            \n            // Update visual node if exists\n            if (bullet.node) {\n                bullet.node.setPosition(bullet.position);\n            }\n        });\n        \n        // Remove expired bullets\n        bulletsToRemove.forEach(id => {\n            this.removeBullet(id);\n        });\n    }\n    \n    /**\n     * Late update - handle any post-update logic\n     */\n    protected onLateUpdate(_deltaTime: number): void {\n        // Could be used for collision detection, visual effects, etc.\n    }\n    \n    /**\n     * Create a new bullet\n     * @param position Starting position\n     * @param direction Direction vector (will be normalized)\n     * @param config Bullet configuration\n     * @param ownerId ID of the entity that created this bullet\n     * @returns The bullet ID or null if creation failed\n     */\n    public createBullet(position: Vec3, direction: Vec3, config: BulletConfig, ownerId: number): string | null {\n        if (this._bullets.size >= this._maxBullets) {\n            console.warn(\"BulletSystem: Cannot create bullet - max bullets reached\");\n            return null;\n        }\n        \n        // Get bullet from pool or create new one\n        const bullet = this._getBulletFromPool();\n        \n        // Set bullet properties\n        bullet.id = this._generateBulletId();\n        bullet.position.set(position);\n        bullet.velocity = Vec3.multiplyScalar(new Vec3(), direction.normalize(), config.speed);\n        bullet.damage = config.damage;\n        bullet.lifetime = 0;\n        bullet.maxLifetime = config.lifetime;\n        bullet.bulletType = config.bulletType;\n        bullet.ownerId = ownerId;\n        \n        // Add to active bullets\n        this._bullets.set(bullet.id, bullet);\n        \n        console.log(`BulletSystem: Created bullet ${bullet.id} for owner ${ownerId}`);\n        return bullet.id;\n    }\n    \n    /**\n     * Remove a bullet by ID\n     * @param bulletId The ID of the bullet to remove\n     * @returns true if the bullet was removed\n     */\n    public removeBullet(bulletId: string): boolean {\n        const bullet = this._bullets.get(bulletId);\n        if (!bullet) {\n            return false;\n        }\n        \n        this._destroyBullet(bullet);\n        this._bullets.delete(bulletId);\n        this._returnBulletToPool(bullet);\n        \n        return true;\n    }\n    \n    /**\n     * Get a bullet by ID\n     * @param bulletId The ID of the bullet to get\n     * @returns The bullet data or null if not found\n     */\n    public getBullet(bulletId: string): BulletData | null {\n        return this._bullets.get(bulletId) || null;\n    }\n    \n    /**\n     * Get all bullets\n     * @returns Array of all active bullets\n     */\n    public getAllBullets(): BulletData[] {\n        return Array.from(this._bullets.values());\n    }\n    \n    /**\n     * Get bullets by owner ID\n     * @param ownerId The owner ID to filter by\n     * @returns Array of bullets owned by the specified entity\n     */\n    public getBulletsByOwner(ownerId: number): BulletData[] {\n        return Array.from(this._bullets.values()).filter(bullet => bullet.ownerId === ownerId);\n    }\n    \n    /**\n     * Get the number of active bullets\n     * @returns The number of active bullets\n     */\n    public getBulletCount(): number {\n        return this._bullets.size;\n    }\n    \n    /**\n     * Set the maximum number of bullets\n     * @param maxBullets The new maximum number of bullets\n     */\n    public setMaxBullets(maxBullets: number): void {\n        this._maxBullets = Math.max(1, maxBullets);\n        console.log(`BulletSystem: Max bullets set to ${this._maxBullets}`);\n    }\n    \n    /**\n     * Clear all bullets\n     */\n    public clearAllBullets(): void {\n        this._bullets.forEach(bullet => {\n            this._destroyBullet(bullet);\n            this._returnBulletToPool(bullet);\n        });\n        \n        this._bullets.clear();\n        console.log(\"BulletSystem: All bullets cleared\");\n    }\n    \n    /**\n     * Pre-allocate bullet pool for performance\n     */\n    private _preallocateBulletPool(): void {\n        const poolSize = Math.min(100, this._maxBullets);\n        \n        for (let i = 0; i < poolSize; i++) {\n            this._bulletPool.push(this._createEmptyBullet());\n        }\n        \n        console.log(`BulletSystem: Pre-allocated ${poolSize} bullets in pool`);\n    }\n    \n    /**\n     * Get a bullet from the pool or create a new one\n     */\n    private _getBulletFromPool(): BulletData {\n        return this._bulletPool.pop() || this._createEmptyBullet();\n    }\n    \n    /**\n     * Return a bullet to the pool\n     */\n    private _returnBulletToPool(bullet: BulletData): void {\n        // Reset bullet data\n        bullet.id = \"\";\n        bullet.position.set(0, 0, 0);\n        bullet.velocity.set(0, 0, 0);\n        bullet.damage = 0;\n        bullet.lifetime = 0;\n        bullet.maxLifetime = 0;\n        bullet.bulletType = \"\";\n        bullet.ownerId = 0;\n        bullet.node = undefined;\n        \n        // Return to pool if not full\n        if (this._bulletPool.length < 100) {\n            this._bulletPool.push(bullet);\n        }\n    }\n    \n    /**\n     * Create an empty bullet data structure\n     */\n    private _createEmptyBullet(): BulletData {\n        return {\n            id: \"\",\n            position: new Vec3(),\n            velocity: new Vec3(),\n            damage: 0,\n            lifetime: 0,\n            maxLifetime: 0,\n            bulletType: \"\",\n            ownerId: 0,\n            node: undefined\n        };\n    }\n    \n    /**\n     * Generate a unique bullet ID\n     */\n    private _generateBulletId(): string {\n        return `bullet_${++this._bulletIdCounter}`;\n    }\n    \n    /**\n     * Destroy a bullet's visual representation\n     */\n    private _destroyBullet(bullet: BulletData): void {\n        if (bullet.node && bullet.node.isValid) {\n            bullet.node.destroy();\n            bullet.node = undefined;\n        }\n    }\n}\n"]}