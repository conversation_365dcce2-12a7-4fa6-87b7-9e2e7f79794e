import { Vec2, Vec3, Rect, Size, Color } from 'cc';

/**
 * Editor modes for the level editor
 */
export enum EditMode {
    Select = 'select',
    AddSpawner = 'add_spawner',
    EditPath = 'edit_path',
    AddBackground = 'add_background',
    EditEvents = 'edit_events',
    AddSubLevel = 'add_sublevel',
    EditSubLevel = 'edit_sublevel'
}

/**
 * Gizmo types for scene view editing
 */
export enum GizmoType {
    SubLevelBounds = 'sublevel_bounds',
    SpawnerPosition = 'spawner_position',
    PathPoint = 'path_point',
    PathControlPoint = 'path_control_point',
    EntryPoint = 'entry_point',
    ExitPoint = 'exit_point',
    CameraViewport = 'camera_viewport',
    ConnectionTrigger = 'connection_trigger'
}

/**
 * Event types for the event system
 */
export enum EventType {
    // Time-based events
    TimeReached = 'time_reached',
    
    // SubLevel events
    SubLevelEnter = 'sublevel_enter',
    SubLevelExit = 'sublevel_exit',
    
    // Entity events
    EntitySpawned = 'entity_spawned',
    EntityDestroyed = 'entity_destroyed',
    BossEnter = 'boss_enter',
    BossDefeated = 'boss_defeated',
    
    // Player events
    PlayerDamaged = 'player_damaged',
    PlayerPowerUp = 'player_powerup',
    
    // Custom events
    Custom = 'custom'
}

/**
 * Event action types
 */
export enum EventActionType {
    PlayAnimation = 'play_animation',
    PlaySound = 'play_sound',
    PlayTimeline = 'play_timeline',
    PlayEffect = 'play_effect',
    DestroyEffect = 'destroy_effect',
    ShowUI = 'show_ui',
    HideUI = 'hide_ui',
    SetCameraTarget = 'set_camera_target',
    TriggerSpawner = 'trigger_spawner',
    ModifySpawner = 'modify_spawner',
    Custom = 'custom'
}

/**
 * Path interpolation types
 */
export enum PathInterpolationType {
    Linear = 'linear',
    Bezier = 'bezier',
    CatmullRom = 'catmull_rom'
}

/**
 * Spawner states
 */
export enum SpawnerState {
    Inactive = 'inactive',
    Active = 'active',
    Paused = 'paused',
    Completed = 'completed'
}

/**
 * SubLevel states
 */
export enum SubLevelState {
    Unloaded = 'unloaded',
    Loading = 'loading',
    Loaded = 'loaded',
    Active = 'active',
    Unloading = 'unloading'
}

/**
 * Camera follow modes
 */
export enum CameraFollowMode {
    None = 'none',
    Player = 'player',
    Entity = 'entity',
    Path = 'path',
    AutoScroll = 'auto_scroll'
}

/**
 * Layer depth constants for map layers
 */
export const LayerDepths = {
    [MapLayerType.BG_VeryFar]: -1000,
    [MapLayerType.BG_Far]: -800,
    [MapLayerType.BG_Mid]: -600,
    [MapLayerType.BG_Close]: -400,
    [MapLayerType.BG_VeryClose]: -200,
    [MapLayerType.Player]: 0,
    [MapLayerType.FG_VeryClose]: 200,
    [MapLayerType.FG_Close]: 400
} as const;

/**
 * Editor preferences and settings
 */
export interface EditorSettings {
    // Grid settings
    gridEnabled: boolean;
    gridSize: number;
    snapToGrid: boolean;
    
    // Gizmo settings
    gizmoSize: number;
    showGizmoLabels: boolean;
    gizmoColors: { [key in GizmoType]: Color };
    
    // Preview settings
    showPreview: boolean;
    previewSpeed: number;
    showDebugInfo: boolean;
    
    // File settings
    autoSave: boolean;
    autoSaveInterval: number;
    backupCount: number;
    
    // Performance settings
    maxVisibleSubLevels: number;
    preloadDistance: number;
    unloadDelay: number;
}

/**
 * Selection information for editor
 */
export interface SelectionInfo {
    type: 'sublevel' | 'spawner' | 'path' | 'map' | 'event_node' | 'none';
    objectId: string;
    subLevelId?: string;
    additionalData?: any;
}

/**
 * Undo/Redo operation data
 */
export interface EditorOperation {
    type: string;
    description: string;
    timestamp: number;
    undoData: any;
    redoData: any;
}

/**
 * Path calculation utilities
 */
export class PathUtils {
    /**
     * Calculate bezier curve point at parameter t (0-1)
     */
    public static calculateBezierPoint(
        p0: Vec3, 
        p1: Vec3, 
        p2: Vec3, 
        p3: Vec3, 
        t: number
    ): Vec3 {
        const u = 1 - t;
        const tt = t * t;
        const uu = u * u;
        const uuu = uu * u;
        const ttt = tt * t;
        
        const result = new Vec3();
        result.x = uuu * p0.x + 3 * uu * t * p1.x + 3 * u * tt * p2.x + ttt * p3.x;
        result.y = uuu * p0.y + 3 * uu * t * p1.y + 3 * u * tt * p2.y + ttt * p3.y;
        result.z = uuu * p0.z + 3 * uu * t * p1.z + 3 * u * tt * p2.z + ttt * p3.z;
        
        return result;
    }
    
    /**
     * Calculate bezier curve tangent at parameter t (0-1)
     */
    public static calculateBezierTangent(
        p0: Vec3, 
        p1: Vec3, 
        p2: Vec3, 
        p3: Vec3, 
        t: number
    ): Vec3 {
        const u = 1 - t;
        const tt = t * t;
        const uu = u * u;
        
        const result = new Vec3();
        result.x = 3 * uu * (p1.x - p0.x) + 6 * u * t * (p2.x - p1.x) + 3 * tt * (p3.x - p2.x);
        result.y = 3 * uu * (p1.y - p0.y) + 6 * u * t * (p2.y - p1.y) + 3 * tt * (p3.y - p2.y);
        result.z = 3 * uu * (p1.z - p0.z) + 6 * u * t * (p2.z - p1.z) + 3 * tt * (p3.z - p2.z);
        
        return result.normalize();
    }
    
    /**
     * Calculate approximate length of bezier curve
     */
    public static calculateBezierLength(
        p0: Vec3, 
        p1: Vec3, 
        p2: Vec3, 
        p3: Vec3, 
        segments: number = 100
    ): number {
        let length = 0;
        let prevPoint = p0;
        
        for (let i = 1; i <= segments; i++) {
            const t = i / segments;
            const currentPoint = this.calculateBezierPoint(p0, p1, p2, p3, t);
            length += Vec3.distance(prevPoint, currentPoint);
            prevPoint = currentPoint;
        }
        
        return length;
    }
}

/**
 * Rectangle utilities
 */
export class RectUtils {
    /**
     * Check if two rectangles intersect
     */
    public static intersects(rect1: Rect, rect2: Rect): boolean {
        return !(rect1.x + rect1.width < rect2.x || 
                rect2.x + rect2.width < rect1.x || 
                rect1.y + rect1.height < rect2.y || 
                rect2.y + rect2.height < rect1.y);
    }
    
    /**
     * Check if rect1 contains rect2
     */
    public static contains(rect1: Rect, rect2: Rect): boolean {
        return rect1.x <= rect2.x && 
               rect1.y <= rect2.y && 
               rect1.x + rect1.width >= rect2.x + rect2.width && 
               rect1.y + rect1.height >= rect2.y + rect2.height;
    }
    
    /**
     * Get the center point of a rectangle
     */
    public static getCenter(rect: Rect): Vec2 {
        return new Vec2(rect.x + rect.width / 2, rect.y + rect.height / 2);
    }
    
    /**
     * Expand rectangle by margin
     */
    public static expand(rect: Rect, margin: number): Rect {
        return new Rect(
            rect.x - margin,
            rect.y - margin,
            rect.width + margin * 2,
            rect.height + margin * 2
        );
    }
}

/**
 * ID generation utilities
 */
export class IDGenerator {
    private static counter: number = 0;
    
    /**
     * Generate a unique ID with optional prefix
     */
    public static generate(prefix: string = 'id'): string {
        this.counter++;
        const timestamp = Date.now().toString(36);
        const random = Math.random().toString(36).substr(2, 5);
        return `${prefix}_${timestamp}_${random}_${this.counter}`;
    }
    
    /**
     * Generate a UUID-like string
     */
    public static generateUUID(): string {
        return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
            const r = Math.random() * 16 | 0;
            const v = c === 'x' ? r : (r & 0x3 | 0x8);
            return v.toString(16);
        });
    }
}

// Re-export MapLayerType from LevelData for convenience
import { MapLayerType } from './LevelData';
export { MapLayerType };
