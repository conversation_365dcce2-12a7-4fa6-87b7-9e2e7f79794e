{"version": 3, "sources": ["file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/GameFactory.ts"], "names": ["GameFactory", "_decorator", "NodePool", "find", "ccclass", "property", "constructor", "productPool", "persistNode", "createProduct", "productType", "recycleProduct", "product", "put"], "mappings": ";;;8FAIaA,W;;;;;;;;;AAJJC,MAAAA,U,OAAAA,U;AAA6BC,MAAAA,Q,OAAAA,Q;AAAUC,MAAAA,I,OAAAA,I;;;;;;;;;OAC1C;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBJ,U;;6BAGjBD,W,GAAN,MAAMA,WAAN,CAAkB;AAIdM,QAAAA,WAAW,GAAG;AAAA,eAHrBC,WAGqB,GAHG,IAGH;AAAA,eAFrBC,WAEqB,GAFD,IAEC;AACjB,eAAKD,WAAL,GAAmB,IAAIL,QAAJ,EAAnB,CADiB,CACsB;;AACvC,eAAKM,WAAL,GAAmBL,IAAI,CAAC,aAAD,CAAvB,CAFiB,CAEuB;AAC3C;;AAEMM,QAAAA,aAAa,CAACC,WAAD,EAA4B;AAC5C;AACA,iBAAO,IAAP;AACH;;AAEMC,QAAAA,cAAc,CAACC,OAAD,EAAgB;AACjC,eAAKL,WAAL,CAAiBM,GAAjB,CAAqBD,OAArB,EADiC,CACA;AACpC;;AAhBoB,O", "sourcesContent": ["import { _decorator, Component, Node, NodePool, find } from 'cc';\nconst { ccclass, property } = _decorator;\n\n\nexport class GameFactory {\n    productPool: NodePool = null;\n    persistNode: Node = null;\n\n    public constructor() {\n        this.productPool = new NodePool();     //建立节点池\n        this.persistNode = find(\"PersistNode\"); //获取常驻节点，常驻节点上会有我们制造产品的原料\n    }\n\n    public createProduct(productType: string): Node {\n        //具体实现交给子类\n        return null;\n    }\n\n    public recycleProduct(product: Node) {\n        this.productPool.put(product);   //回收产品，即放回节点池\n    }\n}\n\n"]}