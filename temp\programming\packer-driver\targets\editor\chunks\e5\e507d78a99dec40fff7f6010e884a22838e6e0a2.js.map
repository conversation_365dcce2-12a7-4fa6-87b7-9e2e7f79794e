{"version": 3, "sources": ["file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameInstance.ts"], "names": ["_decorator", "Component", "LubanMgr", "NetMgr", "ccclass", "GameInstance", "ManagerPool", "_updateContainer", "_lateUpdateC<PERSON>r", "_lubanMgr", "_netMgr", "GetInstance", "_instance", "onLoad", "push", "for<PERSON>ach", "manager", "init", "onUpdate", "bind", "onLateUpdate", "update", "deltaTime", "i", "length", "lateUpdate"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;;AAEZC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,M,iBAAAA,M;;;;;;;;;OACH;AAAEC,QAAAA;AAAF,O,GAAcJ,U;;8BAGPK,Y,WADZD,OAAO,CAAC,cAAD,C,2BAAR,MACaC,YADb,SACkCJ,SADlC,CAC4C;AAAA;AAAA;AAAA,eAMhCK,WANgC,GAMZ,EANY;AAAA,eAOhCC,gBAPgC,GAOF,EAPE;AAAA,eAQhCC,oBARgC,GAQM,EARN;AAAA,eAUhCC,SAVgC,GAUb,IAVa;AAAA,eAWhCC,OAXgC,GAWjB,IAXiB;AAAA;;AAGf,eAAXC,WAAW,GAAiB;AACtC,iBAAON,YAAY,CAACO,SAApB;AACH;;AAQDC,QAAAA,MAAM,GAAI;AACNR,UAAAA,YAAY,CAACO,SAAb,GAAyB,IAAzB;AAEA,eAAKH,SAAL,GAAiB;AAAA;AAAA,qCAAjB;AACA,eAAKH,WAAL,CAAiBQ,IAAjB,CAAsB,KAAKL,SAA3B;AACA,eAAKC,OAAL,GAAe;AAAA;AAAA,iCAAf;AACA,eAAKJ,WAAL,CAAiBQ,IAAjB,CAAsB,KAAKJ,OAA3B;AAEA,eAAKJ,WAAL,CAAiBS,OAAjB,CAAyBC,OAAO,IAAI;AAC/BA,YAAAA,OAAO,CAACC,IAAR;;AACA,iBAAKV,gBAAL,CAAsBO,IAAtB,CAA2BE,OAAO,CAACE,QAAR,CAAiBC,IAAjB,CAAsBH,OAAtB,CAA3B;;AACA,iBAAKR,oBAAL,CAA0BM,IAA1B,CAA+BE,OAAO,CAACI,YAAR,CAAqBD,IAArB,CAA0BH,OAA1B,CAA/B;AACH,WAJF;AAKH;;AACDK,QAAAA,MAAM,CAAEC,SAAF,EAAqB;AACvB,eAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKhB,gBAAL,CAAsBiB,MAA1C,EAAkDD,CAAC,EAAnD,EAAuD;AACnD,iBAAKhB,gBAAL,CAAsBgB,CAAtB,EAAyBD,SAAzB;AACH;AACJ;;AAEDG,QAAAA,UAAU,GAAG;AACT,eAAK,IAAIF,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKf,oBAAL,CAA0BgB,MAA9C,EAAsDD,CAAC,EAAvD,EAA2D;AACvD,iBAAKf,oBAAL,CAA0Be,CAA1B;AACH;AACJ;;AArCuC,O,UAEzBX,S,GAA0B,I", "sourcesContent": ["import { _decorator, Component } from \"cc\";\nimport { IMgr, FnOnUpdate, FnOnLateUpdate } from \"./IMgr\";\nimport { LubanMgr } from \"./Luban/LubanMgr\";\nimport { NetMgr } from \"./Network/NetMgr\";\nconst { ccclass } = _decorator;\n\n@ccclass(\"GameInstance\")\nexport class GameInstance extends Component {\n\n    private static _instance: GameInstance = null;\n    public static GetInstance(): GameInstance {\n        return GameInstance._instance;\n    }\n    private ManagerPool:IMgr[]= [];\n    private _updateContainer:FnOnUpdate[]=[];\n    private _lateUpdateContainer:FnOnLateUpdate[]=[];\n\n    private _lubanMgr:LubanMgr=null;\n    private _netMgr:NetMgr=null;\n\n    onLoad () {\n        GameInstance._instance = this; \n\n        this._lubanMgr = new LubanMgr();\n        this.ManagerPool.push(this._lubanMgr);\n        this._netMgr = new NetMgr();\n        this.ManagerPool.push(this._netMgr);\n\n        this.ManagerPool.forEach(manager => {\n             manager.init();\n             this._updateContainer.push(manager.onUpdate.bind(manager));\n             this._lateUpdateContainer.push(manager.onLateUpdate.bind(manager));\n         });\n    }\n    update (deltaTime: number) {\n        for (let i = 0; i < this._updateContainer.length; i++) {\n            this._updateContainer[i](deltaTime);\n        }\n    }\n\n    lateUpdate() {\n        for (let i = 0; i < this._lateUpdateContainer.length; i++) {\n            this._lateUpdateContainer[i]();\n        }\n    }\n}"]}