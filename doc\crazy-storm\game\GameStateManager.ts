/**
 * CrazyStorm 2.0 Integration for Cocos Creator
 * Game State Manager
 * 
 * This system manages game state synchronization between CrazyStorm
 * and the main game, including difficulty scaling, wave management,
 * and game progression.
 */

import { log, warn } from 'cc';

/**
 * Game state enumeration
 */
export enum GameState {
    Menu = 'Menu',
    Playing = 'Playing',
    Paused = 'Paused',
    GameOver = 'GameOver',
    Victory = 'Victory',
    Loading = 'Loading'
}

/**
 * Difficulty settings
 */
export interface DifficultySettings {
    name: string;
    particleMultiplier: number;
    speedMultiplier: number;
    healthMultiplier: number;
    scoreMultiplier: number;
    patternIntensity: number;
}

/**
 * Wave configuration
 */
export interface WaveConfig {
    id: number;
    name: string;
    enemies: Array<{
        type: string;
        count: number;
        spawnDelay: number;
        patterns: string[];
    }>;
    duration: number;
    difficulty: number;
}

/**
 * Game progression data
 */
export interface GameProgression {
    currentWave: number;
    totalWaves: number;
    score: number;
    lives: number;
    powerLevel: number;
    timeElapsed: number;
    enemiesDestroyed: number;
    bulletsDestroyed: number;
}

/**
 * Game state manager
 */
export class GameStateManager {
    private currentState: GameState = GameState.Menu;
    private previousState: GameState = GameState.Menu;
    
    // Difficulty system
    private difficulties: Map<string, DifficultySettings> = new Map();
    private currentDifficulty: DifficultySettings;
    
    // Wave system
    private waves: WaveConfig[] = [];
    private currentWaveIndex: number = 0;
    private waveStartTime: number = 0;
    
    // Game progression
    private progression: GameProgression;
    
    // State change callbacks
    private stateChangeCallbacks: Map<GameState, (() => void)[]> = new Map();
    private waveChangeCallback: ((wave: WaveConfig) => void) | null = null;
    private difficultyChangeCallback: ((difficulty: DifficultySettings) => void) | null = null;
    
    // Adaptive difficulty
    private adaptiveDifficulty: boolean = true;
    private performanceHistory: number[] = [];
    private difficultyAdjustmentCooldown: number = 30000; // 30 seconds
    private lastDifficultyAdjustment: number = 0;

    constructor() {
        this.initializeDifficulties();
        this.initializeProgression();
        this.initializeStateCallbacks();
    }

    /**
     * Update game state manager
     */
    public update(deltaTime: number): void {
        // Update progression time
        if (this.currentState === GameState.Playing) {
            this.progression.timeElapsed += deltaTime;
            
            // Check wave completion
            this.checkWaveCompletion();
            
            // Update adaptive difficulty
            if (this.adaptiveDifficulty) {
                this.updateAdaptiveDifficulty();
            }
        }
    }

    /**
     * Set game state
     */
    public setState(newState: GameState): void {
        if (newState === this.currentState) {
            return;
        }

        this.previousState = this.currentState;
        this.currentState = newState;

        // Execute state change callbacks
        const callbacks = this.stateChangeCallbacks.get(newState);
        if (callbacks) {
            callbacks.forEach(callback => {
                try {
                    callback();
                } catch (err) {
                    warn('GameStateManager: Error in state change callback:', err);
                }
            });
        }

        log(`GameStateManager: State changed from ${this.previousState} to ${this.currentState}`);
    }

    /**
     * Get current game state
     */
    public getState(): GameState {
        return this.currentState;
    }

    /**
     * Get previous game state
     */
    public getPreviousState(): GameState {
        return this.previousState;
    }

    /**
     * Start new game
     */
    public startNewGame(difficulty: string = 'Normal'): void {
        this.setDifficulty(difficulty);
        this.resetProgression();
        this.currentWaveIndex = 0;
        this.setState(GameState.Playing);
        this.startWave(0);
    }

    /**
     * Pause game
     */
    public pauseGame(): void {
        if (this.currentState === GameState.Playing) {
            this.setState(GameState.Paused);
        }
    }

    /**
     * Resume game
     */
    public resumeGame(): void {
        if (this.currentState === GameState.Paused) {
            this.setState(GameState.Playing);
        }
    }

    /**
     * End game
     */
    public endGame(victory: boolean = false): void {
        this.setState(victory ? GameState.Victory : GameState.GameOver);
    }

    /**
     * Set difficulty
     */
    public setDifficulty(difficultyName: string): boolean {
        const difficulty = this.difficulties.get(difficultyName);
        if (!difficulty) {
            warn(`GameStateManager: Unknown difficulty: ${difficultyName}`);
            return false;
        }

        this.currentDifficulty = difficulty;
        
        if (this.difficultyChangeCallback) {
            this.difficultyChangeCallback(difficulty);
        }

        log(`GameStateManager: Difficulty set to ${difficultyName}`);
        return true;
    }

    /**
     * Get current difficulty
     */
    public getDifficulty(): DifficultySettings {
        return { ...this.currentDifficulty };
    }

    /**
     * Add score
     */
    public addScore(points: number): void {
        const multipliedPoints = Math.floor(points * this.currentDifficulty.scoreMultiplier);
        this.progression.score += multipliedPoints;
    }

    /**
     * Add enemy destroyed
     */
    public addEnemyDestroyed(): void {
        this.progression.enemiesDestroyed++;
    }

    /**
     * Add bullet destroyed
     */
    public addBulletDestroyed(): void {
        this.progression.bulletsDestroyed++;
    }

    /**
     * Lose life
     */
    public loseLife(): boolean {
        this.progression.lives--;
        
        if (this.progression.lives <= 0) {
            this.endGame(false);
            return false;
        }
        
        return true;
    }

    /**
     * Gain life
     */
    public gainLife(): void {
        this.progression.lives++;
    }

    /**
     * Set power level
     */
    public setPowerLevel(level: number): void {
        this.progression.powerLevel = Math.max(0, Math.min(100, level));
    }

    /**
     * Get game progression
     */
    public getProgression(): GameProgression {
        return { ...this.progression };
    }

    /**
     * Start specific wave
     */
    public startWave(waveIndex: number): boolean {
        if (waveIndex < 0 || waveIndex >= this.waves.length) {
            warn(`GameStateManager: Invalid wave index: ${waveIndex}`);
            return false;
        }

        this.currentWaveIndex = waveIndex;
        this.waveStartTime = Date.now();
        this.progression.currentWave = waveIndex + 1;

        const wave = this.waves[waveIndex];
        
        if (this.waveChangeCallback) {
            this.waveChangeCallback(wave);
        }

        log(`GameStateManager: Started wave ${wave.id}: ${wave.name}`);
        return true;
    }

    /**
     * Get current wave
     */
    public getCurrentWave(): WaveConfig | null {
        if (this.currentWaveIndex >= 0 && this.currentWaveIndex < this.waves.length) {
            return { ...this.waves[this.currentWaveIndex] };
        }
        return null;
    }

    /**
     * Set state change callback
     */
    public onStateChange(state: GameState, callback: () => void): void {
        if (!this.stateChangeCallbacks.has(state)) {
            this.stateChangeCallbacks.set(state, []);
        }
        this.stateChangeCallbacks.get(state)!.push(callback);
    }

    /**
     * Set wave change callback
     */
    public onWaveChange(callback: (wave: WaveConfig) => void): void {
        this.waveChangeCallback = callback;
    }

    /**
     * Set difficulty change callback
     */
    public onDifficultyChange(callback: (difficulty: DifficultySettings) => void): void {
        this.difficultyChangeCallback = callback;
    }

    /**
     * Set adaptive difficulty enabled
     */
    public setAdaptiveDifficulty(enabled: boolean): void {
        this.adaptiveDifficulty = enabled;
        log(`GameStateManager: Adaptive difficulty ${enabled ? 'enabled' : 'disabled'}`);
    }

    /**
     * Add performance sample for adaptive difficulty
     */
    public addPerformanceSample(fps: number): void {
        this.performanceHistory.push(fps);
        
        // Keep only recent samples
        if (this.performanceHistory.length > 60) { // 1 minute at 1 sample per second
            this.performanceHistory.shift();
        }
    }

    /**
     * Get game statistics
     */
    public getStats(): {
        state: GameState;
        difficulty: string;
        progression: GameProgression;
        currentWave: WaveConfig | null;
        adaptiveDifficulty: boolean;
    } {
        return {
            state: this.currentState,
            difficulty: this.currentDifficulty.name,
            progression: this.getProgression(),
            currentWave: this.getCurrentWave(),
            adaptiveDifficulty: this.adaptiveDifficulty
        };
    }

    /**
     * Destroy game state manager
     */
    public destroy(): void {
        this.stateChangeCallbacks.clear();
        this.waveChangeCallback = null;
        this.difficultyChangeCallback = null;
        this.performanceHistory = [];
    }

    // Private methods

    /**
     * Initialize difficulty settings
     */
    private initializeDifficulties(): void {
        this.difficulties.set('Easy', {
            name: 'Easy',
            particleMultiplier: 0.7,
            speedMultiplier: 0.8,
            healthMultiplier: 1.5,
            scoreMultiplier: 0.8,
            patternIntensity: 0.6
        });

        this.difficulties.set('Normal', {
            name: 'Normal',
            particleMultiplier: 1.0,
            speedMultiplier: 1.0,
            healthMultiplier: 1.0,
            scoreMultiplier: 1.0,
            patternIntensity: 1.0
        });

        this.difficulties.set('Hard', {
            name: 'Hard',
            particleMultiplier: 1.3,
            speedMultiplier: 1.2,
            healthMultiplier: 0.7,
            scoreMultiplier: 1.5,
            patternIntensity: 1.4
        });

        this.difficulties.set('Insane', {
            name: 'Insane',
            particleMultiplier: 1.8,
            speedMultiplier: 1.5,
            healthMultiplier: 0.5,
            scoreMultiplier: 2.0,
            patternIntensity: 2.0
        });

        this.currentDifficulty = this.difficulties.get('Normal')!;
    }

    /**
     * Initialize game progression
     */
    private initializeProgression(): void {
        this.resetProgression();
    }

    /**
     * Reset game progression
     */
    private resetProgression(): void {
        this.progression = {
            currentWave: 1,
            totalWaves: this.waves.length,
            score: 0,
            lives: 3,
            powerLevel: 0,
            timeElapsed: 0,
            enemiesDestroyed: 0,
            bulletsDestroyed: 0
        };
    }

    /**
     * Initialize state callbacks
     */
    private initializeStateCallbacks(): void {
        Object.values(GameState).forEach(state => {
            this.stateChangeCallbacks.set(state as GameState, []);
        });
    }

    /**
     * Check wave completion
     */
    private checkWaveCompletion(): void {
        const currentWave = this.getCurrentWave();
        if (!currentWave) {
            return;
        }

        const elapsed = Date.now() - this.waveStartTime;
        if (elapsed >= currentWave.duration * 1000) {
            // Wave completed
            if (this.currentWaveIndex + 1 < this.waves.length) {
                this.startWave(this.currentWaveIndex + 1);
            } else {
                // All waves completed
                this.endGame(true);
            }
        }
    }

    /**
     * Update adaptive difficulty
     */
    private updateAdaptiveDifficulty(): void {
        const now = Date.now();
        if (now - this.lastDifficultyAdjustment < this.difficultyAdjustmentCooldown) {
            return;
        }

        if (this.performanceHistory.length < 10) {
            return; // Not enough data
        }

        const avgFPS = this.performanceHistory.reduce((a, b) => a + b, 0) / this.performanceHistory.length;
        
        // Adjust difficulty based on performance
        if (avgFPS < 30 && this.currentDifficulty.particleMultiplier > 0.5) {
            // Reduce difficulty
            this.currentDifficulty.particleMultiplier *= 0.9;
            this.currentDifficulty.patternIntensity *= 0.9;
            this.lastDifficultyAdjustment = now;
            log('GameStateManager: Reduced difficulty due to low performance');
        } else if (avgFPS > 55 && this.currentDifficulty.particleMultiplier < 2.0) {
            // Increase difficulty
            this.currentDifficulty.particleMultiplier *= 1.05;
            this.currentDifficulty.patternIntensity *= 1.05;
            this.lastDifficultyAdjustment = now;
            log('GameStateManager: Increased difficulty due to good performance');
        }
    }
}
