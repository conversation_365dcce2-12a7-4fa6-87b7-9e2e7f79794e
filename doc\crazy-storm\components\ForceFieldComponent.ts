/**
 * CrazyStorm 2.0 Integration for Cocos Creator
 * ForceField Component Implementation
 * 
 * This component applies forces to particles within a defined area,
 * creating effects like gravity wells, wind, magnetic fields, etc.
 */

import { _decorator, Vec2, Vec3, log, warn, Enum } from 'cc';
import { CrazyStormComponent } from './CrazyStormComponent';
import { Particle } from '../physics/ParticlePhysics';
import { 
    ForceFieldComponent as ForceFieldData,
    ForceFieldData as ForceFieldDataStruct,
    FieldShape,
    Reach,
    ForceType,
    ComponentType 
} from '../core/CrazyStormTypes';

const { ccclass, property } = _decorator;

/**
 * ForceField component for Cocos Creator
 */
@ccclass('ForceFieldComponent')
export class ForceFieldComponent extends CrazyStormComponent {
    @property({ displayName: "Half Width" })
    halfWidth: number = 50;

    @property({ displayName: "Half Height" })
    halfHeight: number = 50;

    @property({ type: Enum(FieldShape), displayName: "Field Shape" })
    fieldShape: FieldShape = FieldShape.Rectangle;

    @property({ type: Enum(Reach), displayName: "Reach" })
    reach: Reach = Reach.All;

    @property({ displayName: "Target Name" })
    targetName: string = '';

    @property({ displayName: "Force" })
    force: number = 1;

    @property({ displayName: "Direction" })
    direction: number = 0;

    @property({ type: Enum(ForceType), displayName: "Force Type" })
    forceType: ForceType = ForceType.Direction;

    @property({ displayName: "Debug Draw" })
    debugDraw: boolean = false;

    // Runtime data
    private forceFieldData: ForceFieldDataStruct | null = null;
    private particleManager: any = null; // Reference to particle manager

    protected initializeComponent(): void {
        // Initialize force field
        log(`ForceFieldComponent: Initialized at position [${this.node.position.x}, ${this.node.position.y}]`);
    }

    protected onDataInitialized(): void {
        if (!this.crazyStormData || this.crazyStormData.specificType !== ComponentType.ForceField) {
            warn('ForceFieldComponent: Invalid CrazyStorm data');
            return;
        }

        const data = this.crazyStormData as ForceFieldData;
        this.forceFieldData = data.forceFieldData;

        // Update component properties
        this.halfWidth = this.forceFieldData.halfWidth;
        this.halfHeight = this.forceFieldData.halfHeight;
        this.fieldShape = this.forceFieldData.fieldShape;
        this.reach = this.forceFieldData.reach;
        this.targetName = this.forceFieldData.targetName;
        this.force = this.forceFieldData.force;
        this.direction = this.forceFieldData.direction;
        this.forceType = this.forceFieldData.forceType;

        log(`ForceFieldComponent: Initialized with force ${this.force}, type ${ForceType[this.forceType]}`);
    }

    protected onReset(): void {
        // Reset force field state if needed
    }

    protected onUpdate(deltaTime: number): void {
        // Update force field properties from expressions
        this.updateForceFieldProperties();

        // Apply forces to particles in range
        this.applyForcesToParticles();
    }

    /**
     * Update force field properties from expressions
     */
    private updateForceFieldProperties(): void {
        // Update properties that might be driven by expressions
        this.halfWidth = this.getPropertyValue('HalfWidth', this.halfWidth);
        this.halfHeight = this.getPropertyValue('HalfHeight', this.halfHeight);
        this.force = this.getPropertyValue('Force', this.force);
        this.direction = this.getPropertyValue('Direction', this.direction);
    }

    /**
     * Apply forces to particles within the force field
     */
    private applyForcesToParticles(): void {
        // Get particles in range
        const particlesInRange = this.getParticlesInRange();

        // Apply force to each particle
        particlesInRange.forEach(particle => {
            if (particle.ignoreForce) {
                return;
            }

            // Check reach criteria
            if (!this.shouldAffectParticle(particle)) {
                return;
            }

            // Apply force based on force type
            this.applyForceToParticle(particle);
        });
    }

    /**
     * Get particles within the force field range
     */
    private getParticlesInRange(): Particle[] {
        // This would typically get particles from a particle manager
        // For now, we'll return an empty array as a placeholder
        const particles: Particle[] = [];

        // In a real implementation, this would query the particle manager:
        // const particles = this.particleManager.getParticlesInRect(
        //     this.node.position.x - this.halfWidth,
        //     this.node.position.x + this.halfWidth,
        //     this.node.position.y - this.halfHeight,
        //     this.node.position.y + this.halfHeight
        // );

        // Filter by shape
        return particles.filter(particle => this.isParticleInShape(particle));
    }

    /**
     * Check if particle is within the force field shape
     */
    private isParticleInShape(particle: Particle): boolean {
        const fieldPos = new Vec2(this.node.position.x, this.node.position.y);
        const particlePos = particle.position;

        switch (this.fieldShape) {
            case FieldShape.Rectangle:
                return Math.abs(particlePos.x - fieldPos.x) <= this.halfWidth &&
                       Math.abs(particlePos.y - fieldPos.y) <= this.halfHeight;

            case FieldShape.Circle:
                const distance = Math.sqrt(
                    Math.pow(particlePos.x - fieldPos.x, 2) +
                    Math.pow(particlePos.y - fieldPos.y, 2)
                );
                return distance <= this.halfWidth; // Use halfWidth as radius

            default:
                return false;
        }
    }

    /**
     * Check if the force field should affect this particle based on reach criteria
     */
    private shouldAffectParticle(particle: Particle): boolean {
        switch (this.reach) {
            case Reach.All:
                return true;

            case Reach.Layer:
                // Check if particle is from the target layer or same layer
                return particle.layerId === this.getLayerId() || 
                       this.getLayerName() === this.targetName;

            case Reach.Name:
                // Check if particle emitter name matches target name
                return this.getEmitterName(particle) === this.targetName;

            default:
                return false;
        }
    }

    /**
     * Apply force to a specific particle
     */
    private applyForceToParticle(particle: Particle): void {
        const fieldPos = new Vec2(this.node.position.x, this.node.position.y);
        const forceVector = new Vec2();

        switch (this.forceType) {
            case ForceType.Direction:
                // Apply force in a specific direction
                const radians = this.direction * Math.PI / 180;
                forceVector.set(
                    (this.force / particle.mass) * Math.cos(radians),
                    (this.force / particle.mass) * Math.sin(radians)
                );
                break;

            case ForceType.Inner:
                // Apply force towards the center of the field
                const toCenter = new Vec2(
                    fieldPos.x - particle.position.x,
                    fieldPos.y - particle.position.y
                );
                const distanceToCenter = Math.sqrt(toCenter.x * toCenter.x + toCenter.y * toCenter.y);
                
                if (distanceToCenter > 0) {
                    forceVector.set(
                        (toCenter.x / distanceToCenter) * (this.force / particle.mass),
                        (toCenter.y / distanceToCenter) * (this.force / particle.mass)
                    );
                }
                break;

            case ForceType.Outer:
                // Apply force away from the center of the field
                const fromCenter = new Vec2(
                    particle.position.x - fieldPos.x,
                    particle.position.y - fieldPos.y
                );
                const distanceFromCenter = Math.sqrt(fromCenter.x * fromCenter.x + fromCenter.y * fromCenter.y);
                
                if (distanceFromCenter > 0) {
                    forceVector.set(
                        (fromCenter.x / distanceFromCenter) * (this.force / particle.mass),
                        (fromCenter.y / distanceFromCenter) * (this.force / particle.mass)
                    );
                }
                break;
        }

        // Apply the calculated force
        particle.applyForce(forceVector);
    }

    /**
     * Set particle manager reference
     */
    public setParticleManager(manager: any): void {
        this.particleManager = manager;
    }

    /**
     * Get layer ID (placeholder implementation)
     */
    private getLayerId(): number {
        // This would return the actual layer ID
        return 0;
    }

    /**
     * Get layer name (placeholder implementation)
     */
    private getLayerName(): string {
        // This would return the actual layer name
        return 'Main';
    }

    /**
     * Get emitter name for a particle (placeholder implementation)
     */
    private getEmitterName(particle: Particle): string {
        // This would return the actual emitter name
        return `Emitter_${particle.emitterId}`;
    }

    /**
     * Set force field parameters at runtime
     */
    public setForceParameters(force: number, direction: number, forceType: ForceType): void {
        this.force = force;
        this.direction = direction;
        this.forceType = forceType;
    }

    /**
     * Set field size at runtime
     */
    public setFieldSize(halfWidth: number, halfHeight: number): void {
        this.halfWidth = halfWidth;
        this.halfHeight = halfHeight;
    }

    /**
     * Set field shape at runtime
     */
    public setFieldShape(shape: FieldShape): void {
        this.fieldShape = shape;
    }

    /**
     * Get field bounds for debugging
     */
    public getFieldBounds(): { min: Vec2; max: Vec2 } {
        const center = new Vec2(this.node.position.x, this.node.position.y);
        return {
            min: new Vec2(center.x - this.halfWidth, center.y - this.halfHeight),
            max: new Vec2(center.x + this.halfWidth, center.y + this.halfHeight)
        };
    }

    /**
     * Check if a point is within the force field
     */
    public isPointInField(point: Vec2): boolean {
        const fieldPos = new Vec2(this.node.position.x, this.node.position.y);

        switch (this.fieldShape) {
            case FieldShape.Rectangle:
                return Math.abs(point.x - fieldPos.x) <= this.halfWidth &&
                       Math.abs(point.y - fieldPos.y) <= this.halfHeight;

            case FieldShape.Circle:
                const distance = Math.sqrt(
                    Math.pow(point.x - fieldPos.x, 2) +
                    Math.pow(point.y - fieldPos.y, 2)
                );
                return distance <= this.halfWidth;

            default:
                return false;
        }
    }

    /**
     * Calculate force at a specific point
     */
    public getForceAtPoint(point: Vec2): Vec2 {
        if (!this.isPointInField(point)) {
            return new Vec2(0, 0);
        }

        const fieldPos = new Vec2(this.node.position.x, this.node.position.y);
        const forceVector = new Vec2();

        switch (this.forceType) {
            case ForceType.Direction:
                const radians = this.direction * Math.PI / 180;
                forceVector.set(
                    this.force * Math.cos(radians),
                    this.force * Math.sin(radians)
                );
                break;

            case ForceType.Inner:
                const toCenter = new Vec2(
                    fieldPos.x - point.x,
                    fieldPos.y - point.y
                );
                const distanceToCenter = Math.sqrt(toCenter.x * toCenter.x + toCenter.y * toCenter.y);
                
                if (distanceToCenter > 0) {
                    forceVector.set(
                        (toCenter.x / distanceToCenter) * this.force,
                        (toCenter.y / distanceToCenter) * this.force
                    );
                }
                break;

            case ForceType.Outer:
                const fromCenter = new Vec2(
                    point.x - fieldPos.x,
                    point.y - fieldPos.y
                );
                const distanceFromCenter = Math.sqrt(fromCenter.x * fromCenter.x + fromCenter.y * fromCenter.y);
                
                if (distanceFromCenter > 0) {
                    forceVector.set(
                        (fromCenter.x / distanceFromCenter) * this.force,
                        (fromCenter.y / distanceFromCenter) * this.force
                    );
                }
                break;
        }

        return forceVector;
    }
}
