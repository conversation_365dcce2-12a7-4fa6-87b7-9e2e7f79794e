System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, instantiate, Enemy, Global, PersistNode, GameFactory, EnemyFactory, _crd, ccclass, property;

  function _reportPossibleCrUseOfEnemy(extras) {
    _reporterNs.report("Enemy", "../Enemy", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGlobal(extras) {
    _reporterNs.report("Global", "../Global", _context.meta, extras);
  }

  function _reportPossibleCrUseOfPersistNode(extras) {
    _reporterNs.report("PersistNode", "../PersistNode", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameFactory(extras) {
    _reporterNs.report("GameFactory", "./GameFactory", _context.meta, extras);
  }

  _export("EnemyFactory", void 0);

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      instantiate = _cc.instantiate;
    }, function (_unresolved_2) {
      Enemy = _unresolved_2.Enemy;
    }, function (_unresolved_3) {
      Global = _unresolved_3.Global;
    }, function (_unresolved_4) {
      PersistNode = _unresolved_4.PersistNode;
    }, function (_unresolved_5) {
      GameFactory = _unresolved_5.GameFactory;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "11ec9lvMN5Fzrs3glZ4IW0i", "EnemyFactory", undefined);

      __checkObsolete__(['_decorator', 'Component', 'Node', 'instantiate']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("EnemyFactory", EnemyFactory = class EnemyFactory extends (_crd && GameFactory === void 0 ? (_reportPossibleCrUseOfGameFactory({
        error: Error()
      }), GameFactory) : GameFactory) {
        createProduct(productType) {
          var enemyTemp = null;

          if (this.productPool.size() > 0) {
            enemyTemp = this.productPool.get(); //如果池里有敌机，就直接拿来用
          } else {
            enemyTemp = instantiate(this.persistNode.getComponent(_crd && PersistNode === void 0 ? (_reportPossibleCrUseOfPersistNode({
              error: Error()
            }), PersistNode) : PersistNode).enemyPreb); //从常驻节点拿到预制体原料
          }

          switch (productType) {
            case (_crd && Global === void 0 ? (_reportPossibleCrUseOfGlobal({
              error: Error()
            }), Global) : Global).ENEMY_1:
              enemyTemp.getComponent(_crd && Enemy === void 0 ? (_reportPossibleCrUseOfEnemy({
                error: Error()
              }), Enemy) : Enemy).init(productType, this.persistNode.getComponent(_crd && PersistNode === void 0 ? (_reportPossibleCrUseOfPersistNode({
                error: Error()
              }), PersistNode) : PersistNode).enemy1);
              break;

            case (_crd && Global === void 0 ? (_reportPossibleCrUseOfGlobal({
              error: Error()
            }), Global) : Global).ENEMY_2:
              enemyTemp.getComponent(_crd && Enemy === void 0 ? (_reportPossibleCrUseOfEnemy({
                error: Error()
              }), Enemy) : Enemy).init(productType, this.persistNode.getComponent(_crd && PersistNode === void 0 ? (_reportPossibleCrUseOfPersistNode({
                error: Error()
              }), PersistNode) : PersistNode).enemy2);
              break;
          }

          return enemyTemp;
        }

      });

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=3edc50e04e7f27a742741db9a2f6912ace73edaf.js.map