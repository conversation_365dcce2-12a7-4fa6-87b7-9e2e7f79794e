{"version": 3, "sources": ["file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/IMgr.ts"], "names": ["_decorator", "ccclass", "property", "IMgr", "init", "unInit", "onUpdate", "dt", "onLateUpdate"], "mappings": ";;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;;;;;;;;;OACH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBF,U;;sBAKjBG,I,WADZF,OAAO,CAAC,MAAD,C,gBAAR,MACaE,IADb,CACkB;AACdC,QAAAA,IAAI,GAAG,CACN;;AACDC,QAAAA,MAAM,GAAG,CACR;;AACDC,QAAAA,QAAQ,CAACC,EAAD,EAAY,CACnB;;AACDC,QAAAA,YAAY,CAACD,EAAD,EAAY,CACvB;;AARa,O", "sourcesContent": ["import { _decorator, Component, Node } from \"cc\";\r\nconst { ccclass, property } = _decorator;\r\n\r\nexport type FnOnUpdate = (dt: number) => void;\r\nexport type FnOnLateUpdate = () => void;\r\n@ccclass(\"IMgr\")\r\nexport class IMgr {\r\n    init() {\r\n    }\r\n    unInit() {\r\n    }\r\n    onUpdate(dt:number) {\r\n    }\r\n    onLateUpdate(dt:number) {\r\n    }\r\n}\r\n"]}