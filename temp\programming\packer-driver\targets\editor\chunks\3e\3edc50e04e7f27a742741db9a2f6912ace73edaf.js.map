{"version": 3, "sources": ["file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/EnemyFactory.ts"], "names": ["EnemyFactory", "_decorator", "instantiate", "Enemy", "Global", "PersistNode", "GameFactory", "ccclass", "property", "createProduct", "productType", "enemyTemp", "productPool", "size", "get", "persistNode", "getComponent", "enemyPreb", "ENEMY_1", "init", "enemy1", "ENEMY_2", "enemy2"], "mappings": ";;;iJAOaA,Y;;;;;;;;;;;;;;;;;;;;;;;;;;;AAPJC,MAAAA,U,OAAAA,U;AAA6BC,MAAAA,W,OAAAA,W;;AAC7BC,MAAAA,K,iBAAAA,K;;AACAC,MAAAA,M,iBAAAA,M;;AACAC,MAAAA,W,iBAAAA,W;;AACAC,MAAAA,W,iBAAAA,W;;;;;;;;;OACH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBP,U;;8BAEjBD,Y,GAAN,MAAMA,YAAN;AAAA;AAAA,sCAAsC;AAElCS,QAAAA,aAAa,CAACC,WAAD,EAA4B;AAC5C,cAAIC,SAAe,GAAG,IAAtB;;AAEA,cAAG,KAAKC,WAAL,CAAiBC,IAAjB,KAA0B,CAA7B,EAAgC;AAC5BF,YAAAA,SAAS,GAAG,KAAKC,WAAL,CAAiBE,GAAjB,EAAZ,CAD4B,CACS;AACxC,WAFD,MAEO;AACHH,YAAAA,SAAS,GAAGT,WAAW,CAAC,KAAKa,WAAL,CAAiBC,YAAjB;AAAA;AAAA,4CAA2CC,SAA5C,CAAvB,CADG,CAC6E;AACnF;;AAED,kBAAOP,WAAP;AACI,iBAAK;AAAA;AAAA,kCAAOQ,OAAZ;AACIP,cAAAA,SAAS,CAACK,YAAV;AAAA;AAAA,kCAA8BG,IAA9B,CAAmCT,WAAnC,EAAgD,KAAKK,WAAL,CAAiBC,YAAjB;AAAA;AAAA,8CAA2CI,MAA3F;AACA;;AACJ,iBAAK;AAAA;AAAA,kCAAOC,OAAZ;AACIV,cAAAA,SAAS,CAACK,YAAV;AAAA;AAAA,kCAA8BG,IAA9B,CAAmCT,WAAnC,EAAgD,KAAKK,WAAL,CAAiBC,YAAjB;AAAA;AAAA,8CAA2CM,MAA3F;AACA;AANR;;AASA,iBAAOX,SAAP;AACH;;AArBwC,O", "sourcesContent": ["import { _decorator, Component, Node, instantiate } from 'cc';\nimport { Enemy } from '../Enemy';\nimport { Global } from '../Global';\nimport { PersistNode } from '../PersistNode';\nimport { GameFactory } from './GameFactory';\nconst { ccclass, property } = _decorator;\n\nexport class EnemyFactory extends GameFactory{\n    \n    public createProduct(productType: string): Node {\n        let enemyTemp: Node = null;\n\n        if(this.productPool.size() > 0) {\n            enemyTemp = this.productPool.get();  //如果池里有敌机，就直接拿来用\n        } else {\n            enemyTemp = instantiate(this.persistNode.getComponent(PersistNode).enemyPreb);  //从常驻节点拿到预制体原料\n        }\n\n        switch(productType) {\n            case Global.ENEMY_1:\n                enemyTemp.getComponent(Enemy).init(productType, this.persistNode.getComponent(PersistNode).enemy1);\n                break;\n            case Global.ENEMY_2:\n                enemyTemp.getComponent(Enemy).init(productType, this.persistNode.getComponent(PersistNode).enemy2);\n                break;\n        }\n\n        return enemyTemp;\n    } \n}\n\n"]}