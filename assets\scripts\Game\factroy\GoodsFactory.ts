import { _decorator, Component, Node, instantiate } from 'cc';
import { Global } from '../Global';
import { Goods } from '../Goods';
import { PersistNode } from '../PersistNode';
import { GameFactory } from './GameFactory';
const { ccclass, property } = _decorator;

export class GoodsFactory extends GameFactory {


    public createProduct(productType: string): Node {
        let goodsTemp: Node = null;

        if(this.productPool.size() > 0) {
            goodsTemp = this.productPool.get();  //如果池里有物资，就直接拿来用
        } else {
            goodsTemp = instantiate(this.persistNode.getComponent(PersistNode).goodsPreb);  //从常驻节点拿到预制体原料
        }

        switch(productType) {
            case Global.BLOOD_GOODS:
                goodsTemp.getComponent(Goods).init(productType, this.persistNode.getComponent(PersistNode).bloodGoods);
                break;
            case Global.LIGHT_GOODS:
                goodsTemp.getComponent(Goods).init(productType, this.persistNode.getComponent(PersistNode).lightGoods);
                break;
            case Global.MISSILE_GOODS:
                goodsTemp.getComponent(Goods).init(productType, this.persistNode.getComponent(PersistNode).missileGoods);
                break;
        }

        return goodsTemp;
    }

}

