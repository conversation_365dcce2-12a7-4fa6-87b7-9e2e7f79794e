{"version": 3, "sources": ["file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/index.ts"], "names": ["createQuickWorldSetup", "levelId", "gameMode", "GameMode", "STORY", "difficulty", "DifficultyLevel", "NORMAL", "WorldInitializeData", "modeId", "randomSeed", "Date", "now", "createWorldWithCommonSystems", "world", "World", "registerSystem", "BulletSystem", "LevelSystem", "PlayerSystem", "System", "SystemContainer", "TypeID", "RegisterTypeID", "TypedBase", "TypeIDUtils", "CObject", "WorldState", "Bootstrap", "Emitter", "EmitterArc", "LevelEventType", "PlayerState", "WORLD_SYSTEM_VERSION"], "mappings": ";;;;;AAoEA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,WAASA,qBAAT,CACHC,OADG,EAEHC,QAAkB,GAAGC,QAAQ,CAACC,KAF3B,EAGHC,UAA2B,GAAGC,eAAe,CAACC,MAH3C,EAIgB;AACnB,WAAO,IAAIC,mBAAJ,CAAwB;AAC3BC,MAAAA,MAAM,EAAEP,QADmB;AAE3BD,MAAAA,OAAO,EAAEA,OAFkB;AAG3BI,MAAAA,UAAU,EAAEA,UAHe;AAI3BK,MAAAA,UAAU,EAAEC,IAAI,CAACC,GAAL;AAJe,KAAxB,CAAP;AAMH;AAED;AACA;AACA;AACA;;;AACO,WAASC,4BAAT,GAA+C;AAClD,UAAMC,KAAK,GAAG,IAAIC,KAAJ,EAAd,CADkD,CAGlD;;AACAD,IAAAA,KAAK,CAACE,cAAN,CAAqB,IAAIC,YAAJ,EAArB;AACAH,IAAAA,KAAK,CAACE,cAAN,CAAqB,IAAIE,WAAJ,EAArB;AACAJ,IAAAA,KAAK,CAACE,cAAN,CAAqB,IAAIG,YAAJ,EAArB;AAEA,WAAOL,KAAP;AACH;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;2BAjHgBd,qB;kCAiBAa;;;;;;;qCApFPO,M;;+CACAC,e;;;8BAEAC,M;sCAAQC,c;iCAAgBC;;;2CAExBC,W;;uCACAC,O;;;6BAGAX,K;kCAAOY;;;;2CAIZnB,mB;gCACAL,Q;uCACAG;;;yCAYKsB,S;;4CAGAX,Y;;wCAIAY,O;;2CACAC,U;;;;;;;;;;;oCAKAZ,W;uCAAaa;;;;qCAQbZ,Y;oCAAca;;;;;;;AAxDvB;AACA;AACA;AACA;AACA;AACA;AAEA;AASA;AAGA;AAeA;AAGA;AAIA;AAIA;;;AAkBA;AACA;AACA;sCACaC,oB,GAAuB,O", "sourcesContent": ["/**\n * World System Index\n * \n * This file exports all components of the World mechanism for easy importing\n * throughout the game codebase.\n */\n\n// Base system components\nexport { System } from \"./base/System\";\nexport { SystemContainer } from \"./base/SystemContainer\";\nexport type { SystemUpdateFn, SystemLateUpdateFn } from \"./base/SystemContainer\";\nexport { TypeID, RegisterTypeID, TypedBase } from \"./base/TypeID\";\nexport type { ITyped } from \"./base/TypeID\";\nexport { TypeIDUtils } from \"./base/TypeID\";\nexport { CObject } from \"./base/Object\";\n\n// World core\nexport { World, WorldState } from \"./base/World\";\n\n// World initialization data\nexport {\n    WorldInitializeData,\n    GameMode,\n    DifficultyLevel\n} from \"./WorldInitializeData\";\nexport type {\n    IWorldInitializeData,\n    WorldPhysicsConfig,\n    WorldRenderConfig,\n    PlayerConfig,\n    LevelConfig,\n    DebugFlags\n} from \"./WorldInitializeData\";\n\n// Bootstrap component\nexport { Bootstrap } from \"./Bootstrap\";\n\n// Example systems\nexport { BulletSystem } from \"./bullet/BulletSystem\";\nexport type { BulletData, BulletConfig } from \"./bullet/BulletSystem\";\n\n// Bullet emitters\nexport { Emitter } from \"./bullet/Emitter\";\nexport { EmitterArc } from \"./bullet/EmitterArc\";\n\n// Gizmo system\nexport * from \"../gizmos\";\n\nexport { LevelSystem, LevelEventType } from \"./level/LevelSystem\";\nexport type { \n    LevelEvent, \n    Checkpoint, \n    LevelObjective, \n    LevelConfig as LevelSystemConfig \n} from \"./level/LevelSystem\";\n\nexport { PlayerSystem, PlayerState } from \"./player/PlayerSystem\";\nexport type { \n    PlayerStats, \n    PlayerInput, \n    PlayerData \n} from \"./player/PlayerSystem\";\n\n/**\n * Version information for the World system\n */\nexport const WORLD_SYSTEM_VERSION = \"1.0.0\";\n\n/**\n * Quick start helper function to create a basic world setup\n * @param levelId The level ID to initialize\n * @param gameMode The game mode (defaults to STORY)\n * @param difficulty The difficulty level (defaults to NORMAL)\n * @returns WorldInitializeData configured with the provided parameters\n */\nexport function createQuickWorldSetup(\n    levelId: string,\n    gameMode: GameMode = GameMode.STORY,\n    difficulty: DifficultyLevel = DifficultyLevel.NORMAL\n): WorldInitializeData {\n    return new WorldInitializeData({\n        modeId: gameMode,\n        levelId: levelId,\n        difficulty: difficulty,\n        randomSeed: Date.now()\n    });\n}\n\n/**\n * Helper function to create a world with common systems pre-registered\n * @returns A new World instance with BulletSystem, LevelSystem, and PlayerSystem registered\n */\nexport function createWorldWithCommonSystems(): World {\n    const world = new World();\n\n    // Register common systems\n    world.registerSystem(new BulletSystem());\n    world.registerSystem(new LevelSystem());\n    world.registerSystem(new PlayerSystem());\n\n    return world;\n}\n\n/**\n * Example usage documentation\n * \n * Basic usage:\n * ```typescript\n * import { Bootstrap, createQuickWorldSetup } from \"./Game/world\";\n * \n * // Add Bootstrap component to a node in the scene\n * const bootstrap = node.addComponent(Bootstrap);\n * bootstrap.levelId = \"level_001\";\n * bootstrap.gameMode = GameMode.STORY;\n * bootstrap.autoStart = true;\n * ```\n * \n * Advanced usage:\n * ```typescript\n * import {\n *     World,\n *     WorldInitializeData,\n *     GameMode,\n *     BulletSystem,\n *     LevelSystem,\n *     PlayerSystem\n * } from \"./Game/world\";\n *\n * // Create world manually\n * const world = new World();\n *\n * // Register systems\n * world.registerSystem(new BulletSystem());\n * world.registerSystem(new LevelSystem());\n * world.registerSystem(new PlayerSystem());\n *\n * // Initialize world\n * const initData = new WorldInitializeData({\n *     levelId: \"custom_level\",\n *     modeId: GameMode.CUSTOM\n * });\n *\n * world.initialize(initData).then(success => {\n *     if (success) {\n *         console.log(\"World initialized successfully\");\n *\n *         // Get systems using TypeID\n *         const bulletSystem = world.getSystem(BulletSystem);\n *         const playerSystem = world.getSystem(PlayerSystem);\n *\n *         // Start game loop\n *         const updateLoop = (deltaTime: number) => {\n *             world.update(deltaTime);\n *             world.lateUpdate(deltaTime);\n *         };\n *     }\n * });\n * ```\n * \n * Custom system creation:\n * ```typescript\n * import { System } from \"./Game/world\";\n * \n * class CustomSystem extends System {\n *     getSystemName(): string {\n *         return \"CustomSystem\";\n *     }\n *     \n *     protected onInit(): void {\n *         console.log(\"Custom system initialized\");\n *     }\n *     \n *     protected onUnInit(): void {\n *         console.log(\"Custom system cleaned up\");\n *     }\n *     \n *     protected onUpdate(deltaTime: number): void {\n *         // Custom update logic\n *     }\n *     \n *     protected onLateUpdate(deltaTime: number): void {\n *         // Custom late update logic\n *     }\n * }\n * \n * // Register to world\n * world.registerSystem(new CustomSystem());\n * ```\n */\n"]}