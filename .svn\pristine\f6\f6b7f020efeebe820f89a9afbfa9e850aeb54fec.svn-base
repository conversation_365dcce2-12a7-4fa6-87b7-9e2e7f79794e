/**
 * CrazyStorm 2.0 Integration for Cocos Creator
 * Particle System Manager
 * 
 * This manager handles multiple particle systems, coordinating their
 * execution and managing their lifecycle.
 */

import { Node, log, warn, error } from 'cc';
import { CrazyStormFile, ParticleSystem } from '../core/CrazyStormTypes';
import { LayerManager } from './LayerManager';
import { ResourceManager } from './ResourceManager';
import { GlobalVariableManager } from './GlobalVariableManager';
import { RuntimeConfig } from './CrazyStormManager';

/**
 * Runtime particle system instance
 */
export class RuntimeParticleSystem {
    public name: string;
    public node: Node;
    public data: ParticleSystem;
    public isPlaying: boolean = false;
    public currentFrame: number = 0;
    public totalFrame: number = 0;

    constructor(name: string, data: ParticleSystem, parentNode: Node) {
        this.name = name;
        this.data = data;
        this.node = new Node(name);
        parentNode.addChild(this.node);
        
        // Calculate total frame from layers
        this.totalFrame = this.calculateTotalFrame();
    }

    /**
     * Calculate total frame duration from all layers
     */
    private calculateTotalFrame(): number {
        let maxFrame = 0;
        this.data.layers.forEach(layer => {
            const layerEndFrame = layer.beginFrame + layer.totalFrame;
            maxFrame = Math.max(maxFrame, layerEndFrame);
        });
        return maxFrame;
    }

    /**
     * Update particle system
     */
    public update(globalFrame: number): void {
        if (!this.isPlaying) {
            return;
        }

        this.currentFrame = globalFrame;

        // Check if we've reached the end
        if (this.currentFrame >= this.totalFrame) {
            this.currentFrame = 0; // Loop or stop based on configuration
        }
    }

    /**
     * Play particle system
     */
    public play(): void {
        this.isPlaying = true;
        this.currentFrame = 0;
        log(`RuntimeParticleSystem: Playing ${this.name}`);
    }

    /**
     * Stop particle system
     */
    public stop(): void {
        this.isPlaying = false;
        this.currentFrame = 0;
        log(`RuntimeParticleSystem: Stopped ${this.name}`);
    }

    /**
     * Reset particle system
     */
    public reset(): void {
        this.currentFrame = 0;
        log(`RuntimeParticleSystem: Reset ${this.name}`);
    }

    /**
     * Destroy particle system
     */
    public destroy(): void {
        if (this.node && this.node.isValid) {
            this.node.destroy();
        }
    }
}

/**
 * Manager for all particle systems
 */
export class ParticleSystemManager {
    private parentNode: Node;
    private layerManager: LayerManager;
    private resourceManager: ResourceManager;
    private globalVariableManager: GlobalVariableManager;
    private config: RuntimeConfig;

    private particleSystems: Map<string, RuntimeParticleSystem> = new Map();
    private activeParticleCount: number = 0;
    private maxParticles: number = 5000;
    private collisionEnabled: boolean = true;

    constructor(
        parentNode: Node,
        layerManager: LayerManager,
        resourceManager: ResourceManager,
        globalVariableManager: GlobalVariableManager,
        config: RuntimeConfig
    ) {
        this.parentNode = parentNode;
        this.layerManager = layerManager;
        this.resourceManager = resourceManager;
        this.globalVariableManager = globalVariableManager;
        this.config = config;
        this.maxParticles = config.maxParticles;
        this.collisionEnabled = config.enableCollision;
    }

    /**
     * Initialize from CrazyStorm file
     */
    public initializeFromFile(file: CrazyStormFile): void {
        // Clear existing particle systems
        this.clear();

        // Create runtime particle systems
        file.particleSystems.forEach((systemData, index) => {
            const systemName = systemData.name || `ParticleSystem_${index}`;
            const runtimeSystem = new RuntimeParticleSystem(systemName, systemData, this.parentNode);
            
            this.particleSystems.set(systemName, runtimeSystem);
            
            // Initialize layers for this particle system
            this.layerManager.initializeSystemLayers(systemName, systemData.layers, runtimeSystem.node);
        });

        log(`ParticleSystemManager: Initialized ${this.particleSystems.size} particle systems`);
    }

    /**
     * Update all particle systems
     */
    public update(globalFrame: number): void {
        this.activeParticleCount = 0;

        this.particleSystems.forEach(system => {
            system.update(globalFrame);
            
            if (system.isPlaying) {
                // Update layers for this system
                this.layerManager.updateSystemLayers(system.name, system.currentFrame);
                
                // Count active particles (this would be implemented with actual particle counting)
                this.activeParticleCount += this.getSystemParticleCount(system);
            }
        });

        // Enforce particle limit
        if (this.activeParticleCount > this.maxParticles) {
            this.enforceParticleLimit();
        }
    }

    /**
     * Play all particle systems
     */
    public playAll(): void {
        this.particleSystems.forEach(system => {
            system.play();
        });
        log('ParticleSystemManager: Playing all particle systems');
    }

    /**
     * Stop all particle systems
     */
    public stopAll(): void {
        this.particleSystems.forEach(system => {
            system.stop();
        });
        log('ParticleSystemManager: Stopped all particle systems');
    }

    /**
     * Reset all particle systems
     */
    public resetAll(): void {
        this.particleSystems.forEach(system => {
            system.reset();
        });
        this.layerManager.resetAll();
        log('ParticleSystemManager: Reset all particle systems');
    }

    /**
     * Play specific particle system
     */
    public playSystem(name: string): boolean {
        const system = this.particleSystems.get(name);
        if (system) {
            system.play();
            return true;
        }
        warn(`ParticleSystemManager: Particle system not found: ${name}`);
        return false;
    }

    /**
     * Stop specific particle system
     */
    public stopSystem(name: string): boolean {
        const system = this.particleSystems.get(name);
        if (system) {
            system.stop();
            return true;
        }
        warn(`ParticleSystemManager: Particle system not found: ${name}`);
        return false;
    }

    /**
     * Get particle system by name
     */
    public getSystem(name: string): RuntimeParticleSystem | null {
        return this.particleSystems.get(name) || null;
    }

    /**
     * Get all particle system names
     */
    public getSystemNames(): string[] {
        return Array.from(this.particleSystems.keys());
    }

    /**
     * Get number of particle systems
     */
    public getSystemCount(): number {
        return this.particleSystems.size;
    }

    /**
     * Get active particle count across all systems
     */
    public getActiveParticleCount(): number {
        return this.activeParticleCount;
    }

    /**
     * Set maximum particle count
     */
    public setMaxParticles(maxParticles: number): void {
        this.maxParticles = maxParticles;
        log(`ParticleSystemManager: Set max particles to ${maxParticles}`);
    }

    /**
     * Set collision detection enabled
     */
    public setCollisionEnabled(enabled: boolean): void {
        this.collisionEnabled = enabled;
        log(`ParticleSystemManager: Collision detection ${enabled ? 'enabled' : 'disabled'}`);
    }

    /**
     * Check collision with point (for player collision)
     */
    public checkCollisionWithPoint(x: number, y: number, radius: number): boolean {
        if (!this.collisionEnabled) {
            return false;
        }

        // This would check collision with all active particles
        // For now, return false as placeholder
        return false;
    }

    /**
     * Get particles in area
     */
    public getParticlesInArea(x: number, y: number, width: number, height: number): any[] {
        // This would return particles in the specified area
        // For now, return empty array as placeholder
        return [];
    }

    /**
     * Clear all particles
     */
    public clearAllParticles(): void {
        this.particleSystems.forEach(system => {
            // Clear particles for each system
            this.layerManager.clearSystemParticles(system.name);
        });
        this.activeParticleCount = 0;
        log('ParticleSystemManager: Cleared all particles');
    }

    /**
     * Get system statistics
     */
    public getSystemStats(name: string): {
        isPlaying: boolean;
        currentFrame: number;
        totalFrame: number;
        layerCount: number;
        particleCount: number;
    } | null {
        const system = this.particleSystems.get(name);
        if (!system) {
            return null;
        }

        return {
            isPlaying: system.isPlaying,
            currentFrame: system.currentFrame,
            totalFrame: system.totalFrame,
            layerCount: system.data.layers.length,
            particleCount: this.getSystemParticleCount(system)
        };
    }

    /**
     * Destroy all particle systems
     */
    public destroy(): void {
        this.particleSystems.forEach(system => {
            system.destroy();
        });
        this.particleSystems.clear();
        log('ParticleSystemManager: Destroyed all particle systems');
    }

    // Private helper methods

    /**
     * Clear all particle systems
     */
    private clear(): void {
        this.particleSystems.forEach(system => {
            system.destroy();
        });
        this.particleSystems.clear();
    }

    /**
     * Get particle count for a specific system
     */
    private getSystemParticleCount(system: RuntimeParticleSystem): number {
        // This would count actual particles in the system
        // For now, return 0 as placeholder
        return 0;
    }

    /**
     * Enforce particle limit by removing oldest particles
     */
    private enforceParticleLimit(): void {
        // This would remove oldest particles to stay under the limit
        // Implementation would depend on the actual particle management system
        warn(`ParticleSystemManager: Particle limit exceeded (${this.activeParticleCount}/${this.maxParticles})`);
    }
}
