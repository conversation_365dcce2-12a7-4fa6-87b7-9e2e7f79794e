import { _decorator, Node, Vec3 } from "cc";
import { System } from "../base/System";
import { RegisterTypeID } from "../base/TypeID";
const { ccclass } = _decorator;

/**
 * Level event types
 */
export enum LevelEventType {
    LEVEL_START = "level_start",
    LEVEL_COMPLETE = "level_complete",
    LEVEL_FAILED = "level_failed",
    CHECKPOINT_REACHED = "checkpoint_reached",
    ENEMY_SPAWNED = "enemy_spawned",
    ITEM_COLLECTED = "item_collected"
}

/**
 * Level event data
 */
export interface LevelEvent {
    type: LevelEventType;
    timestamp: number;
    data: any;
}

/**
 * Checkpoint data
 */
export interface Checkpoint {
    id: string;
    position: Vec3;
    isReached: boolean;
    timestamp?: number;
}

/**
 * Level objective
 */
export interface LevelObjective {
    id: string;
    description: string;
    type: string;
    targetValue: number;
    currentValue: number;
    isCompleted: boolean;
}

/**
 * Level configuration
 */
export interface LevelConfig {
    levelId: string;
    name: string;
    description: string;
    timeLimit: number; // in seconds, 0 = no limit
    objectives: LevelObjective[];
    checkpoints: Checkpoint[];
    spawnPoints: Vec3[];
    boundaries: {
        minX: number;
        maxX: number;
        minY: number;
        maxY: number;
    };
}

/**
 * LevelSystem - manages level state, objectives, checkpoints, and events
 */
@ccclass("LevelSystem")
@RegisterTypeID
export class LevelSystem extends System {
    
    private _currentLevel: LevelConfig | null = null;
    private _levelStartTime: number = 0;
    private _levelElapsedTime: number = 0;
    private _isLevelActive: boolean = false;
    private _isLevelCompleted: boolean = false;
    private _isLevelFailed: boolean = false;
    
    // Event system
    private _eventHistory: LevelEvent[] = [];
    private _eventCallbacks: Map<LevelEventType, ((event: LevelEvent) => void)[]> = new Map();
    
    /**
     * Get the system name
     */
    public getSystemName(): string {
        return "LevelSystem";
    }
    
    /**
     * Initialize the level system
     */
    protected onInit(): void {
        console.log("LevelSystem: Initializing level system");
        
        // Initialize event callback maps
        Object.values(LevelEventType).forEach(eventType => {
            this._eventCallbacks.set(eventType as LevelEventType, []);
        });
        
        console.log("LevelSystem: Initialized");
    }
    
    /**
     * Cleanup the level system
     */
    protected onUnInit(): void {
        console.log("LevelSystem: Cleaning up level system");
        
        this._currentLevel = null;
        this._levelStartTime = 0;
        this._levelElapsedTime = 0;
        this._isLevelActive = false;
        this._isLevelCompleted = false;
        this._isLevelFailed = false;
        this._eventHistory.length = 0;
        this._eventCallbacks.clear();
        
        console.log("LevelSystem: Cleanup complete");
    }
    
    /**
     * Update the level system
     */
    protected onUpdate(deltaTime: number): void {
        if (!this._isLevelActive || !this._currentLevel) {
            return;
        }
        
        // Update elapsed time
        this._levelElapsedTime += deltaTime;
        
        // Check time limit
        if (this._currentLevel.timeLimit > 0 && this._levelElapsedTime >= this._currentLevel.timeLimit) {
            this.failLevel("Time limit exceeded");
            return;
        }
        
        // Check if all objectives are completed
        if (this._areAllObjectivesCompleted()) {
            this.completeLevel();
        }
    }
    
    /**
     * Late update - handle any post-update logic
     */
    protected onLateUpdate(deltaTime: number): void {
        // Could be used for UI updates, statistics, etc.
    }
    
    /**
     * Load and start a level
     * @param levelConfig The level configuration to load
     * @returns true if the level was loaded successfully
     */
    public loadLevel(levelConfig: LevelConfig): boolean {
        if (this._isLevelActive) {
            console.warn("LevelSystem: Cannot load level - another level is already active");
            return false;
        }
        
        console.log(`LevelSystem: Loading level ${levelConfig.levelId}`);
        
        // Set current level
        this._currentLevel = this._cloneLevelConfig(levelConfig);
        
        // Reset state
        this._levelStartTime = Date.now();
        this._levelElapsedTime = 0;
        this._isLevelActive = true;
        this._isLevelCompleted = false;
        this._isLevelFailed = false;
        this._eventHistory.length = 0;
        
        // Reset checkpoints
        this._currentLevel.checkpoints.forEach(checkpoint => {
            checkpoint.isReached = false;
            checkpoint.timestamp = undefined;
        });
        
        // Reset objectives
        this._currentLevel.objectives.forEach(objective => {
            objective.currentValue = 0;
            objective.isCompleted = false;
        });
        
        // Emit level start event
        this._emitEvent(LevelEventType.LEVEL_START, {
            levelId: levelConfig.levelId,
            startTime: this._levelStartTime
        });
        
        console.log(`LevelSystem: Level ${levelConfig.levelId} loaded and started`);
        return true;
    }
    
    /**
     * Complete the current level
     */
    public completeLevel(): void {
        if (!this._isLevelActive || this._isLevelCompleted || this._isLevelFailed) {
            return;
        }
        
        this._isLevelCompleted = true;
        this._isLevelActive = false;
        
        console.log(`LevelSystem: Level ${this._currentLevel?.levelId} completed`);
        
        this._emitEvent(LevelEventType.LEVEL_COMPLETE, {
            levelId: this._currentLevel?.levelId,
            completionTime: this._levelElapsedTime,
            objectivesCompleted: this._currentLevel?.objectives.filter(obj => obj.isCompleted).length,
            totalObjectives: this._currentLevel?.objectives.length
        });
    }
    
    /**
     * Fail the current level
     * @param reason The reason for failure
     */
    public failLevel(reason: string): void {
        if (!this._isLevelActive || this._isLevelCompleted || this._isLevelFailed) {
            return;
        }
        
        this._isLevelFailed = true;
        this._isLevelActive = false;
        
        console.log(`LevelSystem: Level ${this._currentLevel?.levelId} failed: ${reason}`);
        
        this._emitEvent(LevelEventType.LEVEL_FAILED, {
            levelId: this._currentLevel?.levelId,
            reason: reason,
            elapsedTime: this._levelElapsedTime
        });
    }
    
    /**
     * Reach a checkpoint
     * @param checkpointId The ID of the checkpoint to reach
     * @returns true if the checkpoint was reached successfully
     */
    public reachCheckpoint(checkpointId: string): boolean {
        if (!this._currentLevel) {
            return false;
        }
        
        const checkpoint = this._currentLevel.checkpoints.find(cp => cp.id === checkpointId);
        if (!checkpoint || checkpoint.isReached) {
            return false;
        }
        
        checkpoint.isReached = true;
        checkpoint.timestamp = this._levelElapsedTime;
        
        console.log(`LevelSystem: Checkpoint ${checkpointId} reached`);
        
        this._emitEvent(LevelEventType.CHECKPOINT_REACHED, {
            checkpointId: checkpointId,
            position: checkpoint.position,
            timestamp: checkpoint.timestamp
        });
        
        return true;
    }
    
    /**
     * Update an objective's progress
     * @param objectiveId The ID of the objective to update
     * @param value The new value for the objective
     * @returns true if the objective was updated successfully
     */
    public updateObjective(objectiveId: string, value: number): boolean {
        if (!this._currentLevel) {
            return false;
        }
        
        const objective = this._currentLevel.objectives.find(obj => obj.id === objectiveId);
        if (!objective) {
            return false;
        }
        
        const oldValue = objective.currentValue;
        objective.currentValue = Math.max(0, value);
        
        // Check if objective is now completed
        if (!objective.isCompleted && objective.currentValue >= objective.targetValue) {
            objective.isCompleted = true;
            console.log(`LevelSystem: Objective ${objectiveId} completed`);
        }
        
        return oldValue !== objective.currentValue;
    }
    
    /**
     * Add event listener for level events
     * @param eventType The type of event to listen for
     * @param callback The callback function to call when the event occurs
     */
    public addEventListener(eventType: LevelEventType, callback: (event: LevelEvent) => void): void {
        const callbacks = this._eventCallbacks.get(eventType);
        if (callbacks) {
            callbacks.push(callback);
        }
    }
    
    /**
     * Remove event listener
     * @param eventType The type of event to stop listening for
     * @param callback The callback function to remove
     */
    public removeEventListener(eventType: LevelEventType, callback: (event: LevelEvent) => void): void {
        const callbacks = this._eventCallbacks.get(eventType);
        if (callbacks) {
            const index = callbacks.indexOf(callback);
            if (index >= 0) {
                callbacks.splice(index, 1);
            }
        }
    }
    
    /**
     * Get the current level configuration
     */
    public getCurrentLevel(): LevelConfig | null {
        return this._currentLevel ? this._cloneLevelConfig(this._currentLevel) : null;
    }
    
    /**
     * Get level elapsed time
     */
    public getLevelElapsedTime(): number {
        return this._levelElapsedTime;
    }
    
    /**
     * Check if level is active
     */
    public isLevelActive(): boolean {
        return this._isLevelActive;
    }
    
    /**
     * Check if level is completed
     */
    public isLevelCompleted(): boolean {
        return this._isLevelCompleted;
    }
    
    /**
     * Check if level is failed
     */
    public isLevelFailed(): boolean {
        return this._isLevelFailed;
    }
    
    /**
     * Get event history
     */
    public getEventHistory(): LevelEvent[] {
        return [...this._eventHistory];
    }
    
    /**
     * Check if all objectives are completed
     */
    private _areAllObjectivesCompleted(): boolean {
        if (!this._currentLevel) {
            return false;
        }
        
        return this._currentLevel.objectives.every(objective => objective.isCompleted);
    }
    
    /**
     * Emit a level event
     */
    private _emitEvent(type: LevelEventType, data: any): void {
        const event: LevelEvent = {
            type: type,
            timestamp: this._levelElapsedTime,
            data: data
        };
        
        this._eventHistory.push(event);
        
        // Call event callbacks
        const callbacks = this._eventCallbacks.get(type);
        if (callbacks) {
            callbacks.forEach(callback => {
                try {
                    callback(event);
                } catch (error) {
                    console.error(`LevelSystem: Error in event callback for ${type}:`, error);
                }
            });
        }
    }
    
    /**
     * Clone level configuration to prevent external modifications
     */
    private _cloneLevelConfig(config: LevelConfig): LevelConfig {
        return JSON.parse(JSON.stringify(config));
    }
}
