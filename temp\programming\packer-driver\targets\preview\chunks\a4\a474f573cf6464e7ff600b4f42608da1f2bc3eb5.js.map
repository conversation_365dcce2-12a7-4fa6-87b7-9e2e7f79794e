{"version": 3, "sources": ["file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/GameOver.ts"], "names": ["_decorator", "Component", "director", "find", "Label", "Global", "ccclass", "property", "GameOver", "onLoad", "getComponent", "string", "SCORE", "toString", "onClicked", "event", "cutom", "loadScene"], "mappings": ";;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAiBC,MAAAA,Q,OAAAA,Q;AAAUC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,K,OAAAA,K;;AAC7CC,MAAAA,M,iBAAAA,M;;;;;;;;;OACH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBP,U;;0BAGjBQ,Q,WADZF,OAAO,CAAC,UAAD,C,gBAAR,MACaE,QADb,SAC8BP,SAD9B,CACwC;AAEpCQ,QAAAA,MAAM,GAAG;AACLN,UAAAA,IAAI,CAAC,cAAD,CAAJ,CAAqBO,YAArB,CAAkCN,KAAlC,EAAyCO,MAAzC,GAAkD,WAAW;AAAA;AAAA,gCAAOC,KAAP,CAAaC,QAAb,EAA7D;AACH;;AAEDC,QAAAA,SAAS,CAACC,KAAD,EAAeC,KAAf,EAA8B;AACnCd,UAAAA,QAAQ,CAACe,SAAT,CAAmB,MAAnB;AACA;AAAA;AAAA,gCAAOL,KAAP,GAAe,CAAf;AACH;;AATmC,O", "sourcesContent": ["import { _decorator, Component, Node, director, find, Label } from 'cc';\nimport { Global } from './Global';\nconst { ccclass, property } = _decorator;\n\n@ccclass('GameOver')\nexport class GameOver extends Component {\n\n    onLoad() {\n        find(\"Canvas/Score\").getComponent(Label).string = \"Score:\" + Global.SCORE.toString();\n    }\n    \n    onClicked(event: Event, cutom: string) {\n        director.loadScene(\"menu\");\n        Global.SCORE = 0;\n    }\n}\n\n"]}