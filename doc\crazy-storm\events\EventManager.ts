/**
 * CrazyStorm 2.0 Integration for Cocos Creator
 * Event Manager
 * 
 * Manages and executes CrazyStorm events globally.
 * Based on the C# EventManager.cs implementation.
 */

import { log, warn } from 'cc';
import { ExpressionVM, VMContext } from '../expression';
import { 
    EventInfo, 
    EventContext,
    PropertyContainer, 
    SpecialEventType,
    EventKeyword,
    PropertyType,
    TypeSet
} from './EventTypes';
import { EventExecutor, EventExecutorHelper } from './EventExecutor';

/**
 * Global event manager for CrazyStorm
 */
export class EventManager {
    private static executorList: EventExecutor[] = [];
    private static bindingCache: Map<string, Map<string, TypeSet>> = new Map();
    private static defaultParticleTypes: any[] = [];
    private static customParticleTypes: any[] = [];

    /**
     * Add an event for execution
     */
    public static addEvent(
        propertyContainer: PropertyContainer,
        bindingContainer: PropertyContainer | undefined,
        eventInfo: EventInfo
    ): void {
        const executor = new EventExecutor(propertyContainer, bindingContainer);
        
        // Set basic properties
        executor.propertyName = eventInfo.resultProperty;
        executor.changeMode = eventInfo.changeMode;
        executor.changeTime = eventInfo.changeTime;

        // Get initial value from property container
        const initialValue = new TypeSet(eventInfo.resultType);
        try {
            if (propertyContainer.pushProperty(eventInfo.resultProperty)) {
                // Property was pushed to VM stack, get it back
                const currentValue = propertyContainer.getProperty(eventInfo.resultProperty);
                initialValue.setValue(currentValue);
            }
        } catch (error) {
            warn(`EventManager: Error getting initial value for ${eventInfo.resultProperty}:`, error);
        }

        // Calculate target value
        let targetValue = eventInfo.resultValue.clone();
        
        if (eventInfo.isExpressionResult && eventInfo.resultExpression) {
            // Execute expression to get target value
            try {
                const context: VMContext = {
                    propertyContainer: propertyContainer
                };
                const result = ExpressionVM.execute(eventInfo.resultExpression, context);
                targetValue.setValue(result);
            } catch (error) {
                warn(`EventManager: Error executing result expression:`, error);
            }
        }

        // Apply change type (Set, Increase, Decrease)
        targetValue = EventExecutorHelper.applyChangeType(
            eventInfo.changeType,
            initialValue,
            targetValue
        );

        // Initialize the executor
        executor.initialize(
            eventInfo.resultProperty,
            eventInfo.changeMode,
            eventInfo.changeTime,
            initialValue,
            targetValue
        );

        // For instant changes, execute immediately
        if (eventInfo.changeMode === EventKeyword.Instant) {
            executor.update();
        }

        // Add to executor list
        this.executorList.push(executor);
        
        log(`EventManager: Added event for ${eventInfo.resultProperty} (${eventInfo.changeMode}, ${eventInfo.changeTime} frames)`);
    }

    /**
     * Execute a special event
     */
    public static executeSpecialEvent(
        propertyContainer: PropertyContainer,
        eventName: string,
        args: string[],
        argumentExpression?: any[]
    ): boolean {
        switch (eventName) {
            case SpecialEventType.EmitParticle:
                return this.executeEmitParticle(propertyContainer);
                
            case SpecialEventType.PlaySound:
                return this.executePlaySound(propertyContainer, args);
                
            case SpecialEventType.Loop:
                return this.executeLoop(propertyContainer, argumentExpression);
                
            case SpecialEventType.ChangeType:
                return this.executeChangeType(propertyContainer, args);
                
            default:
                warn(`EventManager: Unknown special event: ${eventName}`);
                return false;
        }
    }

    /**
     * Update all active events
     */
    public static update(): void {
        for (let i = this.executorList.length - 1; i >= 0; i--) {
            const executor = this.executorList[i];
            
            // Skip binding-dependent executors (handled separately)
            if (executor.bindingContainer) {
                continue;
            }

            if (executor.finished) {
                // Remove finished executors
                this.executorList.splice(i, 1);
            } else {
                // Update active executors
                executor.update();
            }
        }
    }

    /**
     * Update events for a specific binding relationship
     */
    public static bindingUpdate(
        propertyContainer: PropertyContainer,
        bindingContainer: PropertyContainer
    ): boolean {
        let updated = false;
        const uniqueKey = this.getUniqueKey(propertyContainer, bindingContainer);
        
        // Ensure cache exists for this binding
        if (!this.bindingCache.has(uniqueKey)) {
            this.bindingCache.set(uniqueKey, new Map());
        }
        const cache = this.bindingCache.get(uniqueKey)!;

        for (let i = this.executorList.length - 1; i >= 0; i--) {
            const executor = this.executorList[i];
            
            if (executor.propertyContainer === propertyContainer && 
                executor.bindingContainer === bindingContainer) {
                
                if (!executor.finished) {
                    executor.update();
                }
                
                // Cache the current value
                cache.set(executor.propertyName, executor.currentValue.clone());
                
                if (executor.finished) {
                    this.executorList.splice(i, 1);
                }
                
                updated = true;
            }
        }
        
        return updated;
    }

    /**
     * Get cached value for a binding relationship
     */
    public static getCachedValue(
        propertyContainer: PropertyContainer,
        bindingContainer: PropertyContainer,
        propertyName: string
    ): TypeSet | undefined {
        const uniqueKey = this.getUniqueKey(propertyContainer, bindingContainer);
        const cache = this.bindingCache.get(uniqueKey);
        return cache?.get(propertyName);
    }

    /**
     * Clear all events
     */
    public static clear(): void {
        this.executorList.length = 0;
        this.bindingCache.clear();
        log('EventManager: Cleared all events');
    }

    /**
     * Get active event count
     */
    public static getActiveEventCount(): number {
        return this.executorList.length;
    }

    /**
     * Get debug information
     */
    public static getDebugInfo(): string[] {
        return this.executorList.map(executor => executor.getDebugInfo());
    }

    // ============================================================================
    // SPECIAL EVENT IMPLEMENTATIONS
    // ============================================================================

    private static executeEmitParticle(propertyContainer: PropertyContainer): boolean {
        try {
            // Call EmitParticle method if it exists
            if (typeof (propertyContainer as any).emitParticle === 'function') {
                (propertyContainer as any).emitParticle();
                return true;
            } else if (typeof (propertyContainer as any).forceEmit === 'function') {
                (propertyContainer as any).forceEmit();
                return true;
            } else {
                warn('EventManager: EmitParticle called on container without emit capability');
                return false;
            }
        } catch (error) {
            warn('EventManager: Error executing EmitParticle:', error);
            return false;
        }
    }

    private static executePlaySound(propertyContainer: PropertyContainer, args: string[]): boolean {
        try {
            // TODO: Implement sound system integration
            log(`EventManager: PlaySound called with args: ${args.join(', ')}`);
            return true;
        } catch (error) {
            warn('EventManager: Error executing PlaySound:', error);
            return false;
        }
    }

    private static executeLoop(propertyContainer: PropertyContainer, argumentExpression?: any[]): boolean {
        try {
            if (argumentExpression) {
                const context: VMContext = {
                    propertyContainer: propertyContainer
                };
                const result = ExpressionVM.execute(argumentExpression, context);
                
                // If result is false, break the loop (return true to indicate loop break)
                if (!result) {
                    return true;
                }
            }
            return false;
        } catch (error) {
            warn('EventManager: Error executing Loop:', error);
            return false;
        }
    }

    private static executeChangeType(propertyContainer: PropertyContainer, args: string[]): boolean {
        try {
            if (args.length < 2) {
                warn('EventManager: ChangeType requires 2 arguments');
                return false;
            }

            const typeId = parseInt(args[0]) + parseInt(args[1]);
            
            // TODO: Implement particle type changing
            log(`EventManager: ChangeType called with typeId: ${typeId}`);
            
            return true;
        } catch (error) {
            warn('EventManager: Error executing ChangeType:', error);
            return false;
        }
    }

    // ============================================================================
    // UTILITY METHODS
    // ============================================================================

    private static getUniqueKey(
        propertyContainer: PropertyContainer,
        bindingContainer: PropertyContainer
    ): string {
        // Create a unique key for the binding relationship
        const containerHash = this.getObjectHash(propertyContainer);
        const bindingHash = this.getObjectHash(bindingContainer);
        return `${containerHash}_${bindingHash}`;
    }

    private static getObjectHash(obj: any): string {
        // Simple hash function for objects
        if (obj && typeof obj === 'object') {
            if (obj.componentId !== undefined) {
                return `comp_${obj.componentId}`;
            }
            if (obj.id !== undefined) {
                return `obj_${obj.id}`;
            }
        }
        return `obj_${Math.random().toString(36).substr(2, 9)}`;
    }

    /**
     * Set particle type collections
     */
    public static setParticleTypes(defaultTypes: any[], customTypes: any[]): void {
        this.defaultParticleTypes = defaultTypes;
        this.customParticleTypes = customTypes;
    }
}
