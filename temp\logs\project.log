2025-7-26 23:56:01 - log: Load engine in C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine
2025-7-26 23:56:01 - log: Register native engine in C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\native
2025-7-26 23:56:01 - log: Request namespace: device-list
2025-7-26 23:56:05 - warn: [Window] Message does not exist: scene.editor-preview-call-methodError: [Window] Message does not exist: scene.editor-preview-call-method
    at EventEmitter.<anonymous> (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\panel\lib\element.ccc:1:4978)
    at EventEmitter.emit (node:events:519:28)
    at IpcRenderer.<anonymous> (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@base\electron-base-ipc\dist\renderer.ccc:1:3858)
    at IpcRenderer.emit (node:events:519:28)
    at Object.onMessage (node:electron/js2c/renderer_init:2:8837)
2025-7-26 23:56:05 - warn: [Window] Message does not exist: console.updateExtensionVisibleError: [Window] Message does not exist: console.updateExtensionVisible
    at EventEmitter.<anonymous> (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\panel\lib\element.ccc:1:4978)
    at EventEmitter.emit (node:events:519:28)
    at IpcRenderer.<anonymous> (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@base\electron-base-ipc\dist\renderer.ccc:1:3858)
    at IpcRenderer.emit (node:events:519:28)
    at Object.onMessage (node:electron/js2c/renderer_init:2:8837)
2025-7-26 23:56:05 - warn: [Window] Message does not exist: scene.shortcuts:changeError: [Window] Message does not exist: scene.shortcuts:change
    at EventEmitter.<anonymous> (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\panel\lib\element.ccc:1:4978)
    at EventEmitter.emit (node:events:519:28)
    at IpcRenderer.<anonymous> (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@base\electron-base-ipc\dist\renderer.ccc:1:3858)
    at IpcRenderer.emit (node:events:519:28)
    at Object.onMessage (node:electron/js2c/renderer_init:2:8837)
2025-7-26 23:56:07 - info: [PreviewInEditor] preview process is ready
2025-7-26 23:56:23 - log: [Scene] [box2d]:box2d wasm lib loaded.
2025-7-26 23:56:23 - log: [Scene] meshopt wasm decoder initialized
2025-7-26 23:56:23 - log: [Scene] [bullet]:bullet wasm lib loaded.
2025-7-26 23:56:23 - log: [Scene] [PHYSICS]: using builtin.
2025-7-26 23:56:23 - log: [Scene] Cocos Creator v3.8.6
2025-7-26 23:56:23 - log: [Scene] TypeID: Registered type 'BulletSystem' with ID 1
2025-7-26 23:56:23 - log: [Scene] TypeID: Registered type 'LevelSystem' with ID 2
2025-7-26 23:56:23 - log: [Scene] TypeID: Registered type 'PlayerSystem' with ID 3
2025-7-26 23:56:24 - log: [Scene] Using custom pipeline: Builtin
2025-7-26 23:56:24 - log: [Scene] [PHYSICS2D]: switch from box2d-wasm to builtin.
