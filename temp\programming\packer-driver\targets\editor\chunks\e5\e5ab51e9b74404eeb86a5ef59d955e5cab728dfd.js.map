{"version": 3, "sources": ["file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/gizmos/GizmoDrawer.ts"], "names": ["GizmoDrawer", "EDITOR", "enabled", "canHandle", "component", "componentType", "onRegister", "console", "log", "drawerName", "onUnregister", "getPriority", "drawCross", "graphics", "x", "y", "size", "color", "strokeColor", "lineWidth", "moveTo", "lineTo", "stroke", "drawCircle", "radius", "filled", "fillColor", "circle", "fill", "drawArrow", "startX", "startY", "endX", "endY", "arrowSize", "dx", "dy", "length", "Math", "sqrt", "dirX", "dirY", "arrowAngle", "PI", "leftX", "cos", "sin", "leftY", "rightX", "rightY", "drawText", "text"], "mappings": ";;;0EAOsBA,W;;;;;;;;;;AANbC,MAAAA,M,UAAAA,M;;;;;;;;;AAET;AACA;AACA;AACA;6BACsBD,W,GAAf,MAAeA,WAAf,CAA4D;AAAA;AAE/D;AACJ;AACA;;AAGI;AACJ;AACA;;AAGI;AACJ;AACA;AAdmE,eAexDE,OAfwD,GAerC,IAfqC;AAAA;AAiB/D;AACJ;AACA;AACA;AACA;AACA;;;AAGI;AACJ;AACA;AACA;AACA;AACWC,QAAAA,SAAS,CAACC,SAAD,EAAuC;AACnD,iBAAOA,SAAS,YAAY,KAAKC,aAAjC;AACH;AAED;AACJ;AACA;AACA;;;AACWC,QAAAA,UAAU,GAAS;AACtB,cAAIL,MAAJ,EAAY;AACRM,YAAAA,OAAO,CAACC,GAAR,CAAa,2BAA0B,KAAKC,UAAW,EAAvD;AACH;AACJ;AAED;AACJ;AACA;AACA;;;AACWC,QAAAA,YAAY,GAAS;AACxB,cAAIT,MAAJ,EAAY;AACRM,YAAAA,OAAO,CAACC,GAAR,CAAa,6BAA4B,KAAKC,UAAW,EAAzD;AACH;AACJ;AAED;AACJ;AACA;AACA;;;AACWE,QAAAA,WAAW,GAAW;AACzB,iBAAO,CAAP;AACH;AAED;AACJ;AACA;;;AACcC,QAAAA,SAAS,CAACC,QAAD,EAAqBC,CAArB,EAAgCC,CAAhC,EAA2CC,IAA3C,EAAyDC,KAAzD,EAA6E;AAC5FJ,UAAAA,QAAQ,CAACK,WAAT,GAAuBD,KAAvB;AACAJ,UAAAA,QAAQ,CAACM,SAAT,GAAqB,CAArB;AAEAN,UAAAA,QAAQ,CAACO,MAAT,CAAgBN,CAAC,GAAGE,IAApB,EAA0BD,CAA1B;AACAF,UAAAA,QAAQ,CAACQ,MAAT,CAAgBP,CAAC,GAAGE,IAApB,EAA0BD,CAA1B;AACAF,UAAAA,QAAQ,CAACO,MAAT,CAAgBN,CAAhB,EAAmBC,CAAC,GAAGC,IAAvB;AACAH,UAAAA,QAAQ,CAACQ,MAAT,CAAgBP,CAAhB,EAAmBC,CAAC,GAAGC,IAAvB;AACAH,UAAAA,QAAQ,CAACS,MAAT;AACH;AAED;AACJ;AACA;;;AACcC,QAAAA,UAAU,CAACV,QAAD,EAAqBC,CAArB,EAAgCC,CAAhC,EAA2CS,MAA3C,EAA2DP,KAA3D,EAAyEQ,MAAe,GAAG,KAA3F,EAAwG;AACxHZ,UAAAA,QAAQ,CAACK,WAAT,GAAuBD,KAAvB;AACAJ,UAAAA,QAAQ,CAACM,SAAT,GAAqB,CAArB;;AAEA,cAAIM,MAAJ,EAAY;AACRZ,YAAAA,QAAQ,CAACa,SAAT,GAAqBT,KAArB;AACAJ,YAAAA,QAAQ,CAACc,MAAT,CAAgBb,CAAhB,EAAmBC,CAAnB,EAAsBS,MAAtB;AACAX,YAAAA,QAAQ,CAACe,IAAT;AACH,WAJD,MAIO;AACHf,YAAAA,QAAQ,CAACc,MAAT,CAAgBb,CAAhB,EAAmBC,CAAnB,EAAsBS,MAAtB;AACAX,YAAAA,QAAQ,CAACS,MAAT;AACH;AACJ;AAED;AACJ;AACA;;;AACcO,QAAAA,SAAS,CAAChB,QAAD,EAAqBiB,MAArB,EAAqCC,MAArC,EAAqDC,IAArD,EAAmEC,IAAnE,EAAiFhB,KAAjF,EAA+FiB,SAAiB,GAAG,CAAnH,EAA4H;AAC3IrB,UAAAA,QAAQ,CAACK,WAAT,GAAuBD,KAAvB;AACAJ,UAAAA,QAAQ,CAACM,SAAT,GAAqB,CAArB,CAF2I,CAI3I;;AACAN,UAAAA,QAAQ,CAACO,MAAT,CAAgBU,MAAhB,EAAwBC,MAAxB;AACAlB,UAAAA,QAAQ,CAACQ,MAAT,CAAgBW,IAAhB,EAAsBC,IAAtB,EAN2I,CAQ3I;;AACA,gBAAME,EAAE,GAAGH,IAAI,GAAGF,MAAlB;AACA,gBAAMM,EAAE,GAAGH,IAAI,GAAGF,MAAlB;AACA,gBAAMM,MAAM,GAAGC,IAAI,CAACC,IAAL,CAAUJ,EAAE,GAAGA,EAAL,GAAUC,EAAE,GAAGA,EAAzB,CAAf;;AAEA,cAAIC,MAAM,GAAG,CAAb,EAAgB;AACZ,kBAAMG,IAAI,GAAGL,EAAE,GAAGE,MAAlB;AACA,kBAAMI,IAAI,GAAGL,EAAE,GAAGC,MAAlB,CAFY,CAIZ;;AACA,kBAAMK,UAAU,GAAGJ,IAAI,CAACK,EAAL,GAAU,CAA7B,CALY,CAOZ;;AACA,kBAAMC,KAAK,GAAGZ,IAAI,GAAGE,SAAS,IAAIM,IAAI,GAAGF,IAAI,CAACO,GAAL,CAASH,UAAT,CAAP,GAA8BD,IAAI,GAAGH,IAAI,CAACQ,GAAL,CAASJ,UAAT,CAAzC,CAA9B;AACA,kBAAMK,KAAK,GAAGd,IAAI,GAAGC,SAAS,IAAIO,IAAI,GAAGH,IAAI,CAACO,GAAL,CAASH,UAAT,CAAP,GAA8BF,IAAI,GAAGF,IAAI,CAACQ,GAAL,CAASJ,UAAT,CAAzC,CAA9B,CATY,CAWZ;;AACA,kBAAMM,MAAM,GAAGhB,IAAI,GAAGE,SAAS,IAAIM,IAAI,GAAGF,IAAI,CAACO,GAAL,CAAS,CAACH,UAAV,CAAP,GAA+BD,IAAI,GAAGH,IAAI,CAACQ,GAAL,CAAS,CAACJ,UAAV,CAA1C,CAA/B;AACA,kBAAMO,MAAM,GAAGhB,IAAI,GAAGC,SAAS,IAAIO,IAAI,GAAGH,IAAI,CAACO,GAAL,CAAS,CAACH,UAAV,CAAP,GAA+BF,IAAI,GAAGF,IAAI,CAACQ,GAAL,CAAS,CAACJ,UAAV,CAA1C,CAA/B,CAbY,CAeZ;;AACA7B,YAAAA,QAAQ,CAACO,MAAT,CAAgBY,IAAhB,EAAsBC,IAAtB;AACApB,YAAAA,QAAQ,CAACQ,MAAT,CAAgBuB,KAAhB,EAAuBG,KAAvB;AACAlC,YAAAA,QAAQ,CAACO,MAAT,CAAgBY,IAAhB,EAAsBC,IAAtB;AACApB,YAAAA,QAAQ,CAACQ,MAAT,CAAgB2B,MAAhB,EAAwBC,MAAxB;AACH;;AAEDpC,UAAAA,QAAQ,CAACS,MAAT;AACH;AAED;AACJ;AACA;AACA;;;AACc4B,QAAAA,QAAQ,CAACrC,QAAD,EAAqBsC,IAArB,EAAmCrC,CAAnC,EAA8CC,CAA9C,EAAyDE,KAAzD,EAA6E;AAC3F;AACA;AACA,cAAIhB,MAAJ,EAAY;AACRM,YAAAA,OAAO,CAACC,GAAR,CAAa,kBAAiBM,CAAE,KAAIC,CAAE,MAAKoC,IAAK,EAAhD;AACH;AACJ;;AAhJ8D,O", "sourcesContent": ["import { _decorator, Component, Graphics, Color, Node } from 'cc';\nimport { EDITOR } from 'cc/env';\n\n/**\n * Abstract base class for drawing gizmos for specific component types\n * This is not a component itself, but a drawer that can be registered to GizmoManager\n */\nexport abstract class GizmoDrawer<T extends Component = Component> {\n    \n    /**\n     * The component type this drawer handles\n     */\n    public abstract readonly componentType: new (...args: any[]) => T;\n    \n    /**\n     * Name of this gizmo drawer for debugging\n     */\n    public abstract readonly drawerName: string;\n    \n    /**\n     * Whether this drawer is enabled\n     */\n    public enabled: boolean = true;\n    \n    /**\n     * Draw gizmos for the given component\n     * @param component The component to draw gizmos for\n     * @param graphics The graphics component to draw with\n     * @param node The node that contains the component\n     */\n    public abstract drawGizmos(component: T, graphics: Graphics, node: Node): void;\n    \n    /**\n     * Check if this drawer can handle the given component\n     * @param component The component to check\n     * @returns true if this drawer can handle the component\n     */\n    public canHandle(component: Component): component is T {\n        return component instanceof this.componentType;\n    }\n    \n    /**\n     * Called when the drawer is registered to the manager\n     * Override this to perform any initialization\n     */\n    public onRegister(): void {\n        if (EDITOR) {\n            console.log(`GizmoDrawer: Registered ${this.drawerName}`);\n        }\n    }\n    \n    /**\n     * Called when the drawer is unregistered from the manager\n     * Override this to perform any cleanup\n     */\n    public onUnregister(): void {\n        if (EDITOR) {\n            console.log(`GizmoDrawer: Unregistered ${this.drawerName}`);\n        }\n    }\n    \n    /**\n     * Get the priority of this drawer (higher priority draws last/on top)\n     * Override this to change drawing order\n     */\n    public getPriority(): number {\n        return 0;\n    }\n    \n    /**\n     * Helper method to draw a cross at the given position\n     */\n    protected drawCross(graphics: Graphics, x: number, y: number, size: number, color: Color): void {\n        graphics.strokeColor = color;\n        graphics.lineWidth = 2;\n        \n        graphics.moveTo(x - size, y);\n        graphics.lineTo(x + size, y);\n        graphics.moveTo(x, y - size);\n        graphics.lineTo(x, y + size);\n        graphics.stroke();\n    }\n    \n    /**\n     * Helper method to draw a circle\n     */\n    protected drawCircle(graphics: Graphics, x: number, y: number, radius: number, color: Color, filled: boolean = false): void {\n        graphics.strokeColor = color;\n        graphics.lineWidth = 1;\n        \n        if (filled) {\n            graphics.fillColor = color;\n            graphics.circle(x, y, radius);\n            graphics.fill();\n        } else {\n            graphics.circle(x, y, radius);\n            graphics.stroke();\n        }\n    }\n    \n    /**\n     * Helper method to draw an arrow\n     */\n    protected drawArrow(graphics: Graphics, startX: number, startY: number, endX: number, endY: number, color: Color, arrowSize: number = 8): void {\n        graphics.strokeColor = color;\n        graphics.lineWidth = 2;\n        \n        // Draw main line\n        graphics.moveTo(startX, startY);\n        graphics.lineTo(endX, endY);\n        \n        // Calculate arrow head\n        const dx = endX - startX;\n        const dy = endY - startY;\n        const length = Math.sqrt(dx * dx + dy * dy);\n        \n        if (length > 0) {\n            const dirX = dx / length;\n            const dirY = dy / length;\n            \n            // Arrow head angle (30 degrees)\n            const arrowAngle = Math.PI / 6;\n            \n            // Left arrow point\n            const leftX = endX - arrowSize * (dirX * Math.cos(arrowAngle) - dirY * Math.sin(arrowAngle));\n            const leftY = endY - arrowSize * (dirY * Math.cos(arrowAngle) + dirX * Math.sin(arrowAngle));\n            \n            // Right arrow point\n            const rightX = endX - arrowSize * (dirX * Math.cos(-arrowAngle) - dirY * Math.sin(-arrowAngle));\n            const rightY = endY - arrowSize * (dirY * Math.cos(-arrowAngle) + dirX * Math.sin(-arrowAngle));\n            \n            // Draw arrow head\n            graphics.moveTo(endX, endY);\n            graphics.lineTo(leftX, leftY);\n            graphics.moveTo(endX, endY);\n            graphics.lineTo(rightX, rightY);\n        }\n        \n        graphics.stroke();\n    }\n    \n    /**\n     * Helper method to draw text (simple implementation)\n     * Note: For more complex text rendering, consider using Label components\n     */\n    protected drawText(graphics: Graphics, text: string, x: number, y: number, color: Color): void {\n        // This is a placeholder - in a real implementation you might want to use Label components\n        // or a more sophisticated text rendering system\n        if (EDITOR) {\n            console.log(`Gizmo Text at (${x}, ${y}): ${text}`);\n        }\n    }\n}\n"]}