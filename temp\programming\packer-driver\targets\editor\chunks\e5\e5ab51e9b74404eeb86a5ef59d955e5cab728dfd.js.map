{"version": 3, "sources": ["file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/gizmos/GizmoDrawer.ts"], "names": ["GizmoDrawer", "EDITOR", "Gizmo<PERSON><PERSON>s", "enabled", "canHandle", "component", "componentType", "onRegister", "console", "log", "drawerName", "onUnregister", "getPriority", "drawCross", "graphics", "x", "y", "size", "color", "drawCircle", "radius", "filled", "drawArrow", "startX", "startY", "endX", "endY", "arrowSize", "drawLine", "lineWidth", "drawRect", "width", "height", "drawText", "_graphics", "text", "_color"], "mappings": ";;;mGAQsBA,W;;;;;;;;;;;;;;;;AAPbC,MAAAA,M,UAAAA,M;;AACAC,MAAAA,U,iBAAAA,U;;;;;;;;;AAET;AACA;AACA;AACA;6BACsBF,W,GAAf,MAAeA,WAAf,CAA4D;AAAA;AAE/D;AACJ;AACA;;AAGI;AACJ;AACA;;AAGI;AACJ;AACA;AAdmE,eAexDG,OAfwD,GAerC,IAfqC;AAAA;AAiB/D;AACJ;AACA;AACA;AACA;AACA;;;AAGI;AACJ;AACA;AACA;AACA;AACWC,QAAAA,SAAS,CAACC,SAAD,EAAuC;AACnD,iBAAOA,SAAS,YAAY,KAAKC,aAAjC;AACH;AAED;AACJ;AACA;AACA;;;AACWC,QAAAA,UAAU,GAAS;AACtB,cAAIN,MAAJ,EAAY;AACRO,YAAAA,OAAO,CAACC,GAAR,CAAa,2BAA0B,KAAKC,UAAW,EAAvD;AACH;AACJ;AAED;AACJ;AACA;AACA;;;AACWC,QAAAA,YAAY,GAAS;AACxB,cAAIV,MAAJ,EAAY;AACRO,YAAAA,OAAO,CAACC,GAAR,CAAa,6BAA4B,KAAKC,UAAW,EAAzD;AACH;AACJ;AAED;AACJ;AACA;AACA;;;AACWE,QAAAA,WAAW,GAAW;AACzB,iBAAO,CAAP;AACH;AAED;AACJ;AACA;;;AACcC,QAAAA,SAAS,CAACC,QAAD,EAAqBC,CAArB,EAAgCC,CAAhC,EAA2CC,IAA3C,EAAyDC,KAAzD,EAA6E;AAC5F;AAAA;AAAA,wCAAWL,SAAX,CAAqBC,QAArB,EAA+BC,CAA/B,EAAkCC,CAAlC,EAAqCC,IAArC,EAA2CC,KAA3C;AACH;AAED;AACJ;AACA;;;AACcC,QAAAA,UAAU,CAACL,QAAD,EAAqBC,CAArB,EAAgCC,CAAhC,EAA2CI,MAA3C,EAA2DF,KAA3D,EAAyEG,MAAe,GAAG,KAA3F,EAAwG;AACxH;AAAA;AAAA,wCAAWF,UAAX,CAAsBL,QAAtB,EAAgCC,CAAhC,EAAmCC,CAAnC,EAAsCI,MAAtC,EAA8CF,KAA9C,EAAqDG,MAArD;AACH;AAED;AACJ;AACA;;;AACcC,QAAAA,SAAS,CAACR,QAAD,EAAqBS,MAArB,EAAqCC,MAArC,EAAqDC,IAArD,EAAmEC,IAAnE,EAAiFR,KAAjF,EAA+FS,SAAiB,GAAG,CAAnH,EAA4H;AAC3I;AAAA;AAAA,wCAAWL,SAAX,CAAqBR,QAArB,EAA+BS,MAA/B,EAAuCC,MAAvC,EAA+CC,IAA/C,EAAqDC,IAArD,EAA2DR,KAA3D,EAAkES,SAAlE;AACH;AAED;AACJ;AACA;;;AACcC,QAAAA,QAAQ,CAACd,QAAD,EAAqBS,MAArB,EAAqCC,MAArC,EAAqDC,IAArD,EAAmEC,IAAnE,EAAiFR,KAAjF,EAA+FW,SAAiB,GAAG,CAAnH,EAA4H;AAC1I;AAAA;AAAA,wCAAWD,QAAX,CAAoBd,QAApB,EAA8BS,MAA9B,EAAsCC,MAAtC,EAA8CC,IAA9C,EAAoDC,IAApD,EAA0DR,KAA1D,EAAiEW,SAAjE;AACH;AAED;AACJ;AACA;;;AACcC,QAAAA,QAAQ,CAAChB,QAAD,EAAqBC,CAArB,EAAgCC,CAAhC,EAA2Ce,KAA3C,EAA0DC,MAA1D,EAA0Ed,KAA1E,EAAwFG,MAAe,GAAG,KAA1G,EAAuH;AACrI;AAAA;AAAA,wCAAWS,QAAX,CAAoBhB,QAApB,EAA8BC,CAA9B,EAAiCC,CAAjC,EAAoCe,KAApC,EAA2CC,MAA3C,EAAmDd,KAAnD,EAA0DG,MAA1D;AACH;AAED;AACJ;AACA;AACA;;;AACcY,QAAAA,QAAQ,CAACC,SAAD,EAAsBC,IAAtB,EAAoCpB,CAApC,EAA+CC,CAA/C,EAA0DoB,MAA1D,EAA+E;AAC7F;AACA;AACA,cAAInC,MAAJ,EAAY;AACRO,YAAAA,OAAO,CAACC,GAAR,CAAa,kBAAiBM,CAAE,KAAIC,CAAE,MAAKmB,IAAK,EAAhD;AACH;AACJ;;AA3G8D,O", "sourcesContent": ["import { _decorator, Component, Graphics, Color, Node } from 'cc';\nimport { EDITOR } from 'cc/env';\nimport { GizmoUtils } from './GizmoUtils';\n\n/**\n * Abstract base class for drawing gizmos for specific component types\n * This is not a component itself, but a drawer that can be registered to GizmoManager\n */\nexport abstract class GizmoDrawer<T extends Component = Component> {\n    \n    /**\n     * The component type this drawer handles\n     */\n    public abstract readonly componentType: new (...args: any[]) => T;\n    \n    /**\n     * Name of this gizmo drawer for debugging\n     */\n    public abstract readonly drawerName: string;\n    \n    /**\n     * Whether this drawer is enabled\n     */\n    public enabled: boolean = true;\n    \n    /**\n     * Draw gizmos for the given component\n     * @param component The component to draw gizmos for\n     * @param graphics The graphics component to draw with\n     * @param node The node that contains the component\n     */\n    public abstract drawGizmos(component: T, graphics: Graphics, node: Node): void;\n    \n    /**\n     * Check if this drawer can handle the given component\n     * @param component The component to check\n     * @returns true if this drawer can handle the component\n     */\n    public canHandle(component: Component): component is T {\n        return component instanceof this.componentType;\n    }\n    \n    /**\n     * Called when the drawer is registered to the manager\n     * Override this to perform any initialization\n     */\n    public onRegister(): void {\n        if (EDITOR) {\n            console.log(`GizmoDrawer: Registered ${this.drawerName}`);\n        }\n    }\n    \n    /**\n     * Called when the drawer is unregistered from the manager\n     * Override this to perform any cleanup\n     */\n    public onUnregister(): void {\n        if (EDITOR) {\n            console.log(`GizmoDrawer: Unregistered ${this.drawerName}`);\n        }\n    }\n    \n    /**\n     * Get the priority of this drawer (higher priority draws last/on top)\n     * Override this to change drawing order\n     */\n    public getPriority(): number {\n        return 0;\n    }\n    \n    /**\n     * Helper method to draw a cross at the given position\n     */\n    protected drawCross(graphics: Graphics, x: number, y: number, size: number, color: Color): void {\n        GizmoUtils.drawCross(graphics, x, y, size, color);\n    }\n\n    /**\n     * Helper method to draw a circle\n     */\n    protected drawCircle(graphics: Graphics, x: number, y: number, radius: number, color: Color, filled: boolean = false): void {\n        GizmoUtils.drawCircle(graphics, x, y, radius, color, filled);\n    }\n\n    /**\n     * Helper method to draw an arrow\n     */\n    protected drawArrow(graphics: Graphics, startX: number, startY: number, endX: number, endY: number, color: Color, arrowSize: number = 8): void {\n        GizmoUtils.drawArrow(graphics, startX, startY, endX, endY, color, arrowSize);\n    }\n\n    /**\n     * Helper method to draw a line\n     */\n    protected drawLine(graphics: Graphics, startX: number, startY: number, endX: number, endY: number, color: Color, lineWidth: number = 1): void {\n        GizmoUtils.drawLine(graphics, startX, startY, endX, endY, color, lineWidth);\n    }\n\n    /**\n     * Helper method to draw a rectangle\n     */\n    protected drawRect(graphics: Graphics, x: number, y: number, width: number, height: number, color: Color, filled: boolean = false): void {\n        GizmoUtils.drawRect(graphics, x, y, width, height, color, filled);\n    }\n    \n    /**\n     * Helper method to draw text (simple implementation)\n     * Note: For more complex text rendering, consider using Label components\n     */\n    protected drawText(_graphics: Graphics, text: string, x: number, y: number, _color: Color): void {\n        // This is a placeholder - in a real implementation you might want to use Label components\n        // or a more sophisticated text rendering system\n        if (EDITOR) {\n            console.log(`Gizmo Text at (${x}, ${y}): ${text}`);\n        }\n    }\n}\n"]}