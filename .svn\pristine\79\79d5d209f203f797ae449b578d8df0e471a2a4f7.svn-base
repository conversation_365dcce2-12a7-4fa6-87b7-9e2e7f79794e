import { _decorator, Component, Node, Vec2, Vec3, Rect, Prefab, instantiate, resources, UITransform } from 'cc';
import { MapLayer } from '../core/LevelData';
import { LayerDepths } from '../core/Types';

const { ccclass, property } = _decorator;

/**
 * Map layer component for handling scrolling backgrounds
 */
@ccclass('MapLayerComponent')
export class MapLayerComponent extends Component {
    @property({ type: Object })
    public mapLayerData: MapLayer | null = null;
    
    @property
    public autoScroll: boolean = true;
    
    // Runtime state
    private scrollPosition: Vec2 = Vec2.ZERO;
    private layerNodes: Node[] = [];
    private isLoaded: boolean = false;
    private prefab: Prefab | null = null;
    
    // Scrolling parameters
    private worldBounds: Rect = new Rect();
    private layerSize: Vec2 = Vec2.ZERO;
    private tilesNeeded: Vec2 = Vec2.ZERO;
    
    protected start(): void {
        if (this.mapLayerData) {
            this.loadLayer();
        }
    }
    
    protected update(deltaTime: number): void {
        if (this.autoScroll && this.mapLayerData && this.isLoaded) {
            this.updateScrolling(deltaTime);
        }
    }
    
    /**
     * Load the map layer
     */
    public async loadLayer(): Promise<void> {
        if (!this.mapLayerData) {
            console.warn('MapLayerComponent: No map layer data assigned');
            return;
        }
        
        try {
            // Load prefab
            this.prefab = await this.loadPrefab(this.mapLayerData.prefabPath);
            if (!this.prefab) {
                console.error(`Failed to load map layer prefab: ${this.mapLayerData.prefabPath}`);
                return;
            }
            
            // Setup layer properties
            this.setupLayer();
            
            // Create initial tiles
            this.createTiles();
            
            this.isLoaded = true;
            console.log(`Map layer loaded: ${this.mapLayerData.id}`);
        } catch (error) {
            console.error(`Failed to load map layer: ${this.mapLayerData.id}`, error);
        }
    }
    
    /**
     * Unload the map layer
     */
    public unloadLayer(): void {
        // Destroy all layer nodes
        this.layerNodes.forEach(node => {
            if (node && node.isValid) {
                node.destroy();
            }
        });
        
        this.layerNodes = [];
        this.isLoaded = false;
        this.prefab = null;
        
        console.log(`Map layer unloaded: ${this.mapLayerData?.id}`);
    }
    
    /**
     * Update scrolling
     */
    private updateScrolling(deltaTime: number): void {
        if (!this.mapLayerData) return;
        
        // Update scroll position
        const scrollDelta = this.mapLayerData.scrollSpeed.clone().multiplyScalar(deltaTime);
        this.scrollPosition.add(scrollDelta);
        
        // Update tile positions
        this.updateTilePositions();
        
        // Check if we need to create/destroy tiles
        this.manageTiles();
    }
    
    /**
     * Setup layer properties
     */
    private setupLayer(): void {
        if (!this.mapLayerData || !this.prefab) return;
        
        // Set layer depth
        this.node.setSiblingIndex(this.getLayerOrder());
        
        // Get layer size from prefab
        const instance = instantiate(this.prefab);
        const transform = instance.getComponent(UITransform);
        if (transform) {
            this.layerSize = new Vec2(transform.width, transform.height);
        } else {
            this.layerSize = new Vec2(1920, 1080); // Default size
        }
        instance.destroy();
        
        // Calculate world bounds
        this.worldBounds = this.mapLayerData.bounds;
        
        // Calculate how many tiles we need
        this.calculateTilesNeeded();
    }
    
    /**
     * Get layer rendering order based on type
     */
    private getLayerOrder(): number {
        if (!this.mapLayerData) return 0;
        return LayerDepths[this.mapLayerData.layerType] || 0;
    }
    
    /**
     * Calculate how many tiles are needed for seamless scrolling
     */
    private calculateTilesNeeded(): void {
        if (this.layerSize.x <= 0 || this.layerSize.y <= 0) return;
        
        // Calculate based on world bounds and repeat mode
        switch (this.mapLayerData?.repeatMode) {
            case 'horizontal':
                this.tilesNeeded.x = Math.ceil(this.worldBounds.width / this.layerSize.x) + 2; // +2 for seamless scrolling
                this.tilesNeeded.y = 1;
                break;
                
            case 'vertical':
                this.tilesNeeded.x = 1;
                this.tilesNeeded.y = Math.ceil(this.worldBounds.height / this.layerSize.y) + 2;
                break;
                
            case 'both':
                this.tilesNeeded.x = Math.ceil(this.worldBounds.width / this.layerSize.x) + 2;
                this.tilesNeeded.y = Math.ceil(this.worldBounds.height / this.layerSize.y) + 2;
                break;
                
            default: // 'none'
                this.tilesNeeded.x = 1;
                this.tilesNeeded.y = 1;
                break;
        }
    }
    
    /**
     * Create initial tiles
     */
    private createTiles(): void {
        if (!this.prefab || !this.mapLayerData) return;
        
        const totalTiles = this.tilesNeeded.x * this.tilesNeeded.y;
        
        for (let i = 0; i < totalTiles; i++) {
            const tileNode = instantiate(this.prefab);
            tileNode.setParent(this.node);
            
            // Position tile
            const tileX = i % this.tilesNeeded.x;
            const tileY = Math.floor(i / this.tilesNeeded.x);
            
            const position = new Vec3(
                tileX * this.layerSize.x + this.mapLayerData.offset.x,
                tileY * this.layerSize.y + this.mapLayerData.offset.y,
                0
            );
            
            tileNode.setPosition(position);
            this.layerNodes.push(tileNode);
        }
        
        console.log(`Created ${totalTiles} tiles for layer ${this.mapLayerData.id}`);
    }
    
    /**
     * Update tile positions for scrolling
     */
    private updateTilePositions(): void {
        if (!this.mapLayerData) return;
        
        this.layerNodes.forEach((tileNode, index) => {
            if (!tileNode || !tileNode.isValid) return;
            
            const currentPos = tileNode.position;
            const newPos = new Vec3(
                currentPos.x - this.scrollPosition.x,
                currentPos.y - this.scrollPosition.y,
                currentPos.z
            );
            
            tileNode.setPosition(newPos);
        });
    }
    
    /**
     * Manage tile creation/destruction for infinite scrolling
     */
    private manageTiles(): void {
        if (!this.mapLayerData || this.mapLayerData.repeatMode === 'none') return;
        
        // This is a simplified implementation
        // In a full system, you would check which tiles are visible and create/destroy as needed
        this.wrapTilePositions();
    }
    
    /**
     * Wrap tile positions for seamless scrolling
     */
    private wrapTilePositions(): void {
        if (!this.mapLayerData) return;
        
        this.layerNodes.forEach(tileNode => {
            if (!tileNode || !tileNode.isValid) return;
            
            const pos = tileNode.position;
            let newPos = pos.clone();
            
            // Horizontal wrapping
            if (this.mapLayerData.repeatMode === 'horizontal' || this.mapLayerData.repeatMode === 'both') {
                if (pos.x < -this.layerSize.x) {
                    newPos.x += this.tilesNeeded.x * this.layerSize.x;
                } else if (pos.x > this.worldBounds.width + this.layerSize.x) {
                    newPos.x -= this.tilesNeeded.x * this.layerSize.x;
                }
            }
            
            // Vertical wrapping
            if (this.mapLayerData.repeatMode === 'vertical' || this.mapLayerData.repeatMode === 'both') {
                if (pos.y < -this.layerSize.y) {
                    newPos.y += this.tilesNeeded.y * this.layerSize.y;
                } else if (pos.y > this.worldBounds.height + this.layerSize.y) {
                    newPos.y -= this.tilesNeeded.y * this.layerSize.y;
                }
            }
            
            if (!newPos.equals(pos)) {
                tileNode.setPosition(newPos);
            }
        });
    }
    
    /**
     * Load prefab from resources
     */
    private async loadPrefab(prefabPath: string): Promise<Prefab | null> {
        return new Promise((resolve) => {
            resources.load(prefabPath, Prefab, (err, prefab) => {
                if (err) {
                    console.error(`Failed to load prefab: ${prefabPath}`, err);
                    resolve(null);
                } else {
                    resolve(prefab);
                }
            });
        });
    }
    
    /**
     * Set scroll position manually
     */
    public setScrollPosition(position: Vec2): void {
        this.scrollPosition = position.clone();
        this.updateTilePositions();
    }
    
    /**
     * Get current scroll position
     */
    public getScrollPosition(): Vec2 {
        return this.scrollPosition.clone();
    }
    
    /**
     * Set visibility
     */
    public setVisible(visible: boolean): void {
        this.node.active = visible;
        if (this.mapLayerData) {
            this.mapLayerData.isVisible = visible;
        }
    }
    
    /**
     * Check if layer is loaded
     */
    public isLayerLoaded(): boolean {
        return this.isLoaded;
    }
}
