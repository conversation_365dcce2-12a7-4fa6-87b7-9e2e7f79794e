/**
 * CrazyStorm 2.0 Integration for Cocos Creator
 * Virtual Machine Instructions
 * 
 * Defines the instruction set for the CrazyStorm expression virtual machine.
 * Based on the C# VM.cs implementation.
 */

import { Vec2, Color } from 'cc';

/**
 * Virtual Machine operation codes
 * Matches the C# VMCode enum
 */
export enum VMCode {
    NUMBER = 0,
    BOOL = 1,
    NAME = 2,
    VECTOR2 = 3,
    RGB = 4,
    ARGUMENTS = 5,
    CALL = 6,
    AND = 7,
    OR = 8,
    EQUAL = 9,
    ADD = 10,
    SUB = 11,
    MUL = 12,
    DIV = 13,
    MOD = 14,
    MORE = 15,
    LESS = 16,
    MOREOREQUAL = 17,
    LESSOREQUAL = 18,
    NOTEQUAL = 19
}

/**
 * Virtual Machine instruction
 * Represents a single operation in the VM
 */
export class VMInstruction {
    public code: VMCode;
    public intOperand: number = 0;
    public floatOperand: number = 0;
    public boolOperand: boolean = false;
    public stringOperand: string = '';

    constructor(code: VMCode, operand?: any) {
        this.code = code;
        
        if (operand !== undefined) {
            switch (code) {
                case VMCode.NUMBER:
                    this.floatOperand = operand as number;
                    break;
                case VMCode.BOOL:
                    this.boolOperand = operand as boolean;
                    break;
                case VMCode.NAME:
                    this.stringOperand = operand as string;
                    break;
                case VMCode.ARGUMENTS:
                case VMCode.CALL:
                    this.intOperand = operand as number;
                    break;
                default:
                    // No operand needed for other operations
                    break;
            }
        }
    }

    /**
     * Create instruction from bytecode data
     */
    public static fromBytes(bytes: Uint8Array, offset: number): { instruction: VMInstruction, nextOffset: number } {
        const code = bytes[offset] as VMCode;
        let nextOffset = offset + 1;
        let operand: any = undefined;

        switch (code) {
            case VMCode.NUMBER:
                // Read 4 bytes for float
                const floatView = new DataView(bytes.buffer, offset + 1, 4);
                operand = floatView.getFloat32(0, true); // little endian
                nextOffset += 4;
                break;
                
            case VMCode.BOOL:
                operand = bytes[offset + 1] !== 0;
                nextOffset += 1;
                break;
                
            case VMCode.NAME:
                // Read string length (4 bytes) then string data
                const lengthView = new DataView(bytes.buffer, offset + 1, 4);
                const length = lengthView.getInt32(0, true);
                nextOffset += 4;
                
                const stringBytes = bytes.slice(nextOffset, nextOffset + length);
                operand = new TextDecoder().decode(stringBytes);
                nextOffset += length;
                break;
                
            case VMCode.ARGUMENTS:
            case VMCode.CALL:
                const intView = new DataView(bytes.buffer, offset + 1, 4);
                operand = intView.getInt32(0, true);
                nextOffset += 4;
                break;
                
            default:
                // No operand for other operations
                break;
        }

        return {
            instruction: new VMInstruction(code, operand),
            nextOffset: nextOffset
        };
    }

    /**
     * Convert instruction to bytecode
     */
    public toBytes(): Uint8Array {
        const bytes: number[] = [this.code];

        switch (this.code) {
            case VMCode.NUMBER:
                const floatBuffer = new ArrayBuffer(4);
                const floatView = new DataView(floatBuffer);
                floatView.setFloat32(0, this.floatOperand, true);
                bytes.push(...new Uint8Array(floatBuffer));
                break;
                
            case VMCode.BOOL:
                bytes.push(this.boolOperand ? 1 : 0);
                break;
                
            case VMCode.NAME:
                const stringBytes = new TextEncoder().encode(this.stringOperand);
                const lengthBuffer = new ArrayBuffer(4);
                const lengthView = new DataView(lengthBuffer);
                lengthView.setInt32(0, stringBytes.length, true);
                bytes.push(...new Uint8Array(lengthBuffer));
                bytes.push(...stringBytes);
                break;
                
            case VMCode.ARGUMENTS:
            case VMCode.CALL:
                const intBuffer = new ArrayBuffer(4);
                const intView = new DataView(intBuffer);
                intView.setInt32(0, this.intOperand, true);
                bytes.push(...new Uint8Array(intBuffer));
                break;
                
            default:
                // No operand for other operations
                break;
        }

        return new Uint8Array(bytes);
    }

    /**
     * Get human-readable string representation
     */
    public toString(): string {
        switch (this.code) {
            case VMCode.NUMBER:
                return `NUMBER ${this.floatOperand}`;
            case VMCode.BOOL:
                return `BOOL ${this.boolOperand}`;
            case VMCode.NAME:
                return `NAME "${this.stringOperand}"`;
            case VMCode.VECTOR2:
                return `VECTOR2`;
            case VMCode.RGB:
                return `RGB`;
            case VMCode.ARGUMENTS:
                return `ARGUMENTS ${this.intOperand}`;
            case VMCode.CALL:
                return `CALL ${this.intOperand}`;
            case VMCode.AND:
                return `AND`;
            case VMCode.OR:
                return `OR`;
            case VMCode.EQUAL:
                return `EQUAL`;
            case VMCode.ADD:
                return `ADD`;
            case VMCode.SUB:
                return `SUB`;
            case VMCode.MUL:
                return `MUL`;
            case VMCode.DIV:
                return `DIV`;
            case VMCode.MOD:
                return `MOD`;
            case VMCode.MORE:
                return `MORE`;
            case VMCode.LESS:
                return `LESS`;
            case VMCode.MOREOREQUAL:
                return `MOREOREQUAL`;
            case VMCode.LESSOREQUAL:
                return `LESSOREQUAL`;
            case VMCode.NOTEQUAL:
                return `NOTEQUAL`;
            default:
                return `UNKNOWN ${this.code}`;
        }
    }
}

/**
 * RGB color structure for VM operations
 */
export interface RGB {
    r: number;
    g: number;
    b: number;
}

/**
 * Built-in function definitions for the VM
 */
export enum BuiltInFunction {
    ABS = 'abs',
    DIST = 'dist',
    ANGLE = 'angle',
    RAND = 'rand',
    SIN = 'sin',
    COS = 'cos',
    TAN = 'tan',
    ASIN = 'asin',
    ACOS = 'acos',
    ATAN = 'atan',
    ATAN2 = 'atan2',
    SQRT = 'sqrt',
    POW = 'pow',
    LOG = 'log',
    EXP = 'exp',
    FLOOR = 'floor',
    CEIL = 'ceil',
    ROUND = 'round',
    MIN = 'min',
    MAX = 'max',
    CLAMP = 'clamp'
}

/**
 * Function signature for built-in functions
 */
export interface FunctionSignature {
    name: string;
    argCount: number;
    execute: (...args: number[]) => number;
}

/**
 * Built-in function implementations
 */
export const BUILTIN_FUNCTIONS: Map<string, FunctionSignature> = new Map([
    ['abs', { name: 'abs', argCount: 1, execute: (x) => Math.abs(x) }],
    ['dist', { name: 'dist', argCount: 4, execute: (x1, y1, x2, y2) => Math.sqrt((x2-x1)*(x2-x1) + (y2-y1)*(y2-y1)) }],
    ['angle', { name: 'angle', argCount: 4, execute: (x1, y1, x2, y2) => Math.atan2(y2-y1, x2-x1) * 180 / Math.PI }],
    ['rand', { name: 'rand', argCount: 2, execute: (min, max) => Math.random() * (max - min) + min }],
    ['sin', { name: 'sin', argCount: 1, execute: (x) => Math.sin(x * Math.PI / 180) }],
    ['cos', { name: 'cos', argCount: 1, execute: (x) => Math.cos(x * Math.PI / 180) }],
    ['tan', { name: 'tan', argCount: 1, execute: (x) => Math.tan(x * Math.PI / 180) }],
    ['asin', { name: 'asin', argCount: 1, execute: (x) => Math.asin(x) * 180 / Math.PI }],
    ['acos', { name: 'acos', argCount: 1, execute: (x) => Math.acos(x) * 180 / Math.PI }],
    ['atan', { name: 'atan', argCount: 1, execute: (x) => Math.atan(x) * 180 / Math.PI }],
    ['atan2', { name: 'atan2', argCount: 2, execute: (y, x) => Math.atan2(y, x) * 180 / Math.PI }],
    ['sqrt', { name: 'sqrt', argCount: 1, execute: (x) => Math.sqrt(x) }],
    ['pow', { name: 'pow', argCount: 2, execute: (x, y) => Math.pow(x, y) }],
    ['log', { name: 'log', argCount: 1, execute: (x) => Math.log(x) }],
    ['exp', { name: 'exp', argCount: 1, execute: (x) => Math.exp(x) }],
    ['floor', { name: 'floor', argCount: 1, execute: (x) => Math.floor(x) }],
    ['ceil', { name: 'ceil', argCount: 1, execute: (x) => Math.ceil(x) }],
    ['round', { name: 'round', argCount: 1, execute: (x) => Math.round(x) }],
    ['min', { name: 'min', argCount: 2, execute: (x, y) => Math.min(x, y) }],
    ['max', { name: 'max', argCount: 2, execute: (x, y) => Math.max(x, y) }],
    ['clamp', { name: 'clamp', argCount: 3, execute: (x, min, max) => Math.max(min, Math.min(max, x)) }]
]);

/**
 * VM execution context
 */
export interface VMContext {
    propertyContainer?: any; // Will be PropertyContainer when implemented
    globalVariables?: Map<string, number>;
    localVariables?: Map<string, number>;
}
