import { _decorator, Component, Node, Vec2, Rect, Color } from 'cc';
import { MapLayer, MapLayerType } from '../core/LevelData';
import { LayerDepths, IDGenerator } from '../core/Types';
import { MapLayerComponent } from './MapLayerComponent';

const { ccclass, property } = _decorator;




/**
 * Map system manager
 */
@ccclass('MapSystem')
export class MapSystem extends Component {
    private mapLayers: Map<string, MapLayerComponent> = new Map();
    private layerOrder: MapLayerType[] = [
        MapLayerType.BG_VeryFar,
        MapLayerType.BG_Far,
        MapLayerType.BG_Mid,
        MapLayerType.BG_Close,
        MapLayerType.BG_VeryClose,
        MapLayerType.Player,
        MapLayerType.FG_VeryClose,
        MapLayerType.FG_Close
    ];
    
    protected start(): void {
        console.log('MapSystem initialized');
    }
    
    /**
     * Add a map layer
     */
    public async addMapLayer(mapLayer: MapLayer): Promise<MapLayerComponent | null> {
        try {
            // Create layer node
            const layerNode = new Node(`MapLayer_${mapLayer.id}`);
            const component = layerNode.addComponent(MapLayerComponent);
            component.mapLayerData = mapLayer;
            
            // Add to scene with proper ordering
            this.node.addChild(layerNode);
            this.updateLayerOrdering();
            
            // Load the layer
            await component.loadLayer();
            
            // Register the layer
            this.mapLayers.set(mapLayer.id, component);
            
            console.log(`Map layer added: ${mapLayer.id}`);
            return component;
        } catch (error) {
            console.error(`Failed to add map layer: ${mapLayer.id}`, error);
            return null;
        }
    }
    
    /**
     * Remove a map layer
     */
    public removeMapLayer(layerId: string): void {
        const component = this.mapLayers.get(layerId);
        if (component) {
            component.unloadLayer();
            component.node.destroy();
            this.mapLayers.delete(layerId);
            console.log(`Map layer removed: ${layerId}`);
        }
    }
    
    /**
     * Get a map layer component
     */
    public getMapLayer(layerId: string): MapLayerComponent | undefined {
        return this.mapLayers.get(layerId);
    }
    
    /**
     * Get all map layers
     */
    public getAllMapLayers(): MapLayerComponent[] {
        return Array.from(this.mapLayers.values());
    }
    
    /**
     * Update layer ordering based on layer types
     */
    private updateLayerOrdering(): void {
        const layerComponents = Array.from(this.mapLayers.values());
        
        layerComponents.sort((a, b) => {
            const aOrder = this.layerOrder.indexOf(a.mapLayerData?.layerType || MapLayerType.BG_Mid);
            const bOrder = this.layerOrder.indexOf(b.mapLayerData?.layerType || MapLayerType.BG_Mid);
            return aOrder - bOrder;
        });
        
        layerComponents.forEach((component, index) => {
            component.node.setSiblingIndex(index);
        });
    }
    
    /**
     * Set global scroll speed multiplier
     */
    public setGlobalScrollMultiplier(multiplier: number): void {
        this.mapLayers.forEach(component => {
            if (component.mapLayerData) {
                // This would modify the scroll speed, but we need to be careful
                // not to permanently modify the original data
                console.log(`Applying scroll multiplier ${multiplier} to layer ${component.mapLayerData.id}`);
            }
        });
    }
    
    /**
     * Show/hide layers by type
     */
    public setLayerTypeVisibility(layerType: MapLayerType, visible: boolean): void {
        this.mapLayers.forEach(component => {
            if (component.mapLayerData?.layerType === layerType) {
                component.setVisible(visible);
            }
        });
    }
    
    /**
     * Create a new map layer
     */
    public createMapLayer(
        layerType: MapLayerType,
        prefabPath: string,
        bounds: Rect,
        scrollSpeed: Vec2 = Vec2.ZERO,
        repeatMode: 'none' | 'vertical' | 'horizontal' | 'both' = 'vertical'
    ): MapLayer {
        const mapLayer: MapLayer = {
            id: IDGenerator.generate('map_layer'),
            prefabPath: prefabPath,
            layerType: layerType,
            depth: LayerDepths[layerType] || 0,
            scrollSpeed: scrollSpeed.clone(),
            repeatMode: repeatMode,
            offset: Vec2.ZERO,
            bounds: bounds,
            isVisible: true,
            editorData: {
                name: `${layerType} Layer`,
                color: Color.WHITE,
                locked: false
            }
        };
        
        return mapLayer;
    }
}
