{"version": 3, "sources": ["file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/WorldInitializeData.ts"], "names": ["WorldInitializeData", "GameMode", "DifficultyLevel", "constructor", "config", "modeId", "levelId", "randomSeed", "difficulty", "physicsConfig", "renderConfig", "playerConfig", "levelConfig", "customData", "debugFlags", "STORY", "Date", "now", "NORMAL", "gravity", "timeScale", "maxVelocity", "enableCollision", "enableParticles", "maxParticles", "enablePostProcessing", "renderScale", "startPosition", "x", "y", "maxHealth", "startingWeapon", "abilities", "backgroundMusic", "backgroundImage", "environmentEffects", "boundaries", "minX", "maxX", "minY", "maxY", "enableDebugDraw", "showCollisionBounds", "showPerformanceStats", "logSystemUpdates", "fromConfig", "validate", "errors", "trim", "push", "clone", "JSON", "parse", "stringify"], "mappings": ";;;iBAgIaA,mB;;;;;;;;;;;;;AAhIb;AACA;AACA;0BACYC,Q,0BAAAA,Q;AAAAA,QAAAA,Q;AAAAA,QAAAA,Q;AAAAA,QAAAA,Q;AAAAA,QAAAA,Q;AAAAA,QAAAA,Q;eAAAA,Q;;AAQZ;AACA;AACA;;;iCACYC,e,0BAAAA,e;AAAAA,QAAAA,e;AAAAA,QAAAA,e;AAAAA,QAAAA,e;AAAAA,QAAAA,e;eAAAA,e;;AAOZ;AACA;AACA;;AAQA;AACA;AACA;;AAQA;AACA;AACA;;AAQA;AACA;AACA;;AAaA;AACA;AACA;;AAQA;AACA;AACA;AACA;;;AA6CA;AACA;AACA;AACA;qCACaF,mB,GAAN,MAAMA,mBAAN,CAA0D;AAgC7D;AACJ;AACA;AACIG,QAAAA,WAAW,CAACC,MAAD,EAAyC;AAAA;;AAjCpD;AAiCoD,eAhCpCC,MAgCoC;;AA9BpD;AA8BoD,eA7BpCC,OA6BoC;;AA3BpD;AA2BoD,eA1BpCC,UA0BoC;;AAxBpD;AAwBoD,eAvBpCC,UAuBoC;;AArBpD;AAqBoD,eApBpCC,aAoBoC;;AAlBpD;AAkBoD,eAjBpCC,YAiBoC;;AAfpD;AAeoD,eAdpCC,YAcoC;;AAZpD;AAYoD,eAXpCC,WAWoC;;AATpD;AASoD,eARpCC,UAQoC;;AANpD;AAMoD,eALpCC,UAKoC;AAChD;AACA,eAAKT,MAAL,qBAAcD,MAAd,oBAAcA,MAAM,CAAEC,MAAtB,6BAAgCJ,QAAQ,CAACc,KAAzC;AACA,eAAKT,OAAL,sBAAeF,MAAf,oBAAeA,MAAM,CAAEE,OAAvB,8BAAkC,EAAlC;AACA,eAAKC,UAAL,yBAAkBH,MAAlB,oBAAkBA,MAAM,CAAEG,UAA1B,iCAAwCS,IAAI,CAACC,GAAL,EAAxC;AACA,eAAKT,UAAL,yBAAkBJ,MAAlB,oBAAkBA,MAAM,CAAEI,UAA1B,iCAAwCN,eAAe,CAACgB,MAAxD,CALgD,CAOhD;;AACA,eAAKT,aAAL,GAAqB;AACjBU,YAAAA,OAAO,2BAAEf,MAAF,sCAAEA,MAAM,CAAEK,aAAV,qBAAE,uBAAuBU,OAAzB,oCAAoC,CAAC,GAD3B;AAEjBC,YAAAA,SAAS,4BAAEhB,MAAF,sCAAEA,MAAM,CAAEK,aAAV,qBAAE,uBAAuBW,SAAzB,qCAAsC,GAF9B;AAGjBC,YAAAA,WAAW,4BAAEjB,MAAF,sCAAEA,MAAM,CAAEK,aAAV,qBAAE,uBAAuBY,WAAzB,qCAAwC,IAHlC;AAIjBC,YAAAA,eAAe,4BAAElB,MAAF,sCAAEA,MAAM,CAAEK,aAAV,qBAAE,uBAAuBa,eAAzB,qCAA4C;AAJ1C,WAArB,CARgD,CAehD;;AACA,eAAKZ,YAAL,GAAoB;AAChBa,YAAAA,eAAe,2BAAEnB,MAAF,oCAAEA,MAAM,CAAEM,YAAV,qBAAE,qBAAsBa,eAAxB,oCAA2C,IAD1C;AAEhBC,YAAAA,YAAY,4BAAEpB,MAAF,qCAAEA,MAAM,CAAEM,YAAV,qBAAE,sBAAsBc,YAAxB,qCAAwC,IAFpC;AAGhBC,YAAAA,oBAAoB,4BAAErB,MAAF,qCAAEA,MAAM,CAAEM,YAAV,qBAAE,sBAAsBe,oBAAxB,qCAAgD,IAHpD;AAIhBC,YAAAA,WAAW,4BAAEtB,MAAF,qCAAEA,MAAM,CAAEM,YAAV,qBAAE,sBAAsBgB,WAAxB,qCAAuC;AAJlC,WAApB,CAhBgD,CAuBhD;;AACA,eAAKf,YAAL,GAAoB;AAChBgB,YAAAA,aAAa,EAAE;AACXC,cAAAA,CAAC,2BAAExB,MAAF,oCAAEA,MAAM,CAAEO,YAAV,qCAAE,qBAAsBgB,aAAxB,qBAAE,qBAAqCC,CAAvC,oCAA4C,CADlC;AAEXC,cAAAA,CAAC,4BAAEzB,MAAF,qCAAEA,MAAM,CAAEO,YAAV,sCAAE,sBAAsBgB,aAAxB,qBAAE,sBAAqCE,CAAvC,qCAA4C;AAFlC,aADC;AAKhBC,YAAAA,SAAS,4BAAE1B,MAAF,qCAAEA,MAAM,CAAEO,YAAV,qBAAE,sBAAsBmB,SAAxB,qCAAqC,GAL9B;AAMhBC,YAAAA,cAAc,4BAAE3B,MAAF,qCAAEA,MAAM,CAAEO,YAAV,qBAAE,sBAAsBoB,cAAxB,qCAA0C,SANxC;AAOhBC,YAAAA,SAAS,4BAAE5B,MAAF,qCAAEA,MAAM,CAAEO,YAAV,qBAAE,sBAAsBqB,SAAxB,qCAAqC;AAP9B,WAApB,CAxBgD,CAkChD;;AACA,eAAKpB,WAAL,GAAmB;AACfqB,YAAAA,eAAe,2BAAE7B,MAAF,mCAAEA,MAAM,CAAEQ,WAAV,qBAAE,oBAAqBqB,eAAvB,oCAA0C,EAD1C;AAEfC,YAAAA,eAAe,4BAAE9B,MAAF,oCAAEA,MAAM,CAAEQ,WAAV,qBAAE,qBAAqBsB,eAAvB,qCAA0C,EAF1C;AAGfC,YAAAA,kBAAkB,2BAAE/B,MAAF,oCAAEA,MAAM,CAAEQ,WAAV,qBAAE,qBAAqBuB,kBAAvB,oCAA6C,EAHhD;AAIfC,YAAAA,UAAU,EAAE;AACRC,cAAAA,IAAI,4BAAEjC,MAAF,oCAAEA,MAAM,CAAEQ,WAAV,qCAAE,qBAAqBwB,UAAvB,qBAAE,qBAAiCC,IAAnC,qCAA2C,CAAC,IADxC;AAERC,cAAAA,IAAI,4BAAElC,MAAF,oCAAEA,MAAM,CAAEQ,WAAV,qCAAE,qBAAqBwB,UAAvB,qBAAE,qBAAiCE,IAAnC,qCAA2C,IAFvC;AAGRC,cAAAA,IAAI,4BAAEnC,MAAF,oCAAEA,MAAM,CAAEQ,WAAV,qCAAE,qBAAqBwB,UAAvB,qBAAE,qBAAiCG,IAAnC,qCAA2C,CAAC,IAHxC;AAIRC,cAAAA,IAAI,4BAAEpC,MAAF,oCAAEA,MAAM,CAAEQ,WAAV,qCAAE,qBAAqBwB,UAAvB,qBAAE,qBAAiCI,IAAnC,qCAA2C;AAJvC;AAJG,WAAnB,CAnCgD,CA+ChD;;AACA,eAAK3B,UAAL,yBAAkBT,MAAlB,oBAAkBA,MAAM,CAAES,UAA1B,iCAAwC,EAAxC,CAhDgD,CAkDhD;;AACA,eAAKC,UAAL,GAAkB;AACd2B,YAAAA,eAAe,2BAAErC,MAAF,kCAAEA,MAAM,CAAEU,UAAV,qBAAE,mBAAoB2B,eAAtB,oCAAyC,KAD1C;AAEdC,YAAAA,mBAAmB,2BAAEtC,MAAF,mCAAEA,MAAM,CAAEU,UAAV,qBAAE,oBAAoB4B,mBAAtB,oCAA6C,KAFlD;AAGdC,YAAAA,oBAAoB,4BAAEvC,MAAF,mCAAEA,MAAM,CAAEU,UAAV,qBAAE,oBAAoB6B,oBAAtB,qCAA8C,KAHpD;AAIdC,YAAAA,gBAAgB,2BAAExC,MAAF,mCAAEA,MAAM,CAAEU,UAAV,qBAAE,oBAAoB8B,gBAAtB,oCAA0C;AAJ5C,WAAlB;AAMH;AAED;AACJ;AACA;AACA;AACA;;;AAC4B,eAAVC,UAAU,CAACzC,MAAD,EAA6D;AACjF,iBAAO,IAAIJ,mBAAJ,CAAwBI,MAAxB,CAAP;AACH;AAED;AACJ;AACA;AACA;;;AACW0C,QAAAA,QAAQ,GAAa;AACxB,cAAMC,MAAgB,GAAG,EAAzB;;AAEA,cAAI,CAAC,KAAKzC,OAAN,IAAiB,KAAKA,OAAL,CAAa0C,IAAb,OAAwB,EAA7C,EAAiD;AAC7CD,YAAAA,MAAM,CAACE,IAAP,CAAY,sBAAZ;AACH;;AAED,cAAI,KAAK1C,UAAL,GAAkB,CAAtB,EAAyB;AACrBwC,YAAAA,MAAM,CAACE,IAAP,CAAY,kCAAZ;AACH;;AAED,cAAI,KAAKxC,aAAL,CAAmBW,SAAnB,IAAgC,CAApC,EAAuC;AACnC2B,YAAAA,MAAM,CAACE,IAAP,CAAY,qCAAZ;AACH;;AAED,cAAI,KAAKvC,YAAL,CAAkBc,YAAlB,GAAiC,CAArC,EAAwC;AACpCuB,YAAAA,MAAM,CAACE,IAAP,CAAY,oCAAZ;AACH;;AAED,cAAI,KAAKtC,YAAL,CAAkBmB,SAAlB,IAA+B,CAAnC,EAAsC;AAClCiB,YAAAA,MAAM,CAACE,IAAP,CAAY,oCAAZ;AACH;;AAED,iBAAOF,MAAP;AACH;AAED;AACJ;AACA;AACA;;;AACWG,QAAAA,KAAK,GAAyB;AACjC,iBAAOlD,mBAAmB,CAAC6C,UAApB,CAA+BM,IAAI,CAACC,KAAL,CAAWD,IAAI,CAACE,SAAL,CAAe,IAAf,CAAX,CAA/B,CAAP;AACH;;AA3I4D,O", "sourcesContent": ["/**\r\n * Enumeration for different game modes\r\n */\r\nexport enum GameMode {\r\n    STORY = \"story\",\r\n    ENDLESS = \"endless\",\r\n    CHALLENGE = \"challenge\",\r\n    TUTORIAL = \"tutorial\",\r\n    CUSTOM = \"custom\"\r\n}\r\n\r\n/**\r\n * Enumeration for difficulty levels\r\n */\r\nexport enum DifficultyLevel {\r\n    EASY = \"easy\",\r\n    NORMAL = \"normal\",\r\n    HARD = \"hard\",\r\n    EXPERT = \"expert\"\r\n}\r\n\r\n/**\r\n * Configuration for world physics\r\n */\r\nexport interface WorldPhysicsConfig {\r\n    readonly gravity: number;\r\n    readonly timeScale: number;\r\n    readonly maxVelocity: number;\r\n    readonly enableCollision: boolean;\r\n}\r\n\r\n/**\r\n * Configuration for world rendering\r\n */\r\nexport interface WorldRenderConfig {\r\n    readonly enableParticles: boolean;\r\n    readonly maxParticles: number;\r\n    readonly enablePostProcessing: boolean;\r\n    readonly renderScale: number;\r\n}\r\n\r\n/**\r\n * Player configuration for world initialization\r\n */\r\nexport interface PlayerConfig {\r\n    readonly startPosition: { readonly x: number; readonly y: number };\r\n    readonly maxHealth: number;\r\n    readonly startingWeapon: string;\r\n    readonly abilities: readonly string[];\r\n}\r\n\r\n/**\r\n * Level configuration for world initialization\r\n */\r\nexport interface LevelConfig {\r\n    readonly backgroundMusic: string;\r\n    readonly backgroundImage: string;\r\n    readonly environmentEffects: readonly string[];\r\n    readonly boundaries: {\r\n        readonly minX: number;\r\n        readonly maxX: number;\r\n        readonly minY: number;\r\n        readonly maxY: number;\r\n    };\r\n}\r\n\r\n/**\r\n * Debug flags configuration\r\n */\r\nexport interface DebugFlags {\r\n    readonly enableDebugDraw: boolean;\r\n    readonly showCollisionBounds: boolean;\r\n    readonly showPerformanceStats: boolean;\r\n    readonly logSystemUpdates: boolean;\r\n}\r\n\r\n/**\r\n * Interface for world initialization data\r\n * All members are readonly to ensure immutability\r\n */\r\nexport interface IWorldInitializeData {\r\n    /** Unique identifier for the game mode */\r\n    readonly modeId: GameMode;\r\n\r\n    /** Unique identifier for the level */\r\n    readonly levelId: string;\r\n\r\n    /** Random seed for deterministic gameplay */\r\n    readonly randomSeed: number;\r\n\r\n    /** Difficulty level for the game session */\r\n    readonly difficulty: DifficultyLevel;\r\n\r\n    /** Physics configuration for the world */\r\n    readonly physicsConfig: WorldPhysicsConfig;\r\n\r\n    /** Rendering configuration for the world */\r\n    readonly renderConfig: WorldRenderConfig;\r\n\r\n    /** Player configuration */\r\n    readonly playerConfig: PlayerConfig;\r\n\r\n    /** Level configuration */\r\n    readonly levelConfig: LevelConfig;\r\n\r\n    /** Custom data for specific game modes or levels */\r\n    readonly customData: { readonly [key: string]: any };\r\n\r\n    /** Debug flags for development */\r\n    readonly debugFlags: DebugFlags;\r\n\r\n    /**\r\n     * Validate the initialization data\r\n     * @returns Array of validation errors, empty if valid\r\n     */\r\n    validate(): string[];\r\n\r\n    /**\r\n     * Clone the initialization data\r\n     * @returns Deep copy of the initialization data\r\n     */\r\n    clone(): IWorldInitializeData;\r\n}\r\n\r\n/**\r\n * Implementation of world initialization data\r\n * Contains all necessary parameters to set up a complete game session\r\n */\r\nexport class WorldInitializeData implements IWorldInitializeData {\r\n\r\n    /** Unique identifier for the game mode */\r\n    public readonly modeId: GameMode;\r\n\r\n    /** Unique identifier for the level */\r\n    public readonly levelId: string;\r\n\r\n    /** Random seed for deterministic gameplay */\r\n    public readonly randomSeed: number;\r\n\r\n    /** Difficulty level for the game session */\r\n    public readonly difficulty: DifficultyLevel;\r\n\r\n    /** Physics configuration for the world */\r\n    public readonly physicsConfig: WorldPhysicsConfig;\r\n\r\n    /** Rendering configuration for the world */\r\n    public readonly renderConfig: WorldRenderConfig;\r\n\r\n    /** Player configuration */\r\n    public readonly playerConfig: PlayerConfig;\r\n\r\n    /** Level configuration */\r\n    public readonly levelConfig: LevelConfig;\r\n\r\n    /** Custom data for specific game modes or levels */\r\n    public readonly customData: { readonly [key: string]: any };\r\n\r\n    /** Debug flags for development */\r\n    public readonly debugFlags: DebugFlags;\r\n\r\n    /**\r\n     * Create a new WorldInitializeData with default values\r\n     */\r\n    constructor(config?: Partial<IWorldInitializeData>) {\r\n        // Set default values\r\n        this.modeId = config?.modeId ?? GameMode.STORY;\r\n        this.levelId = config?.levelId ?? \"\";\r\n        this.randomSeed = config?.randomSeed ?? Date.now();\r\n        this.difficulty = config?.difficulty ?? DifficultyLevel.NORMAL;\r\n\r\n        // Set default physics config\r\n        this.physicsConfig = {\r\n            gravity: config?.physicsConfig?.gravity ?? -9.8,\r\n            timeScale: config?.physicsConfig?.timeScale ?? 1.0,\r\n            maxVelocity: config?.physicsConfig?.maxVelocity ?? 1000,\r\n            enableCollision: config?.physicsConfig?.enableCollision ?? true\r\n        };\r\n\r\n        // Set default render config\r\n        this.renderConfig = {\r\n            enableParticles: config?.renderConfig?.enableParticles ?? true,\r\n            maxParticles: config?.renderConfig?.maxParticles ?? 1000,\r\n            enablePostProcessing: config?.renderConfig?.enablePostProcessing ?? true,\r\n            renderScale: config?.renderConfig?.renderScale ?? 1.0\r\n        };\r\n\r\n        // Set default player config\r\n        this.playerConfig = {\r\n            startPosition: {\r\n                x: config?.playerConfig?.startPosition?.x ?? 0,\r\n                y: config?.playerConfig?.startPosition?.y ?? 0\r\n            },\r\n            maxHealth: config?.playerConfig?.maxHealth ?? 100,\r\n            startingWeapon: config?.playerConfig?.startingWeapon ?? \"default\",\r\n            abilities: config?.playerConfig?.abilities ?? []\r\n        };\r\n\r\n        // Set default level config\r\n        this.levelConfig = {\r\n            backgroundMusic: config?.levelConfig?.backgroundMusic ?? \"\",\r\n            backgroundImage: config?.levelConfig?.backgroundImage ?? \"\",\r\n            environmentEffects: config?.levelConfig?.environmentEffects ?? [],\r\n            boundaries: {\r\n                minX: config?.levelConfig?.boundaries?.minX ?? -1000,\r\n                maxX: config?.levelConfig?.boundaries?.maxX ?? 1000,\r\n                minY: config?.levelConfig?.boundaries?.minY ?? -1000,\r\n                maxY: config?.levelConfig?.boundaries?.maxY ?? 1000\r\n            }\r\n        };\r\n\r\n        // Set default custom data\r\n        this.customData = config?.customData ?? {};\r\n\r\n        // Set default debug flags\r\n        this.debugFlags = {\r\n            enableDebugDraw: config?.debugFlags?.enableDebugDraw ?? false,\r\n            showCollisionBounds: config?.debugFlags?.showCollisionBounds ?? false,\r\n            showPerformanceStats: config?.debugFlags?.showPerformanceStats ?? false,\r\n            logSystemUpdates: config?.debugFlags?.logSystemUpdates ?? false\r\n        };\r\n    }\r\n\r\n    /**\r\n     * Create WorldInitializeData from a configuration object\r\n     * @param config Partial configuration object\r\n     * @returns New WorldInitializeData instance\r\n     */\r\n    public static fromConfig(config: Partial<IWorldInitializeData>): WorldInitializeData {\r\n        return new WorldInitializeData(config);\r\n    }\r\n\r\n    /**\r\n     * Validate the initialization data\r\n     * @returns Array of validation errors, empty if valid\r\n     */\r\n    public validate(): string[] {\r\n        const errors: string[] = [];\r\n\r\n        if (!this.levelId || this.levelId.trim() === \"\") {\r\n            errors.push(\"Level ID is required\");\r\n        }\r\n\r\n        if (this.randomSeed < 0) {\r\n            errors.push(\"Random seed must be non-negative\");\r\n        }\r\n\r\n        if (this.physicsConfig.timeScale <= 0) {\r\n            errors.push(\"Physics time scale must be positive\");\r\n        }\r\n\r\n        if (this.renderConfig.maxParticles < 0) {\r\n            errors.push(\"Max particles must be non-negative\");\r\n        }\r\n\r\n        if (this.playerConfig.maxHealth <= 0) {\r\n            errors.push(\"Player max health must be positive\");\r\n        }\r\n\r\n        return errors;\r\n    }\r\n\r\n    /**\r\n     * Clone the initialization data\r\n     * @returns Deep copy of the initialization data\r\n     */\r\n    public clone(): IWorldInitializeData {\r\n        return WorldInitializeData.fromConfig(JSON.parse(JSON.stringify(this)));\r\n    }\r\n}"]}