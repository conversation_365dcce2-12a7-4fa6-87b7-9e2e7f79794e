/**
 * CrazyStorm 2.0 Integration for Cocos Creator
 * Rebounder Component Implementation
 * 
 * This component bounces particles when they collide with it,
 * creating ricochet effects and complex particle behaviors.
 */

import { _decorator, Vec2, log, warn, Enum } from 'cc';
import { CrazyStormComponent } from './CrazyStormComponent';
import { Particle } from '../physics/ParticlePhysics';
import { 
    RebounderComponent as RebounderData,
    FieldShape,
    Reach,
    ComponentType
} from '../core/CrazyStormTypes';

const { ccclass, property } = _decorator;

/**
 * Rebound types
 */
export enum ReboundType {
    Reflect = 'Reflect',      // Perfect reflection
    Absorb = 'Absorb',        // Absorb and stop
    Scatter = 'Scatter',      // Random scatter
    Reverse = 'Reverse'       // Reverse direction
}

// Register the enum with Cocos Creator
Enum(ReboundType);

/**
 * Rebounder component for Cocos Creator
 */
@ccclass('RebounderComponent')
export class RebounderComponent extends CrazyStormComponent {
    @property({ displayName: "Half Width" })
    halfWidth: number = 50;

    @property({ displayName: "Half Height" })
    halfHeight: number = 50;

    @property({ type: Enum(FieldShape), displayName: "Field Shape" })
    fieldShape: FieldShape = FieldShape.Rectangle;

    @property({ type: Enum(Reach), displayName: "Reach" })
    reach: Reach = Reach.All;

    @property({ displayName: "Target Name" })
    targetName: string = '';

    @property({ type: Enum(ReboundType), displayName: "Rebound Type" })
    reboundType: ReboundType = ReboundType.Reflect;

    @property({ displayName: "Rebound Factor", range: [0, 2, 0.1] })
    reboundFactor: number = 1.0;

    @property({ displayName: "Scatter Angle", range: [0, 180, 1] })
    scatterAngle: number = 30;

    @property({ displayName: "Debug Draw" })
    debugDraw: boolean = false;

    // Runtime data
    private particleManager: any = null;
    private reboundedParticles: Set<number> = new Set(); // Track recently rebounded particles

    protected initializeComponent(): void {
        this.reboundedParticles = new Set();
        log(`RebounderComponent: Initialized at position [${this.node.position.x}, ${this.node.position.y}]`);
    }

    protected onDataInitialized(): void {
        if (!this.crazyStormData || this.crazyStormData.specificType !== ComponentType.Rebounder) {
            warn('RebounderComponent: Invalid CrazyStorm data');
            return;
        }

        const data = this.crazyStormData as RebounderData;
        
        log(`RebounderComponent: Initialized with rebound type ${this.reboundType}`);
    }

    protected onReset(): void {
        this.reboundedParticles.clear();
    }

    protected onUpdate(deltaTime: number): void {
        // Check for particle collisions and apply rebounds
        this.checkParticleCollisions();
        
        // Clear recently rebounded particles after a short delay
        this.clearRecentlyRebounded();
    }

    /**
     * Check for particle collisions with the rebounder
     */
    private checkParticleCollisions(): void {
        const particlesInRange = this.getParticlesInRange();

        particlesInRange.forEach(particle => {
            if (this.shouldReboundParticle(particle)) {
                this.reboundParticle(particle);
            }
        });
    }

    /**
     * Get particles within the rebounder range
     */
    private getParticlesInRange(): Particle[] {
        // This would typically get particles from a particle manager
        // For now, we'll return an empty array as a placeholder
        const particles: Particle[] = [];

        // In a real implementation, this would query the particle manager:
        // const particles = this.particleManager.getParticlesInRect(
        //     this.node.position.x - this.halfWidth,
        //     this.node.position.x + this.halfWidth,
        //     this.node.position.y - this.halfHeight,
        //     this.node.position.y + this.halfHeight
        // );

        // Filter by shape and reach criteria
        return particles.filter(particle => 
            this.isParticleInShape(particle) && this.shouldAffectParticle(particle)
        );
    }

    /**
     * Check if particle is within the rebounder shape
     */
    private isParticleInShape(particle: Particle): boolean {
        const rebounderPos = new Vec2(this.node.position.x, this.node.position.y);
        const particlePos = particle.position;

        switch (this.fieldShape) {
            case FieldShape.Rectangle:
                return Math.abs(particlePos.x - rebounderPos.x) <= this.halfWidth &&
                       Math.abs(particlePos.y - rebounderPos.y) <= this.halfHeight;

            case FieldShape.Circle:
                const distance = Math.sqrt(
                    Math.pow(particlePos.x - rebounderPos.x, 2) +
                    Math.pow(particlePos.y - rebounderPos.y, 2)
                );
                return distance <= this.halfWidth; // Use halfWidth as radius

            default:
                return false;
        }
    }

    /**
     * Check if the rebounder should affect this particle based on reach criteria
     */
    private shouldAffectParticle(particle: Particle): boolean {
        switch (this.reach) {
            case Reach.All:
                return true;

            case Reach.Layer:
                // Check if particle is from the target layer or same layer
                return particle.layerId === this.getLayerId() || 
                       this.getLayerName() === this.targetName;

            case Reach.Name:
                // Check if particle emitter name matches target name
                return this.getEmitterName(particle) === this.targetName;

            default:
                return false;
        }
    }

    /**
     * Check if particle should be rebounded
     */
    private shouldReboundParticle(particle: Particle): boolean {
        // Don't rebound if particle ignores rebounds
        if (particle.ignoreRebound) {
            return false;
        }

        // Don't rebound the same particle multiple times in quick succession
        if (this.reboundedParticles.has(particle.id)) {
            return false;
        }

        return true;
    }

    /**
     * Rebound a particle
     */
    private reboundParticle(particle: Particle): void {
        log(`RebounderComponent: Rebounding particle ${particle.id}`);

        // Mark particle as recently rebounded
        this.reboundedParticles.add(particle.id);

        // Apply rebound based on type
        switch (this.reboundType) {
            case ReboundType.Reflect:
                this.reflectParticle(particle);
                break;

            case ReboundType.Absorb:
                this.absorbParticle(particle);
                break;

            case ReboundType.Scatter:
                this.scatterParticle(particle);
                break;

            case ReboundType.Reverse:
                this.reverseParticle(particle);
                break;
        }
    }

    /**
     * Reflect particle off the rebounder surface
     */
    private reflectParticle(particle: Particle): void {
        const rebounderPos = new Vec2(this.node.position.x, this.node.position.y);
        const normal = this.getSurfaceNormal(particle.position, rebounderPos);

        // Calculate reflection vector: R = V - 2(V·N)N
        const velocity = particle.velocity;
        const dotProduct = velocity.x * normal.x + velocity.y * normal.y;
        
        particle.velocity.set(
            (velocity.x - 2 * dotProduct * normal.x) * this.reboundFactor,
            (velocity.y - 2 * dotProduct * normal.y) * this.reboundFactor
        );

        // Update speed vector to match velocity
        const speed = Math.sqrt(particle.velocity.x * particle.velocity.x + particle.velocity.y * particle.velocity.y);
        const angle = Math.atan2(particle.velocity.y, particle.velocity.x) * 180 / Math.PI;
        particle.setSpeedVector(speed, angle);
    }

    /**
     * Absorb particle (stop it)
     */
    private absorbParticle(particle: Particle): void {
        particle.velocity.set(0, 0);
        particle.setSpeedVector(0, 0);
    }

    /**
     * Scatter particle in random direction
     */
    private scatterParticle(particle: Particle): void {
        const currentSpeed = Math.sqrt(particle.velocity.x * particle.velocity.x + particle.velocity.y * particle.velocity.y);
        const currentAngle = Math.atan2(particle.velocity.y, particle.velocity.x) * 180 / Math.PI;
        
        // Add random scatter within scatter angle range
        const scatterOffset = (Math.random() - 0.5) * this.scatterAngle * 2;
        const newAngle = currentAngle + scatterOffset;
        
        particle.setSpeedVector(currentSpeed * this.reboundFactor, newAngle);
    }

    /**
     * Reverse particle direction
     */
    private reverseParticle(particle: Particle): void {
        particle.velocity.set(
            -particle.velocity.x * this.reboundFactor,
            -particle.velocity.y * this.reboundFactor
        );

        const speed = Math.sqrt(particle.velocity.x * particle.velocity.x + particle.velocity.y * particle.velocity.y);
        const angle = Math.atan2(particle.velocity.y, particle.velocity.x) * 180 / Math.PI;
        particle.setSpeedVector(speed, angle);
    }

    /**
     * Get surface normal for reflection calculation
     */
    private getSurfaceNormal(particlePos: Vec2, rebounderPos: Vec2): Vec2 {
        switch (this.fieldShape) {
            case FieldShape.Rectangle:
                // For rectangle, find the closest edge and return its normal
                const dx = particlePos.x - rebounderPos.x;
                const dy = particlePos.y - rebounderPos.y;
                
                if (Math.abs(dx) > Math.abs(dy)) {
                    // Hit left or right edge
                    return new Vec2(dx > 0 ? 1 : -1, 0);
                } else {
                    // Hit top or bottom edge
                    return new Vec2(0, dy > 0 ? 1 : -1);
                }

            case FieldShape.Circle:
                // For circle, normal points from center to particle
                const toParticle = new Vec2(
                    particlePos.x - rebounderPos.x,
                    particlePos.y - rebounderPos.y
                );
                const length = Math.sqrt(toParticle.x * toParticle.x + toParticle.y * toParticle.y);
                
                if (length > 0) {
                    return new Vec2(toParticle.x / length, toParticle.y / length);
                }
                return new Vec2(1, 0); // Default normal

            default:
                return new Vec2(1, 0);
        }
    }

    /**
     * Clear recently rebounded particles
     */
    private clearRecentlyRebounded(): void {
        // In a real implementation, this would use a timer to clear particles
        // after a certain delay. For now, we'll clear them every few frames.
        if (this.currentFrame % 5 === 0) {
            this.reboundedParticles.clear();
        }
    }

    /**
     * Set particle manager reference
     */
    public setParticleManager(manager: any): void {
        this.particleManager = manager;
    }

    /**
     * Get layer ID (placeholder implementation)
     */
    private getLayerId(): number {
        return 0;
    }

    /**
     * Get layer name (placeholder implementation)
     */
    private getLayerName(): string {
        return 'Main';
    }

    /**
     * Get emitter name for a particle (placeholder implementation)
     */
    private getEmitterName(particle: Particle): string {
        return `Emitter_${particle.emitterId}`;
    }

    /**
     * Set rebounder parameters at runtime
     */
    public setReboundParameters(type: ReboundType, factor: number, scatterAngle: number = 30): void {
        this.reboundType = type;
        this.reboundFactor = factor;
        this.scatterAngle = scatterAngle;
    }

    /**
     * Set field size at runtime
     */
    public setFieldSize(halfWidth: number, halfHeight: number): void {
        this.halfWidth = halfWidth;
        this.halfHeight = halfHeight;
    }

    /**
     * Set field shape at runtime
     */
    public setFieldShape(shape: FieldShape): void {
        this.fieldShape = shape;
    }

    /**
     * Get field bounds for debugging
     */
    public getFieldBounds(): { min: Vec2; max: Vec2 } {
        const center = new Vec2(this.node.position.x, this.node.position.y);
        return {
            min: new Vec2(center.x - this.halfWidth, center.y - this.halfHeight),
            max: new Vec2(center.x + this.halfWidth, center.y + this.halfHeight)
        };
    }

    /**
     * Check if a point is within the rebounder field
     */
    public isPointInField(point: Vec2): boolean {
        const rebounderPos = new Vec2(this.node.position.x, this.node.position.y);

        switch (this.fieldShape) {
            case FieldShape.Rectangle:
                return Math.abs(point.x - rebounderPos.x) <= this.halfWidth &&
                       Math.abs(point.y - rebounderPos.y) <= this.halfHeight;

            case FieldShape.Circle:
                const distance = Math.sqrt(
                    Math.pow(point.x - rebounderPos.x, 2) +
                    Math.pow(point.y - rebounderPos.y, 2)
                );
                return distance <= this.halfWidth;

            default:
                return false;
        }
    }

    /**
     * Test rebound calculation for a given velocity
     */
    public testRebound(velocity: Vec2, position: Vec2): Vec2 {
        const rebounderPos = new Vec2(this.node.position.x, this.node.position.y);
        const normal = this.getSurfaceNormal(position, rebounderPos);

        switch (this.reboundType) {
            case ReboundType.Reflect:
                const dotProduct = velocity.x * normal.x + velocity.y * normal.y;
                return new Vec2(
                    (velocity.x - 2 * dotProduct * normal.x) * this.reboundFactor,
                    (velocity.y - 2 * dotProduct * normal.y) * this.reboundFactor
                );

            case ReboundType.Reverse:
                return new Vec2(-velocity.x * this.reboundFactor, -velocity.y * this.reboundFactor);

            case ReboundType.Absorb:
                return new Vec2(0, 0);

            default:
                return velocity.clone();
        }
    }
}
