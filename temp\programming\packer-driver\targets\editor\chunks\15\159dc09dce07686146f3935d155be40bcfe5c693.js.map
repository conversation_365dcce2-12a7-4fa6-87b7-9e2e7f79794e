{"version": 3, "sources": ["file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/base/Object.ts"], "names": ["_decorator", "Component", "ccclass", "CObject", "onLoad", "onObjectInit", "onDestroy", "onObjectDestroy"], "mappings": ";;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;;;;;;;;;OACf;AAAEC,QAAAA;AAAF,O,GAAcF,U;AAEpB;AACA;AACA;AACA;;yBAEsBG,O,WADrBD,OAAO,CAAC,SAAD,C,gBAAR,MACsBC,OADtB,SACsCF,SADtC,CACgD;AAE5C;AACJ;AACA;AACA;;AAGI;AACJ;AACA;AACA;;AAGI;AACJ;AACA;AACA;AACcG,QAAAA,MAAM,GAAS;AACrB,eAAKC,YAAL;AACH;AAED;AACJ;AACA;AACA;;;AACcC,QAAAA,SAAS,GAAS;AACxB,eAAKC,eAAL;AACH;;AA5B2C,O", "sourcesContent": ["import { _decorator, Component } from 'cc';\r\nconst { ccclass } = _decorator;\r\n\r\n/**\r\n * Abstract base class for all world objects\r\n * Inherits from Cocos Creator Component and provides common functionality\r\n */\r\n@ccclass('CObject')\r\nexport abstract class CObject extends Component {\r\n\r\n    /**\r\n     * Called when the object is initialized\r\n     * Override this method to implement object-specific initialization logic\r\n     */\r\n    protected abstract onObjectInit(): void;\r\n\r\n    /**\r\n     * Called when the object is destroyed\r\n     * Override this method to implement object-specific cleanup logic\r\n     */\r\n    protected abstract onObjectDestroy(): void;\r\n\r\n    /**\r\n     * Initialize the object\r\n     * This is called automatically by the framework\r\n     */\r\n    protected onLoad(): void {\r\n        this.onObjectInit();\r\n    }\r\n\r\n    /**\r\n     * Cleanup the object\r\n     * This is called automatically by the framework\r\n     */\r\n    protected onDestroy(): void {\r\n        this.onObjectDestroy();\r\n    }\r\n}"]}