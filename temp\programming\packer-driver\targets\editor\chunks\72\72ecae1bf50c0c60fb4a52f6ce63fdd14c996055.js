System.register(["cc"], function (_export, _context) {
  "use strict";

  var _cclegacy, <PERSON><PERSON>ttr, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>s<PERSON><PERSON><PERSON><PERSON><PERSON>, T<PERSON><PERSON><PERSON>pon, TbGlobalAttr, Tables, _crd, TargetScanStrategy, builtin;

  _export({
    GlobalAttr: void 0,
    ResWeapon: void 0,
    ResWhiteList: void 0,
    TbWeapon: void 0,
    TbGlobalAttr: void 0,
    Tables: void 0,
    builtin: void 0
  });

  return {
    setters: [function (_cc) {
      _cclegacy = _cc.cclegacy;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "da084rha1tNepSmiH/bt+BS", "schema", undefined);

      //------------------------------------------------------------------------------
      // <auto-generated>
      //     This code was generated by a tool.
      //     Changes to this file may cause incorrect behavior and will be lost if
      //     the code is regenerated.
      // </auto-generated>
      //------------------------------------------------------------------------------
      _export("TargetScanStrategy", TargetScanStrategy = /*#__PURE__*/function (TargetScanStrategy) {
        TargetScanStrategy[TargetScanStrategy["Refresh"] = 0] = "Refresh";
        TargetScanStrategy[TargetScanStrategy["Keep"] = 1] = "Keep";
        return TargetScanStrategy;
      }({}));

      (function (_builtin3) {
        class vector2 {
          constructor(_json_) {
            this.x = void 0;
            this.y = void 0;

            if (_json_.x === undefined) {
              throw new Error();
            }

            this.x = _json_.x;

            if (_json_.y === undefined) {
              throw new Error();
            }

            this.y = _json_.y;
          }

          resolve(tables) {}

        }

        _builtin3.vector2 = vector2;
      })(builtin || _export("builtin", builtin = {}));

      (function (_builtin) {
        class vector3 {
          constructor(_json_) {
            this.x = void 0;
            this.y = void 0;
            this.z = void 0;

            if (_json_.x === undefined) {
              throw new Error();
            }

            this.x = _json_.x;

            if (_json_.y === undefined) {
              throw new Error();
            }

            this.y = _json_.y;

            if (_json_.z === undefined) {
              throw new Error();
            }

            this.z = _json_.z;
          }

          resolve(tables) {}

        }

        _builtin.vector3 = vector3;
      })(builtin || _export("builtin", builtin = {}));

      (function (_builtin2) {
        class vector4 {
          constructor(_json_) {
            this.x = void 0;
            this.y = void 0;
            this.z = void 0;
            this.w = void 0;

            if (_json_.x === undefined) {
              throw new Error();
            }

            this.x = _json_.x;

            if (_json_.y === undefined) {
              throw new Error();
            }

            this.y = _json_.y;

            if (_json_.z === undefined) {
              throw new Error();
            }

            this.z = _json_.z;

            if (_json_.w === undefined) {
              throw new Error();
            }

            this.w = _json_.w;
          }

          resolve(tables) {}

        }

        _builtin2.vector4 = vector4;
      })(builtin || _export("builtin", builtin = {}));

      _export("GlobalAttr", GlobalAttr = class GlobalAttr {
        constructor(_json_) {
          /**
           * 每回合发放的金币
           */
          this.GoldProducion = void 0;

          if (_json_.GoldProducion === undefined) {
            throw new Error();
          }

          this.GoldProducion = _json_.GoldProducion;
        }

        resolve(tables) {}

      });
      /**
       * 卡牌
       */


      _export("ResWeapon", ResWeapon = class ResWeapon {
        constructor(_json_) {
          this.id = void 0;
          this.name = void 0;

          if (_json_.id === undefined) {
            throw new Error();
          }

          this.id = _json_.id;

          if (_json_.name === undefined) {
            throw new Error();
          }

          this.name = _json_.name;
        }

        resolve(tables) {}

      });

      _export("ResWhiteList", ResWhiteList = class ResWhiteList {
        constructor(_json_) {
          /**
           * 第一行默认是主键
           */
          this.openid = void 0;

          /**
           * 密码的MD5
           */
          this.password = void 0;

          /**
           * account  status: normal/disable
           */
          this.status = void 0;

          /**
           * 可以访问的内容
           */
          this.privilege = void 0;

          if (_json_.openid === undefined) {
            throw new Error();
          }

          this.openid = _json_.openid;

          if (_json_.password === undefined) {
            throw new Error();
          }

          this.password = _json_.password;

          if (_json_.status === undefined) {
            throw new Error();
          }

          this.status = _json_.status;

          if (_json_.privilege === undefined) {
            throw new Error();
          }

          this.privilege = _json_.privilege;
        }

        resolve(tables) {}

      });
      /**
       * 武器
       */


      _export("TbWeapon", TbWeapon = class TbWeapon {
        constructor(_json_) {
          this._dataMap = void 0;
          this._dataList = void 0;
          this._dataMap = new Map();
          this._dataList = [];

          for (var _json2_ of _json_) {
            let _v;

            _v = new ResWeapon(_json2_);

            this._dataList.push(_v);

            this._dataMap.set(_v.id, _v);
          }
        }

        getDataMap() {
          return this._dataMap;
        }

        getDataList() {
          return this._dataList;
        }

        get(key) {
          return this._dataMap.get(key);
        }

        resolve(tables) {
          for (let data of this._dataList) {
            data.resolve(tables);
          }
        }

      });

      _export("TbGlobalAttr", TbGlobalAttr = class TbGlobalAttr {
        constructor(_json_) {
          this._data = void 0;
          if (_json_.length != 1) throw new Error('table mode=one, but size != 1');
          this._data = new GlobalAttr(_json_[0]);
        }

        getData() {
          return this._data;
        }
        /**
         * 每回合发放的金币
         */


        get GoldProducion() {
          return this._data.GoldProducion;
        }

        resolve(tables) {
          this._data.resolve(tables);
        }

      });

      _export("Tables", Tables = class Tables {
        /**
         * 武器
         */
        get TbWeapon() {
          return this._TbWeapon;
        }

        get TbGlobalAttr() {
          return this._TbGlobalAttr;
        }

        constructor(loader) {
          this._TbWeapon = void 0;
          this._TbGlobalAttr = void 0;
          this._TbWeapon = new TbWeapon(loader('tbweapon'));
          this._TbGlobalAttr = new TbGlobalAttr(loader('tbglobalattr'));

          this._TbWeapon.resolve(this);

          this._TbGlobalAttr.resolve(this);
        }

      });

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=72ecae1bf50c0c60fb4a52f6ce63fdd14c996055.js.map