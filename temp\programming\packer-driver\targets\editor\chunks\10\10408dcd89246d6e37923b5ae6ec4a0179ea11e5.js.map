{"version": 3, "sources": ["file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/bullet/Emitter.ts"], "names": ["_decorator", "Node", "CObject", "ccclass", "property", "Emitter", "type", "canTrigger", "onObjectInit", "onObjectDestroy", "unschedule", "emitBullet", "onEnable", "schedule", "frequency", "onDisable"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,I,OAAAA,I;;AACZC,MAAAA,O,iBAAAA,O;;;;;;;;;OACH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBJ,U;;yBAGRK,O,WADrBF,OAAO,CAAC,SAAD,C,UAGHC,QAAQ,CAAC;AAACE,QAAAA,IAAI,EAAEL;AAAP,OAAD,C,2BAHb,MACsBI,OADtB;AAAA;AAAA,8BAC8C;AAAA;AAAA;;AAAA;;AAK1C;AAL0C;;AAS1C;AAT0C;;AAa1C;AAb0C;AAAA;;AAiB1CE,QAAAA,UAAU,GAAY;AAClB;AACA,iBAAO,KAAP;AACH;AAED;AACJ;AACA;AACA;;;AAGI;AACUC,QAAAA,YAAY,GAAS,CAC3B;AACH;;AAESC,QAAAA,eAAe,GAAS;AAC9B;AACA,eAAKC,UAAL,CAAgB,KAAKC,UAArB;AACH;;AAESC,QAAAA,QAAQ,GAAS;AACvB,eAAKC,QAAL,CAAc,KAAKF,UAAnB,EAA+B,KAAKG,SAApC;AACH;;AAESC,QAAAA,SAAS,GAAS;AACxB,eAAKL,UAAL,CAAgB,KAAKC,UAArB;AACH;;AA5CyC,O;;;;;iBAGrB,I;;gFAGpBP,Q;;;;;iBACe,C;;0FAGfA,Q;;;;;iBACyB,C;;oFAGzBA,Q;;;;;iBACmB,C", "sourcesContent": ["import { _decorator, Node } from 'cc';\r\nimport { CObject } from '../base/Object';\r\nconst { ccclass, property } = _decorator;\r\n\r\n@ccclass('Emitter')\r\nexport abstract class Emitter extends CObject {\r\n\r\n    @property({type: Node})\r\n    bulletPrefab: Node = null;\r\n\r\n    // 发射条数\r\n    @property\r\n    count: number = 1;\r\n\r\n    // 子弹速度乘数\r\n    @property\r\n    speedMultiplier: number = 1;\r\n\r\n    // 频率(间隔多少秒发射一次)\r\n    @property\r\n    frequency: number = 1;\r\n\r\n    canTrigger(): boolean {\r\n        // 检查是否可以触发发射\r\n        return false;\r\n    }\r\n\r\n    /**\r\n     * Abstract method for emitting bullets\r\n     * Must be implemented by concrete emitter classes\r\n     */\r\n    abstract emitBullet(): void;\r\n\r\n    // Implementation of CObject abstract methods\r\n    protected onObjectInit(): void {\r\n        // Override in subclasses if needed\r\n    }\r\n\r\n    protected onObjectDestroy(): void {\r\n        // Clean up any scheduled callbacks\r\n        this.unschedule(this.emitBullet);\r\n    }\r\n\r\n    protected onEnable(): void {\r\n        this.schedule(this.emitBullet, this.frequency);\r\n    }\r\n\r\n    protected onDisable(): void {\r\n        this.unschedule(this.emitBullet);\r\n    }\r\n}\r\n"]}