import { _decorator, Node } from 'cc';
import { CObject } from '../base/Object';
const { ccclass, property } = _decorator;

@ccclass('Emitter')
export abstract class Emitter extends CObject {

    @property({type: Node})
    bulletPrefab: Node = null;

    // 发射条数
    @property
    count: number = 1;

    // 子弹速度乘数
    @property
    speedMultiplier: number = 1;

    // 频率(间隔多少秒发射一次)
    @property
    frequency: number = 1;

    canTrigger(): boolean {
        // 检查是否可以触发发射
        return false;
    }

    /**
     * Abstract method for emitting bullets
     * Must be implemented by concrete emitter classes
     */
    abstract emitBullet(): void;

    // Implementation of CObject abstract methods
    protected onObjectInit(): void {
        // Override in subclasses if needed
    }

    protected onObjectDestroy(): void {
        // Clean up any scheduled callbacks
        this.unschedule(this.emitBullet);
    }

    protected onEnable(): void {
        this.schedule(this.emitBullet, this.frequency);
    }

    protected onDisable(): void {
        this.unschedule(this.emitBullet);
    }
}
