/**
 * CrazyStorm 2.0 Integration for Cocos Creator
 * Base Emitter Component
 * 
 * This is the base class for all emitter components (CurveEmitter, MultiEmitter, etc.)
 * It contains all common emitter functionality, properties, and methods.
 * Based on the C# Emitter class architecture.
 */

import { _decorator, Vec2, Vec3, Node, Prefab, instantiate, log, warn } from 'cc';
import { CrazyStormComponent } from './CrazyStormComponent';
import { Particle } from '../physics/ParticlePhysics';
import { ExpressionVM, VMContext } from '../expression';
import { EventManager, PropertyContainer } from '../events';
import { ParticleManager } from '../particle';
import {
    EmitterData,
    ParticleBaseData,
    ParticleType,
    EventGroup,
    ComponentType
} from '../core/CrazyStormTypes';

const { ccclass, property } = _decorator;

/**
 * Base class for all emitter components in CrazyStorm
 * Contains common emitter functionality shared between CurveEmitter, MultiEmitter, etc.
 * Implements PropertyContainer for event system integration.
 */
@ccclass('EmitterComponent')
export abstract class EmitterComponent extends CrazyStormComponent implements PropertyContainer {
    // ============================================================================
    // EMITTER PROPERTIES (exposed in editor)
    // ============================================================================
    
    @property({ type: Prefab, displayName: "Particle Prefab" })
    particlePrefab: Prefab | null = null;

    @property({ displayName: "Max Particles" })
    maxParticles: number = 1000;

    @property({ displayName: "Emit Position" })
    emitPosition: Vec2 = new Vec2(0, 0);

    @property({ displayName: "Emit Count" })
    emitCount: number = 1;

    @property({ displayName: "Emit Cycle" })
    emitCycle: number = 10;

    @property({ displayName: "Emit Angle" })
    emitAngle: number = 0;

    @property({ displayName: "Emit Range" })
    emitRange: number = 360;

    @property({ displayName: "Emit Radius" })
    emitRadius: number = 0;

    // ============================================================================
    // PROTECTED MEMBERS (accessible to subclasses)
    // ============================================================================

    protected emitterData: EmitterData | null = null;
    protected particleBaseData: ParticleBaseData | null = null;
    protected particles: Particle[] = [];
    protected particlePool: Particle[] = [];
    protected particleNodes: Node[] = [];
    protected emitTimer: number = 0;
    protected particleType: ParticleType | null = null;
    protected particleEventGroups: EventGroup[] = [];
    protected layerId: number = 0; // Layer ID for particle management

    // ============================================================================
    // INITIALIZATION
    // ============================================================================

    protected initializeComponent(): void {
        this.particles = [];
        this.particlePool = [];
        this.particleNodes = [];
        this.emitTimer = 0;
        this.particleEventGroups = [];
        
        // Initialize particle pool
        this.initializeParticlePool();
    }

    protected onReset(): void {
        this.emitTimer = 0;
        this.clearAllParticles();
        this.initializeParticlePool();
    }

    /**
     * Initialize the particle pool for efficient particle management
     */
    protected initializeParticlePool(): void {
        this.particlePool = [];
        
        // Pre-allocate particles for the pool
        for (let i = 0; i < this.maxParticles; i++) {
            const particle = this.createParticle();
            particle.alive = false;
            this.particlePool.push(particle);
        }
        
        log(`EmitterComponent: Initialized particle pool with ${this.maxParticles} particles`);
    }

    // ============================================================================
    // CORE EMITTER UPDATE LOGIC
    // ============================================================================

    protected onUpdate(deltaTime: number): void {
        // Update emit timer
        this.emitTimer += deltaTime;

        // Check if it's time to emit
        const emitInterval = this.emitCycle / 60; // Convert frames to seconds (assuming 60 FPS)
        if (this.emitTimer >= emitInterval) {
            this.emit();
            this.emitTimer = 0;
        }

        // Update all active particles
        this.updateParticles(deltaTime);

        // Update emitter properties from expressions
        this.updateEmitterProperties();
    }

    // ============================================================================
    // EMISSION LOGIC (based on C# Emitter.Emit())
    // ============================================================================

    /**
     * Core emission logic - emits particles in a pattern
     * This follows the same algorithm as the C# Emitter.Emit() method
     */
    protected emit(): void {
        if (!this.particleType || !this.particleBaseData) {
            return;
        }

        // Get current property values (may be from expressions)
        const currentEmitPosition = this.getCurrentEmitPosition();
        const currentEmitCount = this.getCurrentEmitCount();
        const currentEmitAngle = this.getCurrentEmitAngle();
        const currentEmitRange = this.getCurrentEmitRange();
        const currentEmitRadius = this.getCurrentEmitRadius();

        // Calculate angle increment (same as C# version)
        const angleIncrement = currentEmitRange / currentEmitCount;
        let angle = currentEmitAngle - (currentEmitRange + angleIncrement) / 2;

        // Emit particles in the calculated pattern
        for (let i = 0; i < currentEmitCount; i++) {
            angle += angleIncrement;
            
            // Get particle from pool
            const particle = this.getParticleFromPool();
            if (!particle) {
                warn('EmitterComponent: No available particles in pool');
                break;
            }

            // Calculate emit position with radius (same as C# version)
            const radians = angle * Math.PI / 180;
            const emitPos = new Vec2(
                currentEmitPosition.x + currentEmitRadius * Math.cos(radians),
                currentEmitPosition.y + currentEmitRadius * Math.sin(radians)
            );

            // Initialize particle
            this.initializeParticle(particle, emitPos, angle);
            
            // Add to active particles
            this.particles.push(particle);
        }
    }

    // ============================================================================
    // PARTICLE MANAGEMENT
    // ============================================================================

    /**
     * Get a particle from the global pool
     */
    protected getParticleFromPool(): Particle | null {
        if (!this.particleType) {
            warn('EmitterComponent: No particle type set');
            return null;
        }

        // Use global particle manager
        const particleManager = ParticleManager.getInstance();
        return particleManager.getParticle(this.layerId, this.particleType, this.componentId);
    }

    /**
     * Return a particle to the global pool
     */
    protected returnParticleToPool(particle: Particle): void {
        // Use global particle manager
        const particleManager = ParticleManager.getInstance();
        particleManager.returnParticle(particle);
    }

    /**
     * Update all active particles
     */
    protected updateParticles(deltaTime: number): void {
        for (let i = this.particles.length - 1; i >= 0; i--) {
            const particle = this.particles[i];
            
            if (!particle.alive) {
                this.returnParticleToPool(particle);
                this.particles.splice(i, 1);
                continue;
            }
            
            // Update particle physics and lifetime
            this.updateParticle(particle, deltaTime);
            
            // Check if particle should die
            if (particle.currentFrame >= particle.maxLife || this.shouldKillParticle(particle)) {
                particle.alive = false;
                this.returnParticleToPool(particle);
                this.particles.splice(i, 1);
            }
        }
    }

    /**
     * Update a single particle (physics, position, etc.)
     */
    protected updateParticle(particle: Particle, deltaTime: number): void {
        // Update particle frame
        particle.currentFrame++;
        
        // Update particle physics
        particle.velocity.x += particle.acceleration.x * deltaTime;
        particle.velocity.y += particle.acceleration.y * deltaTime;
        particle.position.x += particle.velocity.x * deltaTime;
        particle.position.y += particle.velocity.y * deltaTime;
        
        // Update particle rotation
        particle.rotation += particle.rotationSpeed * deltaTime;
        
        // Update particle node position if it exists
        if (particle.node && particle.node.isValid) {
            particle.node.setPosition(particle.position.x, particle.position.y, 0);
            particle.node.setRotationFromEuler(0, 0, particle.rotation);
            
            // Update opacity based on lifetime (fade effect)
            if (this.particleBaseData?.fadeEffect) {
                const lifetimeRatio = particle.currentFrame / particle.maxLife;
                const opacity = Math.max(0, 1 - lifetimeRatio);
                // Apply opacity to node (implementation depends on node type)
            }
        }
        
        // Execute particle events
        this.executeParticleEvents(particle);
    }

    // ============================================================================
    // ABSTRACT METHODS (must be implemented by subclasses)
    // ============================================================================

    /**
     * Create a new particle instance (specific to emitter type)
     */
    protected abstract createParticle(): Particle;

    /**
     * Initialize a particle with position and angle
     */
    protected abstract initializeParticle(particle: Particle, position: Vec2, angle: number): void;

    /**
     * Called when CrazyStorm data is initialized (specific to emitter type)
     */
    protected abstract onDataInitialized(): void;

    // ============================================================================
    // PROPERTY GETTERS (support for expressions using VM)
    // ============================================================================

    /**
     * Get current emit position (may be from expression)
     */
    protected getCurrentEmitPosition(): Vec2 {
        const x = this.evaluatePropertyExpression('EmitPosition.x', this.emitPosition.x);
        const y = this.evaluatePropertyExpression('EmitPosition.y', this.emitPosition.y);
        return new Vec2(x, y);
    }

    /**
     * Get current emit count (may be from expression)
     */
    protected getCurrentEmitCount(): number {
        return this.evaluatePropertyExpression('EmitCount', this.emitCount);
    }

    /**
     * Get current emit angle (may be from expression)
     */
    protected getCurrentEmitAngle(): number {
        return this.evaluatePropertyExpression('EmitAngle', this.emitAngle);
    }

    /**
     * Get current emit range (may be from expression)
     */
    protected getCurrentEmitRange(): number {
        return this.evaluatePropertyExpression('EmitRange', this.emitRange);
    }

    /**
     * Get current emit radius (may be from expression)
     */
    protected getCurrentEmitRadius(): number {
        return this.evaluatePropertyExpression('EmitRadius', this.emitRadius);
    }

    /**
     * Evaluate property expression using the VM system
     */
    protected evaluatePropertyExpression(propertyName: string, defaultValue: number): number {
        const property = this.properties.get(propertyName);
        if (!property || !property.expression || typeof property.value !== 'string') {
            return defaultValue;
        }

        // Convert variables array to Map for VM context
        const localVariables = new Map<string, number>();
        this.variables.forEach(variable => {
            localVariables.set(variable.label, variable.value);
        });

        // Create VM context with current state
        const context: VMContext = {
            globalVariables: this.globals,
            localVariables: localVariables
        };

        // Use the Expression VM to evaluate
        try {
            const result = ExpressionVM.evaluateSimple(property.value, context);
            return typeof result === 'number' ? result : defaultValue;
        } catch (error) {
            warn(`EmitterComponent: Error evaluating expression for ${propertyName}: ${property.value}`, error);
            return defaultValue;
        }
    }

    // ============================================================================
    // EMITTER PROPERTY UPDATES
    // ============================================================================

    /**
     * Update emitter properties from expressions
     */
    protected updateEmitterProperties(): void {
        // Update emit properties if they have expressions
        if (this.emitterData) {
            // These will be updated automatically through property getters
            // when emit() is called, but we can also update the display values here
            const currentPos = this.getCurrentEmitPosition();
            this.emitPosition.set(currentPos.x, currentPos.y);
            this.emitCount = this.getCurrentEmitCount();
            this.emitAngle = this.getCurrentEmitAngle();
            this.emitRange = this.getCurrentEmitRange();
            this.emitRadius = this.getCurrentEmitRadius();
        }
    }

    // ============================================================================
    // PARTICLE LIFECYCLE HELPERS
    // ============================================================================

    /**
     * Check if a particle should be killed (outside bounds, etc.)
     */
    protected shouldKillParticle(particle: Particle): boolean {
        if (!this.particleBaseData?.killOutside) {
            return false;
        }

        // Check if particle is outside screen bounds
        // This would need to be implemented based on your game's screen bounds
        const screenBounds = { left: -1000, right: 1000, top: 1000, bottom: -1000 };

        return particle.position.x < screenBounds.left ||
               particle.position.x > screenBounds.right ||
               particle.position.y < screenBounds.bottom ||
               particle.position.y > screenBounds.top;
    }

    /**
     * Execute particle events
     */
    protected executeParticleEvents(particle: Particle): void {
        // Execute particle event groups
        this.particleEventGroups.forEach(group => {
            group.events.forEach(event => {
                // Event execution would be implemented here
                // This is a placeholder for the event system
            });
        });
    }

    // ============================================================================
    // PUBLIC API METHODS
    // ============================================================================

    /**
     * Get all active particles
     */
    public getActiveParticles(): Particle[] {
        return [...this.particles];
    }

    /**
     * Force emit particles (for manual triggering)
     */
    public forceEmit(): void {
        this.emit();
    }

    /**
     * Clear all particles
     */
    public clearAllParticles(): void {
        // Use global particle manager to clear particles by component
        const particleManager = ParticleManager.getInstance();
        particleManager.clearParticlesByComponent(this.componentId);
        this.particles = [];
    }

    /**
     * Set emit parameters at runtime
     */
    public setEmitParameters(count: number, cycle: number, angle: number, range: number): void {
        this.emitCount = count;
        this.emitCycle = cycle;
        this.emitAngle = angle;
        this.emitRange = range;
    }

    /**
     * Check collision with point (for player collision detection)
     */
    public checkCollisionWithPoint(point: Vec2, radius: number): Particle[] {
        const collisions: Particle[] = [];

        this.particles.forEach(particle => {
            if (particle.collision && particle.alive) {
                const distance = Math.sqrt(
                    Math.pow(particle.position.x - point.x, 2) +
                    Math.pow(particle.position.y - point.y, 2)
                );

                if (distance <= particle.particleType.radius + radius) {
                    collisions.push(particle);
                }
            }
        });

        return collisions;
    }

    /**
     * Initialize emitter with CrazyStorm data
     */
    protected initializeEmitterData(emitterData: EmitterData, particleBaseData: ParticleBaseData, particleEventGroups: EventGroup[]): void {
        // Store emitter data
        this.emitterData = emitterData;
        this.particleBaseData = particleBaseData;
        this.particleEventGroups = [...particleEventGroups];

        // Update component properties
        this.emitPosition.set(emitterData.emitPosition.x, emitterData.emitPosition.y);
        this.emitCount = emitterData.emitCount;
        this.emitCycle = emitterData.emitCycle;
        this.emitAngle = emitterData.emitAngle;
        this.emitRange = emitterData.emitRange;
        this.emitRadius = emitterData.emitRadius;

        log(`EmitterComponent: Initialized with emit count ${this.emitCount}, cycle ${this.emitCycle}`);
    }

    // ============================================================================
    // PROPERTY CONTAINER INTERFACE IMPLEMENTATION
    // ============================================================================

    /**
     * Push property value onto VM stack (PropertyContainer interface)
     */
    public pushProperty(propertyName: string): boolean {
        const value = this.getProperty(propertyName);
        if (value !== undefined) {
            // This would push to VM stack in a full implementation
            return true;
        }
        return false;
    }

    /**
     * Set property value from VM stack (PropertyContainer interface)
     */
    public setProperty(propertyName: string): boolean {
        // This would pop from VM stack and set property in a full implementation
        warn(`EmitterComponent: setProperty not fully implemented for ${propertyName}`);
        return false;
    }

    /**
     * Get property value (PropertyContainer interface)
     */
    public getProperty(propertyName: string): any {
        switch (propertyName.toLowerCase()) {
            case 'emitposition':
            case 'emitposition.x':
                return propertyName.endsWith('.x') ? this.emitPosition.x : this.emitPosition;
            case 'emitposition.y':
                return this.emitPosition.y;
            case 'emitcount':
                return this.emitCount;
            case 'emitcycle':
                return this.emitCycle;
            case 'emitangle':
                return this.emitAngle;
            case 'emitrange':
                return this.emitRange;
            case 'emitradius':
                return this.emitRadius;
            case 'maxparticles':
                return this.maxParticles;
            case 'position':
                return this.node.position;
            case 'position.x':
                return this.node.position.x;
            case 'position.y':
                return this.node.position.y;
            case 'visible':
                return this.visible;
            default:
                // Try to get from properties map
                const property = this.properties.get(propertyName);
                return property ? property.value : undefined;
        }
    }

    /**
     * Set property value directly (PropertyContainer interface)
     */
    public setPropertyValue(propertyName: string, value: any): void {
        switch (propertyName.toLowerCase()) {
            case 'emitposition.x':
                this.emitPosition.x = Number(value) || 0;
                break;
            case 'emitposition.y':
                this.emitPosition.y = Number(value) || 0;
                break;
            case 'emitcount':
                this.emitCount = Number(value) || 0;
                break;
            case 'emitcycle':
                this.emitCycle = Number(value) || 0;
                break;
            case 'emitangle':
                this.emitAngle = Number(value) || 0;
                break;
            case 'emitrange':
                this.emitRange = Number(value) || 0;
                break;
            case 'emitradius':
                this.emitRadius = Number(value) || 0;
                break;
            case 'maxparticles':
                this.maxParticles = Number(value) || 0;
                break;
            case 'position.x':
                this.node.setPosition(Number(value) || 0, this.node.position.y, this.node.position.z);
                break;
            case 'position.y':
                this.node.setPosition(this.node.position.x, Number(value) || 0, this.node.position.z);
                break;
            case 'visible':
                this.visible = Boolean(value);
                this.node.active = this.visible;
                break;
            default:
                // Set in properties map
                const property = this.properties.get(propertyName);
                if (property) {
                    property.value = value;
                } else {
                    this.properties.set(propertyName, { expression: false, value: value });
                }
                break;
        }
    }

    /**
     * Force emit particles (for EmitParticle special event)
     */
    public emitParticle(): void {
        this.forceEmit();
    }

    // ============================================================================
    // CLEANUP
    // ============================================================================

    onDestroy() {
        // Clean up all particle nodes
        this.particleNodes.forEach(node => {
            if (node && node.isValid) {
                node.destroy();
            }
        });
        this.particleNodes = [];
        this.particles = [];
        this.particlePool = [];
    }
}
