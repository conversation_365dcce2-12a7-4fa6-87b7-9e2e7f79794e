{"modules": {"cce:/internal/x/cc": {"mTimestamp": 15566.0199, "chunkId": "93ba276ea7b26ffcdc433fab14afc1ed6f05647b", "imports": [{"value": "cce:/internal/x/cc-fu/2d", "resolved": "__unresolved_0", "loc": {"start": {"line": 1, "column": 14}, "end": {"line": 1, "column": 40}}}, {"value": "cce:/internal/x/cc-fu/sorting", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 14}, "end": {"line": 2, "column": 45}}}, {"value": "cce:/internal/x/cc-fu/affine-transform", "resolved": "__unresolved_2", "loc": {"start": {"line": 3, "column": 14}, "end": {"line": 3, "column": 54}}}, {"value": "cce:/internal/x/cc-fu/animation", "resolved": "__unresolved_3", "loc": {"start": {"line": 4, "column": 14}, "end": {"line": 4, "column": 47}}}, {"value": "cce:/internal/x/cc-fu/audio", "resolved": "__unresolved_4", "loc": {"start": {"line": 5, "column": 14}, "end": {"line": 5, "column": 43}}}, {"value": "cce:/internal/x/cc-fu/base", "resolved": "__unresolved_5", "loc": {"start": {"line": 6, "column": 14}, "end": {"line": 6, "column": 42}}}, {"value": "cce:/internal/x/cc-fu/custom-pipeline", "resolved": "__unresolved_6", "loc": {"start": {"line": 7, "column": 14}, "end": {"line": 7, "column": 53}}}, {"value": "cce:/internal/x/cc-fu/dragon-bones", "resolved": "__unresolved_7", "loc": {"start": {"line": 8, "column": 14}, "end": {"line": 8, "column": 50}}}, {"value": "cce:/internal/x/cc-fu/gfx-webgl", "resolved": "__unresolved_8", "loc": {"start": {"line": 9, "column": 14}, "end": {"line": 9, "column": 47}}}, {"value": "cce:/internal/x/cc-fu/gfx-webgl2", "resolved": "__unresolved_9", "loc": {"start": {"line": 10, "column": 14}, "end": {"line": 10, "column": 48}}}, {"value": "cce:/internal/x/cc-fu/graphics", "resolved": "__unresolved_10", "loc": {"start": {"line": 11, "column": 14}, "end": {"line": 11, "column": 46}}}, {"value": "cce:/internal/x/cc-fu/intersection-2d", "resolved": "__unresolved_11", "loc": {"start": {"line": 12, "column": 14}, "end": {"line": 12, "column": 53}}}, {"value": "cce:/internal/x/cc-fu/mask", "resolved": "__unresolved_12", "loc": {"start": {"line": 13, "column": 14}, "end": {"line": 13, "column": 42}}}, {"value": "cce:/internal/x/cc-fu/particle-2d", "resolved": "__unresolved_13", "loc": {"start": {"line": 14, "column": 14}, "end": {"line": 14, "column": 49}}}, {"value": "cce:/internal/x/cc-fu/physics-2d-builtin", "resolved": "__unresolved_14", "loc": {"start": {"line": 15, "column": 14}, "end": {"line": 15, "column": 56}}}, {"value": "cce:/internal/x/cc-fu/physics-2d-framework", "resolved": "__unresolved_15", "loc": {"start": {"line": 16, "column": 14}, "end": {"line": 16, "column": 58}}}, {"value": "cce:/internal/x/cc-fu/profiler", "resolved": "__unresolved_16", "loc": {"start": {"line": 17, "column": 14}, "end": {"line": 17, "column": 46}}}, {"value": "cce:/internal/x/cc-fu/rich-text", "resolved": "__unresolved_17", "loc": {"start": {"line": 18, "column": 14}, "end": {"line": 18, "column": 47}}}, {"value": "cce:/internal/x/cc-fu/skeletal-animation", "resolved": "__unresolved_18", "loc": {"start": {"line": 19, "column": 14}, "end": {"line": 19, "column": 56}}}, {"value": "cce:/internal/x/cc-fu/spine", "resolved": "__unresolved_19", "loc": {"start": {"line": 20, "column": 14}, "end": {"line": 20, "column": 43}}}, {"value": "cce:/internal/x/cc-fu/tiled-map", "resolved": "__unresolved_20", "loc": {"start": {"line": 21, "column": 14}, "end": {"line": 21, "column": 47}}}, {"value": "cce:/internal/x/cc-fu/tween", "resolved": "__unresolved_21", "loc": {"start": {"line": 22, "column": 14}, "end": {"line": 22, "column": 43}}}, {"value": "cce:/internal/x/cc-fu/ui", "resolved": "__unresolved_22", "loc": {"start": {"line": 23, "column": 14}, "end": {"line": 23, "column": 40}}}, {"value": "cce:/internal/x/cc-fu/video", "resolved": "__unresolved_23", "loc": {"start": {"line": 24, "column": 14}, "end": {"line": 24, "column": 43}}}, {"value": "cce:/internal/x/cc-fu/webview", "resolved": "__unresolved_24", "loc": {"start": {"line": 25, "column": 14}, "end": {"line": 25, "column": 45}}}], "type": "esm", "resolutions": [{"resolved": {"type": "external", "specifierOrURL": "cce:/internal/x/cc-fu/2d"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cce:/internal/x/cc-fu/sorting"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cce:/internal/x/cc-fu/affine-transform"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cce:/internal/x/cc-fu/animation"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cce:/internal/x/cc-fu/audio"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cce:/internal/x/cc-fu/base"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cce:/internal/x/cc-fu/custom-pipeline"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cce:/internal/x/cc-fu/dragon-bones"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cce:/internal/x/cc-fu/gfx-webgl"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cce:/internal/x/cc-fu/gfx-webgl2"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cce:/internal/x/cc-fu/graphics"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cce:/internal/x/cc-fu/intersection-2d"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cce:/internal/x/cc-fu/mask"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cce:/internal/x/cc-fu/particle-2d"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cce:/internal/x/cc-fu/physics-2d-builtin"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cce:/internal/x/cc-fu/physics-2d-framework"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cce:/internal/x/cc-fu/profiler"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cce:/internal/x/cc-fu/rich-text"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cce:/internal/x/cc-fu/skeletal-animation"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cce:/internal/x/cc-fu/spine"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cce:/internal/x/cc-fu/tiled-map"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cce:/internal/x/cc-fu/tween"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cce:/internal/x/cc-fu/ui"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cce:/internal/x/cc-fu/video"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cce:/internal/x/cc-fu/webview"}, "messages": []}]}, "cce:/internal/x/prerequisite-imports": {"mTimestamp": 3279480.8915, "chunkId": "6d8fd2b0177941b032ddc0733af48a561fb60657", "imports": [{"value": "file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts", "resolved": "__unresolved_0", "loc": {"start": {"line": 4, "column": 7}, "end": {"line": 4, "column": 146}}}, {"value": "file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts", "resolved": "__unresolved_1", "loc": {"start": {"line": 5, "column": 7}, "end": {"line": 5, "column": 151}}}, {"value": "file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts", "resolved": "__unresolved_2", "loc": {"start": {"line": 6, "column": 7}, "end": {"line": 6, "column": 155}}}, {"value": "file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts", "resolved": "__unresolved_3", "loc": {"start": {"line": 7, "column": 7}, "end": {"line": 7, "column": 152}}}, {"value": "file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts", "resolved": "__unresolved_4", "loc": {"start": {"line": 8, "column": 7}, "end": {"line": 8, "column": 146}}}, {"value": "file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts", "resolved": "__unresolved_5", "loc": {"start": {"line": 9, "column": 7}, "end": {"line": 9, "column": 139}}}, {"value": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/AutoGen/Luban/schema.ts", "resolved": "__unresolved_6", "loc": {"start": {"line": 10, "column": 7}, "end": {"line": 10, "column": 99}}}, {"value": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/AutoGen/PB/cs_proto.ts", "resolved": "__unresolved_7", "loc": {"start": {"line": 11, "column": 7}, "end": {"line": 11, "column": 98}}}, {"value": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Anim.ts", "resolved": "__unresolved_8", "loc": {"start": {"line": 12, "column": 7}, "end": {"line": 12, "column": 88}}}, {"value": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Background.ts", "resolved": "__unresolved_9", "loc": {"start": {"line": 13, "column": 7}, "end": {"line": 13, "column": 94}}}, {"value": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Enemy.ts", "resolved": "__unresolved_10", "loc": {"start": {"line": 14, "column": 7}, "end": {"line": 14, "column": 89}}}, {"value": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/EnemyBullet.ts", "resolved": "__unresolved_11", "loc": {"start": {"line": 15, "column": 7}, "end": {"line": 15, "column": 95}}}, {"value": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/GameOver.ts", "resolved": "__unresolved_12", "loc": {"start": {"line": 16, "column": 7}, "end": {"line": 16, "column": 92}}}, {"value": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Global.ts", "resolved": "__unresolved_13", "loc": {"start": {"line": 17, "column": 7}, "end": {"line": 17, "column": 90}}}, {"value": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Goods.ts", "resolved": "__unresolved_14", "loc": {"start": {"line": 18, "column": 7}, "end": {"line": 18, "column": 89}}}, {"value": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/MainGame.ts", "resolved": "__unresolved_15", "loc": {"start": {"line": 19, "column": 7}, "end": {"line": 19, "column": 92}}}, {"value": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Menu.ts", "resolved": "__unresolved_16", "loc": {"start": {"line": 20, "column": 7}, "end": {"line": 20, "column": 88}}}, {"value": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/PersistNode.ts", "resolved": "__unresolved_17", "loc": {"start": {"line": 21, "column": 7}, "end": {"line": 21, "column": 95}}}, {"value": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Player.ts", "resolved": "__unresolved_18", "loc": {"start": {"line": 22, "column": 7}, "end": {"line": 22, "column": 90}}}, {"value": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/PlayerBullet.ts", "resolved": "__unresolved_19", "loc": {"start": {"line": 23, "column": 7}, "end": {"line": 23, "column": 96}}}, {"value": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/AnimFactory.ts", "resolved": "__unresolved_20", "loc": {"start": {"line": 24, "column": 7}, "end": {"line": 24, "column": 103}}}, {"value": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/EnemyBulletFactory.ts", "resolved": "__unresolved_21", "loc": {"start": {"line": 25, "column": 7}, "end": {"line": 25, "column": 110}}}, {"value": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/EnemyFactory.ts", "resolved": "__unresolved_22", "loc": {"start": {"line": 26, "column": 7}, "end": {"line": 26, "column": 104}}}, {"value": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/GameFactory.ts", "resolved": "__unresolved_23", "loc": {"start": {"line": 27, "column": 7}, "end": {"line": 27, "column": 103}}}, {"value": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/GoodsFactory.ts", "resolved": "__unresolved_24", "loc": {"start": {"line": 28, "column": 7}, "end": {"line": 28, "column": 104}}}, {"value": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/PlayerBulletFactory.ts", "resolved": "__unresolved_25", "loc": {"start": {"line": 29, "column": 7}, "end": {"line": 29, "column": 111}}}, {"value": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/gizmos/EmitterArcGizmo.ts", "resolved": "__unresolved_26", "loc": {"start": {"line": 30, "column": 7}, "end": {"line": 30, "column": 106}}}, {"value": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/gizmos/EmitterGizmo.ts", "resolved": "__unresolved_27", "loc": {"start": {"line": 31, "column": 7}, "end": {"line": 31, "column": 103}}}, {"value": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/gizmos/GizmoDrawer.ts", "resolved": "__unresolved_28", "loc": {"start": {"line": 32, "column": 7}, "end": {"line": 32, "column": 102}}}, {"value": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/gizmos/GizmoManager.ts", "resolved": "__unresolved_29", "loc": {"start": {"line": 33, "column": 7}, "end": {"line": 33, "column": 103}}}, {"value": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/gizmos/GizmoSetup.ts", "resolved": "__unresolved_30", "loc": {"start": {"line": 34, "column": 7}, "end": {"line": 34, "column": 101}}}, {"value": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/gizmos/index.ts", "resolved": "__unresolved_31", "loc": {"start": {"line": 35, "column": 7}, "end": {"line": 35, "column": 96}}}, {"value": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/Bootstrap.ts", "resolved": "__unresolved_32", "loc": {"start": {"line": 36, "column": 7}, "end": {"line": 36, "column": 99}}}, {"value": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/WorldInitializeData.ts", "resolved": "__unresolved_33", "loc": {"start": {"line": 37, "column": 7}, "end": {"line": 37, "column": 109}}}, {"value": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/base/Object.ts", "resolved": "__unresolved_34", "loc": {"start": {"line": 38, "column": 7}, "end": {"line": 38, "column": 101}}}, {"value": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/base/System.ts", "resolved": "__unresolved_35", "loc": {"start": {"line": 39, "column": 7}, "end": {"line": 39, "column": 101}}}, {"value": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/base/SystemContainer.ts", "resolved": "__unresolved_36", "loc": {"start": {"line": 40, "column": 7}, "end": {"line": 40, "column": 110}}}, {"value": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/base/TypeID.ts", "resolved": "__unresolved_37", "loc": {"start": {"line": 41, "column": 7}, "end": {"line": 41, "column": 101}}}, {"value": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/base/World.ts", "resolved": "__unresolved_38", "loc": {"start": {"line": 42, "column": 7}, "end": {"line": 42, "column": 100}}}, {"value": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/bullet/Bullet.ts", "resolved": "__unresolved_39", "loc": {"start": {"line": 43, "column": 7}, "end": {"line": 43, "column": 103}}}, {"value": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/bullet/BulletSystem.ts", "resolved": "__unresolved_40", "loc": {"start": {"line": 44, "column": 7}, "end": {"line": 44, "column": 109}}}, {"value": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/bullet/Emitter.ts", "resolved": "__unresolved_41", "loc": {"start": {"line": 45, "column": 7}, "end": {"line": 45, "column": 104}}}, {"value": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/bullet/EmitterArc.ts", "resolved": "__unresolved_42", "loc": {"start": {"line": 46, "column": 7}, "end": {"line": 46, "column": 107}}}, {"value": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/index.ts", "resolved": "__unresolved_43", "loc": {"start": {"line": 47, "column": 7}, "end": {"line": 47, "column": 95}}}, {"value": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/level/LevelSystem.ts", "resolved": "__unresolved_44", "loc": {"start": {"line": 48, "column": 7}, "end": {"line": 48, "column": 107}}}, {"value": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/player/PlayerSystem.ts", "resolved": "__unresolved_45", "loc": {"start": {"line": 49, "column": 7}, "end": {"line": 49, "column": 109}}}, {"value": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameInstance.ts", "resolved": "__unresolved_46", "loc": {"start": {"line": 50, "column": 7}, "end": {"line": 50, "column": 91}}}, {"value": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/IMgr.ts", "resolved": "__unresolved_47", "loc": {"start": {"line": 51, "column": 7}, "end": {"line": 51, "column": 83}}}, {"value": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Luban/LubanMgr.ts", "resolved": "__unresolved_48", "loc": {"start": {"line": 52, "column": 7}, "end": {"line": 52, "column": 93}}}, {"value": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/MainUI.ts", "resolved": "__unresolved_49", "loc": {"start": {"line": 53, "column": 7}, "end": {"line": 53, "column": 85}}}, {"value": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Network/NetMgr.ts", "resolved": "__unresolved_50", "loc": {"start": {"line": 54, "column": 7}, "end": {"line": 54, "column": 93}}}, {"value": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ResUpdate/ResUpdate.ts", "resolved": "__unresolved_51", "loc": {"start": {"line": 55, "column": 7}, "end": {"line": 55, "column": 98}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/AutoGen/Luban/schema.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/AutoGen/PB/cs_proto.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Anim.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Background.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Enemy.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/EnemyBullet.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/GameOver.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Global.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Goods.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/MainGame.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Menu.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/PersistNode.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Player.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/PlayerBullet.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/AnimFactory.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/EnemyBulletFactory.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/EnemyFactory.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/GameFactory.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/GoodsFactory.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/PlayerBulletFactory.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/gizmos/EmitterArcGizmo.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/gizmos/EmitterGizmo.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/gizmos/GizmoDrawer.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/gizmos/GizmoManager.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/gizmos/GizmoSetup.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/gizmos/index.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/Bootstrap.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/WorldInitializeData.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/base/Object.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/base/System.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/base/SystemContainer.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/base/TypeID.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/base/World.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/bullet/Bullet.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/bullet/BulletSystem.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/bullet/Emitter.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/bullet/EmitterArc.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/index.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/level/LevelSystem.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/player/PlayerSystem.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameInstance.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/IMgr.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Luban/LubanMgr.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/MainUI.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Network/NetMgr.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ResUpdate/ResUpdate.ts"}, "messages": []}]}, "file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts": {"mTimestamp": {"mtime": 1752422385067.8257, "uuid": "11f3130e-c08c-47bb-a209-71d114594e6d"}, "chunkId": "d208b01558c13077e4a9c9f1302dfbf2b2122e40", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 28, "column": 7}, "end": {"line": 28, "column": 11}}}, {"value": "cc/env", "loc": {"start": {"line": 30, "column": 23}, "end": {"line": 30, "column": 31}}}, {"value": "./builtin-pipeline-settings", "resolved": "__unresolved_1", "loc": {"start": {"line": 34, "column": 7}, "end": {"line": 34, "column": 36}}}, {"value": "./builtin-pipeline-pass", "resolved": "__unresolved_2", "loc": {"start": {"line": 38, "column": 7}, "end": {"line": 38, "column": 32}}}, {"value": "./builtin-pipeline", "resolved": "__unresolved_3", "loc": {"start": {"line": 45, "column": 7}, "end": {"line": 45, "column": 27}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cc/env"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts"}, "messages": []}]}, "file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts": {"mTimestamp": {"mtime": 1752422385067.8257, "uuid": "6f94083c-fc92-438b-a15b-a20ec61666c7"}, "chunkId": "b37de22ab9dff6e9f979ab9e517f60b5b4ae4ba5", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 30, "column": 7}, "end": {"line": 30, "column": 11}}}, {"value": "./builtin-pipeline-settings", "resolved": "__unresolved_1", "loc": {"start": {"line": 32, "column": 40}, "end": {"line": 32, "column": 69}}}, {"value": "cc/env", "loc": {"start": {"line": 34, "column": 23}, "end": {"line": 34, "column": 31}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cc/env"}, "messages": []}]}, "file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts": {"mTimestamp": {"mtime": 1752422385067.8257, "uuid": "de1c2107-70c8-4021-8459-6399f24d01c6"}, "chunkId": "36278e5c964e5e2f737bca1654894a5a7b2a7063", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 28, "column": 7}, "end": {"line": 28, "column": 11}}}, {"value": "cc/env", "loc": {"start": {"line": 30, "column": 23}, "end": {"line": 30, "column": 31}}}, {"value": "./builtin-pipeline-types", "resolved": "__unresolved_1", "loc": {"start": {"line": 34, "column": 7}, "end": {"line": 34, "column": 33}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cc/env"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts"}, "messages": []}]}, "file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts": {"mTimestamp": {"mtime": 1752422385067.8257, "uuid": "cbf30902-517f-40dc-af90-a550bac27cf1"}, "chunkId": "383c24386be9d9de15fc0c17a8951753b54d596a", "imports": [{"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 31, "column": 41}, "end": {"line": 31, "column": 45}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}]}, "file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts": {"mTimestamp": {"mtime": 1752422385067.8257, "uuid": "ff9b0199-ce04-4cfe-86cc-6c719f08d6e4"}, "chunkId": "9846cefb9cb6e16313f2e5e80bbb689314385757", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 29, "column": 7}, "end": {"line": 29, "column": 11}}}, {"value": "cc/env", "loc": {"start": {"line": 31, "column": 30}, "end": {"line": 31, "column": 38}}}, {"value": "./builtin-pipeline-types", "resolved": "__unresolved_1", "loc": {"start": {"line": 36, "column": 7}, "end": {"line": 36, "column": 33}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cc/env"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts"}, "messages": []}]}, "file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts": {"mTimestamp": {"mtime": 1752422385290.3274, "uuid": "b2bd1fa7-8d7c-49c5-a158-df29a6d3a594"}, "chunkId": "49c387c7d23ec5c771c1bd713cdd20f77551061d", "imports": [{"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 220}, "end": {"line": 1, "column": 224}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}]}, "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/gizmos/EmitterGizmo.ts": {"mTimestamp": {"mtime": 1753546895070.1191, "uuid": "92b822be-e838-4774-8286-851dc74ce5af"}, "chunkId": "ef083c913ee08613bada8a2375e41d84691975a2", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 7, "column": 55}, "end": {"line": 7, "column": 59}}}, {"value": "cc/env", "loc": {"start": {"line": 8, "column": 23}, "end": {"line": 8, "column": 31}}}, {"value": "../assets/scripts/Game/world/bullet/Emitter", "resolved": "__unresolved_1", "loc": {"start": {"line": 9, "column": 24}, "end": {"line": 9, "column": 69}}}, {"value": "../assets/scripts/Game/world/bullet/EmitterArc", "resolved": "__unresolved_2", "loc": {"start": {"line": 10, "column": 27}, "end": {"line": 10, "column": 75}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cc/env"}, "messages": []}, {"resolved": {"type": "error", "text": "Error: Modu<PERSON> \"../assets/scripts/Game/world/bullet/Emitter\" not found for file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/gizmos/EmitterGizmo.ts"}, "messages": [{"level": "warn", "text": "Did you forget the extension? Please note that you can not omit extension in module specifier."}]}, {"resolved": {"type": "error", "text": "Error: Modu<PERSON> \"../assets/scripts/Game/world/bullet/EmitterArc\" not found for file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/gizmos/EmitterGizmo.ts"}, "messages": [{"level": "warn", "text": "Did you forget the extension? Please note that you can not omit extension in module specifier."}]}]}, "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/AutoGen/Luban/schema.ts": {"mTimestamp": {"mtime": 1753456348290.6362, "uuid": "da084ae1-6b5b-4d7a-94a6-887fdbb7e052"}, "chunkId": "72ecae1bf50c0c60fb4a52f6ce63fdd14c996055", "imports": [{"value": "cc"}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}]}, "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/AutoGen/PB/cs_proto.ts": {"mTimestamp": {"mtime": 1753456348302.1409, "uuid": "81097702-1c01-407d-aeb2-686f8e932e49"}, "chunkId": "4ef217da3ec9d6cba89a4dfe777bf66855a38ef7", "imports": [{"value": "cc"}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}]}, "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Anim.ts": {"mTimestamp": {"mtime": 1753456348345.1082, "uuid": "5348a46b-267c-4023-8b84-3382eb48f3c8"}, "chunkId": "90c49f1d073d8aed9d07661d554f1a7b97dfca89", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 50}, "end": {"line": 1, "column": 54}}}, {"value": "./PersistNode", "resolved": "__unresolved_1", "loc": {"start": {"line": 3, "column": 28}, "end": {"line": 3, "column": 43}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/PersistNode.ts"}, "messages": []}]}, "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Background.ts": {"mTimestamp": {"mtime": 1753456348346.1387, "uuid": "03439b13-5f90-4827-b94b-4a91188a5588"}, "chunkId": "8303dbd1e4e9a43f61eebb3bbb0e3440d0d9fe98", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 50}, "end": {"line": 1, "column": 54}}}, {"value": "./Global", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 23}, "end": {"line": 2, "column": 33}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Global.ts"}, "messages": []}]}, "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Enemy.ts": {"mTimestamp": {"mtime": 1753456348346.8938, "uuid": "c5c20e85-6542-4c38-8f04-f74386d5ec9f"}, "chunkId": "1790dc4a1142b7747fab0e04c2c046af1354bff0", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 182}, "end": {"line": 1, "column": 186}}}, {"value": "./Global", "resolved": "__unresolved_1", "loc": {"start": {"line": 4, "column": 23}, "end": {"line": 4, "column": 33}}}, {"value": "./PersistNode", "resolved": "__unresolved_2", "loc": {"start": {"line": 5, "column": 28}, "end": {"line": 5, "column": 43}}}, {"value": "./Player", "resolved": "__unresolved_3", "loc": {"start": {"line": 6, "column": 23}, "end": {"line": 6, "column": 33}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Global.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/PersistNode.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Player.ts"}, "messages": []}]}, "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/EnemyBullet.ts": {"mTimestamp": {"mtime": 1753456348347.8955, "uuid": "c8821111-2211-493e-ba13-2fe76ae3fa1c"}, "chunkId": "1064f43aae4974ff1206f811e89073bdde0b5209", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 164}, "end": {"line": 1, "column": 168}}}, {"value": "./Global", "resolved": "__unresolved_1", "loc": {"start": {"line": 3, "column": 23}, "end": {"line": 3, "column": 33}}}, {"value": "./PersistNode", "resolved": "__unresolved_2", "loc": {"start": {"line": 4, "column": 28}, "end": {"line": 4, "column": 43}}}, {"value": "./Player", "resolved": "__unresolved_3", "loc": {"start": {"line": 5, "column": 23}, "end": {"line": 5, "column": 33}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Global.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/PersistNode.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Player.ts"}, "messages": []}]}, "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/GameOver.ts": {"mTimestamp": {"mtime": 1753456348347.8955, "uuid": "382d6722-7526-4b22-8289-be056b91e1aa"}, "chunkId": "a474f573cf6464e7ff600b4f42608da1f2bc3eb5", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 67}, "end": {"line": 1, "column": 71}}}, {"value": "./Global", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 23}, "end": {"line": 2, "column": 33}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Global.ts"}, "messages": []}]}, "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Global.ts": {"mTimestamp": {"mtime": 1753456348348.8992, "uuid": "ffc9acad-fc44-4a0b-8e0d-6670b6c4e175"}, "chunkId": "1172e95150266ca76bdc07f5a6b954d43b6f54fe", "imports": [{"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 50}, "end": {"line": 1, "column": 54}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}]}, "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Goods.ts": {"mTimestamp": {"mtime": 1753456348348.8992, "uuid": "279df947-049f-4830-9cf0-65d34ee16a7e"}, "chunkId": "194397fcfb14206803a6e76a7b51f6837a34a807", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 136}, "end": {"line": 1, "column": 140}}}, {"value": "./Global", "resolved": "__unresolved_1", "loc": {"start": {"line": 3, "column": 23}, "end": {"line": 3, "column": 33}}}, {"value": "./PersistNode", "resolved": "__unresolved_2", "loc": {"start": {"line": 4, "column": 28}, "end": {"line": 4, "column": 43}}}, {"value": "./Player", "resolved": "__unresolved_3", "loc": {"start": {"line": 5, "column": 23}, "end": {"line": 5, "column": 33}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Global.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/PersistNode.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Player.ts"}, "messages": []}]}, "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/MainGame.ts": {"mTimestamp": {"mtime": 1753456348349.8997, "uuid": "226be14a-b41d-43c8-86b2-bee80a67eb84"}, "chunkId": "4b37dd703325c51acf9b0577ef0f37258a01d0c1", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 56}, "end": {"line": 1, "column": 60}}}, {"value": "./Global", "resolved": "__unresolved_1", "loc": {"start": {"line": 3, "column": 23}, "end": {"line": 3, "column": 33}}}, {"value": "./PersistNode", "resolved": "__unresolved_2", "loc": {"start": {"line": 4, "column": 28}, "end": {"line": 4, "column": 43}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Global.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/PersistNode.ts"}, "messages": []}]}, "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Menu.ts": {"mTimestamp": {"mtime": 1753456348350.8994, "uuid": "5d5384ab-499f-4033-bbc9-ee1934c2d0c5"}, "chunkId": "470e324cd2b2b5995d29b49c6f21c681dad4b521", "imports": [{"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 54}, "end": {"line": 1, "column": 58}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}]}, "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/PersistNode.ts": {"mTimestamp": {"mtime": 1753456348350.8994, "uuid": "402f76e8-a281-47a5-8c49-298291c49c53"}, "chunkId": "b66666b7b39dabae6748d57429a55ee99b81250a", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 82}, "end": {"line": 1, "column": 86}}}, {"value": "./factroy/AnimFactory", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 28}, "end": {"line": 2, "column": 51}}}, {"value": "./factroy/EnemyBulletFactory", "resolved": "__unresolved_2", "loc": {"start": {"line": 3, "column": 35}, "end": {"line": 3, "column": 65}}}, {"value": "./factroy/EnemyFactory", "resolved": "__unresolved_3", "loc": {"start": {"line": 4, "column": 29}, "end": {"line": 4, "column": 53}}}, {"value": "./factroy/GoodsFactory", "resolved": "__unresolved_4", "loc": {"start": {"line": 6, "column": 29}, "end": {"line": 6, "column": 53}}}, {"value": "./factroy/PlayerBulletFactory", "resolved": "__unresolved_5", "loc": {"start": {"line": 7, "column": 36}, "end": {"line": 7, "column": 67}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/AnimFactory.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/EnemyBulletFactory.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/EnemyFactory.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/GoodsFactory.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/PlayerBulletFactory.ts"}, "messages": []}]}, "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Player.ts": {"mTimestamp": {"mtime": 1753456348351.8994, "uuid": "53eed65f-04fc-48a2-9b7a-a716c1a966bc"}, "chunkId": "b108afceb543fb56e45eec2df045bf626f20d8d4", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 104}, "end": {"line": 1, "column": 108}}}, {"value": "./Global", "resolved": "__unresolved_1", "loc": {"start": {"line": 3, "column": 23}, "end": {"line": 3, "column": 33}}}, {"value": "./PersistNode", "resolved": "__unresolved_2", "loc": {"start": {"line": 4, "column": 28}, "end": {"line": 4, "column": 43}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Global.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/PersistNode.ts"}, "messages": []}]}, "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/PlayerBullet.ts": {"mTimestamp": {"mtime": 1753456348351.8994, "uuid": "5ac3438e-1947-45e8-b9b1-71747e7a1efd"}, "chunkId": "a8d281c2aa91a9173c90886df51758277359a3aa", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 171}, "end": {"line": 1, "column": 175}}}, {"value": "./Enemy", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 22}, "end": {"line": 2, "column": 31}}}, {"value": "./Global", "resolved": "__unresolved_2", "loc": {"start": {"line": 4, "column": 23}, "end": {"line": 4, "column": 33}}}, {"value": "./PersistNode", "resolved": "__unresolved_3", "loc": {"start": {"line": 5, "column": 28}, "end": {"line": 5, "column": 43}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Enemy.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Global.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/PersistNode.ts"}, "messages": []}]}, "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/AnimFactory.ts": {"mTimestamp": {"mtime": 1753456348352.8992, "uuid": "cdb002a1-a1b1-42c5-99bd-0032423f6753"}, "chunkId": "3a94616c882ba3bcfadc63174891808eca7c82ba", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 57}, "end": {"line": 1, "column": 61}}}, {"value": "../PersistNode", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 28}, "end": {"line": 2, "column": 44}}}, {"value": "./GameFactory", "resolved": "__unresolved_2", "loc": {"start": {"line": 3, "column": 28}, "end": {"line": 3, "column": 43}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/PersistNode.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/GameFactory.ts"}, "messages": []}]}, "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/EnemyBulletFactory.ts": {"mTimestamp": {"mtime": 1753456348352.8992, "uuid": "82bdeebd-c187-4869-82f8-8a78ea38a888"}, "chunkId": "b81146aaf158ea2edec76e5af0d70f6b9b37cda0", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 57}, "end": {"line": 1, "column": 61}}}, {"value": "../EnemyBullet", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 28}, "end": {"line": 2, "column": 44}}}, {"value": "../Global", "resolved": "__unresolved_2", "loc": {"start": {"line": 3, "column": 23}, "end": {"line": 3, "column": 34}}}, {"value": "../PersistNode", "resolved": "__unresolved_3", "loc": {"start": {"line": 4, "column": 28}, "end": {"line": 4, "column": 44}}}, {"value": "./GameFactory", "resolved": "__unresolved_4", "loc": {"start": {"line": 5, "column": 28}, "end": {"line": 5, "column": 43}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/EnemyBullet.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Global.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/PersistNode.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/GameFactory.ts"}, "messages": []}]}, "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/EnemyFactory.ts": {"mTimestamp": {"mtime": 1753456348353.899, "uuid": "11ec996f-30de-45ce-bb37-825678216d22"}, "chunkId": "3edc50e04e7f27a742741db9a2f6912ace73edaf", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 57}, "end": {"line": 1, "column": 61}}}, {"value": "../Enemy", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 22}, "end": {"line": 2, "column": 32}}}, {"value": "../Global", "resolved": "__unresolved_2", "loc": {"start": {"line": 3, "column": 23}, "end": {"line": 3, "column": 34}}}, {"value": "../PersistNode", "resolved": "__unresolved_3", "loc": {"start": {"line": 4, "column": 28}, "end": {"line": 4, "column": 44}}}, {"value": "./GameFactory", "resolved": "__unresolved_4", "loc": {"start": {"line": 5, "column": 28}, "end": {"line": 5, "column": 43}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Enemy.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Global.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/PersistNode.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/GameFactory.ts"}, "messages": []}]}, "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/GameFactory.ts": {"mTimestamp": {"mtime": 1753456348353.899, "uuid": "7c1a95a6-c72a-48b2-82c7-dc072b10d00e"}, "chunkId": "3fb64f09695766ae99536892c9a805c1a641357d", "imports": [{"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 60}, "end": {"line": 1, "column": 64}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}]}, "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/GoodsFactory.ts": {"mTimestamp": {"mtime": 1753456348354.8994, "uuid": "6a38b65e-437d-4341-af31-e037c890fed3"}, "chunkId": "b5b24d8b3d6414b07a092c065950b3c809f7573a", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 57}, "end": {"line": 1, "column": 61}}}, {"value": "../Global", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 23}, "end": {"line": 2, "column": 34}}}, {"value": "../Goods", "resolved": "__unresolved_2", "loc": {"start": {"line": 3, "column": 22}, "end": {"line": 3, "column": 32}}}, {"value": "../PersistNode", "resolved": "__unresolved_3", "loc": {"start": {"line": 4, "column": 28}, "end": {"line": 4, "column": 44}}}, {"value": "./GameFactory", "resolved": "__unresolved_4", "loc": {"start": {"line": 5, "column": 28}, "end": {"line": 5, "column": 43}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Global.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Goods.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/PersistNode.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/GameFactory.ts"}, "messages": []}]}, "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/PlayerBulletFactory.ts": {"mTimestamp": {"mtime": 1753456348354.8994, "uuid": "73373683-639f-498e-a8c0-871383257cbe"}, "chunkId": "3ee4a8584a649c5ab4ed92121fddc68f76b92d90", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 57}, "end": {"line": 1, "column": 61}}}, {"value": "../Global", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 23}, "end": {"line": 2, "column": 34}}}, {"value": "../PersistNode", "resolved": "__unresolved_2", "loc": {"start": {"line": 3, "column": 28}, "end": {"line": 3, "column": 44}}}, {"value": "../PlayerBullet", "resolved": "__unresolved_3", "loc": {"start": {"line": 4, "column": 29}, "end": {"line": 4, "column": 46}}}, {"value": "./GameFactory", "resolved": "__unresolved_4", "loc": {"start": {"line": 5, "column": 28}, "end": {"line": 5, "column": 43}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Global.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/PersistNode.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/PlayerBullet.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/GameFactory.ts"}, "messages": []}]}, "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/Bootstrap.ts": {"mTimestamp": {"mtime": 1753545291648.9524, "uuid": "65d59376-f39a-4365-8dd7-250c1d7140db"}, "chunkId": "31f23b42610b243216ebc78eb807405366313241", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 50}, "end": {"line": 1, "column": 54}}}, {"value": "./base/World", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 34}, "end": {"line": 2, "column": 48}}}, {"value": "./WorldInitializeData", "resolved": "__unresolved_2", "loc": {"start": {"line": 3, "column": 85}, "end": {"line": 3, "column": 108}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/base/World.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/WorldInitializeData.ts"}, "messages": []}]}, "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/WorldInitializeData.ts": {"mTimestamp": {"mtime": 1753545291648.9524, "uuid": "0c36c604-d0de-4dc7-9fc7-132803bb9bf9"}, "chunkId": "36aad766502eb63bb086247bef936a13974a4323", "imports": [{"value": "cc"}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}]}, "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/base/System.ts": {"mTimestamp": {"mtime": 1753548226390.349, "uuid": "68463dec-03c8-4483-a858-88be18da4675"}, "chunkId": "3c1461c684e9efb1bd6d6fe61057e789a806bc2f", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 27}, "end": {"line": 1, "column": 31}}}, {"value": "./TypeID", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 34}, "end": {"line": 2, "column": 44}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/base/TypeID.ts"}, "messages": []}]}, "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/base/SystemContainer.ts": {"mTimestamp": {"mtime": 1753548330880.0435, "uuid": "237cafbd-51ce-4858-b19c-a058b48c9d0c"}, "chunkId": "68172ee5d7715352afa9d500ac999c07dab6d39a", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 27}, "end": {"line": 1, "column": 31}}}, {"value": "./TypeID", "resolved": "__unresolved_1", "loc": {"start": {"line": 4, "column": 36}, "end": {"line": 4, "column": 46}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/base/TypeID.ts"}, "messages": []}]}, "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/base/TypeID.ts": {"mTimestamp": {"mtime": 1753545291650.9573, "uuid": "0eea3cf5-1ec0-4055-99d5-ee74c4c53850"}, "chunkId": "eb4422d712d89490a4c22ed85a1d4b298a69615f", "imports": [{"value": "cc"}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}]}, "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/base/World.ts": {"mTimestamp": {"mtime": 1753548241170.4546, "uuid": "73f5b7e0-b6c4-4bd2-807b-a090d20b7c1c"}, "chunkId": "442921681ed59bd4dd2c9878b5e921e3006a229d", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "./SystemContainer", "resolved": "__unresolved_1", "loc": {"start": {"line": 1, "column": 32}, "end": {"line": 1, "column": 51}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/base/SystemContainer.ts"}, "messages": []}]}, "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/bullet/Bullet.ts": {"mTimestamp": {"mtime": 1753545291678.515, "uuid": "21904ac5-11f3-4245-b818-c752c4f8ce8f"}, "chunkId": "063998d3b3a8ef0a1915a12db47783f8ac686900", "imports": [{"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 44}, "end": {"line": 1, "column": 48}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}]}, "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/bullet/BulletSystem.ts": {"mTimestamp": {"mtime": 1753545291678.515, "uuid": "7cb83a17-5595-4c04-9619-63b94934fc55"}, "chunkId": "b70553d6bbd0a89ebffd0bcece694982f2bbdabe", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 39}, "end": {"line": 1, "column": 43}}}, {"value": "../base/System", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 23}, "end": {"line": 2, "column": 39}}}, {"value": "../base/TypeID", "resolved": "__unresolved_2", "loc": {"start": {"line": 3, "column": 31}, "end": {"line": 3, "column": 47}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/base/System.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/base/TypeID.ts"}, "messages": []}]}, "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/bullet/Emitter.ts": {"mTimestamp": {"mtime": 1753548567590.175, "uuid": "2564d02b-7111-4a64-aa28-de874242b1f0"}, "chunkId": "10408dcd89246d6e37923b5ae6ec4a0179ea11e5", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 33}, "end": {"line": 1, "column": 37}}}, {"value": "../base/Object", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 24}, "end": {"line": 2, "column": 40}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/base/Object.ts"}, "messages": []}]}, "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/index.ts": {"mTimestamp": {"mtime": 1753547993968.4265, "uuid": "22545f8c-666f-4136-8361-0fe5fba57a0f"}, "chunkId": "d2ebc07c2bde4611628c92022e6b5a02c718cde0", "imports": [{"value": "cc"}, {"value": "./base/System", "resolved": "__unresolved_0", "loc": {"start": {"line": 9, "column": 23}, "end": {"line": 9, "column": 38}}}, {"value": "./base/SystemContainer", "resolved": "__unresolved_1", "loc": {"start": {"line": 10, "column": 32}, "end": {"line": 10, "column": 56}}}, {"value": "./base/TypeID", "resolved": "__unresolved_2", "loc": {"start": {"line": 12, "column": 50}, "end": {"line": 12, "column": 65}}}, {"value": "./base/TypeID", "resolved": "__unresolved_3", "loc": {"start": {"line": 14, "column": 28}, "end": {"line": 14, "column": 43}}}, {"value": "./base/Object", "resolved": "__unresolved_4", "loc": {"start": {"line": 15, "column": 24}, "end": {"line": 15, "column": 39}}}, {"value": "./base/World", "resolved": "__unresolved_5", "loc": {"start": {"line": 18, "column": 34}, "end": {"line": 18, "column": 48}}}, {"value": "./WorldInitializeData", "resolved": "__unresolved_6", "loc": {"start": {"line": 25, "column": 7}, "end": {"line": 25, "column": 30}}}, {"value": "./Bootstrap", "resolved": "__unresolved_7", "loc": {"start": {"line": 36, "column": 26}, "end": {"line": 36, "column": 39}}}, {"value": "./bullet/BulletSystem", "resolved": "__unresolved_8", "loc": {"start": {"line": 39, "column": 29}, "end": {"line": 39, "column": 52}}}, {"value": "./bullet/Emitter", "resolved": "__unresolved_9", "loc": {"start": {"line": 43, "column": 24}, "end": {"line": 43, "column": 42}}}, {"value": "./bullet/EmitterArc", "resolved": "__unresolved_10", "loc": {"start": {"line": 44, "column": 27}, "end": {"line": 44, "column": 48}}}, {"value": "../gizmos", "resolved": "__unresolved_11", "loc": {"start": {"line": 47, "column": 14}, "end": {"line": 47, "column": 25}}}, {"value": "./level/LevelSystem", "resolved": "__unresolved_12", "loc": {"start": {"line": 49, "column": 44}, "end": {"line": 49, "column": 65}}}, {"value": "./player/PlayerSystem", "resolved": "__unresolved_13", "loc": {"start": {"line": 57, "column": 42}, "end": {"line": 57, "column": 65}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/base/System.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/base/SystemContainer.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/base/TypeID.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/base/TypeID.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/base/Object.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/base/World.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/WorldInitializeData.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/Bootstrap.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/bullet/BulletSystem.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/bullet/Emitter.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/bullet/EmitterArc.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/gizmos/index.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/level/LevelSystem.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/player/PlayerSystem.ts"}, "messages": []}]}, "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/level/LevelSystem.ts": {"mTimestamp": {"mtime": 1753545291698.309, "uuid": "21f782d5-044f-45eb-b25d-c6291183b1ad"}, "chunkId": "17cbfc224817f88148026e72bf5a79b129141f46", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 39}, "end": {"line": 1, "column": 43}}}, {"value": "../base/System", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 23}, "end": {"line": 2, "column": 39}}}, {"value": "../base/TypeID", "resolved": "__unresolved_2", "loc": {"start": {"line": 3, "column": 31}, "end": {"line": 3, "column": 47}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/base/System.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/base/TypeID.ts"}, "messages": []}]}, "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/player/PlayerSystem.ts": {"mTimestamp": {"mtime": 1753545291706.3325, "uuid": "4fec5f67-8da2-438f-9ffc-6732e3de3cf0"}, "chunkId": "29ed0592949f045efa4660fd34382d22122b896b", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 39}, "end": {"line": 1, "column": 43}}}, {"value": "../base/System", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 23}, "end": {"line": 2, "column": 39}}}, {"value": "../base/TypeID", "resolved": "__unresolved_2", "loc": {"start": {"line": 3, "column": 31}, "end": {"line": 3, "column": 47}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/base/System.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/base/TypeID.ts"}, "messages": []}]}, "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameInstance.ts": {"mTimestamp": {"mtime": 1753456348367.5625, "uuid": "815bc83a-9815-48da-859e-0a61c8d7764f"}, "chunkId": "e507d78a99dec40fff7f6010e884a22838e6e0a2", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 38}, "end": {"line": 1, "column": 42}}}, {"value": "./Luban/LubanMgr", "resolved": "__unresolved_1", "loc": {"start": {"line": 3, "column": 25}, "end": {"line": 3, "column": 43}}}, {"value": "./Network/NetMgr", "resolved": "__unresolved_2", "loc": {"start": {"line": 4, "column": 23}, "end": {"line": 4, "column": 41}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Luban/LubanMgr.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Network/NetMgr.ts"}, "messages": []}]}, "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/IMgr.ts": {"mTimestamp": {"mtime": 1753456348368.566, "uuid": "4f6cfd7a-7406-4eb5-a557-4e504e81c910"}, "chunkId": "01c1b3ea74140a8728554a3aa5bbb75aa7a5f021", "imports": [{"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 44}, "end": {"line": 1, "column": 48}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}]}, "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Luban/LubanMgr.ts": {"mTimestamp": {"mtime": 1753456348370.266, "uuid": "3d9905b2-c72b-4a59-84d9-ab422829ec3b"}, "chunkId": "8cc014d3522682d36f2178eea244a3c19e4d8ca1", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 80}, "end": {"line": 1, "column": 84}}}, {"value": "../IMgr", "resolved": "__unresolved_1", "loc": {"start": {"line": 3, "column": 21}, "end": {"line": 3, "column": 30}}}, {"value": "../AutoGen/Luban/schema", "resolved": "__unresolved_2", "loc": {"start": {"line": 4, "column": 21}, "end": {"line": 4, "column": 46}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/IMgr.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/AutoGen/Luban/schema.ts"}, "messages": []}]}, "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/MainUI.ts": {"mTimestamp": {"mtime": 1753456348376.2693, "uuid": "ae524ea8-f776-4444-9e5b-a5143e06dbf9"}, "chunkId": "5cfa102246b39c5999a31ef4f2db93c24293b989", "imports": [{"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 55}, "end": {"line": 1, "column": 59}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}]}, "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Network/NetMgr.ts": {"mTimestamp": {"mtime": 1753456348377.2693, "uuid": "d7f90c8a-8a20-40f9-9a4a-98f31574e17a"}, "chunkId": "e6ee09e0a836659daeca4b7a85e86f7ac5a5f093", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 80}, "end": {"line": 1, "column": 84}}}, {"value": "../IMgr", "resolved": "__unresolved_1", "loc": {"start": {"line": 3, "column": 21}, "end": {"line": 3, "column": 30}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/IMgr.ts"}, "messages": []}]}, "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ResUpdate/ResUpdate.ts": {"mTimestamp": {"mtime": 1753456348386.1567, "uuid": "30734ca9-59e8-4f53-9672-855d960d7427"}, "chunkId": "b3433f15e11500991d9cadb4ca4daa00193597a3", "imports": [{"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 149}, "end": {"line": 1, "column": 153}}}, {"value": "cc/env", "loc": {"start": {"line": 2, "column": 23}, "end": {"line": 2, "column": 31}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cc/env"}, "messages": []}]}, "cce:/internal/code-quality/cr.mjs": {"mTimestamp": 1753545361655, "chunkId": "6a5019a719a9014c047e67aa1cf34453ab8392ce", "imports": [], "type": "esm", "resolutions": []}, "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/base/Object.ts": {"mTimestamp": {"mtime": 1753546710508.2942, "uuid": "53e332a8-652b-470a-b1f6-9af2a83b25dd"}, "chunkId": "159dc09dce07686146f3935d155be40bcfe5c693", "imports": [{"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 38}, "end": {"line": 1, "column": 42}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}]}, "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/bullet/EmitterArc.ts": {"mTimestamp": {"mtime": 1753545961346.8281, "uuid": "93e57e61-bb20-4f4f-a6ce-46d73aa1b41b"}, "chunkId": "8ff1e1d9446eebc7603d201a812d17fde3622e05", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 27}, "end": {"line": 1, "column": 31}}}, {"value": "./Emitter", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 24}, "end": {"line": 2, "column": 35}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/bullet/Emitter.ts"}, "messages": []}]}, "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/gizmos/EmitterArcGizmo.ts": {"mTimestamp": {"mtime": 1753547878963.7212, "uuid": "3c748240-8509-4d0f-9045-5d2db6ddd6a3"}, "chunkId": "c02c0d4d92b525c8ffe3aef04c2899b33ea9c8ba", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 50}, "end": {"line": 1, "column": 54}}}, {"value": "./GizmoDrawer", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 28}, "end": {"line": 2, "column": 43}}}, {"value": "../world/bullet/EmitterArc", "resolved": "__unresolved_2", "loc": {"start": {"line": 3, "column": 27}, "end": {"line": 3, "column": 55}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/gizmos/GizmoDrawer.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/bullet/EmitterArc.ts"}, "messages": []}]}, "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/gizmos/EmitterGizmo.ts": {"mTimestamp": {"mtime": 1753547899188.8772, "uuid": "c633a654-c806-466f-ae8a-32fdaf7bdcd2"}, "chunkId": "c27e004a01234347d61d9c89455e457d0c361696", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 50}, "end": {"line": 1, "column": 54}}}, {"value": "./GizmoDrawer", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 28}, "end": {"line": 2, "column": 43}}}, {"value": "../world/bullet/Emitter", "resolved": "__unresolved_2", "loc": {"start": {"line": 3, "column": 24}, "end": {"line": 3, "column": 49}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/gizmos/GizmoDrawer.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/bullet/Emitter.ts"}, "messages": []}]}, "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/gizmos/GizmoDrawer.ts": {"mTimestamp": {"mtime": 1753547758227.6746, "uuid": "0088d2f5-c793-493c-834c-205c9a50911f"}, "chunkId": "e5ab51e9b74404eeb86a5ef59d955e5cab728dfd", "imports": [{"value": "cc"}, {"value": "cc"}, {"value": "cc/env", "loc": {"start": {"line": 2, "column": 23}, "end": {"line": 2, "column": 31}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cc/env"}, "messages": []}]}, "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/gizmos/GizmoManager.ts": {"mTimestamp": {"mtime": 1753548103218.5647, "uuid": "35b7ed22-0671-47b6-aaaf-01dd52bab33b"}, "chunkId": "ece4226b846bfb9c768c3ca59fb1e2355e910962", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 67}, "end": {"line": 1, "column": 71}}}, {"value": "cc/env", "loc": {"start": {"line": 2, "column": 23}, "end": {"line": 2, "column": 31}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cc/env"}, "messages": []}]}, "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/gizmos/GizmoSetup.ts": {"mTimestamp": {"mtime": 1753547957083.2244, "uuid": "30d87f27-b1e4-468a-9d97-ef87409c60a9"}, "chunkId": "c28552905bb24483ff88966e741df96e1577ddc1", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 54}, "end": {"line": 1, "column": 58}}}, {"value": "cc/env", "loc": {"start": {"line": 2, "column": 23}, "end": {"line": 2, "column": 31}}}, {"value": "./index", "resolved": "__unresolved_1", "loc": {"start": {"line": 3, "column": 47}, "end": {"line": 3, "column": 56}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cc/env"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/gizmos/index.ts"}, "messages": []}]}, "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/gizmos/index.ts": {"mTimestamp": {"mtime": 1753547982349.011, "uuid": "29b38698-913f-4357-9a8a-dde8b3838535"}, "chunkId": "bed35bbc8820bca845de6304e2cd055fa5a66202", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "./GizmoDrawer", "resolved": "__unresolved_1", "loc": {"start": {"line": 9, "column": 28}, "end": {"line": 9, "column": 43}}}, {"value": "./GizmoManager", "resolved": "__unresolved_2", "loc": {"start": {"line": 10, "column": 29}, "end": {"line": 10, "column": 45}}}, {"value": "./EmitterGizmo", "resolved": "__unresolved_3", "loc": {"start": {"line": 13, "column": 29}, "end": {"line": 13, "column": 45}}}, {"value": "./EmitterArcGizmo", "resolved": "__unresolved_4", "loc": {"start": {"line": 14, "column": 32}, "end": {"line": 14, "column": 51}}}, {"value": "./GizmoManager", "resolved": "__unresolved_5", "loc": {"start": {"line": 17, "column": 29}, "end": {"line": 17, "column": 45}}}, {"value": "./EmitterGizmo", "resolved": "__unresolved_6", "loc": {"start": {"line": 18, "column": 29}, "end": {"line": 18, "column": 45}}}, {"value": "./EmitterArcGizmo", "resolved": "__unresolved_7", "loc": {"start": {"line": 19, "column": 32}, "end": {"line": 19, "column": 51}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/gizmos/GizmoDrawer.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/gizmos/GizmoManager.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/gizmos/EmitterGizmo.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/gizmos/EmitterArcGizmo.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/gizmos/GizmoManager.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/gizmos/EmitterGizmo.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/gizmos/EmitterArcGizmo.ts"}, "messages": []}]}}}