System.register(["cc"], function (_export, _context) {
  "use strict";

  var _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Component, _dec, _class, _crd, ccclass, CObject;

  return {
    setters: [function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Component = _cc.Component;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "53e33KoZStHCrH2mvKoOyXd", "Object", undefined);

      __checkObsolete__(['_decorator', 'Component']);

      ({
        ccclass
      } = _decorator);
      /**
       * Abstract base class for all world objects
       * Inherits from Cocos Creator Component and provides common functionality
       */

      _export("CObject", CObject = (_dec = ccclass('CObject'), _dec(_class = class CObject extends Component {
        /**
         * Called when the object is initialized
         * Override this method to implement object-specific initialization logic
         */

        /**
         * Called when the object is destroyed
         * Override this method to implement object-specific cleanup logic
         */

        /**
         * Initialize the object
         * This is called automatically by the framework
         */
        onLoad() {
          this.onObjectInit();
        }
        /**
         * Cleanup the object
         * This is called automatically by the framework
         */


        onDestroy() {
          this.onObjectDestroy();
        }

      }) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=159dc09dce07686146f3935d155be40bcfe5c693.js.map