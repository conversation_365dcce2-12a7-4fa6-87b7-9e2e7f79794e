import { _decorator, Component } from "cc";
import { IMgr, FnOnUpdate, FnOnLateUpdate } from "./IMgr";
import { LubanMgr } from "./Luban/LubanMgr";
import { NetMgr } from "./Network/NetMgr";
const { ccclass } = _decorator;

@ccclass("GameInstance")
export class GameInstance extends Component {

    private static _instance: GameInstance = null;
    public static GetInstance(): GameInstance {
        return GameInstance._instance;
    }
    private ManagerPool:IMgr[]= [];
    private _updateContainer:FnOnUpdate[]=[];
    private _lateUpdateContainer:FnOnLateUpdate[]=[];

    private _lubanMgr:LubanMgr=null;
    private _netMgr:NetMgr=null;

    onLoad () {
        GameInstance._instance = this; 

        this._lubanMgr = new LubanMgr();
        this.ManagerPool.push(this._lubanMgr);
        this._netMgr = new NetMgr();
        this.ManagerPool.push(this._netMgr);

        this.ManagerPool.forEach(manager => {
             manager.init();
             this._updateContainer.push(manager.onUpdate.bind(manager));
             this._lateUpdateContainer.push(manager.onLateUpdate.bind(manager));
         });
    }
    update (deltaTime: number) {
        for (let i = 0; i < this._updateContainer.length; i++) {
            this._updateContainer[i](deltaTime);
        }
    }

    lateUpdate() {
        for (let i = 0; i < this._lateUpdateContainer.length; i++) {
            this._lateUpdateContainer[i]();
        }
    }
}