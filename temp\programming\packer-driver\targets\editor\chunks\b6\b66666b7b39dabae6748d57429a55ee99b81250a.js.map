{"version": 3, "sources": ["file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/PersistNode.ts"], "names": ["_decorator", "Component", "game", "Prefab", "SpriteFrame", "AudioClip", "AnimFactory", "EnemyBulletFactory", "EnemyFactory", "GoodsFactory", "PlayerBulletFactory", "ccclass", "property", "PersistNode", "playerBulletFactory", "enemyFactory", "enemyBulletFactory", "goodsFactory", "animFactory", "onLoad", "addPersistRootNode", "node"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAiBC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,S,OAAAA,S;;AACxDC,MAAAA,W,iBAAAA,W;;AACAC,MAAAA,kB,iBAAAA,kB;;AACAC,MAAAA,Y,iBAAAA,Y;;AAEAC,MAAAA,Y,iBAAAA,Y;;AACAC,MAAAA,mB,iBAAAA,mB;;;;;;;;;OACH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBZ,U;;6BAGjBa,W,WADZF,OAAO,CAAC,aAAD,C,UAGHC,QAAQ,CAACT,MAAD,C,UAGRS,QAAQ,CAACT,MAAD,C,UAGRS,QAAQ,CAACT,MAAD,C,UAGRS,QAAQ,CAACT,MAAD,C,UAGRS,QAAQ,CAACT,MAAD,C,UAGRS,QAAQ,CAACR,WAAD,C,UAGRQ,QAAQ,CAACR,WAAD,C,UAGRQ,QAAQ,CAACR,WAAD,C,WAGRQ,QAAQ,CAACR,WAAD,C,WAGRQ,QAAQ,CAACR,WAAD,C,WAGRQ,QAAQ,CAACR,WAAD,C,WAGRQ,QAAQ,CAACR,WAAD,C,WAGRQ,QAAQ,CAACR,WAAD,C,WAGRQ,QAAQ,CAACR,WAAD,C,WAGRQ,QAAQ,CAACR,WAAD,C,WAsGRQ,QAAQ,CAACP,SAAD,C,WAGRO,QAAQ,CAACP,SAAD,C,WAGRO,QAAQ,CAACP,SAAD,C,WAGRO,QAAQ,CAACP,SAAD,C,WAGRO,QAAQ,CAACP,SAAD,C,2BA/Jb,MACaQ,WADb,SACiCZ,SADjC,CAC2C;AAAA;AAAA;;AAAA;;AAGH;AAHG;;AAMP;AANO;;AASX;AATW;;AAYlB;AAZkB;;AAejB;AAfiB;;AAkBL;AAlBK;;AAqBL;AArBK;;AAwBH;AAxBG;;AA2BH;AA3BG;;AA8BH;AA9BG;;AAiCH;AAjCG;;AAoCP;AApCO;;AAuCP;AAvCO;;AA0CD;AA1CC;;AA6CD;AA7CC;;AAgDN;AAhDM;;AAmDP;AAnDO;;AAsDL;AAtDK;;AAyDD;AAzDC;;AA4DF;AA5DE;;AA+DA;AA/DA;;AAkEC;AAlED;;AAqEC;AArED;;AAwEF;AAxEE;;AA2EH;AA3EG;;AA8EF;AA9EE;;AAiFH;AAjFG;;AAoFE;AApFF;;AAuFC;AAvFD;;AA0FE;AA1FF;;AA6FC;AA7FD;;AAgGI;AAhGJ;;AAmGG;AAnGH;;AAsGP;AAtGO;;AAyGP;AAzGO;;AA4GP;AA5GO;;AA+GP;AA/GO;;AAkHF;AAlHE;;AAqHP;AArHO;;AAwHK;AAxHL;;AA2HK;AA3HL;;AA8HC;AA9HD;;AAiIA;AAjIA;;AAoIE;AApIF;;AAuIA;AAvIA;;AA0IH;AA1IG;;AA6ID;AA7IC;;AAgJC;AAhJD;;AAmJC;AAnJD;;AAsJA;AAtJA;;AAyJE;AAzJF;;AA4JD;AA5JC;;AA+JG;AA/JH,eAiKvCa,mBAjKuC,GAiKJ,IAjKI;AAiKK;AAjKL,eAmKvCC,YAnKuC,GAmKX,IAnKW;AAmKC;AAnKD,eAqKvCC,kBArKuC,GAqKL,IArKK;AAqKE;AArKF,eAuKvCC,YAvKuC,GAuKX,IAvKW;AAuKJ;AAvKI,eAyKvCC,WAzKuC,GAyKZ,IAzKY;AAAA;;AAyKN;AAEjCC,QAAAA,MAAM,GAAG;AACLjB,UAAAA,IAAI,CAACkB,kBAAL,CAAwB,KAAKC,IAA7B,EADK,CACkC;;AAEvC,eAAKP,mBAAL,GAA2B;AAAA;AAAA,2DAA3B,CAHK,CAGiD;;AAEtD,eAAKC,YAAL,GAAoB;AAAA;AAAA,6CAApB,CALK,CAKmC;;AAExC,eAAKC,kBAAL,GAA0B;AAAA;AAAA,yDAA1B,CAPK,CAO+C;;AAEpD,eAAKC,YAAL,GAAoB;AAAA;AAAA,6CAApB,CATK,CASmC;;AAExC,eAAKC,WAAL,GAAmB;AAAA;AAAA,2CAAnB;AACH;;AAvLsC,O;;;;;iBAGZ,I;;;;;;;iBAGP,I;;;;;;;iBAGF,I;;;;;;;iBAGP,I;;;;;;;iBAGC,I;;;;;;;iBAGc,I;;;;;;;iBAGA,I;;;;;;;iBAGE,I;;;;;;;iBAGA,I;;;;;;;iBAGD,I;;;;;;;iBAGE,I;;;;;;;iBAGP,I;;;;;;;iBAGA,I;;;;;;;iBAGM,I;;;;;;;iBAGA,I;;6FAE3BN,Q;;;;;iBAC2B,C;;4FAE3BA,Q;;;;;iBAC0B,C;;8FAE1BA,Q;;;;;iBAC4B,C;;iGAE5BA,Q;;;;;iBAC+B,C;;gGAE/BA,Q;;;;;iBAC8B,C;;kGAE9BA,Q;;;;;iBACgC,C;;iGAEhCA,Q;;;;;iBAC+B,C;;iGAE/BA,Q;;;;;iBAC+B,C;;gGAE/BA,Q;;;;;iBAC8B,C;;gGAE9BA,Q;;;;;iBAC8B,C;;gGAE9BA,Q;;;;;iBAC8B,C;;gGAE9BA,Q;;;;;iBAC8B,C;;oGAE9BA,Q;;;;;iBACkC,C;;oGAElCA,Q;;;;;iBACkC,C;;oGAElCA,Q;;;;;iBACkC,C;;oGAElCA,Q;;;;;iBACkC,C;;sGAElCA,Q;;;;;iBACoC,C;;sGAEpCA,Q;;;;;iBACoC,C;;2FAEpCA,Q;;;;;iBACyB,C;;2FAEzBA,Q;;;;;iBACyB,C;;4FAEzBA,Q;;;;;iBAC0B,C;;4FAE1BA,Q;;;;;iBAC0B,C;;2FAE1BA,Q;;;;;iBACyB,C;;2FAEzBA,Q;;;;;iBACyB,C;;mGAEzBA,Q;;;;;iBACiC,C;;mGAEjCA,Q;;;;;iBACiC,C;;8FAEjCA,Q;;;;;iBAC4B,C;;6FAE5BA,Q;;;;;iBAC2B,C;;+FAE3BA,Q;;;;;iBAC6B,C;;oGAE7BA,Q;;;;;iBACkC,C;;+FAElCA,Q;;;;;iBAC6B,C;;+FAE7BA,Q;;;;;iBAC6B,C;;iGAE7BA,Q;;;;;iBAC+B,C;;;;;;;iBAGH,I;;;;;;;iBAGD,I;;;;;;;iBAGE,I;;;;;;;iBAGH,I;;;;;;;iBAGI,I", "sourcesContent": ["import { _decorator, Component, Node, game, Prefab, SpriteFrame, AudioClip } from 'cc';\nimport { AnimFactory } from './factroy/AnimFactory';\nimport { EnemyBulletFactory } from './factroy/EnemyBulletFactory';\nimport { EnemyFactory } from './factroy/EnemyFactory';\nimport { GameFactory } from './factroy/GameFactory';\nimport { GoodsFactory } from './factroy/GoodsFactory';\nimport { PlayerBulletFactory } from './factroy/PlayerBulletFactory';\nconst { ccclass, property } = _decorator;\n\n@ccclass('PersistNode')\nexport class PersistNode extends Component {\n\n    @property(Prefab)\n    playerBulletPreb: Prefab = null;    //敌机子弹预制体\n\n    @property(Prefab)\n    enemyPreb: Prefab = null;       //敌机预制体\n\n    @property(Prefab)\n    enemyBulletPreb = null;     //敌机子弹预制体\n\n    @property(Prefab)\n    animPreb = null;     //敌机爆炸动画预制体\n\n    @property(Prefab)\n    goodsPreb = null;     //物资预制体\n\n    @property(SpriteFrame)\n    bloodGoods: SpriteFrame = null;   //加血道具图片\n\n    @property(SpriteFrame)\n    lightGoods: SpriteFrame = null;   //激光道具图片\n\n    @property(SpriteFrame)\n    missileGoods: SpriteFrame = null;   //导弹道具图片\n\n    @property(SpriteFrame)\n    normalBullet: SpriteFrame = null;   //普通子弹图片\n\n    @property(SpriteFrame)\n    lightBullet: SpriteFrame = null;    //激光子弹图片\n\n    @property(SpriteFrame)\n    missileBullet: SpriteFrame = null;  //导弹子弹图片\n\n    @property(SpriteFrame)\n    enemy1: SpriteFrame = null;     //enemy1图片\n\n    @property(SpriteFrame)\n    enemy2: SpriteFrame = null;     //enemy2图片\n\n    @property(SpriteFrame)\n    enemybullet1: SpriteFrame = null;     //enemy1子弹图片\n\n    @property(SpriteFrame)\n    enemybullet2: SpriteFrame = null;     //enemy2子弹图片\n\n    @property\n    normalBulletSpeed: number = 0;   //普通子弹发射时间间隔\n\n    @property\n    lightBulletSpeed: number = 0;   //激光子弹发射时间间隔\n\n    @property\n    missileBulletSpeed: number = 0;   //导弹子弹发射时间间隔\n\n    @property\n    normalBulletMoveSpeed: number = 0;    //普通子弹移动速度\n\n    @property\n    lightBulletMoveSpeed: number = 0;    //激光子弹移动速度\n\n    @property\n    missileBulletMoveSpeed: number = 0;    //激光子弹移动速度\n\n    @property\n    enemyBullet1MoveSpeed: number = 0;      //敌机子弹1的移动速度\n\n    @property\n    enemyBullet2MoveSpeed: number = 0;      //敌机子弹1的移动速度\n\n    @property\n    enemy1MinProduceTime: number = 0;    //enemy1出现时间的间隔的下限\n\n    @property\n    enemy1MaxProduceTime: number = 0;   //enemy1出现时间的间隔的上限\n\n    @property\n    enemy2MinProduceTime: number = 0;    //enemy2出现时间的间隔的下限\n\n    @property\n    enemy2MaxProduceTime: number = 0;   //enemy2出现时间的间隔的上限\n\n    @property\n    bloodGoodsMinProduceTime: number = 0;    //加血物资出现时间的间隔的下限\n\n    @property\n    bloodGoodsMaxProduceTime: number = 0;   //加血物资出现时间的间隔的上限\n\n    @property\n    lightGoodsMinProduceTime: number = 0;    //激光物资出现时间的间隔的下限\n\n    @property\n    lightGoodsMaxProduceTime: number = 0;   //激光物资出现时间的间隔的上限\n\n    @property\n    missileGoodsMinProduceTime: number = 0;    //导弹物资出现时间的间隔的下限\n\n    @property\n    missileGoodsMaxProduceTime: number = 0;   //导弹物资出现时间的间隔的上限\n\n    @property\n    enemy1MoveSpeed: number = 0;    //敌机1的移动速度\n\n    @property\n    enemy2MoveSpeed: number = 0;    //敌机1的移动速度\n\n    @property\n    enemy1ShootSpeed: number = 0;   //敌机1发射子弹时间间隔\n\n    @property\n    enemy2ShootSpeed: number = 0;   //敌机2发射子弹时间间隔\n\n    @property\n    planeTotalBlood: number = 0;         //玩家总血量\n\n    @property\n    enemyTotalBlood: number = 0;    //敌机总血量\n\n    @property\n    enemyBullet1ReduceBlood: number = 0;        //当被敌机子弹一打中后掉多少血\n\n    @property\n    enemyBullet2ReduceBlood: number = 0;        //当被敌机子弹二打中后掉多少血\n\n    @property\n    playerNormalReduce: number = 0;         //被普通子弹击中，敌机掉多少血\n\n    @property\n    playerLightReduce: number = 0;         //被激光子弹击中，敌机掉多少血\n\n    @property\n    playerMissileReduce: number = 0;         //导弹子弹击中，敌机掉多少血\n\n    @property\n    enemyContactPlayerReduce: number = 0;  //敌机碰到玩家，玩家掉多少血\n\n    @property\n    bloodGoodsMoveSpeed: number = 0;    //加血物资移动速度\n\n    @property\n    lightGoodsMoveSpeed: number = 0;      //激光物资移动速度\n\n    @property\n    missileGoodsMoveSpeed: number = 0;      //导弹物资移动速度\n\n    @property(AudioClip)\n    bulletAudioClip: AudioClip = null;      //普通子弹声音\n\n    @property(AudioClip)\n    lightAudioClip: AudioClip = null;      //激光子弹声音\n\n    @property(AudioClip)\n    missileAudioClip: AudioClip = null;      //导弹子弹声音\n\n    @property(AudioClip)\n    boomAudioClip: AudioClip = null;      //敌机爆炸声音\n\n    @property(AudioClip)\n    gameOverAudioClip: AudioClip = null;      //游戏结束声音\n\n    playerBulletFactory: GameFactory = null;    //玩家子弹工厂\n\n    enemyFactory: GameFactory = null;       //敌机工厂\n\n    enemyBulletFactory: GameFactory = null;  //敌机子弹工厂\n\n    goodsFactory: GameFactory = null;  //敌机子弹工厂\n\n    animFactory: AnimFactory = null; //动画工厂\n\n    onLoad() {\n        game.addPersistRootNode(this.node);    //将PersistNode设置为常驻节点\n\n        this.playerBulletFactory = new PlayerBulletFactory(); //把玩家子弹工厂建在持久节点上\n\n        this.enemyFactory = new EnemyFactory(); //把敌机工厂建在持久节点上\n\n        this.enemyBulletFactory = new EnemyBulletFactory(); //把敌机子弹工厂建在持久节点上\n\n        this.goodsFactory = new GoodsFactory(); //把物资工厂建在持久节点上\n\n        this.animFactory = new AnimFactory();\n    }\n\n}\n\n"]}