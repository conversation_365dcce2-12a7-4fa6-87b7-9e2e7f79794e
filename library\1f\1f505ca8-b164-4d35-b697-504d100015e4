// you can write GLSL code directly in here

#include <builtin/uniforms/cc-global>

#define iResolution cc_screenSize
#define iTime cc_time.x
#define iTimeDelta cc_time.y
#define iFrame cc_time.z

// shadertoy template
void mainImage (out vec4 fragColor, in vec2 fragCoord) {
  // Normalized pixel coordinates (from 0 to 1)
  vec2 uv = fragCoord / iResolution.xy;
  // Time varying pixel color
  vec3 col = 0.5 + 0.5 * cos(iTime + uv.xyx + vec3(0, 2, 4));
  // Output to screen
  fragColor = vec4(col, 1.0);
}
