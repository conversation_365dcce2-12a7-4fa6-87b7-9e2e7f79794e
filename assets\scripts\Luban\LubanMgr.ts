import { _decorator, Component, Node, JsonAsset, AssetManager, resources } from "cc";
const { ccclass, property } = _decorator;
import { IMgr } from '../IMgr';
import * as cfg from '../AutoGen/Luban/schema';
@ccclass("LubanMgr")
export class LubanMgr extends IMgr {
    private _table:cfg.Tables = null;
    private static readonly LUBAN_PATH= 'Luban/';


    init(): void {
        super.init();
        resources.loadDir(LubanMgr.LUBAN_PATH, JsonAsset, (err, assets: JsonAsset[]) => {
            var dataMap = new Map<string, JsonAsset>();
            for (let asset of assets) {
                dataMap.set(asset.name, asset);
            }
            this._table = new cfg.Tables((file: string) => {
                if (dataMap.has(file)) {
                    return dataMap.get(file).json;
                }
                console.warn(`LubanMgr: File ${file} not found in loaded assets.`);
                return null;
            })
            console.log("LubanMgr initialized with tables:", this._table.TbGlobalAttr.GoldProducion);
        });
    }
}