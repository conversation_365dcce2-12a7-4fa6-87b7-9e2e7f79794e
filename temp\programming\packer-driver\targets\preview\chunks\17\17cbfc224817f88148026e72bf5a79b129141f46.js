System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, System, RegisterTypeID, _dec, _dec2, _class, _crd, ccclass, LevelEventType, LevelSystem;

  function _reportPossibleCrUseOfSystem(extras) {
    _reporterNs.report("System", "../base/System", _context.meta, extras);
  }

  function _reportPossibleCrUseOfRegisterTypeID(extras) {
    _reporterNs.report("RegisterTypeID", "../base/TypeID", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
    }, function (_unresolved_2) {
      System = _unresolved_2.System;
    }, function (_unresolved_3) {
      RegisterTypeID = _unresolved_3.RegisterTypeID;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "21f78LVBE9F67JdxikRg7Gt", "LevelSystem", undefined);

      __checkObsolete__(['_decorator', 'Node', 'Vec3']);

      ({
        ccclass
      } = _decorator);
      /**
       * Level event types
       */

      _export("LevelEventType", LevelEventType = /*#__PURE__*/function (LevelEventType) {
        LevelEventType["LEVEL_START"] = "level_start";
        LevelEventType["LEVEL_COMPLETE"] = "level_complete";
        LevelEventType["LEVEL_FAILED"] = "level_failed";
        LevelEventType["CHECKPOINT_REACHED"] = "checkpoint_reached";
        LevelEventType["ENEMY_SPAWNED"] = "enemy_spawned";
        LevelEventType["ITEM_COLLECTED"] = "item_collected";
        return LevelEventType;
      }({}));
      /**
       * Level event data
       */

      /**
       * Checkpoint data
       */

      /**
       * Level objective
       */

      /**
       * Level configuration
       */


      /**
       * LevelSystem - manages level state, objectives, checkpoints, and events
       */
      _export("LevelSystem", LevelSystem = (_dec = ccclass("LevelSystem"), _dec2 = _crd && RegisterTypeID === void 0 ? (_reportPossibleCrUseOfRegisterTypeID({
        error: Error()
      }), RegisterTypeID) : RegisterTypeID, _dec(_class = _dec2(_class = class LevelSystem extends (_crd && System === void 0 ? (_reportPossibleCrUseOfSystem({
        error: Error()
      }), System) : System) {
        constructor() {
          super(...arguments);
          this._currentLevel = null;
          this._levelStartTime = 0;
          this._levelElapsedTime = 0;
          this._isLevelActive = false;
          this._isLevelCompleted = false;
          this._isLevelFailed = false;
          // Event system
          this._eventHistory = [];
          this._eventCallbacks = new Map();
        }

        /**
         * Get the system name
         */
        getSystemName() {
          return "LevelSystem";
        }
        /**
         * Initialize the level system
         */


        onInit() {
          console.log("LevelSystem: Initializing level system"); // Initialize event callback maps

          Object.values(LevelEventType).forEach(eventType => {
            this._eventCallbacks.set(eventType, []);
          });
          console.log("LevelSystem: Initialized");
        }
        /**
         * Cleanup the level system
         */


        onUnInit() {
          console.log("LevelSystem: Cleaning up level system");
          this._currentLevel = null;
          this._levelStartTime = 0;
          this._levelElapsedTime = 0;
          this._isLevelActive = false;
          this._isLevelCompleted = false;
          this._isLevelFailed = false;
          this._eventHistory.length = 0;

          this._eventCallbacks.clear();

          console.log("LevelSystem: Cleanup complete");
        }
        /**
         * Update the level system
         */


        onUpdate(deltaTime) {
          if (!this._isLevelActive || !this._currentLevel) {
            return;
          } // Update elapsed time


          this._levelElapsedTime += deltaTime; // Check time limit

          if (this._currentLevel.timeLimit > 0 && this._levelElapsedTime >= this._currentLevel.timeLimit) {
            this.failLevel("Time limit exceeded");
            return;
          } // Check if all objectives are completed


          if (this._areAllObjectivesCompleted()) {
            this.completeLevel();
          }
        }
        /**
         * Late update - handle any post-update logic
         */


        onLateUpdate(deltaTime) {// Could be used for UI updates, statistics, etc.
        }
        /**
         * Load and start a level
         * @param levelConfig The level configuration to load
         * @returns true if the level was loaded successfully
         */


        loadLevel(levelConfig) {
          if (this._isLevelActive) {
            console.warn("LevelSystem: Cannot load level - another level is already active");
            return false;
          }

          console.log("LevelSystem: Loading level " + levelConfig.levelId); // Set current level

          this._currentLevel = this._cloneLevelConfig(levelConfig); // Reset state

          this._levelStartTime = Date.now();
          this._levelElapsedTime = 0;
          this._isLevelActive = true;
          this._isLevelCompleted = false;
          this._isLevelFailed = false;
          this._eventHistory.length = 0; // Reset checkpoints

          this._currentLevel.checkpoints.forEach(checkpoint => {
            checkpoint.isReached = false;
            checkpoint.timestamp = undefined;
          }); // Reset objectives


          this._currentLevel.objectives.forEach(objective => {
            objective.currentValue = 0;
            objective.isCompleted = false;
          }); // Emit level start event


          this._emitEvent(LevelEventType.LEVEL_START, {
            levelId: levelConfig.levelId,
            startTime: this._levelStartTime
          });

          console.log("LevelSystem: Level " + levelConfig.levelId + " loaded and started");
          return true;
        }
        /**
         * Complete the current level
         */


        completeLevel() {
          var _this$_currentLevel, _this$_currentLevel2, _this$_currentLevel3, _this$_currentLevel4;

          if (!this._isLevelActive || this._isLevelCompleted || this._isLevelFailed) {
            return;
          }

          this._isLevelCompleted = true;
          this._isLevelActive = false;
          console.log("LevelSystem: Level " + ((_this$_currentLevel = this._currentLevel) == null ? void 0 : _this$_currentLevel.levelId) + " completed");

          this._emitEvent(LevelEventType.LEVEL_COMPLETE, {
            levelId: (_this$_currentLevel2 = this._currentLevel) == null ? void 0 : _this$_currentLevel2.levelId,
            completionTime: this._levelElapsedTime,
            objectivesCompleted: (_this$_currentLevel3 = this._currentLevel) == null ? void 0 : _this$_currentLevel3.objectives.filter(obj => obj.isCompleted).length,
            totalObjectives: (_this$_currentLevel4 = this._currentLevel) == null ? void 0 : _this$_currentLevel4.objectives.length
          });
        }
        /**
         * Fail the current level
         * @param reason The reason for failure
         */


        failLevel(reason) {
          var _this$_currentLevel5, _this$_currentLevel6;

          if (!this._isLevelActive || this._isLevelCompleted || this._isLevelFailed) {
            return;
          }

          this._isLevelFailed = true;
          this._isLevelActive = false;
          console.log("LevelSystem: Level " + ((_this$_currentLevel5 = this._currentLevel) == null ? void 0 : _this$_currentLevel5.levelId) + " failed: " + reason);

          this._emitEvent(LevelEventType.LEVEL_FAILED, {
            levelId: (_this$_currentLevel6 = this._currentLevel) == null ? void 0 : _this$_currentLevel6.levelId,
            reason: reason,
            elapsedTime: this._levelElapsedTime
          });
        }
        /**
         * Reach a checkpoint
         * @param checkpointId The ID of the checkpoint to reach
         * @returns true if the checkpoint was reached successfully
         */


        reachCheckpoint(checkpointId) {
          if (!this._currentLevel) {
            return false;
          }

          var checkpoint = this._currentLevel.checkpoints.find(cp => cp.id === checkpointId);

          if (!checkpoint || checkpoint.isReached) {
            return false;
          }

          checkpoint.isReached = true;
          checkpoint.timestamp = this._levelElapsedTime;
          console.log("LevelSystem: Checkpoint " + checkpointId + " reached");

          this._emitEvent(LevelEventType.CHECKPOINT_REACHED, {
            checkpointId: checkpointId,
            position: checkpoint.position,
            timestamp: checkpoint.timestamp
          });

          return true;
        }
        /**
         * Update an objective's progress
         * @param objectiveId The ID of the objective to update
         * @param value The new value for the objective
         * @returns true if the objective was updated successfully
         */


        updateObjective(objectiveId, value) {
          if (!this._currentLevel) {
            return false;
          }

          var objective = this._currentLevel.objectives.find(obj => obj.id === objectiveId);

          if (!objective) {
            return false;
          }

          var oldValue = objective.currentValue;
          objective.currentValue = Math.max(0, value); // Check if objective is now completed

          if (!objective.isCompleted && objective.currentValue >= objective.targetValue) {
            objective.isCompleted = true;
            console.log("LevelSystem: Objective " + objectiveId + " completed");
          }

          return oldValue !== objective.currentValue;
        }
        /**
         * Add event listener for level events
         * @param eventType The type of event to listen for
         * @param callback The callback function to call when the event occurs
         */


        addEventListener(eventType, callback) {
          var callbacks = this._eventCallbacks.get(eventType);

          if (callbacks) {
            callbacks.push(callback);
          }
        }
        /**
         * Remove event listener
         * @param eventType The type of event to stop listening for
         * @param callback The callback function to remove
         */


        removeEventListener(eventType, callback) {
          var callbacks = this._eventCallbacks.get(eventType);

          if (callbacks) {
            var index = callbacks.indexOf(callback);

            if (index >= 0) {
              callbacks.splice(index, 1);
            }
          }
        }
        /**
         * Get the current level configuration
         */


        getCurrentLevel() {
          return this._currentLevel ? this._cloneLevelConfig(this._currentLevel) : null;
        }
        /**
         * Get level elapsed time
         */


        getLevelElapsedTime() {
          return this._levelElapsedTime;
        }
        /**
         * Check if level is active
         */


        isLevelActive() {
          return this._isLevelActive;
        }
        /**
         * Check if level is completed
         */


        isLevelCompleted() {
          return this._isLevelCompleted;
        }
        /**
         * Check if level is failed
         */


        isLevelFailed() {
          return this._isLevelFailed;
        }
        /**
         * Get event history
         */


        getEventHistory() {
          return [...this._eventHistory];
        }
        /**
         * Check if all objectives are completed
         */


        _areAllObjectivesCompleted() {
          if (!this._currentLevel) {
            return false;
          }

          return this._currentLevel.objectives.every(objective => objective.isCompleted);
        }
        /**
         * Emit a level event
         */


        _emitEvent(type, data) {
          var event = {
            type: type,
            timestamp: this._levelElapsedTime,
            data: data
          };

          this._eventHistory.push(event); // Call event callbacks


          var callbacks = this._eventCallbacks.get(type);

          if (callbacks) {
            callbacks.forEach(callback => {
              try {
                callback(event);
              } catch (error) {
                console.error("LevelSystem: Error in event callback for " + type + ":", error);
              }
            });
          }
        }
        /**
         * Clone level configuration to prevent external modifications
         */


        _cloneLevelConfig(config) {
          return JSON.parse(JSON.stringify(config));
        }

      }) || _class) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=17cbfc224817f88148026e72bf5a79b129141f46.js.map