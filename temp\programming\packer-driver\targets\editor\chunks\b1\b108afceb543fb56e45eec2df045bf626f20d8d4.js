System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Component, Node, Vec3, v3, UITransform, find, AudioSource, Global, PersistNode, _dec, _class, _crd, ccclass, property, Player;

  function _reportPossibleCrUseOfGameFactory(extras) {
    _reporterNs.report("GameFactory", "./factroy/GameFactory", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGlobal(extras) {
    _reporterNs.report("Global", "./Global", _context.meta, extras);
  }

  function _reportPossibleCrUseOfPersistNode(extras) {
    _reporterNs.report("PersistNode", "./PersistNode", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Component = _cc.Component;
      Node = _cc.Node;
      Vec3 = _cc.Vec3;
      v3 = _cc.v3;
      UITransform = _cc.UITransform;
      find = _cc.find;
      AudioSource = _cc.AudioSource;
    }, function (_unresolved_2) {
      Global = _unresolved_2.Global;
    }, function (_unresolved_3) {
      PersistNode = _unresolved_3.PersistNode;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "53eedZfBPxIopt6pxbBqWa8", "Player", undefined);

      __checkObsolete__(['_decorator', 'Component', 'Node', 'EventTouch', 'Vec2', 'Vec3', 'v3', 'UITransform', 'find', 'AudioSource']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("Player", Player = (_dec = ccclass('Player'), _dec(_class = class Player extends Component {
        constructor(...args) {
          super(...args);
          this.curPos = null;
          //当前player位置
          this.normalBulletTimer = 0;
          //普通子弹定时器
          this.lightBulletTimer = 0;
          //激光子弹定时器
          this.missileBulletTimer = 0;
          //导弹子弹定时器
          this.normalBulletSpeed = 0;
          //普通子弹发射时间间隔
          this.lightBulletSpeed = 0;
          //激光子弹发射时间间隔
          this.missileBulletSpeed = 0;
          //导弹子弹发射时间间隔
          this.planeBlood = 0;
          //飞机当前血量
          this.planeTotalBlood = 0;
          //飞机总血量
          this.persistNode = null;
          //得到常驻节点
          this.isShootLight = false;
          //是否发射激光
          this.isShootMissile = false;
          //是否发射导弹
          this.playerBulletFactory = null;
        }

        onLoad() {
          this.node.on(Node.EventType.TOUCH_MOVE, this.onTouchCallback, this); //开启节点触摸移动监听

          this.persistNode = find("PersistNode");
          this.playerBulletFactory = this.persistNode.getComponent(_crd && PersistNode === void 0 ? (_reportPossibleCrUseOfPersistNode({
            error: Error()
          }), PersistNode) : PersistNode).playerBulletFactory; //得到工厂对象
          //得到面板上的速度

          this.normalBulletSpeed = this.persistNode.getComponent(_crd && PersistNode === void 0 ? (_reportPossibleCrUseOfPersistNode({
            error: Error()
          }), PersistNode) : PersistNode).normalBulletSpeed;
          this.lightBulletSpeed = this.persistNode.getComponent(_crd && PersistNode === void 0 ? (_reportPossibleCrUseOfPersistNode({
            error: Error()
          }), PersistNode) : PersistNode).lightBulletSpeed;
          this.missileBulletSpeed = this.persistNode.getComponent(_crd && PersistNode === void 0 ? (_reportPossibleCrUseOfPersistNode({
            error: Error()
          }), PersistNode) : PersistNode).missileBulletSpeed;
          this.planeTotalBlood = this.persistNode.getComponent(_crd && PersistNode === void 0 ? (_reportPossibleCrUseOfPersistNode({
            error: Error()
          }), PersistNode) : PersistNode).planeTotalBlood;
          this.planeBlood = this.planeTotalBlood;
        }

        update(deltaTime) {
          //发射普通子弹
          this.normalBulletTimer += deltaTime;

          if (this.normalBulletTimer > this.normalBulletSpeed) {
            this.shootNormalBullet();
            this.normalBulletTimer = 0;
          }

          if (this.isShootLight) {
            //发射激光子弹
            this.lightBulletTimer += deltaTime;

            if (this.lightBulletTimer > this.lightBulletSpeed) {
              this.shootLightBullet();
              this.lightBulletTimer = 0;
            }
          }

          if (this.isShootMissile) {
            //发射导弹子弹
            this.missileBulletTimer += deltaTime;

            if (this.missileBulletTimer > this.missileBulletSpeed) {
              this.shootMissileBullet();
              this.missileBulletTimer = 0;
            }
          }
        }
        /**
         * 发射普通子弹
         */


        shootNormalBullet() {
          let posBegin = new Vec3(); //定义子弹开始的位置

          let normalBullet = null;
          normalBullet = this.playerBulletFactory.createProduct((_crd && Global === void 0 ? (_reportPossibleCrUseOfGlobal({
            error: Error()
          }), Global) : Global).NORMAL_BULLET); //制作子弹

          this.node.parent.addChild(normalBullet); //添加节点到画布

          this.curPos = this.node.getPosition(); //得到飞机当前位置

          posBegin.x = this.curPos.x;
          posBegin.y = this.curPos.y + 50; //设置到机头位置

          normalBullet.setPosition(posBegin);
          this.persistNode.getComponent(AudioSource).playOneShot(this.persistNode.getComponent(_crd && PersistNode === void 0 ? (_reportPossibleCrUseOfPersistNode({
            error: Error()
          }), PersistNode) : PersistNode).bulletAudioClip);
        }
        /**
         * 发射激光子弹
         */


        shootLightBullet() {
          let posBegin = new Vec3(); //定义子弹开始的位置

          let normalBullet = null; //左边激光

          normalBullet = this.playerBulletFactory.createProduct((_crd && Global === void 0 ? (_reportPossibleCrUseOfGlobal({
            error: Error()
          }), Global) : Global).LIGHT_BULLET); //制作激光子弹

          this.node.parent.addChild(normalBullet); //添加节点到画布

          this.curPos = this.node.getPosition(); //得到飞机当前位置

          posBegin.x = this.curPos.x - 50;
          posBegin.y = this.curPos.y + 50; //设置到机头位置

          normalBullet.setPosition(posBegin); //右边激光

          normalBullet = this.playerBulletFactory.createProduct((_crd && Global === void 0 ? (_reportPossibleCrUseOfGlobal({
            error: Error()
          }), Global) : Global).LIGHT_BULLET); //制作激光子弹

          this.node.parent.addChild(normalBullet); //添加节点到画布

          this.curPos = this.node.getPosition(); //得到飞机当前位置

          posBegin.x = this.curPos.x + 50;
          posBegin.y = this.curPos.y + 50; //设置到机头位置

          normalBullet.setPosition(posBegin);
          this.persistNode.getComponent(AudioSource).playOneShot(this.persistNode.getComponent(_crd && PersistNode === void 0 ? (_reportPossibleCrUseOfPersistNode({
            error: Error()
          }), PersistNode) : PersistNode).lightAudioClip);
        }
        /**
         * 发射导弹子弹
         */


        shootMissileBullet() {
          let posBegin = new Vec3(); //定义子弹开始的位置

          let normalBullet = null;
          normalBullet = this.playerBulletFactory.createProduct((_crd && Global === void 0 ? (_reportPossibleCrUseOfGlobal({
            error: Error()
          }), Global) : Global).MISSILE_BULLET); //制作导弹子弹

          this.node.parent.addChild(normalBullet); //添加节点到画布

          this.curPos = this.node.getPosition(); //得到飞机当前位置

          posBegin.x = this.curPos.x;
          posBegin.y = this.curPos.y + 50; //设置到机头位置

          normalBullet.setPosition(posBegin);
          this.persistNode.getComponent(AudioSource).playOneShot(this.persistNode.getComponent(_crd && PersistNode === void 0 ? (_reportPossibleCrUseOfPersistNode({
            error: Error()
          }), PersistNode) : PersistNode).missileAudioClip);
        }
        /**
         * 鼠标或者手指移动时候的回调
         * @param event
         */


        onTouchCallback(event) {
          let location = event.getLocation(); //得到手指鼠标位置,得到的是世界坐标

          this.curPos = this.node.parent.getComponent(UITransform).convertToNodeSpaceAR(v3(location.x, location.y, 0)); //player当前位置设置为手指或者鼠标，并转化为局部坐标

          this.node.setPosition(this.curPos);
        }

        onDisable() {
          this.node.off(Node.EventType.TOUCH_MOVE, this.onTouchCallback, this); //取消监听
        }

      }) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=b108afceb543fb56e45eec2df045bf626f20d8d4.js.map