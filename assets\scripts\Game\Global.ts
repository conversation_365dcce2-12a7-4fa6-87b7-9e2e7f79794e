import { _decorator, Component, Node, view } from 'cc';
const { ccclass, property } = _decorator;

@ccclass('Global')
export class Global extends Component {
    public static WIDTH: number = view.getDesignResolutionSize().width;  //获得设计宽度
    public static HEIGHT: number = view.getDesignResolutionSize().height;  //获得设计高度

    public static NORMAL_BULLET:string = "normal_bullet";
    public static LIGHT_BULLET:string = "light_bullet";
    public static MISSILE_BULLET:string = "missile_bullet";

    public static ENEMY_1: string = "enemy1";
    public static ENEMY_2: string = "enemy2";

    public static ENEMY_BULLET_1: string = "enemybullet1"
    public static ENEMY_BULLET_2: string = "enemybullet2"

    public static BLOOD_GOODS: string = "bloodGoods"
    public static LIGHT_GOODS: string = "lightGoods"
    public static MISSILE_GOODS: string = "missileGoods"

    public static SCORE: number = 0;
}

