{"version": 3, "sources": ["file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/gizmos/EmitterArcGizmo.ts"], "names": ["EmitterArcGizmo", "Color", "GizmoDrawer", "EmitterArc", "componentType", "drawerName", "showRadius", "showDirections", "showCenter", "showArc", "radiusColor", "GRAY", "directionColor", "RED", "centerColor", "WHITE", "arcColor", "YELLOW", "speedScale", "arrowSize", "centerSize", "drawGizmos", "emitter", "graphics", "node", "worldPos", "worldPosition", "worldRot", "worldRotation", "worldScale", "save", "translate", "x", "y", "rotate", "z", "scale", "drawCenter", "radius", "drawRadius", "arc", "drawArcIndicator", "count", "drawDirections", "restore", "drawCross", "drawCircle", "strokeColor", "lineWidth", "baseAngleRad", "angle", "Math", "PI", "arcRad", "startAngle", "endAngle", "arcRadius", "max", "segments", "floor", "i", "cos", "sin", "moveTo", "lineTo", "startX", "startY", "endX", "endY", "stroke", "baseLength", "speedFactor", "speedMultiplier", "<PERSON><PERSON><PERSON><PERSON>", "direction", "getDirection", "spawnPos", "getSpawnPosition", "drawArrow", "getPriority", "configure", "options", "undefined"], "mappings": ";;;+GAQaA,e;;;;;;;;;;;;;;;;;;;AARkBC,MAAAA,K,OAAAA,K;;AACtBC,MAAAA,W,iBAAAA,W;;AACAC,MAAAA,U,iBAAAA,U;;;;;;;;;AAET;AACA;AACA;AACA;iCACaH,e,GAAN,MAAMA,eAAN;AAAA;AAAA,sCAAsD;AAAA;AAAA;AAAA,eAEzCI,aAFyC;AAAA;AAAA;AAAA,eAGzCC,UAHyC,GAG5B,iBAH4B;AAKzD;AALyD,eAMlDC,UANkD,GAM5B,IAN4B;AAAA,eAOlDC,cAPkD,GAOxB,IAPwB;AAAA,eAQlDC,UARkD,GAQ5B,IAR4B;AAAA,eASlDC,OATkD,GAS/B,IAT+B;AAWzD;AAXyD,eAYlDC,WAZkD,GAY7BT,KAAK,CAACU,IAZuB;AAAA,eAalDC,cAbkD,GAa1BX,KAAK,CAACY,GAboB;AAAA,eAclDC,WAdkD,GAc7Bb,KAAK,CAACc,KAduB;AAAA,eAelDC,QAfkD,GAehCf,KAAK,CAACgB,MAf0B;AAiBzD;AAjByD,eAkBlDC,UAlBkD,GAkB7B,GAlB6B;AAAA,eAmBlDC,SAnBkD,GAmB9B,CAnB8B;AAAA,eAoBlDC,UApBkD,GAoB7B,CApB6B;AAAA;;AAsBlDC,QAAAA,UAAU,CAACC,OAAD,EAAsBC,QAAtB,EAA0CC,IAA1C,EAA4D;AACzE;AACA,gBAAMC,QAAQ,GAAGD,IAAI,CAACE,aAAtB;AACA,gBAAMC,QAAQ,GAAGH,IAAI,CAACI,aAAtB;AACA,gBAAMC,UAAU,GAAGL,IAAI,CAACK,UAAxB,CAJyE,CAMzE;;AACAN,UAAAA,QAAQ,CAACO,IAAT,GAPyE,CASzE;;AACAP,UAAAA,QAAQ,CAACQ,SAAT,CAAmBN,QAAQ,CAACO,CAA5B,EAA+BP,QAAQ,CAACQ,CAAxC;AACAV,UAAAA,QAAQ,CAACW,MAAT,CAAgBP,QAAQ,CAACQ,CAAzB;AACAZ,UAAAA,QAAQ,CAACa,KAAT,CAAeP,UAAU,CAACG,CAA1B,EAA6BH,UAAU,CAACI,CAAxC,EAZyE,CAczE;;AACA,cAAI,KAAKzB,UAAT,EAAqB;AACjB,iBAAK6B,UAAL,CAAgBd,QAAhB;AACH,WAjBwE,CAmBzE;;;AACA,cAAI,KAAKjB,UAAL,IAAmBgB,OAAO,CAACgB,MAAR,GAAiB,CAAxC,EAA2C;AACvC,iBAAKC,UAAL,CAAgBhB,QAAhB,EAA0BD,OAAO,CAACgB,MAAlC;AACH,WAtBwE,CAwBzE;;;AACA,cAAI,KAAK7B,OAAL,IAAgBa,OAAO,CAACkB,GAAR,GAAc,CAAlC,EAAqC;AACjC,iBAAKC,gBAAL,CAAsBlB,QAAtB,EAAgCD,OAAhC;AACH,WA3BwE,CA6BzE;;;AACA,cAAI,KAAKf,cAAL,IAAuBe,OAAO,CAACoB,KAAR,GAAgB,CAA3C,EAA8C;AAC1C,iBAAKC,cAAL,CAAoBpB,QAApB,EAA8BD,OAA9B;AACH,WAhCwE,CAkCzE;;;AACAC,UAAAA,QAAQ,CAACqB,OAAT;AACH;;AAEOP,QAAAA,UAAU,CAACd,QAAD,EAA2B;AACzC,eAAKsB,SAAL,CAAetB,QAAf,EAAyB,CAAzB,EAA4B,CAA5B,EAA+B,KAAKH,UAApC,EAAgD,KAAKN,WAArD;AACH;;AAEOyB,QAAAA,UAAU,CAAChB,QAAD,EAAqBe,MAArB,EAA2C;AACzD,eAAKQ,UAAL,CAAgBvB,QAAhB,EAA0B,CAA1B,EAA6B,CAA7B,EAAgCe,MAAhC,EAAwC,KAAK5B,WAA7C,EAA0D,KAA1D;AACH;;AAEO+B,QAAAA,gBAAgB,CAAClB,QAAD,EAAqBD,OAArB,EAAgD;AACpE,cAAIA,OAAO,CAACkB,GAAR,IAAe,CAAnB,EAAsB;AAEtBjB,UAAAA,QAAQ,CAACwB,WAAT,GAAuB,KAAK/B,QAA5B;AACAO,UAAAA,QAAQ,CAACyB,SAAT,GAAqB,CAArB,CAJoE,CAMpE;;AACA,gBAAMC,YAAY,GAAG,CAAC3B,OAAO,CAAC4B,KAAR,GAAgB,EAAjB,IAAuBC,IAAI,CAACC,EAA5B,GAAiC,GAAtD,CAPoE,CAOT;;AAC3D,gBAAMC,MAAM,GAAG/B,OAAO,CAACkB,GAAR,GAAcW,IAAI,CAACC,EAAnB,GAAwB,GAAvC;AAEA,gBAAME,UAAU,GAAGL,YAAY,GAAGI,MAAM,GAAG,CAA3C;AACA,gBAAME,QAAQ,GAAGN,YAAY,GAAGI,MAAM,GAAG,CAAzC,CAXoE,CAapE;;AACA,gBAAMG,SAAS,GAAGL,IAAI,CAACM,GAAL,CAASnC,OAAO,CAACgB,MAAjB,EAAyB,EAAzB,CAAlB,CAdoE,CAcpB;AAEhD;;AACA,gBAAMoB,QAAQ,GAAGP,IAAI,CAACM,GAAL,CAAS,CAAT,EAAYN,IAAI,CAACQ,KAAL,CAAWrC,OAAO,CAACkB,GAAR,GAAc,CAAzB,CAAZ,CAAjB,CAjBoE,CAiBT;;AAC3D,eAAK,IAAIoB,CAAC,GAAG,CAAb,EAAgBA,CAAC,IAAIF,QAArB,EAA+BE,CAAC,EAAhC,EAAoC;AAChC,kBAAMV,KAAK,GAAGI,UAAU,GAAG,CAACC,QAAQ,GAAGD,UAAZ,KAA2BM,CAAC,GAAGF,QAA/B,CAA3B;AACA,kBAAM1B,CAAC,GAAGmB,IAAI,CAACU,GAAL,CAASX,KAAT,IAAkBM,SAA5B;AACA,kBAAMvB,CAAC,GAAGkB,IAAI,CAACW,GAAL,CAASZ,KAAT,IAAkBM,SAA5B;;AAEA,gBAAII,CAAC,KAAK,CAAV,EAAa;AACTrC,cAAAA,QAAQ,CAACwC,MAAT,CAAgB/B,CAAhB,EAAmBC,CAAnB;AACH,aAFD,MAEO;AACHV,cAAAA,QAAQ,CAACyC,MAAT,CAAgBhC,CAAhB,EAAmBC,CAAnB;AACH;AACJ,WA5BmE,CA8BpE;;;AACA,gBAAMgC,MAAM,GAAGd,IAAI,CAACU,GAAL,CAASP,UAAT,IAAuBE,SAAtC;AACA,gBAAMU,MAAM,GAAGf,IAAI,CAACW,GAAL,CAASR,UAAT,IAAuBE,SAAtC;AACA,gBAAMW,IAAI,GAAGhB,IAAI,CAACU,GAAL,CAASN,QAAT,IAAqBC,SAAlC;AACA,gBAAMY,IAAI,GAAGjB,IAAI,CAACW,GAAL,CAASP,QAAT,IAAqBC,SAAlC,CAlCoE,CAoCpE;;AACAjC,UAAAA,QAAQ,CAACwC,MAAT,CAAgB,CAAhB,EAAmB,CAAnB;AACAxC,UAAAA,QAAQ,CAACyC,MAAT,CAAgBC,MAAhB,EAAwBC,MAAxB;AACA3C,UAAAA,QAAQ,CAACwC,MAAT,CAAgB,CAAhB,EAAmB,CAAnB;AACAxC,UAAAA,QAAQ,CAACyC,MAAT,CAAgBG,IAAhB,EAAsBC,IAAtB;AAEA7C,UAAAA,QAAQ,CAAC8C,MAAT;AACH;;AAEO1B,QAAAA,cAAc,CAACpB,QAAD,EAAqBD,OAArB,EAAgD;AAClE,gBAAMgD,UAAU,GAAG,EAAnB;AACA,gBAAMC,WAAW,GAAGjD,OAAO,CAACkD,eAAR,IAA2B,CAA/C;AACA,gBAAMC,WAAW,GAAGtB,IAAI,CAACM,GAAL,CAASa,UAAT,EAAqBA,UAAU,GAAGC,WAAb,GAA2B,KAAKrD,UAArD,CAApB;;AAEA,eAAK,IAAI0C,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGtC,OAAO,CAACoB,KAA5B,EAAmCkB,CAAC,EAApC,EAAwC;AACpC,kBAAMc,SAAS,GAAGpD,OAAO,CAACqD,YAAR,CAAqBf,CAArB,CAAlB;AACA,kBAAMgB,QAAQ,GAAGtD,OAAO,CAACuD,gBAAR,CAAyBjB,CAAzB,CAAjB,CAFoC,CAIpC;;AACA,kBAAMK,MAAM,GAAGW,QAAQ,CAAC5C,CAAxB;AACA,kBAAMkC,MAAM,GAAGU,QAAQ,CAAC3C,CAAxB,CANoC,CAQpC;;AACA,kBAAMkC,IAAI,GAAGF,MAAM,GAAGS,SAAS,CAAC1C,CAAV,GAAcyC,WAApC;AACA,kBAAML,IAAI,GAAGF,MAAM,GAAGQ,SAAS,CAACzC,CAAV,GAAcwC,WAApC,CAVoC,CAYpC;;AACA,iBAAKK,SAAL,CAAevD,QAAf,EAAyB0C,MAAzB,EAAiCC,MAAjC,EAAyCC,IAAzC,EAA+CC,IAA/C,EAAqD,KAAKxD,cAA1D,EAA0E,KAAKO,SAA/E;AACH;AACJ;;AAEM4D,QAAAA,WAAW,GAAW;AACzB,iBAAO,EAAP,CADyB,CACd;AACd;AAED;AACJ;AACA;;;AACWC,QAAAA,SAAS,CAACC,OAAD,EAYP;AACL,cAAIA,OAAO,CAAC3E,UAAR,KAAuB4E,SAA3B,EAAsC,KAAK5E,UAAL,GAAkB2E,OAAO,CAAC3E,UAA1B;AACtC,cAAI2E,OAAO,CAAC1E,cAAR,KAA2B2E,SAA/B,EAA0C,KAAK3E,cAAL,GAAsB0E,OAAO,CAAC1E,cAA9B;AAC1C,cAAI0E,OAAO,CAACzE,UAAR,KAAuB0E,SAA3B,EAAsC,KAAK1E,UAAL,GAAkByE,OAAO,CAACzE,UAA1B;AACtC,cAAIyE,OAAO,CAACxE,OAAR,KAAoByE,SAAxB,EAAmC,KAAKzE,OAAL,GAAewE,OAAO,CAACxE,OAAvB;AACnC,cAAIwE,OAAO,CAACvE,WAAR,KAAwBwE,SAA5B,EAAuC,KAAKxE,WAAL,GAAmBuE,OAAO,CAACvE,WAA3B;AACvC,cAAIuE,OAAO,CAACrE,cAAR,KAA2BsE,SAA/B,EAA0C,KAAKtE,cAAL,GAAsBqE,OAAO,CAACrE,cAA9B;AAC1C,cAAIqE,OAAO,CAACnE,WAAR,KAAwBoE,SAA5B,EAAuC,KAAKpE,WAAL,GAAmBmE,OAAO,CAACnE,WAA3B;AACvC,cAAImE,OAAO,CAACjE,QAAR,KAAqBkE,SAAzB,EAAoC,KAAKlE,QAAL,GAAgBiE,OAAO,CAACjE,QAAxB;AACpC,cAAIiE,OAAO,CAAC/D,UAAR,KAAuBgE,SAA3B,EAAsC,KAAKhE,UAAL,GAAkB+D,OAAO,CAAC/D,UAA1B;AACtC,cAAI+D,OAAO,CAAC9D,SAAR,KAAsB+D,SAA1B,EAAqC,KAAK/D,SAAL,GAAiB8D,OAAO,CAAC9D,SAAzB;AACrC,cAAI8D,OAAO,CAAC7D,UAAR,KAAuB8D,SAA3B,EAAsC,KAAK9D,UAAL,GAAkB6D,OAAO,CAAC7D,UAA1B;AACzC;;AAtKwD,O", "sourcesContent": ["import { _decorator, Graphics, Color, Node } from 'cc';\nimport { GizmoDrawer } from './GizmoDrawer';\nimport { EmitterArc } from '../world/bullet/EmitterArc';\n\n/**\n * Gizmo drawer for EmitterArc components\n * Draws visual debugging information for arc-based bullet emitters\n */\nexport class EmitterArcGizmo extends GizmoDrawer<EmitterArc> {\n    \n    public readonly componentType = EmitterArc;\n    public readonly drawerName = \"EmitterArcGizmo\";\n    \n    // Gizmo display options\n    public showRadius: boolean = true;\n    public showDirections: boolean = true;\n    public showCenter: boolean = true;\n    public showArc: boolean = true;\n    \n    // Colors\n    public radiusColor: Color = Color.GRAY;\n    public directionColor: Color = Color.RED;\n    public centerColor: Color = Color.WHITE;\n    public arcColor: Color = Color.YELLOW;\n    \n    // Display settings\n    public speedScale: number = 1.0;\n    public arrowSize: number = 8;\n    public centerSize: number = 8;\n    \n    public drawGizmos(emitter: EmitterArc, graphics: Graphics, node: Node): void {\n        // Set transform to match the emitter's node\n        const worldPos = node.worldPosition;\n        const worldRot = node.worldRotation;\n        const worldScale = node.worldScale;\n        \n        // Save current transform\n        graphics.save();\n        \n        // Apply node transform\n        graphics.translate(worldPos.x, worldPos.y);\n        graphics.rotate(worldRot.z);\n        graphics.scale(worldScale.x, worldScale.y);\n        \n        // Draw center point\n        if (this.showCenter) {\n            this.drawCenter(graphics);\n        }\n        \n        // Draw radius circle\n        if (this.showRadius && emitter.radius > 0) {\n            this.drawRadius(graphics, emitter.radius);\n        }\n        \n        // Draw arc indicator\n        if (this.showArc && emitter.arc > 0) {\n            this.drawArcIndicator(graphics, emitter);\n        }\n        \n        // Draw direction arrows\n        if (this.showDirections && emitter.count > 0) {\n            this.drawDirections(graphics, emitter);\n        }\n        \n        // Restore transform\n        graphics.restore();\n    }\n    \n    private drawCenter(graphics: Graphics): void {\n        this.drawCross(graphics, 0, 0, this.centerSize, this.centerColor);\n    }\n    \n    private drawRadius(graphics: Graphics, radius: number): void {\n        this.drawCircle(graphics, 0, 0, radius, this.radiusColor, false);\n    }\n    \n    private drawArcIndicator(graphics: Graphics, emitter: EmitterArc): void {\n        if (emitter.arc <= 0) return;\n        \n        graphics.strokeColor = this.arcColor;\n        graphics.lineWidth = 2;\n        \n        // Convert angle and arc to radians\n        const baseAngleRad = (emitter.angle + 90) * Math.PI / 180; // +90 because 0 degrees is up in Cocos\n        const arcRad = emitter.arc * Math.PI / 180;\n        \n        const startAngle = baseAngleRad - arcRad / 2;\n        const endAngle = baseAngleRad + arcRad / 2;\n        \n        // Draw arc at radius distance\n        const arcRadius = Math.max(emitter.radius, 30); // Minimum radius for visibility\n        \n        // Draw arc\n        const segments = Math.max(8, Math.floor(emitter.arc / 5)); // More segments for larger arcs\n        for (let i = 0; i <= segments; i++) {\n            const angle = startAngle + (endAngle - startAngle) * (i / segments);\n            const x = Math.cos(angle) * arcRadius;\n            const y = Math.sin(angle) * arcRadius;\n            \n            if (i === 0) {\n                graphics.moveTo(x, y);\n            } else {\n                graphics.lineTo(x, y);\n            }\n        }\n        \n        // Draw arc end indicators\n        const startX = Math.cos(startAngle) * arcRadius;\n        const startY = Math.sin(startAngle) * arcRadius;\n        const endX = Math.cos(endAngle) * arcRadius;\n        const endY = Math.sin(endAngle) * arcRadius;\n        \n        // Lines from center to arc ends\n        graphics.moveTo(0, 0);\n        graphics.lineTo(startX, startY);\n        graphics.moveTo(0, 0);\n        graphics.lineTo(endX, endY);\n        \n        graphics.stroke();\n    }\n    \n    private drawDirections(graphics: Graphics, emitter: EmitterArc): void {\n        const baseLength = 30;\n        const speedFactor = emitter.speedMultiplier || 1;\n        const arrowLength = Math.max(baseLength, baseLength * speedFactor * this.speedScale);\n        \n        for (let i = 0; i < emitter.count; i++) {\n            const direction = emitter.getDirection(i);\n            const spawnPos = emitter.getSpawnPosition(i);\n            \n            // Start position (at spawn position)\n            const startX = spawnPos.x;\n            const startY = spawnPos.y;\n            \n            // End position (direction from spawn position)\n            const endX = startX + direction.x * arrowLength;\n            const endY = startY + direction.y * arrowLength;\n            \n            // Draw arrow\n            this.drawArrow(graphics, startX, startY, endX, endY, this.directionColor, this.arrowSize);\n        }\n    }\n    \n    public getPriority(): number {\n        return 10; // Draw emitter gizmos with medium priority\n    }\n    \n    /**\n     * Configure display options\n     */\n    public configure(options: {\n        showRadius?: boolean;\n        showDirections?: boolean;\n        showCenter?: boolean;\n        showArc?: boolean;\n        radiusColor?: Color;\n        directionColor?: Color;\n        centerColor?: Color;\n        arcColor?: Color;\n        speedScale?: number;\n        arrowSize?: number;\n        centerSize?: number;\n    }): void {\n        if (options.showRadius !== undefined) this.showRadius = options.showRadius;\n        if (options.showDirections !== undefined) this.showDirections = options.showDirections;\n        if (options.showCenter !== undefined) this.showCenter = options.showCenter;\n        if (options.showArc !== undefined) this.showArc = options.showArc;\n        if (options.radiusColor !== undefined) this.radiusColor = options.radiusColor;\n        if (options.directionColor !== undefined) this.directionColor = options.directionColor;\n        if (options.centerColor !== undefined) this.centerColor = options.centerColor;\n        if (options.arcColor !== undefined) this.arcColor = options.arcColor;\n        if (options.speedScale !== undefined) this.speedScale = options.speedScale;\n        if (options.arrowSize !== undefined) this.arrowSize = options.arrowSize;\n        if (options.centerSize !== undefined) this.centerSize = options.centerSize;\n    }\n}\n"]}