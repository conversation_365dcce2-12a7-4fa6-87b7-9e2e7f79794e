{"version": 3, "sources": ["file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/gizmos/EmitterArcGizmo.ts"], "names": ["EmitterArcGizmo", "Color", "GizmoDrawer", "EmitterArc", "componentType", "drawerName", "showRadius", "showDirections", "showCenter", "showArc", "radiusColor", "GRAY", "directionColor", "RED", "centerColor", "WHITE", "arcColor", "YELLOW", "speedScale", "arrowSize", "centerSize", "drawGizmos", "emitter", "graphics", "node", "worldPos", "worldPosition", "worldX", "x", "worldY", "y", "drawCenter", "radius", "drawRadius", "arc", "drawArcIndicator", "count", "drawDirections", "drawCross", "drawCircle", "strokeColor", "lineWidth", "baseAngleRad", "angle", "Math", "PI", "arcRad", "startAngle", "endAngle", "arcRadius", "max", "segments", "floor", "i", "cos", "sin", "moveTo", "lineTo", "startX", "startY", "endX", "endY", "stroke", "baseLength", "speedFactor", "speedMultiplier", "<PERSON><PERSON><PERSON><PERSON>", "direction", "getDirection", "spawnPos", "getSpawnPosition", "drawArrow", "getPriority", "configure", "options", "undefined"], "mappings": ";;;+GAQaA,e;;;;;;;;;;;;;;;;;;;AARkBC,MAAAA,K,OAAAA,K;;AACtBC,MAAAA,W,iBAAAA,W;;AACAC,MAAAA,U,iBAAAA,U;;;;;;;;;AAET;AACA;AACA;AACA;iCACaH,e,GAAN,MAAMA,eAAN;AAAA;AAAA,sCAAsD;AAAA;AAAA;AAAA,eAEzCI,aAFyC;AAAA;AAAA;AAAA,eAGzCC,UAHyC,GAG5B,iBAH4B;AAKzD;AALyD,eAMlDC,UANkD,GAM5B,IAN4B;AAAA,eAOlDC,cAPkD,GAOxB,IAPwB;AAAA,eAQlDC,UARkD,GAQ5B,IAR4B;AAAA,eASlDC,OATkD,GAS/B,IAT+B;AAWzD;AAXyD,eAYlDC,WAZkD,GAY7BT,KAAK,CAACU,IAZuB;AAAA,eAalDC,cAbkD,GAa1BX,KAAK,CAACY,GAboB;AAAA,eAclDC,WAdkD,GAc7Bb,KAAK,CAACc,KAduB;AAAA,eAelDC,QAfkD,GAehCf,KAAK,CAACgB,MAf0B;AAiBzD;AAjByD,eAkBlDC,UAlBkD,GAkB7B,GAlB6B;AAAA,eAmBlDC,SAnBkD,GAmB9B,CAnB8B;AAAA,eAoBlDC,UApBkD,GAoB7B,CApB6B;AAAA;;AAsBlDC,QAAAA,UAAU,CAACC,OAAD,EAAsBC,QAAtB,EAA0CC,IAA1C,EAA4D;AACzE;AACA,gBAAMC,QAAQ,GAAGD,IAAI,CAACE,aAAtB;AACA,gBAAMC,MAAM,GAAGF,QAAQ,CAACG,CAAxB;AACA,gBAAMC,MAAM,GAAGJ,QAAQ,CAACK,CAAxB,CAJyE,CAMzE;;AACA,cAAI,KAAKtB,UAAT,EAAqB;AACjB,iBAAKuB,UAAL,CAAgBR,QAAhB,EAA0BI,MAA1B,EAAkCE,MAAlC;AACH,WATwE,CAWzE;;;AACA,cAAI,KAAKvB,UAAL,IAAmBgB,OAAO,CAACU,MAAR,GAAiB,CAAxC,EAA2C;AACvC,iBAAKC,UAAL,CAAgBV,QAAhB,EAA0BI,MAA1B,EAAkCE,MAAlC,EAA0CP,OAAO,CAACU,MAAlD;AACH,WAdwE,CAgBzE;;;AACA,cAAI,KAAKvB,OAAL,IAAgBa,OAAO,CAACY,GAAR,GAAc,CAAlC,EAAqC;AACjC,iBAAKC,gBAAL,CAAsBZ,QAAtB,EAAgCD,OAAhC,EAAyCK,MAAzC,EAAiDE,MAAjD;AACH,WAnBwE,CAqBzE;;;AACA,cAAI,KAAKtB,cAAL,IAAuBe,OAAO,CAACc,KAAR,GAAgB,CAA3C,EAA8C;AAC1C,iBAAKC,cAAL,CAAoBd,QAApB,EAA8BD,OAA9B,EAAuCK,MAAvC,EAA+CE,MAA/C;AACH;AACJ;;AAEOE,QAAAA,UAAU,CAACR,QAAD,EAAqBI,MAArB,EAAqCE,MAArC,EAA2D;AACzE,eAAKS,SAAL,CAAef,QAAf,EAAyBI,MAAzB,EAAiCE,MAAjC,EAAyC,KAAKT,UAA9C,EAA0D,KAAKN,WAA/D;AACH;;AAEOmB,QAAAA,UAAU,CAACV,QAAD,EAAqBI,MAArB,EAAqCE,MAArC,EAAqDG,MAArD,EAA2E;AACzF,eAAKO,UAAL,CAAgBhB,QAAhB,EAA0BI,MAA1B,EAAkCE,MAAlC,EAA0CG,MAA1C,EAAkD,KAAKtB,WAAvD,EAAoE,KAApE;AACH;;AAEOyB,QAAAA,gBAAgB,CAACZ,QAAD,EAAqBD,OAArB,EAA0CK,MAA1C,EAA0DE,MAA1D,EAAgF;AACpG,cAAIP,OAAO,CAACY,GAAR,IAAe,CAAnB,EAAsB;AAEtBX,UAAAA,QAAQ,CAACiB,WAAT,GAAuB,KAAKxB,QAA5B;AACAO,UAAAA,QAAQ,CAACkB,SAAT,GAAqB,CAArB,CAJoG,CAMpG;;AACA,gBAAMC,YAAY,GAAG,CAACpB,OAAO,CAACqB,KAAR,GAAgB,EAAjB,IAAuBC,IAAI,CAACC,EAA5B,GAAiC,GAAtD,CAPoG,CAOzC;;AAC3D,gBAAMC,MAAM,GAAGxB,OAAO,CAACY,GAAR,GAAcU,IAAI,CAACC,EAAnB,GAAwB,GAAvC;AAEA,gBAAME,UAAU,GAAGL,YAAY,GAAGI,MAAM,GAAG,CAA3C;AACA,gBAAME,QAAQ,GAAGN,YAAY,GAAGI,MAAM,GAAG,CAAzC,CAXoG,CAapG;;AACA,gBAAMG,SAAS,GAAGL,IAAI,CAACM,GAAL,CAAS5B,OAAO,CAACU,MAAjB,EAAyB,EAAzB,CAAlB,CAdoG,CAcpD;AAEhD;;AACA,gBAAMmB,QAAQ,GAAGP,IAAI,CAACM,GAAL,CAAS,CAAT,EAAYN,IAAI,CAACQ,KAAL,CAAW9B,OAAO,CAACY,GAAR,GAAc,CAAzB,CAAZ,CAAjB,CAjBoG,CAiBzC;;AAC3D,eAAK,IAAImB,CAAC,GAAG,CAAb,EAAgBA,CAAC,IAAIF,QAArB,EAA+BE,CAAC,EAAhC,EAAoC;AAChC,kBAAMV,KAAK,GAAGI,UAAU,GAAG,CAACC,QAAQ,GAAGD,UAAZ,KAA2BM,CAAC,GAAGF,QAA/B,CAA3B;AACA,kBAAMvB,CAAC,GAAGD,MAAM,GAAGiB,IAAI,CAACU,GAAL,CAASX,KAAT,IAAkBM,SAArC;AACA,kBAAMnB,CAAC,GAAGD,MAAM,GAAGe,IAAI,CAACW,GAAL,CAASZ,KAAT,IAAkBM,SAArC;;AAEA,gBAAII,CAAC,KAAK,CAAV,EAAa;AACT9B,cAAAA,QAAQ,CAACiC,MAAT,CAAgB5B,CAAhB,EAAmBE,CAAnB;AACH,aAFD,MAEO;AACHP,cAAAA,QAAQ,CAACkC,MAAT,CAAgB7B,CAAhB,EAAmBE,CAAnB;AACH;AACJ,WA5BmG,CA8BpG;;;AACA,gBAAM4B,MAAM,GAAG/B,MAAM,GAAGiB,IAAI,CAACU,GAAL,CAASP,UAAT,IAAuBE,SAA/C;AACA,gBAAMU,MAAM,GAAG9B,MAAM,GAAGe,IAAI,CAACW,GAAL,CAASR,UAAT,IAAuBE,SAA/C;AACA,gBAAMW,IAAI,GAAGjC,MAAM,GAAGiB,IAAI,CAACU,GAAL,CAASN,QAAT,IAAqBC,SAA3C;AACA,gBAAMY,IAAI,GAAGhC,MAAM,GAAGe,IAAI,CAACW,GAAL,CAASP,QAAT,IAAqBC,SAA3C,CAlCoG,CAoCpG;;AACA1B,UAAAA,QAAQ,CAACiC,MAAT,CAAgB7B,MAAhB,EAAwBE,MAAxB;AACAN,UAAAA,QAAQ,CAACkC,MAAT,CAAgBC,MAAhB,EAAwBC,MAAxB;AACApC,UAAAA,QAAQ,CAACiC,MAAT,CAAgB7B,MAAhB,EAAwBE,MAAxB;AACAN,UAAAA,QAAQ,CAACkC,MAAT,CAAgBG,IAAhB,EAAsBC,IAAtB;AAEAtC,UAAAA,QAAQ,CAACuC,MAAT;AACH;;AAEOzB,QAAAA,cAAc,CAACd,QAAD,EAAqBD,OAArB,EAA0CK,MAA1C,EAA0DE,MAA1D,EAAgF;AAClG,gBAAMkC,UAAU,GAAG,EAAnB;AACA,gBAAMC,WAAW,GAAG1C,OAAO,CAAC2C,eAAR,IAA2B,CAA/C;AACA,gBAAMC,WAAW,GAAGtB,IAAI,CAACM,GAAL,CAASa,UAAT,EAAqBA,UAAU,GAAGC,WAAb,GAA2B,KAAK9C,UAArD,CAApB;;AAEA,eAAK,IAAImC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG/B,OAAO,CAACc,KAA5B,EAAmCiB,CAAC,EAApC,EAAwC;AACpC,kBAAMc,SAAS,GAAG7C,OAAO,CAAC8C,YAAR,CAAqBf,CAArB,CAAlB;AACA,kBAAMgB,QAAQ,GAAG/C,OAAO,CAACgD,gBAAR,CAAyBjB,CAAzB,CAAjB,CAFoC,CAIpC;;AACA,kBAAMK,MAAM,GAAG/B,MAAM,GAAG0C,QAAQ,CAACzC,CAAjC;AACA,kBAAM+B,MAAM,GAAG9B,MAAM,GAAGwC,QAAQ,CAACvC,CAAjC,CANoC,CAQpC;;AACA,kBAAM8B,IAAI,GAAGF,MAAM,GAAGS,SAAS,CAACvC,CAAV,GAAcsC,WAApC;AACA,kBAAML,IAAI,GAAGF,MAAM,GAAGQ,SAAS,CAACrC,CAAV,GAAcoC,WAApC,CAVoC,CAYpC;;AACA,iBAAKK,SAAL,CAAehD,QAAf,EAAyBmC,MAAzB,EAAiCC,MAAjC,EAAyCC,IAAzC,EAA+CC,IAA/C,EAAqD,KAAKjD,cAA1D,EAA0E,KAAKO,SAA/E;AACH;AACJ;;AAEMqD,QAAAA,WAAW,GAAW;AACzB,iBAAO,EAAP,CADyB,CACd;AACd;AAED;AACJ;AACA;;;AACWC,QAAAA,SAAS,CAACC,OAAD,EAYP;AACL,cAAIA,OAAO,CAACpE,UAAR,KAAuBqE,SAA3B,EAAsC,KAAKrE,UAAL,GAAkBoE,OAAO,CAACpE,UAA1B;AACtC,cAAIoE,OAAO,CAACnE,cAAR,KAA2BoE,SAA/B,EAA0C,KAAKpE,cAAL,GAAsBmE,OAAO,CAACnE,cAA9B;AAC1C,cAAImE,OAAO,CAAClE,UAAR,KAAuBmE,SAA3B,EAAsC,KAAKnE,UAAL,GAAkBkE,OAAO,CAAClE,UAA1B;AACtC,cAAIkE,OAAO,CAACjE,OAAR,KAAoBkE,SAAxB,EAAmC,KAAKlE,OAAL,GAAeiE,OAAO,CAACjE,OAAvB;AACnC,cAAIiE,OAAO,CAAChE,WAAR,KAAwBiE,SAA5B,EAAuC,KAAKjE,WAAL,GAAmBgE,OAAO,CAAChE,WAA3B;AACvC,cAAIgE,OAAO,CAAC9D,cAAR,KAA2B+D,SAA/B,EAA0C,KAAK/D,cAAL,GAAsB8D,OAAO,CAAC9D,cAA9B;AAC1C,cAAI8D,OAAO,CAAC5D,WAAR,KAAwB6D,SAA5B,EAAuC,KAAK7D,WAAL,GAAmB4D,OAAO,CAAC5D,WAA3B;AACvC,cAAI4D,OAAO,CAAC1D,QAAR,KAAqB2D,SAAzB,EAAoC,KAAK3D,QAAL,GAAgB0D,OAAO,CAAC1D,QAAxB;AACpC,cAAI0D,OAAO,CAACxD,UAAR,KAAuByD,SAA3B,EAAsC,KAAKzD,UAAL,GAAkBwD,OAAO,CAACxD,UAA1B;AACtC,cAAIwD,OAAO,CAACvD,SAAR,KAAsBwD,SAA1B,EAAqC,KAAKxD,SAAL,GAAiBuD,OAAO,CAACvD,SAAzB;AACrC,cAAIuD,OAAO,CAACtD,UAAR,KAAuBuD,SAA3B,EAAsC,KAAKvD,UAAL,GAAkBsD,OAAO,CAACtD,UAA1B;AACzC;;AA3JwD,O", "sourcesContent": ["import { _decorator, Graphics, Color, Node } from 'cc';\nimport { GizmoDrawer } from './GizmoDrawer';\nimport { EmitterArc } from '../world/bullet/EmitterArc';\n\n/**\n * Gizmo drawer for EmitterArc components\n * Draws visual debugging information for arc-based bullet emitters\n */\nexport class EmitterArcGizmo extends GizmoDrawer<EmitterArc> {\n    \n    public readonly componentType = EmitterArc;\n    public readonly drawerName = \"EmitterArcGizmo\";\n    \n    // Gizmo display options\n    public showRadius: boolean = true;\n    public showDirections: boolean = true;\n    public showCenter: boolean = true;\n    public showArc: boolean = true;\n    \n    // Colors\n    public radiusColor: Color = Color.GRAY;\n    public directionColor: Color = Color.RED;\n    public centerColor: Color = Color.WHITE;\n    public arcColor: Color = Color.YELLOW;\n    \n    // Display settings\n    public speedScale: number = 1.0;\n    public arrowSize: number = 8;\n    public centerSize: number = 8;\n    \n    public drawGizmos(emitter: EmitterArc, graphics: Graphics, node: Node): void {\n        // Get world position for drawing\n        const worldPos = node.worldPosition;\n        const worldX = worldPos.x;\n        const worldY = worldPos.y;\n\n        // Draw center point\n        if (this.showCenter) {\n            this.drawCenter(graphics, worldX, worldY);\n        }\n\n        // Draw radius circle\n        if (this.showRadius && emitter.radius > 0) {\n            this.drawRadius(graphics, worldX, worldY, emitter.radius);\n        }\n\n        // Draw arc indicator\n        if (this.showArc && emitter.arc > 0) {\n            this.drawArcIndicator(graphics, emitter, worldX, worldY);\n        }\n\n        // Draw direction arrows\n        if (this.showDirections && emitter.count > 0) {\n            this.drawDirections(graphics, emitter, worldX, worldY);\n        }\n    }\n    \n    private drawCenter(graphics: Graphics, worldX: number, worldY: number): void {\n        this.drawCross(graphics, worldX, worldY, this.centerSize, this.centerColor);\n    }\n\n    private drawRadius(graphics: Graphics, worldX: number, worldY: number, radius: number): void {\n        this.drawCircle(graphics, worldX, worldY, radius, this.radiusColor, false);\n    }\n    \n    private drawArcIndicator(graphics: Graphics, emitter: EmitterArc, worldX: number, worldY: number): void {\n        if (emitter.arc <= 0) return;\n\n        graphics.strokeColor = this.arcColor;\n        graphics.lineWidth = 2;\n\n        // Convert angle and arc to radians\n        const baseAngleRad = (emitter.angle + 90) * Math.PI / 180; // +90 because 0 degrees is up in Cocos\n        const arcRad = emitter.arc * Math.PI / 180;\n\n        const startAngle = baseAngleRad - arcRad / 2;\n        const endAngle = baseAngleRad + arcRad / 2;\n\n        // Draw arc at radius distance\n        const arcRadius = Math.max(emitter.radius, 30); // Minimum radius for visibility\n\n        // Draw arc\n        const segments = Math.max(8, Math.floor(emitter.arc / 5)); // More segments for larger arcs\n        for (let i = 0; i <= segments; i++) {\n            const angle = startAngle + (endAngle - startAngle) * (i / segments);\n            const x = worldX + Math.cos(angle) * arcRadius;\n            const y = worldY + Math.sin(angle) * arcRadius;\n\n            if (i === 0) {\n                graphics.moveTo(x, y);\n            } else {\n                graphics.lineTo(x, y);\n            }\n        }\n\n        // Draw arc end indicators\n        const startX = worldX + Math.cos(startAngle) * arcRadius;\n        const startY = worldY + Math.sin(startAngle) * arcRadius;\n        const endX = worldX + Math.cos(endAngle) * arcRadius;\n        const endY = worldY + Math.sin(endAngle) * arcRadius;\n\n        // Lines from center to arc ends\n        graphics.moveTo(worldX, worldY);\n        graphics.lineTo(startX, startY);\n        graphics.moveTo(worldX, worldY);\n        graphics.lineTo(endX, endY);\n\n        graphics.stroke();\n    }\n    \n    private drawDirections(graphics: Graphics, emitter: EmitterArc, worldX: number, worldY: number): void {\n        const baseLength = 30;\n        const speedFactor = emitter.speedMultiplier || 1;\n        const arrowLength = Math.max(baseLength, baseLength * speedFactor * this.speedScale);\n\n        for (let i = 0; i < emitter.count; i++) {\n            const direction = emitter.getDirection(i);\n            const spawnPos = emitter.getSpawnPosition(i);\n\n            // Start position (at spawn position relative to world position)\n            const startX = worldX + spawnPos.x;\n            const startY = worldY + spawnPos.y;\n\n            // End position (direction from spawn position)\n            const endX = startX + direction.x * arrowLength;\n            const endY = startY + direction.y * arrowLength;\n\n            // Draw arrow\n            this.drawArrow(graphics, startX, startY, endX, endY, this.directionColor, this.arrowSize);\n        }\n    }\n    \n    public getPriority(): number {\n        return 10; // Draw emitter gizmos with medium priority\n    }\n    \n    /**\n     * Configure display options\n     */\n    public configure(options: {\n        showRadius?: boolean;\n        showDirections?: boolean;\n        showCenter?: boolean;\n        showArc?: boolean;\n        radiusColor?: Color;\n        directionColor?: Color;\n        centerColor?: Color;\n        arcColor?: Color;\n        speedScale?: number;\n        arrowSize?: number;\n        centerSize?: number;\n    }): void {\n        if (options.showRadius !== undefined) this.showRadius = options.showRadius;\n        if (options.showDirections !== undefined) this.showDirections = options.showDirections;\n        if (options.showCenter !== undefined) this.showCenter = options.showCenter;\n        if (options.showArc !== undefined) this.showArc = options.showArc;\n        if (options.radiusColor !== undefined) this.radiusColor = options.radiusColor;\n        if (options.directionColor !== undefined) this.directionColor = options.directionColor;\n        if (options.centerColor !== undefined) this.centerColor = options.centerColor;\n        if (options.arcColor !== undefined) this.arcColor = options.arcColor;\n        if (options.speedScale !== undefined) this.speedScale = options.speedScale;\n        if (options.arrowSize !== undefined) this.arrowSize = options.arrowSize;\n        if (options.centerSize !== undefined) this.centerSize = options.centerSize;\n    }\n}\n"]}