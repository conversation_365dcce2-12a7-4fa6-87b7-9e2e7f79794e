{"version": 3, "sources": ["file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Luban/LubanMgr.ts"], "names": ["_decorator", "JsonAsset", "resources", "IMgr", "cfg", "ccclass", "property", "LubanMgr", "_table", "init", "loadDir", "LUBAN_PATH", "err", "assets", "dataMap", "Map", "asset", "set", "name", "Tables", "file", "has", "get", "json", "console", "warn", "log", "TbGlobalAttr", "GoldProducion"], "mappings": ";;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAA6BC,MAAAA,S,OAAAA,S;AAAyBC,MAAAA,S,OAAAA,S;;AAEtDC,MAAAA,I,iBAAAA,I;;AACGC,MAAAA,G;;;;;;;;;OAFN;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBN,U;;0BAIjBO,Q,WADZF,OAAO,CAAC,UAAD,C,2BAAR,MACaE,QADb;AAAA;AAAA,wBACmC;AAAA;AAAA;AAAA,eACvBC,MADuB,GACH,IADG;AAAA;;AAK/BC,QAAAA,IAAI,GAAS;AACT,gBAAMA,IAAN;AACAP,UAAAA,SAAS,CAACQ,OAAV,CAAkBH,QAAQ,CAACI,UAA3B,EAAuCV,SAAvC,EAAkD,CAACW,GAAD,EAAMC,MAAN,KAA8B;AAC5E,gBAAIC,OAAO,GAAG,IAAIC,GAAJ,EAAd;;AACA,iBAAK,IAAIC,KAAT,IAAkBH,MAAlB,EAA0B;AACtBC,cAAAA,OAAO,CAACG,GAAR,CAAYD,KAAK,CAACE,IAAlB,EAAwBF,KAAxB;AACH;;AACD,iBAAKR,MAAL,GAAc,IAAIJ,GAAG,CAACe,MAAR,CAAgBC,IAAD,IAAkB;AAC3C,kBAAIN,OAAO,CAACO,GAAR,CAAYD,IAAZ,CAAJ,EAAuB;AACnB,uBAAON,OAAO,CAACQ,GAAR,CAAYF,IAAZ,EAAkBG,IAAzB;AACH;;AACDC,cAAAA,OAAO,CAACC,IAAR,qBAA+BL,IAA/B;AACA,qBAAO,IAAP;AACH,aANa,CAAd;AAOAI,YAAAA,OAAO,CAACE,GAAR,CAAY,mCAAZ,EAAiD,KAAKlB,MAAL,CAAYmB,YAAZ,CAAyBC,aAA1E;AACH,WAbD;AAcH;;AArB8B,O,UAEPjB,U,GAAY,Q", "sourcesContent": ["import { _decorator, Component, Node, JsonAsset, AssetManager, resources } from \"cc\";\nconst { ccclass, property } = _decorator;\nimport { IMgr } from '../IMgr';\nimport * as cfg from '../AutoGen/Luban/schema';\n@ccclass(\"LubanMgr\")\nexport class LubanMgr extends IMgr {\n    private _table:cfg.Tables = null;\n    private static readonly LUBAN_PATH= 'Luban/';\n\n\n    init(): void {\n        super.init();\n        resources.loadDir(LubanMgr.LUBAN_PATH, JsonAsset, (err, assets: JsonAsset[]) => {\n            var dataMap = new Map<string, JsonAsset>();\n            for (let asset of assets) {\n                dataMap.set(asset.name, asset);\n            }\n            this._table = new cfg.Tables((file: string) => {\n                if (dataMap.has(file)) {\n                    return dataMap.get(file).json;\n                }\n                console.warn(`LubanMgr: File ${file} not found in loaded assets.`);\n                return null;\n            })\n            console.log(\"LubanMgr initialized with tables:\", this._table.TbGlobalAttr.GoldProducion);\n        });\n    }\n}"]}