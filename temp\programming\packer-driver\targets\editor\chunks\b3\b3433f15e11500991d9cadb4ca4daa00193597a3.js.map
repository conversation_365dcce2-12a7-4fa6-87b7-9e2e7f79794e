{"version": 3, "sources": ["file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ResUpdate/ResUpdate.ts"], "names": ["ResLoadingTips", "_decorator", "Component", "director", "LabelComponent", "ProgressBar", "ccenum", "WECHAT", "ccclass", "property", "Progress", "Context", "warnCustom", "console", "warn", "res", "indexOf", "groupCustom", "group", "GameLogLevel", "ResUpdate", "type", "start", "SetLogLevel", "THIS", "cc", "loader", "onProgress", "OnLoadProgress", "bind", "log", "loadScene", "logLevel", "ERROR", "WARN", "info", "INFO", "LOG", "debug", "DEBUG", "trace", "TRACE", "completedCount", "totalCount", "item", "progress", "toFixed", "id", "node", "per<PERSON><PERSON><PERSON>", "string", "<PERSON><PERSON><PERSON><PERSON>", "loadingBar", "onLoad", "onDestroy", "update", "deltaTime"], "mappings": ";;;gJAIMA,c;;;;;;;;;;;;;AAJGC,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAA+BC,MAAAA,Q,OAAAA,Q;AAAUC,MAAAA,c,OAAAA,c;AAAgBC,MAAAA,W,OAAAA,W;AAA8CC,MAAAA,M,OAAAA,M;;AACnHC,MAAAA,M,UAAAA,M;;;;;;;;;OACH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBR,U;AAExBD,MAAAA,c,GAAN,MAAMA,cAAN,CAAqB;AAAA;AAAA,eACZU,QADY;AAAA,eAEZC,OAFY;AAAA;;AAAA,O;;AAMrB,UAAIJ,MAAJ,EAAY;AACJK,QAAAA,UADI,GACSC,OAAO,CAACC,IADjB;;AAERD,QAAAA,OAAO,CAACC,IAAR,GAAc,UAASC,GAAT,EAAa;AACvB,cAAG,OAAOA,GAAP,IAAa,QAAb,IAAuBA,GAAG,CAACC,OAAJ,CAAY,gBAAZ,IAA8B,CAAC,CAAzD,EAA4D;AACxD;AACH,WAFD,MAEK;AACDJ,YAAAA,UAAU,CAACG,GAAD,CAAV;AACH;AAGJ,SARD;;AASIE,QAAAA,WAXI,GAWUJ,OAAO,CAACK,KAXlB;;AAYRL,QAAAA,OAAO,CAACK,KAAR,GAAe,UAASH,GAAT,EAAa;AACxB,cAAG,OAAOA,GAAP,IAAa,QAAb,IAAuBA,GAAG,CAACC,OAAJ,CAAY,YAAZ,IAA0B,CAAC,CAArD,EAAwD;AACpD;AACH,WAFD,MAEK;AACDC,YAAAA,WAAW,CAACF,GAAD,CAAX;AACH;AACJ,SAND;AAOH;;AAEII,MAAAA,Y,0BAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;eAAAA,Y;QAAAA,Y;;AAQLb,MAAAA,MAAM,CAACa,YAAD,CAAN;;2BAGaC,S,WADZZ,OAAO,CAAC,WAAD,C,UAKHC,QAAQ,CAACL,cAAD,C,UAERK,QAAQ,CAACL,cAAD,C,UAERK,QAAQ,CAACL,cAAD,C,UAERK,QAAQ,CAACJ,WAAD,C,UAGRI,QAAQ,CAAC;AAACY,QAAAA,IAAI,EAACF;AAAN,OAAD,C,4BAdb,MACaC,SADb,SAC+BlB,SAD/B,CACyC;AAAA;AAAA;;AACrC;AACA;AAFqC;;AAAA;;AAAA;;AAAA;;AAYrC;AAZqC;AAAA;;AAgBrCoB,QAAAA,KAAK,GAAG;AACJ,eAAKC,WAAL,GADI,CAEJ;;AACA,cAAIC,IAAI,GAAG,IAAX;AACAC,UAAAA,EAAE,CAACC,MAAH,CAAUC,UAAV,GAAuB,KAAKC,cAAL,CAAoBC,IAApB,CAAyB,IAAzB,CAAvB;AACAhB,UAAAA,OAAO,CAACiB,GAAR,CAAY,4BAAZ;AACA3B,UAAAA,QAAQ,CAAC4B,SAAT,CAAmB,MAAnB,EAA2B,MAAM;AAC7BlB,YAAAA,OAAO,CAACiB,GAAR,CAAY,yBAAZ;AACH,WAFD,EANI,CASJ;AACI;AACA;AACJ;AACH;;AACDP,QAAAA,WAAW,GACX;AACI,kBAAO,KAAKS,QAAZ;AAEI,iBAAKb,YAAY,CAACc,KAAlB;AACIpB,cAAAA,OAAO,CAACC,IAAR,GAAe,MAAI,CAAE,CAArB;;AACJ,iBAAKK,YAAY,CAACe,IAAlB;AACIrB,cAAAA,OAAO,CAACsB,IAAR,GAAe,MAAI,CAAE,CAArB;;AACJ,iBAAKhB,YAAY,CAACiB,IAAlB;AACIvB,cAAAA,OAAO,CAACiB,GAAR,GAAc,MAAI,CAAE,CAApB;;AACJ,iBAAKX,YAAY,CAACkB,GAAlB;AACIxB,cAAAA,OAAO,CAACyB,KAAR,GAAgB,MAAI,CAAE,CAAtB;;AACJ,iBAAKnB,YAAY,CAACoB,KAAlB;AACI1B,cAAAA,OAAO,CAAC2B,KAAR,GAAgB,MAAI,CAAE,CAAtB;;AACJ,iBAAKrB,YAAY,CAACsB,KAAlB;AAZJ;AAcH;;AAEDb,QAAAA,cAAc,CAACc,cAAD,EAAwBC,UAAxB,EAA2CC,IAA3C,EACd;AACI,cAAIC,QAAQ,GAAIH,cAAc,GAAGC,UAAjC;AACA9B,UAAAA,OAAO,CAACiB,GAAR,CAAY,sBAAZ,EAAoCY,cAApC,EAAoDC,UAApD,EAAgE,CAACE,QAAQ,GAAC,GAAV,EAAeC,OAAf,CAAuB,CAAvB,CAAhE,EAA2FF,IAAI,CAACG,EAAhG;;AACA,cAAI,KAAKC,IAAL,IAAa,IAAjB,EAAuB;AACnB;AACH;;AACD,eAAKC,QAAL,CAAcC,MAAd,GAAuB,CAACL,QAAQ,GAAG,GAAZ,EAAiBC,OAAjB,CAAyB,CAAzB,IAA8B,GAArD;AACA,eAAKK,UAAL,CAAgBD,MAAhB,GAAyB,YAAYR,cAAZ,GAA6B,GAA7B,GAAmCC,UAAnC,GAAgD,GAAzE;AACA,eAAKS,UAAL,CAAgBP,QAAhB,GAA2BA,QAA3B;AACH;;AAEDQ,QAAAA,MAAM,GAAG,CACR;;AACDC,QAAAA,SAAS,GACT;AACI7B,UAAAA,EAAE,CAACC,MAAH,CAAUC,UAAV,GAAuB,IAAvB;AACH;;AAED4B,QAAAA,MAAM,CAACC,SAAD,EAAoB,CACtB;AACA;AACA;AACA;AACA;AACH;;AAzEoC,O;;;;;iBAKC,I;;;;;;;iBAEF,I;;;;;;;iBAEI,I;;;;;;;iBAEN,I;;;;;;;iBAGfrC,YAAY,CAACsB,K", "sourcesContent": ["import { _decorator, Component,Prefab, Node,loader, director, LabelComponent, ProgressBar, JsonAsset, math, Enum, CCString, ccenum, resources } from \"cc\";\r\nimport { WECHAT } from \"cc/env\";\r\nconst { ccclass, property } = _decorator;\r\n\r\nclass ResLoadingTips {\r\n\t public Progress : number;\r\n\t public Context : string;\r\n}\r\n\r\n\r\nif (WECHAT) {\r\n    var warnCustom = console.warn;\r\n    console.warn =function(res){\r\n        if(typeof res ==\"string\"&&res.indexOf(\"文件路径在真机上可能无法读取\")>-1 ){\r\n            return;\r\n        }else{\r\n            warnCustom(res)\r\n        }\r\n    \r\n    \r\n    }\r\n    var groupCustom = console.group;\r\n    console.group =function(res){\r\n        if(typeof res ==\"string\"&&res.indexOf(\"读取文件/文件夹警告\")>-1 ){\r\n            return;\r\n        }else{\r\n            groupCustom(res)\r\n        }\r\n    }\r\n}\r\n\r\nenum GameLogLevel {\r\n    TRACE = 0,\r\n    DEBUG = 1,\r\n    LOG = 2,\r\n    INFO = 3,\r\n    WARN = 4,\r\n    ERROR = 5,\r\n}\r\nccenum(GameLogLevel)\r\n\r\n@ccclass(\"ResUpdate\") \r\nexport class ResUpdate extends Component {\r\n    /* class member could be defined like this */\r\n    // dummy = '';\r\n\r\n    @property(LabelComponent)\r\n    private countLabel : LabelComponent = null;\r\n    @property(LabelComponent)\r\n    private perLabel : LabelComponent = null;\r\n    @property(LabelComponent)\r\n    private versionLabel : LabelComponent = null;\r\n    @property(ProgressBar)\r\n    private loadingBar : ProgressBar= null;\r\n    //private isMainSceneLoaded = false;\r\n    @property({type:GameLogLevel})\r\n    private logLevel = GameLogLevel.TRACE;\r\n\r\n    start() {\r\n        this.SetLogLevel();\r\n        // Your initialization goes here.\r\n        let THIS = this;\r\n        cc.loader.onProgress = this.OnLoadProgress.bind(this)\r\n        console.log('ybgg start load main scene')\r\n        director.loadScene(\"Main\", () => {\r\n            console.log(\"load main scene success\")\r\n        })\r\n        //director.preloadScene(\"MainScene\", this.OnLoadProgress.bind(this), () => {\r\n            //this.isMainSceneLoaded = true;\r\n            //console.log(\"ybgg load main scene success\")\r\n        //})\r\n    }\r\n    SetLogLevel()\r\n    {\r\n        switch(this.logLevel)\r\n        {\r\n            case GameLogLevel.ERROR :\r\n                console.warn = ()=>{};\r\n            case GameLogLevel.WARN :\r\n                console.info = ()=>{};\r\n            case GameLogLevel.INFO :\r\n                console.log = ()=>{};\r\n            case GameLogLevel.LOG :\r\n                console.debug = ()=>{};\r\n            case GameLogLevel.DEBUG :\r\n                console.trace = ()=>{};\r\n            case GameLogLevel.TRACE :\r\n        }\r\n    }\r\n\r\n    OnLoadProgress(completedCount:number, totalCount:number, item)\r\n    {\r\n        let progress = (completedCount / totalCount)\r\n        console.log('ybgg load main scene', completedCount, totalCount, (progress*100).toFixed(2), item.id)\r\n        if (this.node == null) {\r\n            return;\r\n        }\r\n        this.perLabel.string = (progress * 100).toFixed(2) + \"%\"\r\n        this.countLabel.string = '加载中...(' + completedCount + '/' + totalCount + ')'\r\n        this.loadingBar.progress = progress\r\n    }\r\n\r\n    onLoad() {\r\n    }\r\n    onDestroy()\r\n    {\r\n        cc.loader.onProgress = null;\r\n    }\r\n\r\n    update(deltaTime: number) {\r\n        //if (this.isMainSceneLoaded) //&& window.GameInstance && window.GameInstance.getLogin().isLogined)\r\n        //{\r\n        //    director.loadScene(\"MainScene\")\r\n        //}\r\n        // Your update function goes here.\r\n    }\r\n}\r\n"]}