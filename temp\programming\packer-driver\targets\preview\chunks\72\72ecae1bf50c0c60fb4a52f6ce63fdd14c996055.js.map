{"version": 3, "sources": ["file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/AutoGen/Luban/schema.ts"], "names": ["GlobalAttr", "ResWeapon", "ResWhiteList", "TbWeapon", "TbGlobalAttr", "Tables", "TargetScanStrategy", "vector2", "constructor", "_json_", "x", "y", "undefined", "Error", "resolve", "tables", "builtin", "vector3", "z", "vector4", "w", "GoldProducion", "id", "name", "openid", "password", "status", "privilege", "_dataMap", "_dataList", "Map", "_json2_", "_v", "push", "set", "getDataMap", "getDataList", "get", "key", "data", "_data", "length", "getData", "_TbWeapon", "_TbGlobalAttr", "loader"], "mappings": ";;;iBA0GaA,U,EAwBAC,S,EAsBAC,Y,EA8CAC,Q,EA+BAC,Y,EA2BAC,M;;;;;;;;;;;;;;;;;;;;;AA/Pb;AACA;AACA;AACA;AACA;AACA;AACA;oCAIYC,kB,0BAAAA,kB;AAAAA,QAAAA,kB,CAAAA,kB;AAAAA,QAAAA,kB,CAAAA,kB;eAAAA,kB;;;;AAiBL,cAAMC,OAAN,CAAc;AAEjBC,UAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,iBAOhBC,CAPgB;AAAA,iBAQhBC,CARgB;;AACrB,gBAAIF,MAAM,CAACC,CAAP,KAAaE,SAAjB,EAA4B;AAAE,oBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACjD,iBAAKH,CAAL,GAASD,MAAM,CAACC,CAAhB;;AACA,gBAAID,MAAM,CAACE,CAAP,KAAaC,SAAjB,EAA4B;AAAE,oBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACjD,iBAAKF,CAAL,GAASF,MAAM,CAACE,CAAhB;AACH;;AAKDG,UAAAA,OAAO,CAACC,MAAD,EAAgB,CAGtB;;AAfgB;;;SADJC,O,uBAAAA,O;;;AAuBV,cAAMC,OAAN,CAAc;AAEjBT,UAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,iBAShBC,CATgB;AAAA,iBAUhBC,CAVgB;AAAA,iBAWhBO,CAXgB;;AACrB,gBAAIT,MAAM,CAACC,CAAP,KAAaE,SAAjB,EAA4B;AAAE,oBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACjD,iBAAKH,CAAL,GAASD,MAAM,CAACC,CAAhB;;AACA,gBAAID,MAAM,CAACE,CAAP,KAAaC,SAAjB,EAA4B;AAAE,oBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACjD,iBAAKF,CAAL,GAASF,MAAM,CAACE,CAAhB;;AACA,gBAAIF,MAAM,CAACS,CAAP,KAAaN,SAAjB,EAA4B;AAAE,oBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACjD,iBAAKK,CAAL,GAAST,MAAM,CAACS,CAAhB;AACH;;AAMDJ,UAAAA,OAAO,CAACC,MAAD,EAAgB,CAItB;;AAnBgB;;;SADJC,O,uBAAAA,O;;;AA2BV,cAAMG,OAAN,CAAc;AAEjBX,UAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,iBAWhBC,CAXgB;AAAA,iBAYhBC,CAZgB;AAAA,iBAahBO,CAbgB;AAAA,iBAchBE,CAdgB;;AACrB,gBAAIX,MAAM,CAACC,CAAP,KAAaE,SAAjB,EAA4B;AAAE,oBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACjD,iBAAKH,CAAL,GAASD,MAAM,CAACC,CAAhB;;AACA,gBAAID,MAAM,CAACE,CAAP,KAAaC,SAAjB,EAA4B;AAAE,oBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACjD,iBAAKF,CAAL,GAASF,MAAM,CAACE,CAAhB;;AACA,gBAAIF,MAAM,CAACS,CAAP,KAAaN,SAAjB,EAA4B;AAAE,oBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACjD,iBAAKK,CAAL,GAAST,MAAM,CAACS,CAAhB;;AACA,gBAAIT,MAAM,CAACW,CAAP,KAAaR,SAAjB,EAA4B;AAAE,oBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACjD,iBAAKO,CAAL,GAASX,MAAM,CAACW,CAAhB;AACH;;AAODN,UAAAA,OAAO,CAACC,MAAD,EAAgB,CAKtB;;AAvBgB;;;SADJC,O,uBAAAA,O;;4BA+BJhB,U,GAAN,MAAMA,UAAN,CAAiB;AAEpBQ,QAAAA,WAAW,CAACC,MAAD,EAAc;AAKzB;AACJ;AACA;AAP6B,eAQhBY,aARgB;;AACrB,cAAIZ,MAAM,CAACY,aAAP,KAAyBT,SAA7B,EAAwC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC7D,eAAKQ,aAAL,GAAqBZ,MAAM,CAACY,aAA5B;AACH;;AAODP,QAAAA,OAAO,CAACC,MAAD,EAAgB,CAEtB;;AAdmB,O;AAqBxB;AACA;AACA;;;2BACad,S,GAAN,MAAMA,SAAN,CAAgB;AAEnBO,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAOhBa,EAPgB;AAAA,eAQhBC,IARgB;;AACrB,cAAId,MAAM,CAACa,EAAP,KAAcV,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKS,EAAL,GAAUb,MAAM,CAACa,EAAjB;;AACA,cAAIb,MAAM,CAACc,IAAP,KAAgBX,SAApB,EAA+B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACpD,eAAKU,IAAL,GAAYd,MAAM,CAACc,IAAnB;AACH;;AAKDT,QAAAA,OAAO,CAACC,MAAD,EAAgB,CAGtB;;AAfkB,O;;8BAsBVb,Y,GAAN,MAAMA,YAAN,CAAmB;AAEtBM,QAAAA,WAAW,CAACC,MAAD,EAAc;AAWzB;AACJ;AACA;AAb6B,eAchBe,MAdgB;;AAezB;AACJ;AACA;AAjB6B,eAkBhBC,QAlBgB;;AAmBzB;AACJ;AACA;AArB6B,eAsBhBC,MAtBgB;;AAuBzB;AACJ;AACA;AAzB6B,eA0BhBC,SA1BgB;;AACrB,cAAIlB,MAAM,CAACe,MAAP,KAAkBZ,SAAtB,EAAiC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACtD,eAAKW,MAAL,GAAcf,MAAM,CAACe,MAArB;;AACA,cAAIf,MAAM,CAACgB,QAAP,KAAoBb,SAAxB,EAAmC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACxD,eAAKY,QAAL,GAAgBhB,MAAM,CAACgB,QAAvB;;AACA,cAAIhB,MAAM,CAACiB,MAAP,KAAkBd,SAAtB,EAAiC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACtD,eAAKa,MAAL,GAAcjB,MAAM,CAACiB,MAArB;;AACA,cAAIjB,MAAM,CAACkB,SAAP,KAAqBf,SAAzB,EAAoC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACzD,eAAKc,SAAL,GAAiBlB,MAAM,CAACkB,SAAxB;AACH;;AAmBDb,QAAAA,OAAO,CAACC,MAAD,EAAgB,CAKtB;;AAnCqB,O;AA2C1B;AACA;AACA;;;0BACaZ,Q,GAAN,MAAMA,QAAN,CAAe;AAGlBK,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAFjBmB,QAEiB;AAAA,eADjBC,SACiB;AACrB,eAAKD,QAAL,GAAgB,IAAIE,GAAJ,EAAhB;AACA,eAAKD,SAAL,GAAiB,EAAjB;;AACA,eAAI,IAAIE,OAAR,IAAmBtB,MAAnB,EAA2B;AACvB,gBAAIuB,EAAa,SAAjB;;AACAA,YAAAA,EAAE,GAAG,IAAI/B,SAAJ,CAAc8B,OAAd,CAAL;;AACA,iBAAKF,SAAL,CAAeI,IAAf,CAAoBD,EAApB;;AACA,iBAAKJ,QAAL,CAAcM,GAAd,CAAkBF,EAAE,CAACV,EAArB,EAAyBU,EAAzB;AACH;AACJ;;AAEDG,QAAAA,UAAU,GAA2B;AAAE,iBAAO,KAAKP,QAAZ;AAAuB;;AAC9DQ,QAAAA,WAAW,GAAgB;AAAE,iBAAO,KAAKP,SAAZ;AAAwB;;AAErDQ,QAAAA,GAAG,CAACC,GAAD,EAAqC;AAAE,iBAAO,KAAKV,QAAL,CAAcS,GAAd,CAAkBC,GAAlB,CAAP;AAAgC;;AAE1ExB,QAAAA,OAAO,CAACC,MAAD,EAAgB;AACnB,eAAI,IAAKwB,IAAT,IAAiB,KAAKV,SAAtB,EACA;AACIU,YAAAA,IAAI,CAACzB,OAAL,CAAaC,MAAb;AACH;AACJ;;AAxBiB,O;;8BA+BTX,Y,GAAN,MAAMA,YAAN,CAAmB;AAGtBI,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eADjB+B,KACiB;AACrB,cAAI/B,MAAM,CAACgC,MAAP,IAAiB,CAArB,EAAwB,MAAM,IAAI5B,KAAJ,CAAU,+BAAV,CAAN;AACxB,eAAK2B,KAAL,GAAa,IAAIxC,UAAJ,CAAeS,MAAM,CAAC,CAAD,CAArB,CAAb;AACH;;AAEDiC,QAAAA,OAAO,GAAe;AAAE,iBAAO,KAAKF,KAAZ;AAAoB;AAE5C;AACJ;AACA;;;AACsB,YAAbnB,aAAa,GAAW;AAAE,iBAAO,KAAKmB,KAAL,CAAWnB,aAAlB;AAAkC;;AAEjEP,QAAAA,OAAO,CAACC,MAAD,EACP;AACI,eAAKyB,KAAL,CAAW1B,OAAX,CAAmBC,MAAnB;AACH;;AAlBqB,O;;wBA2BbV,M,GAAN,MAAMA,MAAN,CAAa;AAEhB;AACJ;AACA;AACgB,YAARF,QAAQ,GAAc;AAAE,iBAAO,KAAKwC,SAAZ;AAAuB;;AAEnC,YAAZvC,YAAY,GAAkB;AAAE,iBAAO,KAAKwC,aAAZ;AAA2B;;AAE/DpC,QAAAA,WAAW,CAACqC,MAAD,EAAqB;AAAA,eARxBF,SAQwB;AAAA,eAHxBC,aAGwB;AAC5B,eAAKD,SAAL,GAAiB,IAAIxC,QAAJ,CAAa0C,MAAM,CAAC,UAAD,CAAnB,CAAjB;AACA,eAAKD,aAAL,GAAqB,IAAIxC,YAAJ,CAAiByC,MAAM,CAAC,cAAD,CAAvB,CAArB;;AAEA,eAAKF,SAAL,CAAe7B,OAAf,CAAuB,IAAvB;;AACA,eAAK8B,aAAL,CAAmB9B,OAAnB,CAA2B,IAA3B;AACH;;AAfe,O", "sourcesContent": ["\n//------------------------------------------------------------------------------\n// <auto-generated>\n//     This code was generated by a tool.\n//     Changes to this file may cause incorrect behavior and will be lost if\n//     the code is regenerated.\n// </auto-generated>\n//------------------------------------------------------------------------------\n\n\n \nexport enum TargetScanStrategy {\n    /**\n     * 更新\n     */\n    Refresh = 0,\n    /**\n     * 保持\n     */\n    Keep = 1,\n}\n\n \n\n\n\n\nexport namespace builtin {\nexport class vector2 {\n\n    constructor(_json_: any) {\n        if (_json_.x === undefined) { throw new Error() }\n        this.x = _json_.x\n        if (_json_.y === undefined) { throw new Error() }\n        this.y = _json_.y\n    }\n\n    readonly x: number\n    readonly y: number\n\n    resolve(tables:Tables) {\n        \n        \n    }\n}\n\n}\n\n\nexport namespace builtin {\nexport class vector3 {\n\n    constructor(_json_: any) {\n        if (_json_.x === undefined) { throw new Error() }\n        this.x = _json_.x\n        if (_json_.y === undefined) { throw new Error() }\n        this.y = _json_.y\n        if (_json_.z === undefined) { throw new Error() }\n        this.z = _json_.z\n    }\n\n    readonly x: number\n    readonly y: number\n    readonly z: number\n\n    resolve(tables:Tables) {\n        \n        \n        \n    }\n}\n\n}\n\n\nexport namespace builtin {\nexport class vector4 {\n\n    constructor(_json_: any) {\n        if (_json_.x === undefined) { throw new Error() }\n        this.x = _json_.x\n        if (_json_.y === undefined) { throw new Error() }\n        this.y = _json_.y\n        if (_json_.z === undefined) { throw new Error() }\n        this.z = _json_.z\n        if (_json_.w === undefined) { throw new Error() }\n        this.w = _json_.w\n    }\n\n    readonly x: number\n    readonly y: number\n    readonly z: number\n    readonly w: number\n\n    resolve(tables:Tables) {\n        \n        \n        \n        \n    }\n}\n\n}\n\n\n\nexport class GlobalAttr {\n\n    constructor(_json_: any) {\n        if (_json_.GoldProducion === undefined) { throw new Error() }\n        this.GoldProducion = _json_.GoldProducion\n    }\n\n    /**\n     * 每回合发放的金币\n     */\n    readonly GoldProducion: number\n\n    resolve(tables:Tables) {\n        \n    }\n}\n\n\n\n\n\n/**\n * 卡牌\n */\nexport class ResWeapon {\n\n    constructor(_json_: any) {\n        if (_json_.id === undefined) { throw new Error() }\n        this.id = _json_.id\n        if (_json_.name === undefined) { throw new Error() }\n        this.name = _json_.name\n    }\n\n    readonly id: number\n    readonly name: string\n\n    resolve(tables:Tables) {\n        \n        \n    }\n}\n\n\n\n\n\nexport class ResWhiteList {\n\n    constructor(_json_: any) {\n        if (_json_.openid === undefined) { throw new Error() }\n        this.openid = _json_.openid\n        if (_json_.password === undefined) { throw new Error() }\n        this.password = _json_.password\n        if (_json_.status === undefined) { throw new Error() }\n        this.status = _json_.status\n        if (_json_.privilege === undefined) { throw new Error() }\n        this.privilege = _json_.privilege\n    }\n\n    /**\n     * 第一行默认是主键\n     */\n    readonly openid: string\n    /**\n     * 密码的MD5\n     */\n    readonly password: string\n    /**\n     * account  status: normal/disable\n     */\n    readonly status: number\n    /**\n     * 可以访问的内容\n     */\n    readonly privilege: number\n\n    resolve(tables:Tables) {\n        \n        \n        \n        \n    }\n}\n\n\n\n\n\n\n/**\n * 武器\n */\nexport class TbWeapon {\n    private _dataMap: Map<number, ResWeapon>\n    private _dataList: ResWeapon[]\n    constructor(_json_: any) {\n        this._dataMap = new Map<number, ResWeapon>()\n        this._dataList = []\n        for(var _json2_ of _json_) {\n            let _v: ResWeapon\n            _v = new ResWeapon(_json2_)\n            this._dataList.push(_v)\n            this._dataMap.set(_v.id, _v)\n        }\n    }\n\n    getDataMap(): Map<number, ResWeapon> { return this._dataMap; }\n    getDataList(): ResWeapon[] { return this._dataList; }\n\n    get(key: number): ResWeapon | undefined { return this._dataMap.get(key); }\n\n    resolve(tables:Tables) {\n        for(let  data of this._dataList)\n        {\n            data.resolve(tables)\n        }\n    }\n\n}\n\n\n\n\nexport class TbGlobalAttr {\n\n    private _data: GlobalAttr\n    constructor(_json_: any) {\n        if (_json_.length != 1) throw new Error('table mode=one, but size != 1')\n        this._data = new GlobalAttr(_json_[0])\n    }\n\n    getData(): GlobalAttr { return this._data; }\n\n    /**\n     * 每回合发放的金币\n     */\n    get  GoldProducion(): number { return this._data.GoldProducion; }\n\n    resolve(tables:Tables)\n    {\n        this._data.resolve(tables)\n    }\n    \n}\n\n\n\n\ntype JsonLoader = (file: string) => any\n\nexport class Tables {\n    private _TbWeapon: TbWeapon\n    /**\n     * 武器\n     */\n    get TbWeapon(): TbWeapon  { return this._TbWeapon;}\n    private _TbGlobalAttr: TbGlobalAttr\n    get TbGlobalAttr(): TbGlobalAttr  { return this._TbGlobalAttr;}\n\n    constructor(loader: JsonLoader) {\n        this._TbWeapon = new TbWeapon(loader('tbweapon'))\n        this._TbGlobalAttr = new TbGlobalAttr(loader('tbglobalattr'))\n\n        this._TbWeapon.resolve(this)\n        this._TbGlobalAttr.resolve(this)\n    }\n}\n\n"]}