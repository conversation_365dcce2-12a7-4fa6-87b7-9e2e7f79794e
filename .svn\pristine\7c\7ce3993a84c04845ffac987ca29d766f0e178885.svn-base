import { _decorator, Component, Node, Vec3, Color } from 'cc';
import { Spawner, Wave } from '../core/LevelData';
import { SpawnType, IDGenerator } from '../core/Types';
import { SpawnerComponent } from './SpawnerComponent';

const { ccclass, property } = _decorator;




/**
 * Spawner system manager
 */
@ccclass('SpawnerSystem')
export class SpawnerSystem extends Component {
    private spawnerComponents: Map<string, SpawnerComponent> = new Map();
    private globalTime: number = 0;
    
    // Events
    public onEntitySpawned?: (entity: Node, spawner: Spawner) => void;
    public onSpawnerCompleted?: (spawner: Spawner) => void;
    
    protected start(): void {
        console.log('SpawnerSystem initialized');
    }
    
    protected update(deltaTime: number): void {
        this.globalTime += deltaTime;
        // Individual spawners handle their own updates
    }
    
    /**
     * Register a spawner with the system
     */
    public registerSpawner(spawner: Spawner): SpawnerComponent {
        // Create spawner node and component
        const spawnerNode = new Node(`Spawner_${spawner.id}`);
        const component = spawnerNode.addComponent(SpawnerComponent);
        component.spawnerData = spawner;
        
        // Setup events
        component.onEntitySpawned = this.onEntitySpawned;
        component.onSpawnerCompleted = this.onSpawnerCompleted;
        
        // Add to scene and register
        this.node.addChild(spawnerNode);
        this.spawnerComponents.set(spawner.id, component);
        
        console.log(`Spawner registered: ${spawner.name}`);
        return component;
    }
    
    /**
     * Unregister a spawner
     */
    public unregisterSpawner(spawnerId: string): void {
        const component = this.spawnerComponents.get(spawnerId);
        if (component) {
            component.node.destroy();
            this.spawnerComponents.delete(spawnerId);
            console.log(`Spawner unregistered: ${spawnerId}`);
        }
    }
    
    /**
     * Get spawner component by ID
     */
    public getSpawner(spawnerId: string): SpawnerComponent | undefined {
        return this.spawnerComponents.get(spawnerId);
    }
    
    /**
     * Get all spawner components
     */
    public getAllSpawners(): SpawnerComponent[] {
        return Array.from(this.spawnerComponents.values());
    }
    
    /**
     * Activate all spawners
     */
    public activateAllSpawners(): void {
        this.spawnerComponents.forEach(component => {
            component.activate();
        });
    }
    
    /**
     * Deactivate all spawners
     */
    public deactivateAllSpawners(): void {
        this.spawnerComponents.forEach(component => {
            component.deactivate();
        });
    }
    
    /**
     * Pause all spawners
     */
    public pauseAllSpawners(): void {
        this.spawnerComponents.forEach(component => {
            component.pause();
        });
    }
    
    /**
     * Resume all spawners
     */
    public resumeAllSpawners(): void {
        this.spawnerComponents.forEach(component => {
            component.resume();
        });
    }
    
    /**
     * Get global time
     */
    public getGlobalTime(): number {
        return this.globalTime;
    }
    
    /**
     * Reset global time
     */
    public resetGlobalTime(): void {
        this.globalTime = 0;
    }
    
    /**
     * Create a new spawner
     */
    public createSpawner(name: string, position: Vec3, prefabPath: string): Spawner {
        const spawner: Spawner = {
            id: IDGenerator.generate('spawner'),
            name: name,
            position: position.clone(),
            prefabPath: prefabPath,
            spawnPattern: {
                type: SpawnType.Static,
                entities: [{
                    prefabPath: prefabPath,
                    weight: 1
                }],
                count: 1,
                interval: 1.0,
                delay: 0
            },
            isActive: true,
            waves: [],
            editorData: {
                gizmoColor: Color.GREEN,
                showPreview: true,
                notes: ''
            }
        };
        
        return spawner;
    }
    
    /**
     * Create a new wave
     */
    public createWave(name: string, startTime: number, endTime: number): Wave {
        const wave: Wave = {
            id: IDGenerator.generate('wave'),
            name: name,
            startTime: startTime,
            endTime: endTime,
            spawnerConfigs: [],
            isActive: true
        };
        
        return wave;
    }
}
