/**
 * CrazyStorm 2.0 Integration for Cocos Creator
 * Layer Manager
 * 
 * This manager handles layer timing, visibility, and component coordination
 * within particle systems.
 */

import { Node, log, warn } from 'cc';
import { Layer, Component as CrazyStormComponentData } from '../core/CrazyStormTypes';
import { GlobalVariableManager } from './GlobalVariableManager';
import { ComponentFactory } from '../components/ComponentFactory';
import { CrazyStormComponent } from '../components/CrazyStormComponent';

/**
 * Runtime layer instance
 */
export class RuntimeLayer {
    public name: string;
    public node: Node;
    public data: Layer;
    public components: CrazyStormComponent[] = [];
    public isActive: boolean = false;
    public isVisible: boolean = true;
    public currentFrame: number = 0;

    constructor(name: string, data: Layer, parentNode: Node) {
        this.name = name;
        this.data = data;
        this.isVisible = data.visible;
        
        // Create layer node
        this.node = new Node(name);
        parentNode.addChild(this.node);
        this.node.active = this.isVisible;
    }

    /**
     * Update layer for current frame
     */
    public update(globalFrame: number): void {
        this.currentFrame = globalFrame;
        
        // Check if layer should be active
        const layerFrame = globalFrame - this.data.beginFrame;
        this.isActive = layerFrame >= 0 && layerFrame < this.data.totalFrame && this.isVisible;
        
        // Update node visibility
        this.node.active = this.isActive;
        
        // Update all components in this layer
        this.components.forEach(component => {
            component.setLayerFrame(globalFrame);
        });
    }

    /**
     * Reset layer to initial state
     */
    public reset(): void {
        this.currentFrame = 0;
        this.isActive = false;
        this.node.active = this.isVisible;
        
        // Reset all components
        this.components.forEach(component => {
            component.reset();
        });
    }

    /**
     * Set layer visibility
     */
    public setVisible(visible: boolean): void {
        this.isVisible = visible;
        this.node.active = this.isActive && this.isVisible;
    }

    /**
     * Add component to layer
     */
    public addComponent(component: CrazyStormComponent): void {
        this.components.push(component);
    }

    /**
     * Remove component from layer
     */
    public removeComponent(component: CrazyStormComponent): void {
        const index = this.components.indexOf(component);
        if (index >= 0) {
            this.components.splice(index, 1);
        }
    }

    /**
     * Get component count
     */
    public getComponentCount(): number {
        return this.components.length;
    }

    /**
     * Clear all particles in this layer
     */
    public clearParticles(): void {
        this.components.forEach(component => {
            // Clear particles if component is an emitter
            if ('clearAllParticles' in component) {
                (component as any).clearAllParticles();
            }
        });
    }

    /**
     * Destroy layer
     */
    public destroy(): void {
        if (this.node && this.node.isValid) {
            this.node.destroy();
        }
        this.components = [];
    }
}

/**
 * Manager for all layers across particle systems
 */
export class LayerManager {
    private globalVariableManager: GlobalVariableManager;
    private systemLayers: Map<string, RuntimeLayer[]> = new Map();
    private totalLayerCount: number = 0;
    private activeLayerCount: number = 0;

    constructor(globalVariableManager: GlobalVariableManager) {
        this.globalVariableManager = globalVariableManager;
    }

    /**
     * Initialize layers for a particle system
     */
    public initializeSystemLayers(systemName: string, layersData: Layer[], parentNode: Node): void {
        const runtimeLayers: RuntimeLayer[] = [];

        layersData.forEach((layerData, index) => {
            const layerName = layerData.name || `Layer_${index}`;
            const runtimeLayer = new RuntimeLayer(layerName, layerData, parentNode);
            
            // Create components for this layer
            this.createLayerComponents(runtimeLayer, layerData.components);
            
            runtimeLayers.push(runtimeLayer);
        });

        this.systemLayers.set(systemName, runtimeLayers);
        this.totalLayerCount += runtimeLayers.length;

        log(`LayerManager: Initialized ${runtimeLayers.length} layers for system ${systemName}`);
    }

    /**
     * Update layers for a specific particle system
     */
    public updateSystemLayers(systemName: string, systemFrame: number): void {
        const layers = this.systemLayers.get(systemName);
        if (!layers) {
            return;
        }

        let activeCount = 0;
        layers.forEach(layer => {
            layer.update(systemFrame);
            if (layer.isActive) {
                activeCount++;
            }
        });

        // Update active layer count (this is a simplified version)
        this.activeLayerCount = activeCount;
    }

    /**
     * Update all layers
     */
    public update(globalFrame: number): void {
        this.activeLayerCount = 0;

        this.systemLayers.forEach((layers, systemName) => {
            layers.forEach(layer => {
                layer.update(globalFrame);
                if (layer.isActive) {
                    this.activeLayerCount++;
                }
            });
        });
    }

    /**
     * Reset all layers
     */
    public resetAll(): void {
        this.systemLayers.forEach(layers => {
            layers.forEach(layer => {
                layer.reset();
            });
        });
        this.activeLayerCount = 0;
        log('LayerManager: Reset all layers');
    }

    /**
     * Get layers for a specific system
     */
    public getSystemLayers(systemName: string): RuntimeLayer[] {
        return this.systemLayers.get(systemName) || [];
    }

    /**
     * Get layer by name within a system
     */
    public getLayer(systemName: string, layerName: string): RuntimeLayer | null {
        const layers = this.systemLayers.get(systemName);
        if (!layers) {
            return null;
        }

        return layers.find(layer => layer.name === layerName) || null;
    }

    /**
     * Set layer visibility
     */
    public setLayerVisible(systemName: string, layerName: string, visible: boolean): boolean {
        const layer = this.getLayer(systemName, layerName);
        if (layer) {
            layer.setVisible(visible);
            return true;
        }
        warn(`LayerManager: Layer not found: ${systemName}.${layerName}`);
        return false;
    }

    /**
     * Get total layer count
     */
    public getTotalLayerCount(): number {
        return this.totalLayerCount;
    }

    /**
     * Get active layer count
     */
    public getActiveLayerCount(): number {
        return this.activeLayerCount;
    }

    /**
     * Clear particles in a specific system
     */
    public clearSystemParticles(systemName: string): void {
        const layers = this.systemLayers.get(systemName);
        if (layers) {
            layers.forEach(layer => {
                layer.clearParticles();
            });
        }
    }

    /**
     * Clear all particles in all systems
     */
    public clearAllParticles(): void {
        this.systemLayers.forEach(layers => {
            layers.forEach(layer => {
                layer.clearParticles();
            });
        });
        log('LayerManager: Cleared all particles');
    }

    /**
     * Get layer statistics for a system
     */
    public getSystemLayerStats(systemName: string): {
        totalLayers: number;
        activeLayers: number;
        visibleLayers: number;
        totalComponents: number;
    } {
        const layers = this.systemLayers.get(systemName) || [];
        
        let activeLayers = 0;
        let visibleLayers = 0;
        let totalComponents = 0;

        layers.forEach(layer => {
            if (layer.isActive) activeLayers++;
            if (layer.isVisible) visibleLayers++;
            totalComponents += layer.getComponentCount();
        });

        return {
            totalLayers: layers.length,
            activeLayers,
            visibleLayers,
            totalComponents
        };
    }

    /**
     * Destroy all layers
     */
    public destroy(): void {
        this.systemLayers.forEach(layers => {
            layers.forEach(layer => {
                layer.destroy();
            });
        });
        this.systemLayers.clear();
        this.totalLayerCount = 0;
        this.activeLayerCount = 0;
        log('LayerManager: Destroyed all layers');
    }

    // Private helper methods

    /**
     * Create components for a layer
     */
    private createLayerComponents(layer: RuntimeLayer, componentsData: CrazyStormComponentData[]): void {
        const globals = this.globalVariableManager.getAllVariables();

        componentsData.forEach(componentData => {
            const component = ComponentFactory.createComponent(componentData, layer.node, globals);
            if (component) {
                layer.addComponent(component);
            }
        });

        log(`LayerManager: Created ${layer.getComponentCount()} components for layer ${layer.name}`);
    }
}
