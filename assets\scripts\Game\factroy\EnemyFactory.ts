import { _decorator, Component, Node, instantiate } from 'cc';
import { Enemy } from '../Enemy';
import { Global } from '../Global';
import { PersistNode } from '../PersistNode';
import { GameFactory } from './GameFactory';
const { ccclass, property } = _decorator;

export class EnemyFactory extends GameFactory{
    
    public createProduct(productType: string): Node {
        let enemyTemp: Node = null;

        if(this.productPool.size() > 0) {
            enemyTemp = this.productPool.get();  //如果池里有敌机，就直接拿来用
        } else {
            enemyTemp = instantiate(this.persistNode.getComponent(PersistNode).enemyPreb);  //从常驻节点拿到预制体原料
        }

        switch(productType) {
            case Global.ENEMY_1:
                enemyTemp.getComponent(Enemy).init(productType, this.persistNode.getComponent(PersistNode).enemy1);
                break;
            case Global.ENEMY_2:
                enemyTemp.getComponent(Enemy).init(productType, this.persistNode.getComponent(PersistNode).enemy2);
                break;
        }

        return enemyTemp;
    } 
}

