System.register(["cc", "cc/env"], function (_export, _context) {
  "use strict";

  var _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, EDITOR, GizmoDrawer, _crd;

  _export("GizmoDrawer", void 0);

  return {
    setters: [function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
    }, function (_ccEnv) {
      EDITOR = _ccEnv.EDITOR;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "0088dL1x5NJPINMIFyaUJEf", "GizmoDrawer", undefined);

      __checkObsolete__(['_decorator', 'Component', 'Graphics', 'Color', 'Node']);

      /**
       * Abstract base class for drawing gizmos for specific component types
       * This is not a component itself, but a drawer that can be registered to GizmoManager
       */
      _export("GizmoDrawer", GizmoDrawer = class GizmoDrawer {
        constructor() {
          /**
           * The component type this drawer handles
           */

          /**
           * Name of this gizmo drawer for debugging
           */

          /**
           * Whether this drawer is enabled
           */
          this.enabled = true;
        }
        /**
         * Draw gizmos for the given component
         * @param component The component to draw gizmos for
         * @param graphics The graphics component to draw with
         * @param node The node that contains the component
         */


        /**
         * Check if this drawer can handle the given component
         * @param component The component to check
         * @returns true if this drawer can handle the component
         */
        canHandle(component) {
          return component instanceof this.componentType;
        }
        /**
         * Called when the drawer is registered to the manager
         * Override this to perform any initialization
         */


        onRegister() {
          if (EDITOR) {
            console.log(`GizmoDrawer: Registered ${this.drawerName}`);
          }
        }
        /**
         * Called when the drawer is unregistered from the manager
         * Override this to perform any cleanup
         */


        onUnregister() {
          if (EDITOR) {
            console.log(`GizmoDrawer: Unregistered ${this.drawerName}`);
          }
        }
        /**
         * Get the priority of this drawer (higher priority draws last/on top)
         * Override this to change drawing order
         */


        getPriority() {
          return 0;
        }
        /**
         * Helper method to draw a cross at the given position
         */


        drawCross(graphics, x, y, size, color) {
          graphics.strokeColor = color;
          graphics.lineWidth = 2;
          graphics.moveTo(x - size, y);
          graphics.lineTo(x + size, y);
          graphics.moveTo(x, y - size);
          graphics.lineTo(x, y + size);
          graphics.stroke();
        }
        /**
         * Helper method to draw a circle
         */


        drawCircle(graphics, x, y, radius, color, filled = false) {
          graphics.strokeColor = color;
          graphics.lineWidth = 1;

          if (filled) {
            graphics.fillColor = color;
            graphics.circle(x, y, radius);
            graphics.fill();
          } else {
            graphics.circle(x, y, radius);
            graphics.stroke();
          }
        }
        /**
         * Helper method to draw an arrow
         */


        drawArrow(graphics, startX, startY, endX, endY, color, arrowSize = 8) {
          graphics.strokeColor = color;
          graphics.lineWidth = 2; // Draw main line

          graphics.moveTo(startX, startY);
          graphics.lineTo(endX, endY); // Calculate arrow head

          const dx = endX - startX;
          const dy = endY - startY;
          const length = Math.sqrt(dx * dx + dy * dy);

          if (length > 0) {
            const dirX = dx / length;
            const dirY = dy / length; // Arrow head angle (30 degrees)

            const arrowAngle = Math.PI / 6; // Left arrow point

            const leftX = endX - arrowSize * (dirX * Math.cos(arrowAngle) - dirY * Math.sin(arrowAngle));
            const leftY = endY - arrowSize * (dirY * Math.cos(arrowAngle) + dirX * Math.sin(arrowAngle)); // Right arrow point

            const rightX = endX - arrowSize * (dirX * Math.cos(-arrowAngle) - dirY * Math.sin(-arrowAngle));
            const rightY = endY - arrowSize * (dirY * Math.cos(-arrowAngle) + dirX * Math.sin(-arrowAngle)); // Draw arrow head

            graphics.moveTo(endX, endY);
            graphics.lineTo(leftX, leftY);
            graphics.moveTo(endX, endY);
            graphics.lineTo(rightX, rightY);
          }

          graphics.stroke();
        }
        /**
         * Helper method to draw text (simple implementation)
         * Note: For more complex text rendering, consider using Label components
         */


        drawText(graphics, text, x, y, color) {
          // This is a placeholder - in a real implementation you might want to use Label components
          // or a more sophisticated text rendering system
          if (EDITOR) {
            console.log(`Gizmo Text at (${x}, ${y}): ${text}`);
          }
        }

      });

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=e5ab51e9b74404eeb86a5ef59d955e5cab728dfd.js.map