{"version": 3, "sources": ["file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/PlayerBullet.ts"], "names": ["_decorator", "Component", "Sprite", "find", "Collider2D", "Contact2DType", "v2", "ProgressBar", "Label", "AudioSource", "Animation", "Enemy", "Global", "PersistNode", "ccclass", "property", "<PERSON><PERSON><PERSON><PERSON>", "playerBulletType", "curPos", "normalBulletMoveSpeed", "lightBulletMoveSpeed", "missileBulletMoveSpeed", "playerNormalReduce", "playerLightReduce", "playerMissileReduce", "playerBulletFactory", "enemyFactory", "persistNode", "onLoad", "getComponent", "collider", "node", "on", "BEGIN_CONTACT", "onBeginContact", "selfCollider", "otherCollider", "contact", "NORMAL_BULLET", "enemyBlood", "LIGHT_BULLET", "MISSILE_BULLET", "getChildByName", "progress", "enemyTotalBlood", "anim", "animFactory", "createAnim", "setPosition", "getPosition", "<PERSON><PERSON><PERSON><PERSON>", "play", "recycleProduct", "playOneShot", "boomAudioClip", "enemyType", "ENEMY_1", "SCORE", "string", "toString", "init", "spriteFrame", "angle", "signAngle", "Math", "PI", "update", "deltaTime", "normalBulletMove", "lightBulletMove", "missileBulletMove", "y", "HEIGHT", "enemy", "parent", "enemyPos", "normalizeVec", "subtract", "normalize", "x"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAA8BC,MAAAA,M,OAAAA,M;AAAcC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,U,OAAAA,U;AAAYC,MAAAA,a,OAAAA,a;AAAkCC,MAAAA,E,OAAAA,E;AAAIC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,S,OAAAA,S;;AACjJC,MAAAA,K,iBAAAA,K;;AAEAC,MAAAA,M,iBAAAA,M;;AACAC,MAAAA,W,iBAAAA,W;;;;;;;;;OACH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBf,U;;8BAGjBgB,Y,WADZF,OAAO,CAAC,cAAD,C,gBAAR,MACaE,YADb,SACkCf,SADlC,CAC4C;AAAA;AAAA;AAAA,eAExCgB,gBAFwC,GAEb,IAFa;AAEJ;AAFI,eAIxCC,MAJwC,GAIzB,IAJyB;AAIjB;AAJiB,eAMxCC,qBANwC,GAMR,CANQ;AAMF;AANE,eAQxCC,oBARwC,GAQT,CARS;AAQH;AARG,eAUxCC,sBAVwC,GAUP,CAVO;AAUD;AAVC,eAYxCC,kBAZwC,GAYX,CAZW;AAYA;AAZA,eAcxCC,iBAdwC,GAcZ,CAdY;AAcD;AAdC,eAgBxCC,mBAhBwC,GAgBV,CAhBU;AAgBC;AAhBD,eAkBxCC,mBAlBwC,GAkBL,IAlBK;AAAA,eAoBxCC,YApBwC,GAoBZ,IApBY;AAoBJ;AApBI,eAsBxCC,WAtBwC,GAsBpB,IAtBoB;AAAA;;AAwBxCC,QAAAA,MAAM,GAAG;AACL,eAAKD,WAAL,GAAmBxB,IAAI,CAAC,aAAD,CAAvB;AACA,eAAKsB,mBAAL,GAA2B,KAAKE,WAAL,CAAiBE,YAAjB;AAAA;AAAA,0CAA2CJ,mBAAtE;AACA,eAAKC,YAAL,GAAoB,KAAKC,WAAL,CAAiBE,YAAjB;AAAA;AAAA,0CAA2CH,YAA/D,CAHK,CAKL;;AACA,eAAKP,qBAAL,GAA6B,KAAKQ,WAAL,CAAiBE,YAAjB;AAAA;AAAA,0CAA2CV,qBAAxE;AACA,eAAKC,oBAAL,GAA4B,KAAKO,WAAL,CAAiBE,YAAjB;AAAA;AAAA,0CAA2CT,oBAAvE;AACA,eAAKC,sBAAL,GAA8B,KAAKM,WAAL,CAAiBE,YAAjB;AAAA;AAAA,0CAA2CR,sBAAzE,CARK,CAUL;;AACA,eAAKC,kBAAL,GAA0B,KAAKK,WAAL,CAAiBE,YAAjB;AAAA;AAAA,0CAA2CP,kBAArE;AACA,eAAKC,iBAAL,GAAyB,KAAKI,WAAL,CAAiBE,YAAjB;AAAA;AAAA,0CAA2CN,iBAApE;AACA,eAAKC,mBAAL,GAA2B,KAAKG,WAAL,CAAiBE,YAAjB;AAAA;AAAA,0CAA2CL,mBAAtE;AAEA,cAAIM,QAAQ,GAAG,KAAKC,IAAL,CAAUF,YAAV,CAAuBzB,UAAvB,CAAf;;AAEA,cAAI0B,QAAJ,EAAc;AACVA,YAAAA,QAAQ,CAACE,EAAT,CAAY3B,aAAa,CAAC4B,aAA1B,EAAyC,KAAKC,cAA9C,EAA8D,IAA9D;AACH;AACJ;AAED;AACJ;AACA;;;AACIA,QAAAA,cAAc,CAACC,YAAD,EAA2BC,aAA3B,EAAsDC,OAAtD,EAAyF;AACnG,cAAI,KAAKpB,gBAAL,IAAyB;AAAA;AAAA,gCAAOqB,aAApC,EAAmD;AAC/CF,YAAAA,aAAa,CAACP,YAAd;AAAA;AAAA,gCAAkCU,UAAlC,IAAgD,KAAKjB,kBAArD;AACH,WAFD,MAEO,IAAI,KAAKL,gBAAL,IAAyB;AAAA;AAAA,gCAAOuB,YAApC,EAAkD;AACrDJ,YAAAA,aAAa,CAACP,YAAd;AAAA;AAAA,gCAAkCU,UAAlC,IAAgD,KAAKhB,iBAArD;AACH,WAFM,MAEA,IAAI,KAAKN,gBAAL,IAAyB;AAAA;AAAA,gCAAOwB,cAApC,EAAoD;AACvDL,YAAAA,aAAa,CAACP,YAAd;AAAA;AAAA,gCAAkCU,UAAlC,IAAgD,KAAKf,mBAArD;AACH;;AAEDY,UAAAA,aAAa,CAACL,IAAd,CAAmBW,cAAnB,CAAkC,YAAlC,EAAgDb,YAAhD,CAA6DtB,WAA7D,EAA0EoC,QAA1E,GAAqFP,aAAa,CAACP,YAAd;AAAA;AAAA,8BAAkCU,UAAlC,GAA+CH,aAAa,CAACP,YAAd;AAAA;AAAA,8BAAkCe,eAAtK;;AAEA,cAAIR,aAAa,CAACP,YAAd;AAAA;AAAA,8BAAkCU,UAAlC,IAAgD,CAApD,EAAuD;AAAS;AAC5D;AACA,gBAAIM,IAAI,GAAG,KAAKlB,WAAL,CAAiBE,YAAjB;AAAA;AAAA,4CAA2CiB,WAA3C,CAAuDC,UAAvD,EAAX;AACAF,YAAAA,IAAI,CAACG,WAAL,CAAiBZ,aAAa,CAACL,IAAd,CAAmBkB,WAAnB,EAAjB;AACA9C,YAAAA,IAAI,CAAC,QAAD,CAAJ,CAAe+C,QAAf,CAAwBL,IAAxB;AACAA,YAAAA,IAAI,CAAChB,YAAL,CAAkBnB,SAAlB,EAA6ByC,IAA7B,GALmD,CAKX;;AAExC,iBAAKzB,YAAL,CAAkB0B,cAAlB,CAAiChB,aAAa,CAACL,IAA/C,EAPmD,CAOK;;AACxD,iBAAKJ,WAAL,CAAiBE,YAAjB,CAA8BpB,WAA9B,EAA2C4C,WAA3C,CAAuD,KAAK1B,WAAL,CAAiBE,YAAjB;AAAA;AAAA,4CAA2CyB,aAAlG;;AACA,gBAAIlB,aAAa,CAACL,IAAd,CAAmBF,YAAnB;AAAA;AAAA,gCAAuC0B,SAAvC,IAAoD;AAAA;AAAA,kCAAOC,OAA/D,EAAwE;AACpE;AAAA;AAAA,oCAAOC,KAAP,IAAgB,EAAhB;AACH,aAFD,MAEO;AACH;AAAA;AAAA,oCAAOA,KAAP,IAAgB,EAAhB;AACH;;AACDtD,YAAAA,IAAI,CAAC,cAAD,CAAJ,CAAqB0B,YAArB,CAAkCrB,KAAlC,EAAyCkD,MAAzC,GAAkD,WAAW;AAAA;AAAA,kCAAOD,KAAP,CAAaE,QAAb,EAA7D;AACH;;AAED,eAAKlC,mBAAL,CAAyB2B,cAAzB,CAAwC,KAAKrB,IAA7C,EA5BmG,CA4B3C;AAC3D;AAED;AACJ;AACA;;;AACI6B,QAAAA,IAAI,CAAC3C,gBAAD,EAA2B4C,WAA3B,EAAqD;AACrD,eAAK5C,gBAAL,GAAwBA,gBAAxB;AACA,eAAKc,IAAL,CAAUF,YAAV,CAAuB3B,MAAvB,EAA+B2D,WAA/B,GAA6CA,WAA7C;AAEA,eAAK9B,IAAL,CAAU+B,KAAV,GAAkBxD,EAAE,CAAC,CAAD,EAAI,CAAJ,CAAF,CAASyD,SAAT,CAAmBzD,EAAE,CAAC,CAAD,EAAI,CAAJ,CAArB,IAA+B,GAA/B,GAAqC0D,IAAI,CAACC,EAA5D;AACH;;AAEDC,QAAAA,MAAM,CAACC,SAAD,EAAoB;AACtB,cAAI,KAAKlD,gBAAL,IAAyB;AAAA;AAAA,gCAAOqB,aAApC,EAAmD;AAC/C,iBAAK8B,gBAAL,CAAsBD,SAAtB;AACH,WAFD,MAEO,IAAI,KAAKlD,gBAAL,IAAyB;AAAA;AAAA,gCAAOuB,YAApC,EAAkD;AACrD,iBAAK6B,eAAL,CAAqBF,SAArB;AACH,WAFM,MAEA,IAAI,KAAKlD,gBAAL,IAAyB;AAAA;AAAA,gCAAOwB,cAApC,EAAoD;AACvD,iBAAK6B,iBAAL,CAAuBH,SAAvB;AACH;AACJ;AAED;AACJ;AACA;AACA;;;AACIC,QAAAA,gBAAgB,CAACD,SAAD,EAAoB;AAChC,eAAKjD,MAAL,GAAc,KAAKa,IAAL,CAAUkB,WAAV,EAAd;AACA,eAAK/B,MAAL,CAAYqD,CAAZ,IAAiB,KAAKpD,qBAAL,GAA6BgD,SAA9C;AACA,eAAKpC,IAAL,CAAUiB,WAAV,CAAsB,KAAK9B,MAA3B;;AAEA,cAAI,KAAKA,MAAL,CAAYqD,CAAZ,GAAgB;AAAA;AAAA,gCAAOC,MAAP,GAAgB,CAApC,EAAuC;AACnC,iBAAK/C,mBAAL,CAAyB2B,cAAzB,CAAwC,KAAKrB,IAA7C;AACH;AACJ;AAED;AACJ;AACA;AACA;;;AACIsC,QAAAA,eAAe,CAACF,SAAD,EAAoB;AAC/B,eAAKjD,MAAL,GAAc,KAAKa,IAAL,CAAUkB,WAAV,EAAd;AACA,eAAK/B,MAAL,CAAYqD,CAAZ,IAAiB,KAAKnD,oBAAL,GAA4B+C,SAA7C;AACA,eAAKpC,IAAL,CAAUiB,WAAV,CAAsB,KAAK9B,MAA3B;;AAEA,cAAI,KAAKA,MAAL,CAAYqD,CAAZ,GAAgB;AAAA;AAAA,gCAAOC,MAAP,GAAgB,CAApC,EAAuC;AACnC,iBAAK/C,mBAAL,CAAyB2B,cAAzB,CAAwC,KAAKrB,IAA7C;AACH;AACJ;AAED;AACJ;AACA;AACA;;;AACIuC,QAAAA,iBAAiB,CAACH,SAAD,EAAoB;AACjC,cAAIM,KAAW,GAAG,KAAK1C,IAAL,CAAU2C,MAAV,CAAiBhC,cAAjB,CAAgC,OAAhC,CAAlB,CADiC,CAC2B;;AAC5D,eAAKxB,MAAL,GAAc,KAAKa,IAAL,CAAUkB,WAAV,EAAd;;AACA,cAAIwB,KAAK,IAAI,IAAb,EAAmB;AACf,gBAAIE,QAAc,GAAGF,KAAK,CAACxB,WAAN,EAArB,CADe,CAC6B;;AAC5C,gBAAI2B,YAAkB,GAAGD,QAAQ,CAACE,QAAT,CAAkB,KAAK3D,MAAvB,EAA+B4D,SAA/B,EAAzB,CAFe,CAEuD;;AAEtE,iBAAK5D,MAAL,CAAY6D,CAAZ,IAAiBH,YAAY,CAACG,CAAb,GAAiB,KAAK1D,sBAAtB,GAA+C8C,SAAhE,CAJe,CAI6D;;AAC5E,iBAAKjD,MAAL,CAAYqD,CAAZ,IAAiBK,YAAY,CAACL,CAAb,GAAiB,KAAKlD,sBAAtB,GAA+C8C,SAAhE;AACA,iBAAKpC,IAAL,CAAUiB,WAAV,CAAsB,KAAK9B,MAA3B;AAEA,iBAAKa,IAAL,CAAU+B,KAAV,GAAkBxD,EAAE,CAAC,CAAD,EAAI,CAAJ,CAAF,CAASyD,SAAT,CAAmBzD,EAAE,CAACsE,YAAY,CAACG,CAAd,EAAiBH,YAAY,CAACL,CAA9B,CAArB,IAAyD,GAAzD,GAA+DP,IAAI,CAACC,EAAtF,CARe,CAQ4E;AAC9F,WATD,MASO;AAAS;AACZ,iBAAK/C,MAAL,CAAYqD,CAAZ,IAAiB,KAAKlD,sBAAL,GAA8B8C,SAA/C;AACA,iBAAKpC,IAAL,CAAUiB,WAAV,CAAsB,KAAK9B,MAA3B;AACA,iBAAKa,IAAL,CAAU+B,KAAV,GAAkBxD,EAAE,CAAC,CAAD,EAAI,CAAJ,CAAF,CAASyD,SAAT,CAAmBzD,EAAE,CAAC,CAAD,EAAI,CAAJ,CAArB,IAA+B,GAA/B,GAAqC0D,IAAI,CAACC,EAA5D;AACH;;AAED,cAAI,KAAK/C,MAAL,CAAYqD,CAAZ,GAAgB;AAAA;AAAA,gCAAOC,MAAP,GAAgB,CAApC,EAAuC;AACnC,iBAAK/C,mBAAL,CAAyB2B,cAAzB,CAAwC,KAAKrB,IAA7C;AACH;AACJ;;AAzJuC,O", "sourcesContent": ["import { _decorator, Component, Node, SpriteFrame, Sprite, Vec3, find, Collider2D, Contact2DType, IPhysics2DContact, v2, ProgressBar, Label, AudioSource, Animation } from 'cc';\nimport { Enemy } from './Enemy';\nimport { GameFactory } from './factroy/GameFactory';\nimport { Global } from './Global';\nimport { PersistNode } from './PersistNode';\nconst { ccclass, property } = _decorator;\n\n@ccclass('PlayerBullet')\nexport class PlayerBullet extends Component {\n\n    playerBulletType: string = null;    //子弹类型\n\n    curPos: Vec3 = null;   //当前子弹位置\n\n    normalBulletMoveSpeed: number = 0;    //普通子弹移动速度\n\n    lightBulletMoveSpeed: number = 0;    //激光子弹移动速度\n\n    missileBulletMoveSpeed: number = 0;    //激光子弹移动速度\n\n    playerNormalReduce: number = 0;         //被普通子弹击中，敌机掉多少血\n\n    playerLightReduce: number = 0;         //被激光子弹击中，敌机掉多少血\n\n    playerMissileReduce: number = 0;         //导弹子弹击中，敌机掉多少血\n\n    playerBulletFactory: GameFactory = null;\n\n    enemyFactory: GameFactory = null;   //敌机工厂\n\n    persistNode: Node = null;\n\n    onLoad() {\n        this.persistNode = find(\"PersistNode\");\n        this.playerBulletFactory = this.persistNode.getComponent(PersistNode).playerBulletFactory;\n        this.enemyFactory = this.persistNode.getComponent(PersistNode).enemyFactory;\n\n        //关联面板子弹移动速度\n        this.normalBulletMoveSpeed = this.persistNode.getComponent(PersistNode).normalBulletMoveSpeed;\n        this.lightBulletMoveSpeed = this.persistNode.getComponent(PersistNode).lightBulletMoveSpeed;\n        this.missileBulletMoveSpeed = this.persistNode.getComponent(PersistNode).missileBulletMoveSpeed;\n\n        //关联面板敌机掉血\n        this.playerNormalReduce = this.persistNode.getComponent(PersistNode).playerNormalReduce;\n        this.playerLightReduce = this.persistNode.getComponent(PersistNode).playerLightReduce;\n        this.playerMissileReduce = this.persistNode.getComponent(PersistNode).playerMissileReduce;\n\n        let collider = this.node.getComponent(Collider2D);\n\n        if (collider) {\n            collider.on(Contact2DType.BEGIN_CONTACT, this.onBeginContact, this);\n        }\n    }\n\n    /**\n     * 开始碰撞后的回调函数\n     */\n    onBeginContact(selfCollider: Collider2D, otherCollider: Collider2D, contact: IPhysics2DContact | null) {\n        if (this.playerBulletType == Global.NORMAL_BULLET) {\n            otherCollider.getComponent(Enemy).enemyBlood -= this.playerNormalReduce;\n        } else if (this.playerBulletType == Global.LIGHT_BULLET) {\n            otherCollider.getComponent(Enemy).enemyBlood -= this.playerLightReduce;\n        } else if (this.playerBulletType == Global.MISSILE_BULLET) {\n            otherCollider.getComponent(Enemy).enemyBlood -= this.playerMissileReduce;\n        }\n\n        otherCollider.node.getChildByName(\"EnemyBlood\").getComponent(ProgressBar).progress = otherCollider.getComponent(Enemy).enemyBlood / otherCollider.getComponent(Enemy).enemyTotalBlood;\n\n        if (otherCollider.getComponent(Enemy).enemyBlood <= 0) {        //敌机血量判断是否小于0\n            //添加动画节点\n            let anim = this.persistNode.getComponent(PersistNode).animFactory.createAnim();\n            anim.setPosition(otherCollider.node.getPosition());\n            find(\"Canvas\").addChild(anim);\n            anim.getComponent(Animation).play();    //播放动画\n\n            this.enemyFactory.recycleProduct(otherCollider.node);   //敌机消失\n            this.persistNode.getComponent(AudioSource).playOneShot(this.persistNode.getComponent(PersistNode).boomAudioClip);\n            if (otherCollider.node.getComponent(Enemy).enemyType == Global.ENEMY_1) {\n                Global.SCORE += 20;\n            } else {\n                Global.SCORE += 40;\n            }\n            find(\"Canvas/Score\").getComponent(Label).string = \"Score:\" + Global.SCORE.toString();\n        }\n\n        this.playerBulletFactory.recycleProduct(this.node);     //子弹消失\n    }\n\n    /**\n     * 初始化playerBullet\n     */\n    init(playerBulletType: string, spriteFrame: SpriteFrame) {\n        this.playerBulletType = playerBulletType;\n        this.node.getComponent(Sprite).spriteFrame = spriteFrame;\n\n        this.node.angle = v2(0, 1).signAngle(v2(0, 1)) * 180 / Math.PI;\n    }\n\n    update(deltaTime: number) {\n        if (this.playerBulletType == Global.NORMAL_BULLET) {\n            this.normalBulletMove(deltaTime);\n        } else if (this.playerBulletType == Global.LIGHT_BULLET) {\n            this.lightBulletMove(deltaTime);\n        } else if (this.playerBulletType == Global.MISSILE_BULLET) {\n            this.missileBulletMove(deltaTime);\n        }\n    }\n\n    /**\n     * 普通子弹行为\n     * @param deltaTime \n     */\n    normalBulletMove(deltaTime: number) {\n        this.curPos = this.node.getPosition();\n        this.curPos.y += this.normalBulletMoveSpeed * deltaTime;\n        this.node.setPosition(this.curPos);\n\n        if (this.curPos.y > Global.HEIGHT / 2) {\n            this.playerBulletFactory.recycleProduct(this.node);\n        }\n    }\n\n    /**\n     * 激光子弹行为\n     * @param deltaTime \n     */\n    lightBulletMove(deltaTime: number) {\n        this.curPos = this.node.getPosition();\n        this.curPos.y += this.lightBulletMoveSpeed * deltaTime;\n        this.node.setPosition(this.curPos);\n\n        if (this.curPos.y > Global.HEIGHT / 2) {\n            this.playerBulletFactory.recycleProduct(this.node);\n        }\n    }\n\n    /**\n     * 导弹子弹行为\n     * @param deltaTime \n     */\n    missileBulletMove(deltaTime: number) {\n        let enemy: Node = this.node.parent.getChildByName(\"Enemy\"); //得到敌机节点\n        this.curPos = this.node.getPosition();\n        if (enemy != null) {\n            let enemyPos: Vec3 = enemy.getPosition();   ////得到敌机位置\n            let normalizeVec: Vec3 = enemyPos.subtract(this.curPos).normalize();  //得到子弹指向敌机的单位向量\n\n            this.curPos.x += normalizeVec.x * this.missileBulletMoveSpeed * deltaTime;  //子弹沿着单位向量移动\n            this.curPos.y += normalizeVec.y * this.missileBulletMoveSpeed * deltaTime;\n            this.node.setPosition(this.curPos);\n\n            this.node.angle = v2(0, 1).signAngle(v2(normalizeVec.x, normalizeVec.y)) * 180 / Math.PI;  //让导弹有夹角\n        } else {        //没有敌机时，跟普通子弹一样向上移动\n            this.curPos.y += this.missileBulletMoveSpeed * deltaTime;\n            this.node.setPosition(this.curPos);\n            this.node.angle = v2(0, 1).signAngle(v2(0, 1)) * 180 / Math.PI;\n        }\n\n        if (this.curPos.y > Global.HEIGHT / 2) {\n            this.playerBulletFactory.recycleProduct(this.node);\n        }\n    }\n}\n\n"]}