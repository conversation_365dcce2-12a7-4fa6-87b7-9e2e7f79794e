import { _decorator, Component, Node, SpriteFrame, Sprite, Vec3, find, Collider2D, Contact2DType, IPhysics2DContact, v2, ProgressBar, Label, AudioSource, Animation } from 'cc';
import { Enemy } from './Enemy';
import { GameFactory } from './factroy/GameFactory';
import { Global } from './Global';
import { PersistNode } from './PersistNode';
const { ccclass, property } = _decorator;

@ccclass('PlayerBullet')
export class PlayerBullet extends Component {

    playerBulletType: string = null;    //子弹类型

    curPos: Vec3 = null;   //当前子弹位置

    normalBulletMoveSpeed: number = 0;    //普通子弹移动速度

    lightBulletMoveSpeed: number = 0;    //激光子弹移动速度

    missileBulletMoveSpeed: number = 0;    //激光子弹移动速度

    playerNormalReduce: number = 0;         //被普通子弹击中，敌机掉多少血

    playerLightReduce: number = 0;         //被激光子弹击中，敌机掉多少血

    playerMissileReduce: number = 0;         //导弹子弹击中，敌机掉多少血

    playerBulletFactory: GameFactory = null;

    enemyFactory: GameFactory = null;   //敌机工厂

    persistNode: Node = null;

    onLoad() {
        this.persistNode = find("PersistNode");
        this.playerBulletFactory = this.persistNode.getComponent(PersistNode).playerBulletFactory;
        this.enemyFactory = this.persistNode.getComponent(PersistNode).enemyFactory;

        //关联面板子弹移动速度
        this.normalBulletMoveSpeed = this.persistNode.getComponent(PersistNode).normalBulletMoveSpeed;
        this.lightBulletMoveSpeed = this.persistNode.getComponent(PersistNode).lightBulletMoveSpeed;
        this.missileBulletMoveSpeed = this.persistNode.getComponent(PersistNode).missileBulletMoveSpeed;

        //关联面板敌机掉血
        this.playerNormalReduce = this.persistNode.getComponent(PersistNode).playerNormalReduce;
        this.playerLightReduce = this.persistNode.getComponent(PersistNode).playerLightReduce;
        this.playerMissileReduce = this.persistNode.getComponent(PersistNode).playerMissileReduce;

        let collider = this.node.getComponent(Collider2D);

        if (collider) {
            collider.on(Contact2DType.BEGIN_CONTACT, this.onBeginContact, this);
        }
    }

    /**
     * 开始碰撞后的回调函数
     */
    onBeginContact(selfCollider: Collider2D, otherCollider: Collider2D, contact: IPhysics2DContact | null) {
        if (this.playerBulletType == Global.NORMAL_BULLET) {
            otherCollider.getComponent(Enemy).enemyBlood -= this.playerNormalReduce;
        } else if (this.playerBulletType == Global.LIGHT_BULLET) {
            otherCollider.getComponent(Enemy).enemyBlood -= this.playerLightReduce;
        } else if (this.playerBulletType == Global.MISSILE_BULLET) {
            otherCollider.getComponent(Enemy).enemyBlood -= this.playerMissileReduce;
        }

        otherCollider.node.getChildByName("EnemyBlood").getComponent(ProgressBar).progress = otherCollider.getComponent(Enemy).enemyBlood / otherCollider.getComponent(Enemy).enemyTotalBlood;

        if (otherCollider.getComponent(Enemy).enemyBlood <= 0) {        //敌机血量判断是否小于0
            //添加动画节点
            let anim = this.persistNode.getComponent(PersistNode).animFactory.createAnim();
            anim.setPosition(otherCollider.node.getPosition());
            find("Canvas").addChild(anim);
            anim.getComponent(Animation).play();    //播放动画

            this.enemyFactory.recycleProduct(otherCollider.node);   //敌机消失
            this.persistNode.getComponent(AudioSource).playOneShot(this.persistNode.getComponent(PersistNode).boomAudioClip);
            if (otherCollider.node.getComponent(Enemy).enemyType == Global.ENEMY_1) {
                Global.SCORE += 20;
            } else {
                Global.SCORE += 40;
            }
            find("Canvas/Score").getComponent(Label).string = "Score:" + Global.SCORE.toString();
        }

        this.playerBulletFactory.recycleProduct(this.node);     //子弹消失
    }

    /**
     * 初始化playerBullet
     */
    init(playerBulletType: string, spriteFrame: SpriteFrame) {
        this.playerBulletType = playerBulletType;
        this.node.getComponent(Sprite).spriteFrame = spriteFrame;

        this.node.angle = v2(0, 1).signAngle(v2(0, 1)) * 180 / Math.PI;
    }

    update(deltaTime: number) {
        if (this.playerBulletType == Global.NORMAL_BULLET) {
            this.normalBulletMove(deltaTime);
        } else if (this.playerBulletType == Global.LIGHT_BULLET) {
            this.lightBulletMove(deltaTime);
        } else if (this.playerBulletType == Global.MISSILE_BULLET) {
            this.missileBulletMove(deltaTime);
        }
    }

    /**
     * 普通子弹行为
     * @param deltaTime 
     */
    normalBulletMove(deltaTime: number) {
        this.curPos = this.node.getPosition();
        this.curPos.y += this.normalBulletMoveSpeed * deltaTime;
        this.node.setPosition(this.curPos);

        if (this.curPos.y > Global.HEIGHT / 2) {
            this.playerBulletFactory.recycleProduct(this.node);
        }
    }

    /**
     * 激光子弹行为
     * @param deltaTime 
     */
    lightBulletMove(deltaTime: number) {
        this.curPos = this.node.getPosition();
        this.curPos.y += this.lightBulletMoveSpeed * deltaTime;
        this.node.setPosition(this.curPos);

        if (this.curPos.y > Global.HEIGHT / 2) {
            this.playerBulletFactory.recycleProduct(this.node);
        }
    }

    /**
     * 导弹子弹行为
     * @param deltaTime 
     */
    missileBulletMove(deltaTime: number) {
        let enemy: Node = this.node.parent.getChildByName("Enemy"); //得到敌机节点
        this.curPos = this.node.getPosition();
        if (enemy != null) {
            let enemyPos: Vec3 = enemy.getPosition();   ////得到敌机位置
            let normalizeVec: Vec3 = enemyPos.subtract(this.curPos).normalize();  //得到子弹指向敌机的单位向量

            this.curPos.x += normalizeVec.x * this.missileBulletMoveSpeed * deltaTime;  //子弹沿着单位向量移动
            this.curPos.y += normalizeVec.y * this.missileBulletMoveSpeed * deltaTime;
            this.node.setPosition(this.curPos);

            this.node.angle = v2(0, 1).signAngle(v2(normalizeVec.x, normalizeVec.y)) * 180 / Math.PI;  //让导弹有夹角
        } else {        //没有敌机时，跟普通子弹一样向上移动
            this.curPos.y += this.missileBulletMoveSpeed * deltaTime;
            this.node.setPosition(this.curPos);
            this.node.angle = v2(0, 1).signAngle(v2(0, 1)) * 180 / Math.PI;
        }

        if (this.curPos.y > Global.HEIGHT / 2) {
            this.playerBulletFactory.recycleProduct(this.node);
        }
    }
}

