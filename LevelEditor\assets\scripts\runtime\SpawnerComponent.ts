import { _decorator, Component, Node, Vec3, Prefab, instantiate, resources } from 'cc';
import { Spawner, Wave, SpawnPattern, SpawnableEntity } from '../core/LevelData';
import { SpawnType, SpawnerState } from '../core/Types';
import { PathFollower } from './PathFollower';

const { ccclass, property } = _decorator;

/**
 * Spawner component for spawning entities
 */
@ccclass('SpawnerComponent')
export class SpawnerComponent extends Component {
    @property({ type: Object })
    public spawnerData: Spawner | null = null;
    
    // Runtime state
    private currentState: SpawnerState = SpawnerState.Inactive;
    private currentWaveIndex: number = 0;
    private currentEntityIndex: number = 0;
    private timeSinceLastSpawn: number = 0;
    private totalSpawned: number = 0;
    
    // Wave timing
    private waveStartTime: number = 0;
    private isWaveActive: boolean = false;
    
    // Events
    public onEntitySpawned?: (entity: Node, spawner: Spawner) => void;
    public onWaveStarted?: (wave: Wave, spawner: Spawner) => void;
    public onWaveCompleted?: (wave: Wave, spawner: Spawner) => void;
    public onSpawnerCompleted?: (spawner: Spawner) => void;
    
    protected start(): void {
        if (this.spawnerData && this.spawnerData.isActive) {
            this.activate();
        }
    }
    
    protected update(deltaTime: number): void {
        if (this.currentState === SpawnerState.Active) {
            this.updateSpawning(deltaTime);
        }
    }
    
    /**
     * Activate the spawner
     */
    public activate(): void {
        if (!this.spawnerData) {
            console.warn('SpawnerComponent: No spawner data assigned');
            return;
        }
        
        this.currentState = SpawnerState.Active;
        this.currentWaveIndex = 0;
        this.currentEntityIndex = 0;
        this.totalSpawned = 0;
        this.timeSinceLastSpawn = 0;
        
        console.log(`Spawner activated: ${this.spawnerData.name}`);
    }
    
    /**
     * Deactivate the spawner
     */
    public deactivate(): void {
        this.currentState = SpawnerState.Inactive;
        this.isWaveActive = false;
        console.log(`Spawner deactivated: ${this.spawnerData?.name}`);
    }
    
    /**
     * Pause the spawner
     */
    public pause(): void {
        if (this.currentState === SpawnerState.Active) {
            this.currentState = SpawnerState.Paused;
        }
    }
    
    /**
     * Resume the spawner
     */
    public resume(): void {
        if (this.currentState === SpawnerState.Paused) {
            this.currentState = SpawnerState.Active;
        }
    }
    
    /**
     * Update spawning logic
     */
    private updateSpawning(deltaTime: number): void {
        if (!this.spawnerData || this.spawnerData.waves.length === 0) return;
        
        // Check if we need to start a new wave
        if (!this.isWaveActive && this.currentWaveIndex < this.spawnerData.waves.length) {
            const currentWave = this.spawnerData.waves[this.currentWaveIndex];
            
            // Check if it's time to start this wave
            if (this.shouldStartWave(currentWave)) {
                this.startWave(currentWave);
            }
        }
        
        // Update active wave
        if (this.isWaveActive) {
            this.updateWave(deltaTime);
        }
    }
    
    /**
     * Check if a wave should start
     */
    private shouldStartWave(wave: Wave): boolean {
        // For now, start waves immediately when previous wave completes
        // In a more complex system, this could check global time or other conditions
        return wave.isActive;
    }
    
    /**
     * Start a wave
     */
    private startWave(wave: Wave): void {
        this.isWaveActive = true;
        this.waveStartTime = 0;
        this.currentEntityIndex = 0;
        this.timeSinceLastSpawn = 0;
        
        this.onWaveStarted?.(wave, this.spawnerData!);
        console.log(`Wave started: ${wave.name} on spawner ${this.spawnerData!.name}`);
    }
    
    /**
     * Update current wave
     */
    private updateWave(deltaTime: number): void {
        if (!this.spawnerData || this.currentWaveIndex >= this.spawnerData.waves.length) return;
        
        const currentWave = this.spawnerData.waves[this.currentWaveIndex];
        this.waveStartTime += deltaTime;
        this.timeSinceLastSpawn += deltaTime;
        
        // Check if wave should end
        if (this.waveStartTime >= currentWave.endTime - currentWave.startTime) {
            this.endWave(currentWave);
            return;
        }
        
        // Check if we should spawn next entity
        if (this.shouldSpawnNext()) {
            this.spawnNextEntity();
        }
    }
    
    /**
     * Check if next entity should spawn
     */
    private shouldSpawnNext(): boolean {
        if (!this.spawnerData) return false;
        
        const pattern = this.spawnerData.spawnPattern;
        
        // Check if we've spawned enough entities
        if (this.totalSpawned >= pattern.count) return false;
        
        // Check timing
        if (this.timeSinceLastSpawn < pattern.interval) return false;
        
        return true;
    }
    
    /**
     * Spawn the next entity
     */
    private spawnNextEntity(): void {
        if (!this.spawnerData) return;
        
        const pattern = this.spawnerData.spawnPattern;
        const entityToSpawn = this.selectEntityToSpawn(pattern);
        
        if (entityToSpawn) {
            this.spawnEntity(entityToSpawn);
            this.timeSinceLastSpawn = 0;
            this.totalSpawned++;
            
            // Update entity index for sequential spawning
            if (pattern.type === SpawnType.Sequential) {
                this.currentEntityIndex = (this.currentEntityIndex + 1) % pattern.entities.length;
            }
        }
    }
    
    /**
     * Select which entity to spawn based on pattern
     */
    private selectEntityToSpawn(pattern: SpawnPattern): SpawnableEntity | null {
        if (pattern.entities.length === 0) return null;
        
        switch (pattern.type) {
            case SpawnType.Sequential:
                return pattern.entities[this.currentEntityIndex];
                
            case SpawnType.Random:
                return this.selectRandomEntity(pattern.entities);
                
            case SpawnType.Static:
                return pattern.entities[0];
                
            default:
                return pattern.entities[0];
        }
    }
    
    /**
     * Select random entity based on weights
     */
    private selectRandomEntity(entities: SpawnableEntity[]): SpawnableEntity {
        const totalWeight = entities.reduce((sum, entity) => sum + entity.weight, 0);
        let randomValue = Math.random() * totalWeight;
        
        for (const entity of entities) {
            randomValue -= entity.weight;
            if (randomValue <= 0) {
                return entity;
            }
        }
        
        // Fallback to first entity
        return entities[0];
    }
    
    /**
     * Spawn an entity
     */
    private async spawnEntity(entityData: SpawnableEntity): Promise<void> {
        try {
            // Load prefab
            const prefab = await this.loadPrefab(entityData.prefabPath);
            if (!prefab) {
                console.error(`Failed to load prefab: ${entityData.prefabPath}`);
                return;
            }
            
            // Instantiate entity
            const entity = instantiate(prefab);
            entity.setPosition(this.spawnerData!.position);
            
            // Add to scene
            this.node.scene?.addChild(entity);
            
            // Setup path following if needed
            const pathId = entityData.pathId || this.spawnerData!.pathId;
            if (pathId) {
                this.setupPathFollowing(entity, pathId);
            }
            
            // Trigger event
            this.onEntitySpawned?.(entity, this.spawnerData!);
            
            console.log(`Entity spawned: ${entityData.prefabPath} from ${this.spawnerData!.name}`);
        } catch (error) {
            console.error(`Failed to spawn entity: ${entityData.prefabPath}`, error);
        }
    }
    
    /**
     * Load prefab from resources
     */
    private async loadPrefab(prefabPath: string): Promise<Prefab | null> {
        return new Promise((resolve) => {
            resources.load(prefabPath, Prefab, (err, prefab) => {
                if (err) {
                    console.error(`Failed to load prefab: ${prefabPath}`, err);
                    resolve(null);
                } else {
                    resolve(prefab);
                }
            });
        });
    }
    
    /**
     * Setup path following for spawned entity
     */
    private setupPathFollowing(entity: Node, pathId: string): void {
        // This will be implemented when PathManager is integrated
        // For now, just add the component
        const pathFollower = entity.addComponent(PathFollower);
        pathFollower.autoStart = true;

        // TODO: Get path from PathManager and set it
        console.log(`Path following setup for entity on path: ${pathId}`);
    }

    /**
     * End current wave
     */
    private endWave(wave: Wave): void {
        this.isWaveActive = false;
        this.onWaveCompleted?.(wave, this.spawnerData!);

        console.log(`Wave completed: ${wave.name} on spawner ${this.spawnerData!.name}`);

        // Move to next wave
        this.currentWaveIndex++;

        // Check if all waves completed
        if (this.currentWaveIndex >= this.spawnerData!.waves.length) {
            this.completeSpawner();
        }
    }

    /**
     * Complete spawner (all waves finished)
     */
    private completeSpawner(): void {
        this.currentState = SpawnerState.Completed;
        this.onSpawnerCompleted?.(this.spawnerData!);

        console.log(`Spawner completed: ${this.spawnerData!.name}`);
    }

    // Getters
    public getState(): SpawnerState {
        return this.currentState;
    }

    public getCurrentWaveIndex(): number {
        return this.currentWaveIndex;
    }

    public getTotalSpawned(): number {
        return this.totalSpawned;
    }

    public isActive(): boolean {
        return this.currentState === SpawnerState.Active;
    }
}
