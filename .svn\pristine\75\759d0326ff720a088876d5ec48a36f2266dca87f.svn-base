import { _decorator, Component, Node, Sprite<PERSON>rame, Sprite, Vec3, find, Collider2D, Contact2DType, IPhysics2DContact, log, ProgressB<PERSON>, director, AudioSource } from 'cc';
import { GameFactory } from './factroy/GameFactory';
import { Global } from './Global';
import { PersistNode } from './PersistNode';
import { Player } from './Player';
const { ccclass, property } = _decorator;

@ccclass('EnemyBullet')
export class EnemyBullet extends Component {

    enemyBulletType: string = null;

    curPos: Vec3 = null;

    enemyBullet1MoveSpeed: number = 0;    //敌机子弹1的移动速度

    enemyBullet2MoveSpeed: number = 0;    //敌机子弹2的移动速度

    enemyBullet1ReduceBlood: number = 0;        //当被敌机子弹一打中后掉多少血

    enemyBullet2ReduceBlood: number = 0;        //当被敌机子弹二打中后掉多少血

    enemyBulletFactory: GameFactory = null;

    persistNode: Node = null;

    isLeft: boolean = true;
    

    onLoad() {
        this.persistNode = find("PersistNode");
        this.enemyBulletFactory = this.persistNode.getComponent(PersistNode).enemyBulletFactory;

        this.enemyBullet1MoveSpeed = this.persistNode.getComponent(PersistNode).enemyBullet1MoveSpeed;
        this.enemyBullet2MoveSpeed = this.persistNode.getComponent(PersistNode).enemyBullet2MoveSpeed;

        this.enemyBullet1ReduceBlood = this.persistNode.getComponent(PersistNode).enemyBullet1ReduceBlood;
        this.enemyBullet2ReduceBlood = this.persistNode.getComponent(PersistNode).enemyBullet2ReduceBlood;


        let collider = this.node.getComponent(Collider2D);

        if (collider) {
            collider.on(Contact2DType.BEGIN_CONTACT, this.onBeginContact, this);
        }
    }

    onBeginContact(selfCollider: Collider2D, otherCollider: Collider2D, contact: IPhysics2DContact | null) {
        //判断被哪种敌机子弹击中
        if (this.enemyBulletType == Global.ENEMY_BULLET_1) {
            otherCollider.getComponent(Player).planeBlood -= this.enemyBullet1ReduceBlood;
        } else {
            otherCollider.getComponent(Player).planeBlood -= this.enemyBullet2ReduceBlood;
        }

        //更新血条
        otherCollider.node.getChildByName("Blood").getComponent(ProgressBar).progress = otherCollider.getComponent(Player).planeBlood / otherCollider.getComponent(Player).planeTotalBlood;
        this.enemyBulletFactory.recycleProduct(this.node);

        if (otherCollider.getComponent(Player).planeBlood <= 0) {
            director.loadScene("Main");
            this.persistNode.getComponent(AudioSource).playOneShot(this.persistNode.getComponent(PersistNode).gameOverAudioClip);
        }
    }

    /**
     * 初始化enemyBullet
     */
     init(enemyBulletType: string, spriteFrame: SpriteFrame) {
        this.enemyBulletType = enemyBulletType;
        this.node.getComponent(Sprite).spriteFrame = spriteFrame;
    }

    update(deltaTime: number) {
        if (this.enemyBulletType == Global.ENEMY_BULLET_1) {
            this.enemyBullet1Move(deltaTime);
        } else if(this.enemyBulletType == Global.ENEMY_BULLET_2) {
            this.enemyBullet2Move(deltaTime);
        }
    }

    /**
     * 敌机子弹1移动
     */
    enemyBullet1Move(deltaTime: number){
        this.curPos = this.node.getPosition();
        this.curPos.y -= this.enemyBullet1MoveSpeed * deltaTime;

        //子弹一水平方向上移动
        if (this.isLeft) {
            this.curPos.x -= this.enemyBullet1MoveSpeed * deltaTime;
            if (this.curPos.x < -Global.WIDTH / 2) {
                this.isLeft = false;
            }
        } else {
            this.curPos.x += this.enemyBullet1MoveSpeed * deltaTime;
            if (this.curPos.x > Global.WIDTH / 2) {
                this.isLeft = true;
            }
        }

        this.node.setPosition(this.curPos);

        if (this.curPos.y < -Global.HEIGHT / 2) {
            this.enemyBulletFactory.recycleProduct(this.node);
        }
    }

    /**
     * 敌机子弹2移动
     */
    enemyBullet2Move(deltaTime: number) {
        this.curPos = this.node.getPosition();
        this.curPos.y -= this.enemyBullet2MoveSpeed * deltaTime;
        this.node.setPosition(this.curPos);

        if (this.curPos.y < -Global.HEIGHT / 2) {
            this.enemyBulletFactory.recycleProduct(this.node);
        }
    }
}

