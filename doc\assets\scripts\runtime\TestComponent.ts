import { _decorator, Component, Node } from 'cc';
const { ccclass, property } = _decorator;

/**
 * Simple test component to verify the system is working
 */
@ccclass('TestComponent')
export class TestComponent extends Component {
    @property
    public testMessage: string = 'Hello from TestComponent!';

    protected start(): void {
        console.log('TestComponent started:', this.testMessage);
    }

    protected update(deltaTime: number): void {
        // Test component update logic
        // This component is working correctly if you see the start message in console
    }
}
