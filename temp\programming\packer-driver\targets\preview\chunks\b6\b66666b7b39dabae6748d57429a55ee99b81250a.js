System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4", "__unresolved_5"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Component, game, Prefab, SpriteFrame, AudioClip, AnimFactory, EnemyBulletFactory, EnemyFactory, GoodsFactory, PlayerBulletFactory, _dec, _dec2, _dec3, _dec4, _dec5, _dec6, _dec7, _dec8, _dec9, _dec10, _dec11, _dec12, _dec13, _dec14, _dec15, _dec16, _dec17, _dec18, _dec19, _dec20, _dec21, _class, _class2, _descriptor, _descriptor2, _descriptor3, _descriptor4, _descriptor5, _descriptor6, _descriptor7, _descriptor8, _descriptor9, _descriptor10, _descriptor11, _descriptor12, _descriptor13, _descriptor14, _descriptor15, _descriptor16, _descriptor17, _descriptor18, _descriptor19, _descriptor20, _descriptor21, _descriptor22, _descriptor23, _descriptor24, _descriptor25, _descriptor26, _descriptor27, _descriptor28, _descriptor29, _descriptor30, _descriptor31, _descriptor32, _descriptor33, _descriptor34, _descriptor35, _descriptor36, _descriptor37, _descriptor38, _descriptor39, _descriptor40, _descriptor41, _descriptor42, _descriptor43, _descriptor44, _descriptor45, _descriptor46, _descriptor47, _descriptor48, _descriptor49, _descriptor50, _descriptor51, _descriptor52, _descriptor53, _crd, ccclass, property, PersistNode;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfAnimFactory(extras) {
    _reporterNs.report("AnimFactory", "./factroy/AnimFactory", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEnemyBulletFactory(extras) {
    _reporterNs.report("EnemyBulletFactory", "./factroy/EnemyBulletFactory", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEnemyFactory(extras) {
    _reporterNs.report("EnemyFactory", "./factroy/EnemyFactory", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameFactory(extras) {
    _reporterNs.report("GameFactory", "./factroy/GameFactory", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGoodsFactory(extras) {
    _reporterNs.report("GoodsFactory", "./factroy/GoodsFactory", _context.meta, extras);
  }

  function _reportPossibleCrUseOfPlayerBulletFactory(extras) {
    _reporterNs.report("PlayerBulletFactory", "./factroy/PlayerBulletFactory", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Component = _cc.Component;
      game = _cc.game;
      Prefab = _cc.Prefab;
      SpriteFrame = _cc.SpriteFrame;
      AudioClip = _cc.AudioClip;
    }, function (_unresolved_2) {
      AnimFactory = _unresolved_2.AnimFactory;
    }, function (_unresolved_3) {
      EnemyBulletFactory = _unresolved_3.EnemyBulletFactory;
    }, function (_unresolved_4) {
      EnemyFactory = _unresolved_4.EnemyFactory;
    }, function (_unresolved_5) {
      GoodsFactory = _unresolved_5.GoodsFactory;
    }, function (_unresolved_6) {
      PlayerBulletFactory = _unresolved_6.PlayerBulletFactory;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "402f7boooFHpYxJKYKRxJxT", "PersistNode", undefined);

      __checkObsolete__(['_decorator', 'Component', 'Node', 'game', 'Prefab', 'SpriteFrame', 'AudioClip']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("PersistNode", PersistNode = (_dec = ccclass('PersistNode'), _dec2 = property(Prefab), _dec3 = property(Prefab), _dec4 = property(Prefab), _dec5 = property(Prefab), _dec6 = property(Prefab), _dec7 = property(SpriteFrame), _dec8 = property(SpriteFrame), _dec9 = property(SpriteFrame), _dec10 = property(SpriteFrame), _dec11 = property(SpriteFrame), _dec12 = property(SpriteFrame), _dec13 = property(SpriteFrame), _dec14 = property(SpriteFrame), _dec15 = property(SpriteFrame), _dec16 = property(SpriteFrame), _dec17 = property(AudioClip), _dec18 = property(AudioClip), _dec19 = property(AudioClip), _dec20 = property(AudioClip), _dec21 = property(AudioClip), _dec(_class = (_class2 = class PersistNode extends Component {
        constructor() {
          super(...arguments);

          _initializerDefineProperty(this, "playerBulletPreb", _descriptor, this);

          //敌机子弹预制体
          _initializerDefineProperty(this, "enemyPreb", _descriptor2, this);

          //敌机预制体
          _initializerDefineProperty(this, "enemyBulletPreb", _descriptor3, this);

          //敌机子弹预制体
          _initializerDefineProperty(this, "animPreb", _descriptor4, this);

          //敌机爆炸动画预制体
          _initializerDefineProperty(this, "goodsPreb", _descriptor5, this);

          //物资预制体
          _initializerDefineProperty(this, "bloodGoods", _descriptor6, this);

          //加血道具图片
          _initializerDefineProperty(this, "lightGoods", _descriptor7, this);

          //激光道具图片
          _initializerDefineProperty(this, "missileGoods", _descriptor8, this);

          //导弹道具图片
          _initializerDefineProperty(this, "normalBullet", _descriptor9, this);

          //普通子弹图片
          _initializerDefineProperty(this, "lightBullet", _descriptor10, this);

          //激光子弹图片
          _initializerDefineProperty(this, "missileBullet", _descriptor11, this);

          //导弹子弹图片
          _initializerDefineProperty(this, "enemy1", _descriptor12, this);

          //enemy1图片
          _initializerDefineProperty(this, "enemy2", _descriptor13, this);

          //enemy2图片
          _initializerDefineProperty(this, "enemybullet1", _descriptor14, this);

          //enemy1子弹图片
          _initializerDefineProperty(this, "enemybullet2", _descriptor15, this);

          //enemy2子弹图片
          _initializerDefineProperty(this, "normalBulletSpeed", _descriptor16, this);

          //普通子弹发射时间间隔
          _initializerDefineProperty(this, "lightBulletSpeed", _descriptor17, this);

          //激光子弹发射时间间隔
          _initializerDefineProperty(this, "missileBulletSpeed", _descriptor18, this);

          //导弹子弹发射时间间隔
          _initializerDefineProperty(this, "normalBulletMoveSpeed", _descriptor19, this);

          //普通子弹移动速度
          _initializerDefineProperty(this, "lightBulletMoveSpeed", _descriptor20, this);

          //激光子弹移动速度
          _initializerDefineProperty(this, "missileBulletMoveSpeed", _descriptor21, this);

          //激光子弹移动速度
          _initializerDefineProperty(this, "enemyBullet1MoveSpeed", _descriptor22, this);

          //敌机子弹1的移动速度
          _initializerDefineProperty(this, "enemyBullet2MoveSpeed", _descriptor23, this);

          //敌机子弹1的移动速度
          _initializerDefineProperty(this, "enemy1MinProduceTime", _descriptor24, this);

          //enemy1出现时间的间隔的下限
          _initializerDefineProperty(this, "enemy1MaxProduceTime", _descriptor25, this);

          //enemy1出现时间的间隔的上限
          _initializerDefineProperty(this, "enemy2MinProduceTime", _descriptor26, this);

          //enemy2出现时间的间隔的下限
          _initializerDefineProperty(this, "enemy2MaxProduceTime", _descriptor27, this);

          //enemy2出现时间的间隔的上限
          _initializerDefineProperty(this, "bloodGoodsMinProduceTime", _descriptor28, this);

          //加血物资出现时间的间隔的下限
          _initializerDefineProperty(this, "bloodGoodsMaxProduceTime", _descriptor29, this);

          //加血物资出现时间的间隔的上限
          _initializerDefineProperty(this, "lightGoodsMinProduceTime", _descriptor30, this);

          //激光物资出现时间的间隔的下限
          _initializerDefineProperty(this, "lightGoodsMaxProduceTime", _descriptor31, this);

          //激光物资出现时间的间隔的上限
          _initializerDefineProperty(this, "missileGoodsMinProduceTime", _descriptor32, this);

          //导弹物资出现时间的间隔的下限
          _initializerDefineProperty(this, "missileGoodsMaxProduceTime", _descriptor33, this);

          //导弹物资出现时间的间隔的上限
          _initializerDefineProperty(this, "enemy1MoveSpeed", _descriptor34, this);

          //敌机1的移动速度
          _initializerDefineProperty(this, "enemy2MoveSpeed", _descriptor35, this);

          //敌机1的移动速度
          _initializerDefineProperty(this, "enemy1ShootSpeed", _descriptor36, this);

          //敌机1发射子弹时间间隔
          _initializerDefineProperty(this, "enemy2ShootSpeed", _descriptor37, this);

          //敌机2发射子弹时间间隔
          _initializerDefineProperty(this, "planeTotalBlood", _descriptor38, this);

          //玩家总血量
          _initializerDefineProperty(this, "enemyTotalBlood", _descriptor39, this);

          //敌机总血量
          _initializerDefineProperty(this, "enemyBullet1ReduceBlood", _descriptor40, this);

          //当被敌机子弹一打中后掉多少血
          _initializerDefineProperty(this, "enemyBullet2ReduceBlood", _descriptor41, this);

          //当被敌机子弹二打中后掉多少血
          _initializerDefineProperty(this, "playerNormalReduce", _descriptor42, this);

          //被普通子弹击中，敌机掉多少血
          _initializerDefineProperty(this, "playerLightReduce", _descriptor43, this);

          //被激光子弹击中，敌机掉多少血
          _initializerDefineProperty(this, "playerMissileReduce", _descriptor44, this);

          //导弹子弹击中，敌机掉多少血
          _initializerDefineProperty(this, "enemyContactPlayerReduce", _descriptor45, this);

          //敌机碰到玩家，玩家掉多少血
          _initializerDefineProperty(this, "bloodGoodsMoveSpeed", _descriptor46, this);

          //加血物资移动速度
          _initializerDefineProperty(this, "lightGoodsMoveSpeed", _descriptor47, this);

          //激光物资移动速度
          _initializerDefineProperty(this, "missileGoodsMoveSpeed", _descriptor48, this);

          //导弹物资移动速度
          _initializerDefineProperty(this, "bulletAudioClip", _descriptor49, this);

          //普通子弹声音
          _initializerDefineProperty(this, "lightAudioClip", _descriptor50, this);

          //激光子弹声音
          _initializerDefineProperty(this, "missileAudioClip", _descriptor51, this);

          //导弹子弹声音
          _initializerDefineProperty(this, "boomAudioClip", _descriptor52, this);

          //敌机爆炸声音
          _initializerDefineProperty(this, "gameOverAudioClip", _descriptor53, this);

          //游戏结束声音
          this.playerBulletFactory = null;
          //玩家子弹工厂
          this.enemyFactory = null;
          //敌机工厂
          this.enemyBulletFactory = null;
          //敌机子弹工厂
          this.goodsFactory = null;
          //敌机子弹工厂
          this.animFactory = null;
        }

        //动画工厂
        onLoad() {
          game.addPersistRootNode(this.node); //将PersistNode设置为常驻节点

          this.playerBulletFactory = new (_crd && PlayerBulletFactory === void 0 ? (_reportPossibleCrUseOfPlayerBulletFactory({
            error: Error()
          }), PlayerBulletFactory) : PlayerBulletFactory)(); //把玩家子弹工厂建在持久节点上

          this.enemyFactory = new (_crd && EnemyFactory === void 0 ? (_reportPossibleCrUseOfEnemyFactory({
            error: Error()
          }), EnemyFactory) : EnemyFactory)(); //把敌机工厂建在持久节点上

          this.enemyBulletFactory = new (_crd && EnemyBulletFactory === void 0 ? (_reportPossibleCrUseOfEnemyBulletFactory({
            error: Error()
          }), EnemyBulletFactory) : EnemyBulletFactory)(); //把敌机子弹工厂建在持久节点上

          this.goodsFactory = new (_crd && GoodsFactory === void 0 ? (_reportPossibleCrUseOfGoodsFactory({
            error: Error()
          }), GoodsFactory) : GoodsFactory)(); //把物资工厂建在持久节点上

          this.animFactory = new (_crd && AnimFactory === void 0 ? (_reportPossibleCrUseOfAnimFactory({
            error: Error()
          }), AnimFactory) : AnimFactory)();
        }

      }, (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "playerBulletPreb", [_dec2], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor2 = _applyDecoratedDescriptor(_class2.prototype, "enemyPreb", [_dec3], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor3 = _applyDecoratedDescriptor(_class2.prototype, "enemyBulletPreb", [_dec4], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor4 = _applyDecoratedDescriptor(_class2.prototype, "animPreb", [_dec5], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor5 = _applyDecoratedDescriptor(_class2.prototype, "goodsPreb", [_dec6], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor6 = _applyDecoratedDescriptor(_class2.prototype, "bloodGoods", [_dec7], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor7 = _applyDecoratedDescriptor(_class2.prototype, "lightGoods", [_dec8], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor8 = _applyDecoratedDescriptor(_class2.prototype, "missileGoods", [_dec9], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor9 = _applyDecoratedDescriptor(_class2.prototype, "normalBullet", [_dec10], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor10 = _applyDecoratedDescriptor(_class2.prototype, "lightBullet", [_dec11], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor11 = _applyDecoratedDescriptor(_class2.prototype, "missileBullet", [_dec12], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor12 = _applyDecoratedDescriptor(_class2.prototype, "enemy1", [_dec13], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor13 = _applyDecoratedDescriptor(_class2.prototype, "enemy2", [_dec14], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor14 = _applyDecoratedDescriptor(_class2.prototype, "enemybullet1", [_dec15], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor15 = _applyDecoratedDescriptor(_class2.prototype, "enemybullet2", [_dec16], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor16 = _applyDecoratedDescriptor(_class2.prototype, "normalBulletSpeed", [property], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return 0;
        }
      }), _descriptor17 = _applyDecoratedDescriptor(_class2.prototype, "lightBulletSpeed", [property], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return 0;
        }
      }), _descriptor18 = _applyDecoratedDescriptor(_class2.prototype, "missileBulletSpeed", [property], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return 0;
        }
      }), _descriptor19 = _applyDecoratedDescriptor(_class2.prototype, "normalBulletMoveSpeed", [property], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return 0;
        }
      }), _descriptor20 = _applyDecoratedDescriptor(_class2.prototype, "lightBulletMoveSpeed", [property], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return 0;
        }
      }), _descriptor21 = _applyDecoratedDescriptor(_class2.prototype, "missileBulletMoveSpeed", [property], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return 0;
        }
      }), _descriptor22 = _applyDecoratedDescriptor(_class2.prototype, "enemyBullet1MoveSpeed", [property], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return 0;
        }
      }), _descriptor23 = _applyDecoratedDescriptor(_class2.prototype, "enemyBullet2MoveSpeed", [property], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return 0;
        }
      }), _descriptor24 = _applyDecoratedDescriptor(_class2.prototype, "enemy1MinProduceTime", [property], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return 0;
        }
      }), _descriptor25 = _applyDecoratedDescriptor(_class2.prototype, "enemy1MaxProduceTime", [property], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return 0;
        }
      }), _descriptor26 = _applyDecoratedDescriptor(_class2.prototype, "enemy2MinProduceTime", [property], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return 0;
        }
      }), _descriptor27 = _applyDecoratedDescriptor(_class2.prototype, "enemy2MaxProduceTime", [property], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return 0;
        }
      }), _descriptor28 = _applyDecoratedDescriptor(_class2.prototype, "bloodGoodsMinProduceTime", [property], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return 0;
        }
      }), _descriptor29 = _applyDecoratedDescriptor(_class2.prototype, "bloodGoodsMaxProduceTime", [property], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return 0;
        }
      }), _descriptor30 = _applyDecoratedDescriptor(_class2.prototype, "lightGoodsMinProduceTime", [property], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return 0;
        }
      }), _descriptor31 = _applyDecoratedDescriptor(_class2.prototype, "lightGoodsMaxProduceTime", [property], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return 0;
        }
      }), _descriptor32 = _applyDecoratedDescriptor(_class2.prototype, "missileGoodsMinProduceTime", [property], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return 0;
        }
      }), _descriptor33 = _applyDecoratedDescriptor(_class2.prototype, "missileGoodsMaxProduceTime", [property], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return 0;
        }
      }), _descriptor34 = _applyDecoratedDescriptor(_class2.prototype, "enemy1MoveSpeed", [property], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return 0;
        }
      }), _descriptor35 = _applyDecoratedDescriptor(_class2.prototype, "enemy2MoveSpeed", [property], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return 0;
        }
      }), _descriptor36 = _applyDecoratedDescriptor(_class2.prototype, "enemy1ShootSpeed", [property], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return 0;
        }
      }), _descriptor37 = _applyDecoratedDescriptor(_class2.prototype, "enemy2ShootSpeed", [property], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return 0;
        }
      }), _descriptor38 = _applyDecoratedDescriptor(_class2.prototype, "planeTotalBlood", [property], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return 0;
        }
      }), _descriptor39 = _applyDecoratedDescriptor(_class2.prototype, "enemyTotalBlood", [property], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return 0;
        }
      }), _descriptor40 = _applyDecoratedDescriptor(_class2.prototype, "enemyBullet1ReduceBlood", [property], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return 0;
        }
      }), _descriptor41 = _applyDecoratedDescriptor(_class2.prototype, "enemyBullet2ReduceBlood", [property], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return 0;
        }
      }), _descriptor42 = _applyDecoratedDescriptor(_class2.prototype, "playerNormalReduce", [property], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return 0;
        }
      }), _descriptor43 = _applyDecoratedDescriptor(_class2.prototype, "playerLightReduce", [property], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return 0;
        }
      }), _descriptor44 = _applyDecoratedDescriptor(_class2.prototype, "playerMissileReduce", [property], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return 0;
        }
      }), _descriptor45 = _applyDecoratedDescriptor(_class2.prototype, "enemyContactPlayerReduce", [property], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return 0;
        }
      }), _descriptor46 = _applyDecoratedDescriptor(_class2.prototype, "bloodGoodsMoveSpeed", [property], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return 0;
        }
      }), _descriptor47 = _applyDecoratedDescriptor(_class2.prototype, "lightGoodsMoveSpeed", [property], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return 0;
        }
      }), _descriptor48 = _applyDecoratedDescriptor(_class2.prototype, "missileGoodsMoveSpeed", [property], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return 0;
        }
      }), _descriptor49 = _applyDecoratedDescriptor(_class2.prototype, "bulletAudioClip", [_dec17], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor50 = _applyDecoratedDescriptor(_class2.prototype, "lightAudioClip", [_dec18], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor51 = _applyDecoratedDescriptor(_class2.prototype, "missileAudioClip", [_dec19], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor52 = _applyDecoratedDescriptor(_class2.prototype, "boomAudioClip", [_dec20], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor53 = _applyDecoratedDescriptor(_class2.prototype, "gameOverAudioClip", [_dec21], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      })), _class2)) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=b66666b7b39dabae6748d57429a55ee99b81250a.js.map