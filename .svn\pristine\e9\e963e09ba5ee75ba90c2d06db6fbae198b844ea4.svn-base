# Level Editor for a vertical scrolling airforce fighting game.

## Core concepts

### Datas

#### Level

A level contains all the necessary components for this game.
It can be serialized and deserialized to and from a JSON file.

It should be stored under a folder like 'assets/levels/1.json'.

#### SubLevel

A sublevel is a part of a level. It's responsible for:
  - backgrounds setup
  - how enemies spawn
  - how power-ups spawn
  - can have or have not an entry point and an exit point, of which we can configure things like how plane should enter and exit the sublevel, popup a UI for user to select cards, etc.

#### Background(or Map)

A background is a set of textures that can be scrolled vertically. It can be configured to loop or not.
We should have a set of predefined layers for designers to easily configure the background

Layer definition example: BG_VeryFar, BG_Far, BG_Mid, BG_Close, BG_VeryClose, Player, FG_VeryClose, FG_Close.

Textures in the same layer should be connected vertically for seamless scrolling.

#### Spawner

A spawner is responsible for spawning any Prefab or Entity by Id or path(Let's use path first, later I might need to change into Id).
It should have a list of Prefab or Entity to configure into, each with a weight for randomly spawning, also with a path to spawn from.
It should be able to configuer the number of entities to spawn.
It contains a list of paths for entities to select from.
It can have different spawn types: sequential, random, static.
  - Sequential: spawn from the first entity or prefab to the last.
  - Random: spawn from any entity or prefab randomly.
  - Static: spawn from a specific entity or prefab only.

#### Wave

A wave contains a list of spawners to configure into. It should have a start time and an end time.
For editing purpose, we should be able to configure either the spawn time of each spawner in the wave, or set a delay and interval for the spawners.

#### Path

A path is a list of points in the scene. It can be bezier curve or straight line.
we can add points to the path, move points, delete points, to edit the path.
For each point we can set the speed when entity go through this point. So through out the path, speed are lerped between points and pass to the entity.
For each point we should also be able to set the rotation for entity to face when passing through this point.

#### Events

I think we need the ability to configure certain events during this Level. Like the Entry and Exit events of a SubLevel. And other events like Boss entering and Boss died. Events can be triggered by certain time, or by certain pre-defined conditions.
The event can have a list of event actions, each event action can have a delay, and can be easily extended to support more actions.
Some event actions I have in mind:
  - Play animation on certain entity 
  - Play sound
  - Play a timeline
  - Play an effect, or detroy an effect
  - Show or Hide a UI.
  - etc...

### Editor

The editor should be able to edit the datas mentiond above. It should be able to select and load a level from a json file, It would be better to support what you see is what you get.

