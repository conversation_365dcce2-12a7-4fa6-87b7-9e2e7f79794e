{"version": 3, "sources": ["file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Player.ts"], "names": ["_decorator", "Component", "Node", "Vec3", "v3", "UITransform", "find", "AudioSource", "Global", "PersistNode", "ccclass", "property", "Player", "curPos", "normalBulletTimer", "lightBulletTimer", "missileBulletTimer", "normalBulletSpeed", "lightBulletSpeed", "missileBulletSpeed", "planeBlood", "planeTotalBlood", "persistNode", "isShootLight", "isShootMissile", "playerBulletFactory", "onLoad", "node", "on", "EventType", "TOUCH_MOVE", "onTouchCallback", "getComponent", "update", "deltaTime", "shootNormalBullet", "shootLightBullet", "shootMissileBullet", "posBegin", "normalBullet", "createProduct", "NORMAL_BULLET", "parent", "<PERSON><PERSON><PERSON><PERSON>", "getPosition", "x", "y", "setPosition", "playOneShot", "bulletAudioClip", "LIGHT_BULLET", "lightAudioClip", "MISSILE_BULLET", "missileAudioClip", "event", "location", "getLocation", "convertToNodeSpaceAR", "onDisable", "off"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,I,OAAAA,I;AAAwBC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,E,OAAAA,E;AAAIC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,W,OAAAA,W;;AAE5EC,MAAAA,M,iBAAAA,M;;AACAC,MAAAA,W,iBAAAA,W;;;;;;;;;OACH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBX,U;;wBAGjBY,M,WADZF,OAAO,CAAC,QAAD,C,gBAAR,MACaE,MADb,SAC4BX,SAD5B,CACsC;AAAA;AAAA;AAAA,eAElCY,MAFkC,GAEnB,IAFmB;AAEZ;AAFY,eAIlCC,iBAJkC,GAIN,CAJM;AAIF;AAJE,eAMlCC,gBANkC,GAMP,CANO;AAMF;AANE,eAQlCC,kBARkC,GAQL,CARK;AAQA;AARA,eAUlCC,iBAVkC,GAUN,CAVM;AAUD;AAVC,eAYlCC,gBAZkC,GAYP,CAZO;AAYF;AAZE,eAclCC,kBAdkC,GAcL,CAdK;AAcA;AAdA,eAgBlCC,UAhBkC,GAgBb,CAhBa;AAgBL;AAhBK,eAkBlCC,eAlBkC,GAkBR,CAlBQ;AAkBJ;AAlBI,eAoBlCC,WApBkC,GAoBd,IApBc;AAoBN;AApBM,eAsBlCC,YAtBkC,GAsBV,KAtBU;AAsBD;AAtBC,eAwBlCC,cAxBkC,GAwBR,KAxBQ;AAwBE;AAxBF,eA0BlCC,mBA1BkC,GA0BC,IA1BD;AAAA;;AA8BlCC,QAAAA,MAAM,GAAG;AACL,eAAKC,IAAL,CAAUC,EAAV,CAAa1B,IAAI,CAAC2B,SAAL,CAAeC,UAA5B,EAAwC,KAAKC,eAA7C,EAA8D,IAA9D,EADK,CACiE;;AACtE,eAAKT,WAAL,GAAmBhB,IAAI,CAAC,aAAD,CAAvB;AACA,eAAKmB,mBAAL,GAA2B,KAAKH,WAAL,CAAiBU,YAAjB;AAAA;AAAA,0CAA2CP,mBAAtE,CAHK,CAGuF;AAE5F;;AACA,eAAKR,iBAAL,GAAyB,KAAKK,WAAL,CAAiBU,YAAjB;AAAA;AAAA,0CAA2Cf,iBAApE;AACA,eAAKC,gBAAL,GAAwB,KAAKI,WAAL,CAAiBU,YAAjB;AAAA;AAAA,0CAA2Cd,gBAAnE;AACA,eAAKC,kBAAL,GAA0B,KAAKG,WAAL,CAAiBU,YAAjB;AAAA;AAAA,0CAA2Cb,kBAArE;AAEA,eAAKE,eAAL,GAAuB,KAAKC,WAAL,CAAiBU,YAAjB;AAAA;AAAA,0CAA2CX,eAAlE;AACA,eAAKD,UAAL,GAAkB,KAAKC,eAAvB;AACH;;AAEDY,QAAAA,MAAM,CAACC,SAAD,EAAoB;AAEtB;AACA,eAAKpB,iBAAL,IAA0BoB,SAA1B;;AACA,cAAI,KAAKpB,iBAAL,GAAyB,KAAKG,iBAAlC,EAAqD;AACjD,iBAAKkB,iBAAL;AACA,iBAAKrB,iBAAL,GAAyB,CAAzB;AACH;;AAED,cAAI,KAAKS,YAAT,EAAuB;AACnB;AACA,iBAAKR,gBAAL,IAAyBmB,SAAzB;;AACA,gBAAI,KAAKnB,gBAAL,GAAwB,KAAKG,gBAAjC,EAAmD;AAC/C,mBAAKkB,gBAAL;AACA,mBAAKrB,gBAAL,GAAwB,CAAxB;AACH;AACJ;;AAED,cAAI,KAAKS,cAAT,EAAyB;AACrB;AACA,iBAAKR,kBAAL,IAA2BkB,SAA3B;;AACA,gBAAI,KAAKlB,kBAAL,GAA0B,KAAKG,kBAAnC,EAAuD;AACnD,mBAAKkB,kBAAL;AACA,mBAAKrB,kBAAL,GAA0B,CAA1B;AACH;AACJ;AACJ;AAED;AACJ;AACA;;;AACImB,QAAAA,iBAAiB,GAAG;AAChB,cAAIG,QAAc,GAAG,IAAInC,IAAJ,EAArB,CADgB,CACoB;;AACpC,cAAIoC,YAAkB,GAAG,IAAzB;AAEAA,UAAAA,YAAY,GAAG,KAAKd,mBAAL,CAAyBe,aAAzB,CAAuC;AAAA;AAAA,gCAAOC,aAA9C,CAAf,CAJgB,CAI8D;;AAC9E,eAAKd,IAAL,CAAUe,MAAV,CAAiBC,QAAjB,CAA0BJ,YAA1B,EALgB,CAKgC;;AAEhD,eAAK1B,MAAL,GAAc,KAAKc,IAAL,CAAUiB,WAAV,EAAd,CAPgB,CAO4B;;AAC5CN,UAAAA,QAAQ,CAACO,CAAT,GAAa,KAAKhC,MAAL,CAAYgC,CAAzB;AACAP,UAAAA,QAAQ,CAACQ,CAAT,GAAa,KAAKjC,MAAL,CAAYiC,CAAZ,GAAgB,EAA7B,CATgB,CASwB;;AACxCP,UAAAA,YAAY,CAACQ,WAAb,CAAyBT,QAAzB;AACA,eAAKhB,WAAL,CAAiBU,YAAjB,CAA8BzB,WAA9B,EAA2CyC,WAA3C,CAAuD,KAAK1B,WAAL,CAAiBU,YAAjB;AAAA;AAAA,0CAA2CiB,eAAlG;AACH;AAED;AACJ;AACA;;;AACKb,QAAAA,gBAAgB,GAAG;AAChB,cAAIE,QAAc,GAAG,IAAInC,IAAJ,EAArB,CADgB,CACoB;;AACpC,cAAIoC,YAAkB,GAAG,IAAzB,CAFgB,CAIhB;;AACAA,UAAAA,YAAY,GAAG,KAAKd,mBAAL,CAAyBe,aAAzB,CAAuC;AAAA;AAAA,gCAAOU,YAA9C,CAAf,CALgB,CAK6D;;AAC7E,eAAKvB,IAAL,CAAUe,MAAV,CAAiBC,QAAjB,CAA0BJ,YAA1B,EANgB,CAMgC;;AAEhD,eAAK1B,MAAL,GAAc,KAAKc,IAAL,CAAUiB,WAAV,EAAd,CARgB,CAQ4B;;AAC5CN,UAAAA,QAAQ,CAACO,CAAT,GAAa,KAAKhC,MAAL,CAAYgC,CAAZ,GAAgB,EAA7B;AACAP,UAAAA,QAAQ,CAACQ,CAAT,GAAa,KAAKjC,MAAL,CAAYiC,CAAZ,GAAgB,EAA7B,CAVgB,CAUwB;;AACxCP,UAAAA,YAAY,CAACQ,WAAb,CAAyBT,QAAzB,EAXgB,CAahB;;AACAC,UAAAA,YAAY,GAAG,KAAKd,mBAAL,CAAyBe,aAAzB,CAAuC;AAAA;AAAA,gCAAOU,YAA9C,CAAf,CAdgB,CAc6D;;AAC7E,eAAKvB,IAAL,CAAUe,MAAV,CAAiBC,QAAjB,CAA0BJ,YAA1B,EAfgB,CAegC;;AAEhD,eAAK1B,MAAL,GAAc,KAAKc,IAAL,CAAUiB,WAAV,EAAd,CAjBgB,CAiB4B;;AAC5CN,UAAAA,QAAQ,CAACO,CAAT,GAAa,KAAKhC,MAAL,CAAYgC,CAAZ,GAAgB,EAA7B;AACAP,UAAAA,QAAQ,CAACQ,CAAT,GAAa,KAAKjC,MAAL,CAAYiC,CAAZ,GAAgB,EAA7B,CAnBgB,CAmBwB;;AACxCP,UAAAA,YAAY,CAACQ,WAAb,CAAyBT,QAAzB;AAEA,eAAKhB,WAAL,CAAiBU,YAAjB,CAA8BzB,WAA9B,EAA2CyC,WAA3C,CAAuD,KAAK1B,WAAL,CAAiBU,YAAjB;AAAA;AAAA,0CAA2CmB,cAAlG;AACH;AAED;AACJ;AACA;;;AACKd,QAAAA,kBAAkB,GAAG;AAClB,cAAIC,QAAc,GAAG,IAAInC,IAAJ,EAArB,CADkB,CACkB;;AACpC,cAAIoC,YAAkB,GAAG,IAAzB;AAEAA,UAAAA,YAAY,GAAG,KAAKd,mBAAL,CAAyBe,aAAzB,CAAuC;AAAA;AAAA,gCAAOY,cAA9C,CAAf,CAJkB,CAI6D;;AAC/E,eAAKzB,IAAL,CAAUe,MAAV,CAAiBC,QAAjB,CAA0BJ,YAA1B,EALkB,CAK8B;;AAEhD,eAAK1B,MAAL,GAAc,KAAKc,IAAL,CAAUiB,WAAV,EAAd,CAPkB,CAO0B;;AAC5CN,UAAAA,QAAQ,CAACO,CAAT,GAAa,KAAKhC,MAAL,CAAYgC,CAAzB;AACAP,UAAAA,QAAQ,CAACQ,CAAT,GAAa,KAAKjC,MAAL,CAAYiC,CAAZ,GAAgB,EAA7B,CATkB,CASsB;;AACxCP,UAAAA,YAAY,CAACQ,WAAb,CAAyBT,QAAzB;AAEA,eAAKhB,WAAL,CAAiBU,YAAjB,CAA8BzB,WAA9B,EAA2CyC,WAA3C,CAAuD,KAAK1B,WAAL,CAAiBU,YAAjB;AAAA;AAAA,0CAA2CqB,gBAAlG;AACH;AAED;AACJ;AACA;AACA;;;AACItB,QAAAA,eAAe,CAACuB,KAAD,EAAoB;AAC/B,cAAIC,QAAc,GAAGD,KAAK,CAACE,WAAN,EAArB,CAD+B,CACa;;AAC5C,eAAK3C,MAAL,GAAc,KAAKc,IAAL,CAAUe,MAAV,CAAiBV,YAAjB,CAA8B3B,WAA9B,EAA2CoD,oBAA3C,CAAgErD,EAAE,CAACmD,QAAQ,CAACV,CAAV,EAAaU,QAAQ,CAACT,CAAtB,EAAyB,CAAzB,CAAlE,CAAd,CAF+B,CAEiF;;AAChH,eAAKnB,IAAL,CAAUoB,WAAV,CAAsB,KAAKlC,MAA3B;AACH;;AAED6C,QAAAA,SAAS,GAAG;AACR,eAAK/B,IAAL,CAAUgC,GAAV,CAAczD,IAAI,CAAC2B,SAAL,CAAeC,UAA7B,EAAyC,KAAKC,eAA9C,EAA+D,IAA/D,EADQ,CAC+D;AAC1E;;AAnJiC,O", "sourcesContent": ["import { _decorator, Component, Node, EventTouch, Vec2, Vec3, v3, UITransform, find, AudioSource } from 'cc';\nimport { GameFactory } from './factroy/GameFactory';\nimport { Global } from './Global';\nimport { PersistNode } from './PersistNode';\nconst { ccclass, property } = _decorator;\n\n@ccclass('Player')\nexport class Player extends Component {\n\n    curPos: Vec3 = null;  //当前player位置\n\n    normalBulletTimer: number = 0;  //普通子弹定时器\n\n    lightBulletTimer: number = 0;   //激光子弹定时器\n\n    missileBulletTimer: number = 0;   //导弹子弹定时器\n\n    normalBulletSpeed: number = 0;   //普通子弹发射时间间隔\n\n    lightBulletSpeed: number = 0;   //激光子弹发射时间间隔\n\n    missileBulletSpeed: number = 0;   //导弹子弹发射时间间隔\n\n    planeBlood: number = 0;      //飞机当前血量\n\n    planeTotalBlood: number = 0;  //飞机总血量\n\n    persistNode: Node = null;   //得到常驻节点\n\n    isShootLight: boolean = false;   //是否发射激光\n\n    isShootMissile: boolean = false;    //是否发射导弹\n\n    playerBulletFactory: GameFactory = null;\n\n    \n\n    onLoad() {\n        this.node.on(Node.EventType.TOUCH_MOVE, this.onTouchCallback, this);  //开启节点触摸移动监听\n        this.persistNode = find(\"PersistNode\");\n        this.playerBulletFactory = this.persistNode.getComponent(PersistNode).playerBulletFactory;  //得到工厂对象\n\n        //得到面板上的速度\n        this.normalBulletSpeed = this.persistNode.getComponent(PersistNode).normalBulletSpeed;\n        this.lightBulletSpeed = this.persistNode.getComponent(PersistNode).lightBulletSpeed;\n        this.missileBulletSpeed = this.persistNode.getComponent(PersistNode).missileBulletSpeed;\n\n        this.planeTotalBlood = this.persistNode.getComponent(PersistNode).planeTotalBlood;\n        this.planeBlood = this.planeTotalBlood;\n    }\n\n    update(deltaTime: number) {\n\n        //发射普通子弹\n        this.normalBulletTimer += deltaTime;\n        if (this.normalBulletTimer > this.normalBulletSpeed) {\n            this.shootNormalBullet();\n            this.normalBulletTimer = 0;\n        }\n\n        if (this.isShootLight) {\n            //发射激光子弹\n            this.lightBulletTimer += deltaTime;\n            if (this.lightBulletTimer > this.lightBulletSpeed) {\n                this.shootLightBullet();\n                this.lightBulletTimer = 0;\n            }\n        }\n        \n        if (this.isShootMissile) {\n            //发射导弹子弹\n            this.missileBulletTimer += deltaTime;\n            if (this.missileBulletTimer > this.missileBulletSpeed) {\n                this.shootMissileBullet();\n                this.missileBulletTimer = 0;\n            }\n        }\n    }\n\n    /**\n     * 发射普通子弹\n     */\n    shootNormalBullet() {\n        let posBegin: Vec3 = new Vec3();    //定义子弹开始的位置\n        let normalBullet: Node = null;\n\n        normalBullet = this.playerBulletFactory.createProduct(Global.NORMAL_BULLET);  //制作子弹\n        this.node.parent.addChild(normalBullet);        //添加节点到画布\n\n        this.curPos = this.node.getPosition();      //得到飞机当前位置\n        posBegin.x = this.curPos.x; \n        posBegin.y = this.curPos.y + 50;        //设置到机头位置\n        normalBullet.setPosition(posBegin);\n        this.persistNode.getComponent(AudioSource).playOneShot(this.persistNode.getComponent(PersistNode).bulletAudioClip);\n    }\n\n    /**\n     * 发射激光子弹\n     */\n     shootLightBullet() {\n        let posBegin: Vec3 = new Vec3();    //定义子弹开始的位置\n        let normalBullet: Node = null;\n\n        //左边激光\n        normalBullet = this.playerBulletFactory.createProduct(Global.LIGHT_BULLET);  //制作激光子弹\n        this.node.parent.addChild(normalBullet);        //添加节点到画布\n\n        this.curPos = this.node.getPosition();      //得到飞机当前位置\n        posBegin.x = this.curPos.x - 50; \n        posBegin.y = this.curPos.y + 50;        //设置到机头位置\n        normalBullet.setPosition(posBegin);\n\n        //右边激光\n        normalBullet = this.playerBulletFactory.createProduct(Global.LIGHT_BULLET);  //制作激光子弹\n        this.node.parent.addChild(normalBullet);        //添加节点到画布\n\n        this.curPos = this.node.getPosition();      //得到飞机当前位置\n        posBegin.x = this.curPos.x + 50; \n        posBegin.y = this.curPos.y + 50;        //设置到机头位置\n        normalBullet.setPosition(posBegin);\n\n        this.persistNode.getComponent(AudioSource).playOneShot(this.persistNode.getComponent(PersistNode).lightAudioClip);\n    }\n\n    /**\n     * 发射导弹子弹\n     */\n     shootMissileBullet() {\n        let posBegin: Vec3 = new Vec3();    //定义子弹开始的位置\n        let normalBullet: Node = null;\n\n        normalBullet = this.playerBulletFactory.createProduct(Global.MISSILE_BULLET);  //制作导弹子弹\n        this.node.parent.addChild(normalBullet);        //添加节点到画布\n\n        this.curPos = this.node.getPosition();      //得到飞机当前位置\n        posBegin.x = this.curPos.x; \n        posBegin.y = this.curPos.y + 50;        //设置到机头位置\n        normalBullet.setPosition(posBegin);\n\n        this.persistNode.getComponent(AudioSource).playOneShot(this.persistNode.getComponent(PersistNode).missileAudioClip);\n    }\n\n    /**\n     * 鼠标或者手指移动时候的回调\n     * @param event\n     */\n    onTouchCallback(event: EventTouch) {\n        let location: Vec2 = event.getLocation();   //得到手指鼠标位置,得到的是世界坐标\n        this.curPos = this.node.parent.getComponent(UITransform).convertToNodeSpaceAR(v3(location.x, location.y, 0));   //player当前位置设置为手指或者鼠标，并转化为局部坐标\n        this.node.setPosition(this.curPos);\n    }\n\n    onDisable() {\n        this.node.off(Node.EventType.TOUCH_MOVE, this.onTouchCallback, this);  //取消监听\n    }\n}\n\n"]}