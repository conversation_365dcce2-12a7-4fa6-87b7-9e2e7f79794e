System.register(["cc"], function (_export, _context) {
  "use strict";

  var _cclegacy, WorldInitializeData, _crd, GameMode, DifficultyLevel;

  _export("WorldInitializeData", void 0);

  return {
    setters: [function (_cc) {
      _cclegacy = _cc.cclegacy;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "0c36cYE0N5Nx5/HEygDu5v5", "WorldInitializeData", undefined);

      /**
       * Enumeration for different game modes
       */
      _export("GameMode", GameMode = /*#__PURE__*/function (GameMode) {
        GameMode["STORY"] = "story";
        GameMode["ENDLESS"] = "endless";
        GameMode["CHALLENGE"] = "challenge";
        GameMode["TUTORIAL"] = "tutorial";
        GameMode["CUSTOM"] = "custom";
        return GameMode;
      }({}));
      /**
       * Enumeration for difficulty levels
       */


      _export("DifficultyLevel", DifficultyLevel = /*#__PURE__*/function (DifficultyLevel) {
        DifficultyLevel["EASY"] = "easy";
        DifficultyLevel["NORMAL"] = "normal";
        DifficultyLevel["HARD"] = "hard";
        DifficultyLevel["EXPERT"] = "expert";
        return DifficultyLevel;
      }({}));
      /**
       * Configuration for world physics
       */

      /**
       * Configuration for world rendering
       */

      /**
       * Player configuration for world initialization
       */

      /**
       * Level configuration for world initialization
       */

      /**
       * Debug flags configuration
       */

      /**
       * Interface for world initialization data
       * All members are readonly to ensure immutability
       */


      /**
       * Implementation of world initialization data
       * Contains all necessary parameters to set up a complete game session
       */
      _export("WorldInitializeData", WorldInitializeData = class WorldInitializeData {
        /**
         * Create a new WorldInitializeData with default values
         */
        constructor(config) {
          var _config$modeId, _config$levelId, _config$randomSeed, _config$difficulty, _config$physicsConfig, _config$physicsConfig2, _config$physicsConfig3, _config$physicsConfig4, _config$physicsConfig5, _config$physicsConfig6, _config$physicsConfig7, _config$physicsConfig8, _config$renderConfig$, _config$renderConfig, _config$renderConfig$2, _config$renderConfig2, _config$renderConfig$3, _config$renderConfig3, _config$renderConfig$4, _config$renderConfig4, _config$playerConfig$, _config$playerConfig, _config$playerConfig$2, _config$playerConfig2, _config$playerConfig$3, _config$playerConfig3, _config$playerConfig$4, _config$playerConfig4, _config$playerConfig$5, _config$playerConfig5, _config$levelConfig$b, _config$levelConfig, _config$levelConfig$b2, _config$levelConfig2, _config$levelConfig$e, _config$levelConfig3, _config$levelConfig$b3, _config$levelConfig4, _config$levelConfig$b4, _config$levelConfig5, _config$levelConfig$b5, _config$levelConfig6, _config$levelConfig$b6, _config$levelConfig7, _config$customData, _config$debugFlags$en, _config$debugFlags, _config$debugFlags$sh, _config$debugFlags2, _config$debugFlags$sh2, _config$debugFlags3, _config$debugFlags$lo, _config$debugFlags4;

          /** Unique identifier for the game mode */
          this.modeId = void 0;

          /** Unique identifier for the level */
          this.levelId = void 0;

          /** Random seed for deterministic gameplay */
          this.randomSeed = void 0;

          /** Difficulty level for the game session */
          this.difficulty = void 0;

          /** Physics configuration for the world */
          this.physicsConfig = void 0;

          /** Rendering configuration for the world */
          this.renderConfig = void 0;

          /** Player configuration */
          this.playerConfig = void 0;

          /** Level configuration */
          this.levelConfig = void 0;

          /** Custom data for specific game modes or levels */
          this.customData = void 0;

          /** Debug flags for development */
          this.debugFlags = void 0;
          // Set default values
          this.modeId = (_config$modeId = config == null ? void 0 : config.modeId) != null ? _config$modeId : GameMode.STORY;
          this.levelId = (_config$levelId = config == null ? void 0 : config.levelId) != null ? _config$levelId : "";
          this.randomSeed = (_config$randomSeed = config == null ? void 0 : config.randomSeed) != null ? _config$randomSeed : Date.now();
          this.difficulty = (_config$difficulty = config == null ? void 0 : config.difficulty) != null ? _config$difficulty : DifficultyLevel.NORMAL; // Set default physics config

          this.physicsConfig = {
            gravity: (_config$physicsConfig = config == null || (_config$physicsConfig2 = config.physicsConfig) == null ? void 0 : _config$physicsConfig2.gravity) != null ? _config$physicsConfig : -9.8,
            timeScale: (_config$physicsConfig3 = config == null || (_config$physicsConfig4 = config.physicsConfig) == null ? void 0 : _config$physicsConfig4.timeScale) != null ? _config$physicsConfig3 : 1.0,
            maxVelocity: (_config$physicsConfig5 = config == null || (_config$physicsConfig6 = config.physicsConfig) == null ? void 0 : _config$physicsConfig6.maxVelocity) != null ? _config$physicsConfig5 : 1000,
            enableCollision: (_config$physicsConfig7 = config == null || (_config$physicsConfig8 = config.physicsConfig) == null ? void 0 : _config$physicsConfig8.enableCollision) != null ? _config$physicsConfig7 : true
          }; // Set default render config

          this.renderConfig = {
            enableParticles: (_config$renderConfig$ = config == null || (_config$renderConfig = config.renderConfig) == null ? void 0 : _config$renderConfig.enableParticles) != null ? _config$renderConfig$ : true,
            maxParticles: (_config$renderConfig$2 = config == null || (_config$renderConfig2 = config.renderConfig) == null ? void 0 : _config$renderConfig2.maxParticles) != null ? _config$renderConfig$2 : 1000,
            enablePostProcessing: (_config$renderConfig$3 = config == null || (_config$renderConfig3 = config.renderConfig) == null ? void 0 : _config$renderConfig3.enablePostProcessing) != null ? _config$renderConfig$3 : true,
            renderScale: (_config$renderConfig$4 = config == null || (_config$renderConfig4 = config.renderConfig) == null ? void 0 : _config$renderConfig4.renderScale) != null ? _config$renderConfig$4 : 1.0
          }; // Set default player config

          this.playerConfig = {
            startPosition: {
              x: (_config$playerConfig$ = config == null || (_config$playerConfig = config.playerConfig) == null || (_config$playerConfig = _config$playerConfig.startPosition) == null ? void 0 : _config$playerConfig.x) != null ? _config$playerConfig$ : 0,
              y: (_config$playerConfig$2 = config == null || (_config$playerConfig2 = config.playerConfig) == null || (_config$playerConfig2 = _config$playerConfig2.startPosition) == null ? void 0 : _config$playerConfig2.y) != null ? _config$playerConfig$2 : 0
            },
            maxHealth: (_config$playerConfig$3 = config == null || (_config$playerConfig3 = config.playerConfig) == null ? void 0 : _config$playerConfig3.maxHealth) != null ? _config$playerConfig$3 : 100,
            startingWeapon: (_config$playerConfig$4 = config == null || (_config$playerConfig4 = config.playerConfig) == null ? void 0 : _config$playerConfig4.startingWeapon) != null ? _config$playerConfig$4 : "default",
            abilities: (_config$playerConfig$5 = config == null || (_config$playerConfig5 = config.playerConfig) == null ? void 0 : _config$playerConfig5.abilities) != null ? _config$playerConfig$5 : []
          }; // Set default level config

          this.levelConfig = {
            backgroundMusic: (_config$levelConfig$b = config == null || (_config$levelConfig = config.levelConfig) == null ? void 0 : _config$levelConfig.backgroundMusic) != null ? _config$levelConfig$b : "",
            backgroundImage: (_config$levelConfig$b2 = config == null || (_config$levelConfig2 = config.levelConfig) == null ? void 0 : _config$levelConfig2.backgroundImage) != null ? _config$levelConfig$b2 : "",
            environmentEffects: (_config$levelConfig$e = config == null || (_config$levelConfig3 = config.levelConfig) == null ? void 0 : _config$levelConfig3.environmentEffects) != null ? _config$levelConfig$e : [],
            boundaries: {
              minX: (_config$levelConfig$b3 = config == null || (_config$levelConfig4 = config.levelConfig) == null || (_config$levelConfig4 = _config$levelConfig4.boundaries) == null ? void 0 : _config$levelConfig4.minX) != null ? _config$levelConfig$b3 : -1000,
              maxX: (_config$levelConfig$b4 = config == null || (_config$levelConfig5 = config.levelConfig) == null || (_config$levelConfig5 = _config$levelConfig5.boundaries) == null ? void 0 : _config$levelConfig5.maxX) != null ? _config$levelConfig$b4 : 1000,
              minY: (_config$levelConfig$b5 = config == null || (_config$levelConfig6 = config.levelConfig) == null || (_config$levelConfig6 = _config$levelConfig6.boundaries) == null ? void 0 : _config$levelConfig6.minY) != null ? _config$levelConfig$b5 : -1000,
              maxY: (_config$levelConfig$b6 = config == null || (_config$levelConfig7 = config.levelConfig) == null || (_config$levelConfig7 = _config$levelConfig7.boundaries) == null ? void 0 : _config$levelConfig7.maxY) != null ? _config$levelConfig$b6 : 1000
            }
          }; // Set default custom data

          this.customData = (_config$customData = config == null ? void 0 : config.customData) != null ? _config$customData : {}; // Set default debug flags

          this.debugFlags = {
            enableDebugDraw: (_config$debugFlags$en = config == null || (_config$debugFlags = config.debugFlags) == null ? void 0 : _config$debugFlags.enableDebugDraw) != null ? _config$debugFlags$en : false,
            showCollisionBounds: (_config$debugFlags$sh = config == null || (_config$debugFlags2 = config.debugFlags) == null ? void 0 : _config$debugFlags2.showCollisionBounds) != null ? _config$debugFlags$sh : false,
            showPerformanceStats: (_config$debugFlags$sh2 = config == null || (_config$debugFlags3 = config.debugFlags) == null ? void 0 : _config$debugFlags3.showPerformanceStats) != null ? _config$debugFlags$sh2 : false,
            logSystemUpdates: (_config$debugFlags$lo = config == null || (_config$debugFlags4 = config.debugFlags) == null ? void 0 : _config$debugFlags4.logSystemUpdates) != null ? _config$debugFlags$lo : false
          };
        }
        /**
         * Create WorldInitializeData from a configuration object
         * @param config Partial configuration object
         * @returns New WorldInitializeData instance
         */


        static fromConfig(config) {
          return new WorldInitializeData(config);
        }
        /**
         * Validate the initialization data
         * @returns Array of validation errors, empty if valid
         */


        validate() {
          const errors = [];

          if (!this.levelId || this.levelId.trim() === "") {
            errors.push("Level ID is required");
          }

          if (this.randomSeed < 0) {
            errors.push("Random seed must be non-negative");
          }

          if (this.physicsConfig.timeScale <= 0) {
            errors.push("Physics time scale must be positive");
          }

          if (this.renderConfig.maxParticles < 0) {
            errors.push("Max particles must be non-negative");
          }

          if (this.playerConfig.maxHealth <= 0) {
            errors.push("Player max health must be positive");
          }

          return errors;
        }
        /**
         * Clone the initialization data
         * @returns Deep copy of the initialization data
         */


        clone() {
          return WorldInitializeData.fromConfig(JSON.parse(JSON.stringify(this)));
        }

      });

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=36aad766502eb63bb086247bef936a13974a4323.js.map