[{"__type__": "cc.Prefab", "_name": "Label", "_objFlags": 0, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "asyncLoadAssets": false, "persistent": false}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "_parent": null, "_children": [], "_active": true, "_components": [{"__id__": 2}, {"__id__": 4}], "_prefab": {"__id__": 6}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 42.26, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "", "__prefab": {"__id__": 3}}, {"__type__": "cc.CompPrefabInfo", "fileId": "c68UOAlNhN171Umca6yVvF"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_sharedMaterial": null, "_useOriginalSize": true, "_string": "label", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 20, "_fontSize": 20, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_cacheMode": 0, "_id": "", "__prefab": {"__id__": 5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "2frm37uaJHQr0AEEaYyM82"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "85tZs04QNDoJ8Zw+ovirTw"}]