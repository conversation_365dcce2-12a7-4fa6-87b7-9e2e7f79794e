/**
 * CrazyStorm 2.0 Integration for Cocos Creator
 * Performance Monitor
 * 
 * This system monitors performance metrics and provides optimization
 * recommendations for mobile devices.
 */

import { log, warn, sys } from 'cc';

/**
 * Performance metrics
 */
export interface PerformanceMetrics {
    fps: number;
    frameTime: number;
    particleCount: number;
    drawCalls: number;
    memoryUsage: number;
    cpuUsage: number;
    timestamp: number;
}

/**
 * Performance thresholds for different device tiers
 */
export interface PerformanceThresholds {
    minFPS: number;
    maxFrameTime: number;
    maxParticles: number;
    maxDrawCalls: number;
    maxMemoryMB: number;
}

/**
 * Device performance tier
 */
export enum DeviceTier {
    Low = 'Low',
    Medium = 'Medium',
    High = 'High',
    Unknown = 'Unknown'
}

/**
 * Performance optimization recommendation
 */
export interface OptimizationRecommendation {
    type: 'particles' | 'quality' | 'effects' | 'memory';
    severity: 'low' | 'medium' | 'high';
    message: string;
    action: string;
}

/**
 * Performance monitor class
 */
export class PerformanceMonitor {
    private static instance: PerformanceMonitor;
    
    private metrics: PerformanceMetrics[] = [];
    private maxHistorySize: number = 300; // 5 minutes at 60 FPS
    private updateInterval: number = 1000; // 1 second
    private lastUpdate: number = 0;
    private isMonitoring: boolean = false;
    
    // Performance tracking
    private frameCount: number = 0;
    private frameTimeSum: number = 0;
    private lastFrameTime: number = 0;
    
    // Device information
    private deviceTier: DeviceTier = DeviceTier.Unknown;
    private thresholds: PerformanceThresholds;
    
    // Callbacks
    private onPerformanceIssue: ((recommendations: OptimizationRecommendation[]) => void) | null = null;

    private constructor() {
        this.detectDeviceTier();
        this.setThresholds();
    }

    /**
     * Get singleton instance
     */
    public static getInstance(): PerformanceMonitor {
        if (!PerformanceMonitor.instance) {
            PerformanceMonitor.instance = new PerformanceMonitor();
        }
        return PerformanceMonitor.instance;
    }

    /**
     * Start monitoring
     */
    public startMonitoring(): void {
        this.isMonitoring = true;
        this.lastUpdate = Date.now();
        this.lastFrameTime = performance.now();
        log('PerformanceMonitor: Started monitoring');
    }

    /**
     * Stop monitoring
     */
    public stopMonitoring(): void {
        this.isMonitoring = false;
        log('PerformanceMonitor: Stopped monitoring');
    }

    /**
     * Update performance metrics (call this every frame)
     */
    public update(deltaTime: number, particleCount: number = 0, drawCalls: number = 0): void {
        if (!this.isMonitoring) {
            return;
        }

        const now = performance.now();
        const frameTime = now - this.lastFrameTime;
        this.lastFrameTime = now;

        this.frameCount++;
        this.frameTimeSum += frameTime;

        // Update metrics every second
        const currentTime = Date.now();
        if (currentTime - this.lastUpdate >= this.updateInterval) {
            this.recordMetrics(particleCount, drawCalls);
            this.lastUpdate = currentTime;
        }
    }

    /**
     * Get current performance metrics
     */
    public getCurrentMetrics(): PerformanceMetrics | null {
        if (this.metrics.length === 0) {
            return null;
        }
        return this.metrics[this.metrics.length - 1];
    }

    /**
     * Get average metrics over time period
     */
    public getAverageMetrics(seconds: number = 10): PerformanceMetrics | null {
        if (this.metrics.length === 0) {
            return null;
        }

        const cutoffTime = Date.now() - (seconds * 1000);
        const recentMetrics = this.metrics.filter(m => m.timestamp >= cutoffTime);
        
        if (recentMetrics.length === 0) {
            return null;
        }

        const sum = recentMetrics.reduce((acc, m) => ({
            fps: acc.fps + m.fps,
            frameTime: acc.frameTime + m.frameTime,
            particleCount: acc.particleCount + m.particleCount,
            drawCalls: acc.drawCalls + m.drawCalls,
            memoryUsage: acc.memoryUsage + m.memoryUsage,
            cpuUsage: acc.cpuUsage + m.cpuUsage,
            timestamp: 0
        }), {
            fps: 0, frameTime: 0, particleCount: 0, 
            drawCalls: 0, memoryUsage: 0, cpuUsage: 0, timestamp: 0
        });

        const count = recentMetrics.length;
        return {
            fps: sum.fps / count,
            frameTime: sum.frameTime / count,
            particleCount: sum.particleCount / count,
            drawCalls: sum.drawCalls / count,
            memoryUsage: sum.memoryUsage / count,
            cpuUsage: sum.cpuUsage / count,
            timestamp: Date.now()
        };
    }

    /**
     * Get device tier
     */
    public getDeviceTier(): DeviceTier {
        return this.deviceTier;
    }

    /**
     * Get performance thresholds
     */
    public getThresholds(): PerformanceThresholds {
        return { ...this.thresholds };
    }

    /**
     * Check for performance issues and get recommendations
     */
    public getOptimizationRecommendations(): OptimizationRecommendation[] {
        const current = this.getCurrentMetrics();
        if (!current) {
            return [];
        }

        const recommendations: OptimizationRecommendation[] = [];

        // Check FPS
        if (current.fps < this.thresholds.minFPS) {
            const severity = current.fps < this.thresholds.minFPS * 0.7 ? 'high' : 'medium';
            recommendations.push({
                type: 'quality',
                severity,
                message: `Low FPS detected: ${current.fps.toFixed(1)} (target: ${this.thresholds.minFPS})`,
                action: 'Reduce particle count or visual effects quality'
            });
        }

        // Check frame time
        if (current.frameTime > this.thresholds.maxFrameTime) {
            recommendations.push({
                type: 'effects',
                severity: 'medium',
                message: `High frame time: ${current.frameTime.toFixed(2)}ms (target: <${this.thresholds.maxFrameTime}ms)`,
                action: 'Optimize particle updates or reduce complexity'
            });
        }

        // Check particle count
        if (current.particleCount > this.thresholds.maxParticles) {
            const severity = current.particleCount > this.thresholds.maxParticles * 1.5 ? 'high' : 'medium';
            recommendations.push({
                type: 'particles',
                severity,
                message: `High particle count: ${current.particleCount} (target: <${this.thresholds.maxParticles})`,
                action: 'Reduce emitter rates or implement LOD system'
            });
        }

        // Check memory usage
        if (current.memoryUsage > this.thresholds.maxMemoryMB) {
            recommendations.push({
                type: 'memory',
                severity: 'medium',
                message: `High memory usage: ${current.memoryUsage.toFixed(1)}MB (target: <${this.thresholds.maxMemoryMB}MB)`,
                action: 'Clear unused resources or reduce texture sizes'
            });
        }

        return recommendations;
    }

    /**
     * Set performance issue callback
     */
    public setPerformanceIssueCallback(callback: (recommendations: OptimizationRecommendation[]) => void): void {
        this.onPerformanceIssue = callback;
    }

    /**
     * Get performance history
     */
    public getHistory(seconds: number = 60): PerformanceMetrics[] {
        const cutoffTime = Date.now() - (seconds * 1000);
        return this.metrics.filter(m => m.timestamp >= cutoffTime);
    }

    /**
     * Clear performance history
     */
    public clearHistory(): void {
        this.metrics = [];
        log('PerformanceMonitor: Cleared history');
    }

    /**
     * Export performance data
     */
    public exportData(): {
        deviceTier: DeviceTier;
        thresholds: PerformanceThresholds;
        metrics: PerformanceMetrics[];
        summary: {
            averageFPS: number;
            averageFrameTime: number;
            averageParticles: number;
            peakParticles: number;
            issueCount: number;
        };
    } {
        const summary = this.calculateSummary();
        return {
            deviceTier: this.deviceTier,
            thresholds: this.thresholds,
            metrics: [...this.metrics],
            summary
        };
    }

    // Private methods

    /**
     * Detect device performance tier
     */
    private detectDeviceTier(): void {
        // This is a simplified device detection
        // In a real implementation, you'd use more sophisticated detection
        
        if (sys.isMobile) {
            // Mobile device detection logic
            const platform = sys.platform;
            const memory = sys.getSafeAreaRect(); // Rough approximation
            
            // Very basic tier detection - you'd want more sophisticated logic
            if (platform === sys.Platform.ANDROID) {
                this.deviceTier = DeviceTier.Medium; // Default for Android
            } else if (platform === sys.Platform.IOS) {
                this.deviceTier = DeviceTier.High; // iOS devices generally perform better
            } else {
                this.deviceTier = DeviceTier.Low;
            }
        } else {
            // Desktop - assume high performance
            this.deviceTier = DeviceTier.High;
        }

        log(`PerformanceMonitor: Detected device tier: ${this.deviceTier}`);
    }

    /**
     * Set performance thresholds based on device tier
     */
    private setThresholds(): void {
        switch (this.deviceTier) {
            case DeviceTier.Low:
                this.thresholds = {
                    minFPS: 30,
                    maxFrameTime: 33,
                    maxParticles: 500,
                    maxDrawCalls: 50,
                    maxMemoryMB: 100
                };
                break;
            case DeviceTier.Medium:
                this.thresholds = {
                    minFPS: 45,
                    maxFrameTime: 22,
                    maxParticles: 1000,
                    maxDrawCalls: 100,
                    maxMemoryMB: 200
                };
                break;
            case DeviceTier.High:
                this.thresholds = {
                    minFPS: 55,
                    maxFrameTime: 18,
                    maxParticles: 2000,
                    maxDrawCalls: 200,
                    maxMemoryMB: 400
                };
                break;
            default:
                this.thresholds = {
                    minFPS: 30,
                    maxFrameTime: 33,
                    maxParticles: 500,
                    maxDrawCalls: 50,
                    maxMemoryMB: 100
                };
        }
    }

    /**
     * Record current metrics
     */
    private recordMetrics(particleCount: number, drawCalls: number): void {
        const fps = this.frameCount / (this.updateInterval / 1000);
        const avgFrameTime = this.frameTimeSum / this.frameCount;
        
        const metrics: PerformanceMetrics = {
            fps,
            frameTime: avgFrameTime,
            particleCount,
            drawCalls,
            memoryUsage: this.estimateMemoryUsage(),
            cpuUsage: 0, // Would need platform-specific implementation
            timestamp: Date.now()
        };

        this.metrics.push(metrics);

        // Limit history size
        if (this.metrics.length > this.maxHistorySize) {
            this.metrics.shift();
        }

        // Reset counters
        this.frameCount = 0;
        this.frameTimeSum = 0;

        // Check for performance issues
        const recommendations = this.getOptimizationRecommendations();
        if (recommendations.length > 0 && this.onPerformanceIssue) {
            this.onPerformanceIssue(recommendations);
        }
    }

    /**
     * Estimate memory usage
     */
    private estimateMemoryUsage(): number {
        // This is a rough estimate - in a real implementation,
        // you'd use platform-specific APIs to get actual memory usage
        return 50; // Placeholder value in MB
    }

    /**
     * Calculate performance summary
     */
    private calculateSummary(): any {
        if (this.metrics.length === 0) {
            return {
                averageFPS: 0,
                averageFrameTime: 0,
                averageParticles: 0,
                peakParticles: 0,
                issueCount: 0
            };
        }

        const sum = this.metrics.reduce((acc, m) => ({
            fps: acc.fps + m.fps,
            frameTime: acc.frameTime + m.frameTime,
            particles: acc.particles + m.particleCount
        }), { fps: 0, frameTime: 0, particles: 0 });

        const count = this.metrics.length;
        const peakParticles = Math.max(...this.metrics.map(m => m.particleCount));
        
        // Count performance issues
        let issueCount = 0;
        this.metrics.forEach(m => {
            if (m.fps < this.thresholds.minFPS || 
                m.frameTime > this.thresholds.maxFrameTime ||
                m.particleCount > this.thresholds.maxParticles) {
                issueCount++;
            }
        });

        return {
            averageFPS: sum.fps / count,
            averageFrameTime: sum.frameTime / count,
            averageParticles: sum.particles / count,
            peakParticles,
            issueCount
        };
    }
}

/**
 * Convenience function to get the global performance monitor
 */
export function getPerformanceMonitor(): PerformanceMonitor {
    return PerformanceMonitor.getInstance();
}
