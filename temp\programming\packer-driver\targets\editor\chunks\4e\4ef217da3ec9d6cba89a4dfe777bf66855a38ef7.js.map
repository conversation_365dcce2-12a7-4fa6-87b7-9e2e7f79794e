{"version": 3, "sources": ["file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/AutoGen/PB/cs_proto.ts"], "names": ["encodeRoleBaseAttr", "message", "bb", "popByteBuffer", "_encodeRoleBaseAttr", "toUint8Array", "$uin", "uin", "undefined", "writeVarint32", "writeInt64", "$openid", "openid", "writeString", "$area_id", "area_id", "writeVarint64", "intToLong", "$money", "money", "$gold", "gold", "$game_money", "game_money", "$level", "level", "$xp", "xp", "decodeRoleBaseAttr", "binary", "_decodeRoleBaseAttr", "wrapByteBuffer", "end_of_message", "isAtEnd", "tag", "readVarint32", "readInt64", "readString", "readVarint64", "skipUnknownField", "encodeRoleExtAttr", "_encodeRoleExtAttr", "$gender", "gender", "encodeGENDER_TYPE", "$nick_name", "nick_name", "$picture", "picture", "$client_ver", "client_ver", "$client_ver_str", "client_ver_str", "$create_time", "create_time", "$last_login_time", "last_login_time", "$last_login_ip", "last_login_ip", "$last_logout_time", "last_logout_time", "$total_online_time", "total_online_time", "$bit_flag", "bit_flag", "decodeRoleExtAttr", "_decodeRoleExtAttr", "decodeGENDER_TYPE", "encodeClientData", "_encodeClientData", "$data", "data", "length", "writeBytes", "decodeClientData", "_decodeClientData", "readBytes", "encodeCardGroupItem", "_encodeCardGroupItem", "$card_id", "card_id", "decodeCardGroupItem", "_decodeCardGroupItem", "encodeCardGroupInfo", "_encodeCardGroupInfo", "$index", "index", "$name", "name", "$force_id", "force_id", "array$cards", "cards", "value", "nested", "limit", "writeByteBuffer", "pushByteBuffer", "decodeCardGroupInfo", "_decodeCardGroupInfo", "pushTemporaryLength", "values", "push", "encodeCardInfo", "_encodeCardInfo", "$unlock_time", "unlock_time", "$expired_time", "expired_time", "$guid", "guid", "$num", "num", "decodeCardInfo", "_decodeCardInfo", "encodeHeroInfo", "_encodeHeroInfo", "$hero_id", "hero_id", "decodeHeroInfo", "_decodeHeroInfo", "encodeCardGroupList", "_encodeCardGroupList", "array$groups", "groups", "$default_group", "default_group", "writeVarint32ZigZag", "decodeCardGroupList", "_decodeCardGroupList", "readVarint32ZigZag", "encodeGameCardInfo", "_encodeGameCardInfo", "decodeGameCardInfo", "_decodeGameCardInfo", "encodeRoleGameStartInfo", "_encodeRoleGameStartInfo", "$game_id", "game_id", "$mode", "mode", "encodeGAME_MODE", "$map_id", "map_id", "$gamesvr_id", "gamesvr_id", "$begin_time", "begin_time", "$url", "url", "$session_id", "session_id", "$session_key", "session_key", "decodeRoleGameStartInfo", "_decodeRoleGameStartInfo", "decodeGAME_MODE", "encodeRoleGameInfo", "_encodeRoleGameInfo", "$status", "status", "encodeROLE_GAME_STATUS", "$status_time", "status_time", "$start_info", "start_info", "decodeRoleGameInfo", "_decodeRoleGameInfo", "decodeROLE_GAME_STATUS", "encodeGamePlayer", "_encodeGamePlayer", "$camp_id", "camp_id", "$position", "position", "$is_ai", "is_ai", "writeByte", "decodeGamePlayer", "_decodeGamePlayer", "readByte", "encodeGameInfo", "_encodeGameInfo", "$version", "version", "$random_num", "random_num", "array$players", "players", "decodeGameInfo", "_decodeGameInfo", "encodeGamePlayerStat", "_encodeGamePlayerStat", "$score", "score", "decodeGamePlayerStat", "_decodeGamePlayerStat", "encodeGameResult", "_encodeGameResult", "$start_time", "start_time", "$end_time", "end_time", "$duration", "duration", "$result", "result", "encodeGAME_RESULT", "array$stat", "stat", "decodeGameResult", "_decodeGameResult", "decodeGAME_RESULT", "encodeRoleSimpleDataOthers", "_encodeRoleSimpleDataOthers", "decodeRoleSimpleDataOthers", "_decodeRoleSimpleDataOthers", "encodeGameFrameInputUnit", "_encodeGameFrameInputUnit", "$input", "input", "decodeGameFrameInputUnit", "_decodeGameFrameInputUnit", "encodeGameFrameInput", "_encodeGameFrameInput", "$frame_seq", "frame_seq", "decodeGameFrameInput", "_decodeGameFrameInput", "offset", "type", "skip", "Error", "stringToLong", "low", "charCodeAt", "high", "unsigned", "longToString", "String", "fromCharCode", "bbStack", "pop", "bytes", "Uint8Array", "subarray", "grow", "count", "finalOffset", "newBytes", "set", "advance", "buffer", "invalid", "text", "i", "c1", "c2", "c3", "c4", "c", "n", "byteCount", "from", "to", "readFloat", "f32_u8", "f32", "writeFloat", "readDouble", "f64_u8", "f64", "writeDouble", "readInt32", "writeInt32", "b", "part0", "part1", "part2", "size", "readVarint64ZigZag", "flip", "writeVarint64ZigZag", "encodeRET_CODE", "NO_ERROR", "INVALID_OPENID", "NOT_IN_WHITE_LIST", "NOT_RIGHT", "INVALID_PASSWORD", "INVALID_INPUT", "SYSTEM_ERROR", "INVALID_TOKEN", "RELOGIN", "NAME_TOO_SHORT", "NAME_TOO_LONG", "NAME_INVALID", "NOT_AUTHED", "DATA_TOO_LONG", "NOT_EXIST", "TOO_FREQUENT", "TIMEOUT", "INVALID_SEQ", "IN_PROCESS", "NOT_FREE_STATUS", "CLIENT_VERSION_TOO_OLD", "CLIENT_VERSION_TOO_NEW", "CLIENT_VERSION_BACK_OLD", "CARD_NOT_UNLOCK", "DEFAULT_CARDGROUP_NOSET", "CARD_NOT_CONFIG", "CARD_GROUP_INDEX_INVALID", "CARD_GROUP_NAME_INVALID", "CARD_GROUP_NAME_TOO_LONG", "CARD_GROUP_FORCE_ID_INVALID", "CARD_GROUP_INDEX_NOT_CONFIG", "CARD_GROUP_CARD_DUPLICATE", "CARD_GROUP_CARD_NUM_INVALID", "CUSTOM_ROOM_IS_FULL", "CUSTOM_ROOM_POSITION_NOT_EMPTY", "CUSTOM_ROOM_CAMP_FULL", "CUSTOM_ROOM_NOT_EXIST", "NOT_IN_CUSTOM_ROOM", "CUSTOM_ROOM_IN_FIGHTING", "CUSTOM_ROOM_PLAYER_NOT_ENOUGH", "CUSTOM_ROOM_NOT_CREATOR", "ALREADY_IN_CUSTOM_ROOM", "CUSTOM_ROOM_HAS_OFFLINE_PLAYER", "CUSTOM_ROOM_CHANGE_POS_WITH_PLAYER_NOT_SUPPORT", "CUSTOM_ROOM_VERSION_NOT_MATCH", "NOT_IN_GAME", "GAME_NOT_IN_PREPARE", "GAME_HAS_READY", "GAME_NOT_READY", "GAME_CANNOT_READY", "ROOM_NOT_EXIST", "ALREADY_IN_ROOM", "ROOM_NUM_LIMIT", "ROOM_HAS_START", "ROOM_IS_FULL", "NOT_IN_ROOM", "ROOM_SIDE_FULL", "ROOM_POSITION_NOT_EMPTY", "ROOM_NOT_CORRECT", "ROOM_NOT_CREATOR", "ROOM_NOT_READY", "ROOM_CANNOT_SET_OTHER_PLAYER", "ROOM_CANNOT_ADD_AI", "decodeRET_CODE", "RET_CODE", "encodeACCOUNT_TYPE", "ACCOUNT_RAW", "ACCOUNT_QQ", "ACCOUNT_WX", "ACCOUNT_FACEBOOK", "ACCOUNT_GOOGLE", "ACCOUNT_MAIL", "ACCOUNT_APPLEID", "decodeACCOUNT_TYPE", "ACCOUNT_TYPE", "encodeAREA_STATUS_TYPE", "AREA_STATUSCLOSED", "AREA_STATUSFREE", "AREA_STATUSHIGH", "AREA_STATUSFULL", "decodeAREA_STATUS_TYPE", "AREA_STATUS_TYPE", "GENDER_NONE", "GENDER_MALE", "GENDER_FEMALE", "GENDER_TYPE", "NONE", "P1V1", "P2V2", "P4V4", "GAME_MODE", "encodeCUSTOM_ROOM_STATUS", "OPEN", "CREATING", "FIGHTING", "decodeCUSTOM_ROOM_STATUS", "CUSTOM_ROOM_STATUS", "RED_WIN", "BLUE_WIN", "DRAW", "GAME_RESULT", "IN_TEAM", "IN_CUSTOM_ROOM", "IN_FIGHTING", "IN_GUILDING", "ROLE_GAME_STATUS", "Float32Array", "Float64Array"], "mappings": ";;;;;AA0WO,WAASA,kBAAT,CAA4BC,OAA5B,EAA+D;AACpE,QAAIC,EAAE,GAAGC,aAAa,EAAtB;;AACAC,IAAAA,mBAAmB,CAACH,OAAD,EAAUC,EAAV,CAAnB;;AACA,WAAOG,YAAY,CAACH,EAAD,CAAnB;AACD;;AAED,WAASE,mBAAT,CAA6BH,OAA7B,EAAoDC,EAApD,EAA0E;AACxE;AACA,QAAII,IAAI,GAAGL,OAAO,CAACM,GAAnB;;AACA,QAAID,IAAI,KAAKE,SAAb,EAAwB;AACtBC,MAAAA,aAAa,CAACP,EAAD,EAAK,CAAL,CAAb;AACAQ,MAAAA,UAAU,CAACR,EAAD,EAAKI,IAAL,CAAV;AACD,KANuE,CAQxE;;;AACA,QAAIK,OAAO,GAAGV,OAAO,CAACW,MAAtB;;AACA,QAAID,OAAO,KAAKH,SAAhB,EAA2B;AACzBC,MAAAA,aAAa,CAACP,EAAD,EAAK,EAAL,CAAb;AACAW,MAAAA,WAAW,CAACX,EAAD,EAAKS,OAAL,CAAX;AACD,KAbuE,CAexE;;;AACA,QAAIG,QAAQ,GAAGb,OAAO,CAACc,OAAvB;;AACA,QAAID,QAAQ,KAAKN,SAAjB,EAA4B;AAC1BC,MAAAA,aAAa,CAACP,EAAD,EAAK,EAAL,CAAb;AACAc,MAAAA,aAAa,CAACd,EAAD,EAAKe,SAAS,CAACH,QAAD,CAAd,CAAb;AACD,KApBuE,CAsBxE;;;AACA,QAAII,MAAM,GAAGjB,OAAO,CAACkB,KAArB;;AACA,QAAID,MAAM,KAAKV,SAAf,EAA0B;AACxBC,MAAAA,aAAa,CAACP,EAAD,EAAK,EAAL,CAAb;AACAc,MAAAA,aAAa,CAACd,EAAD,EAAKgB,MAAL,CAAb;AACD,KA3BuE,CA6BxE;;;AACA,QAAIE,KAAK,GAAGnB,OAAO,CAACoB,IAApB;;AACA,QAAID,KAAK,KAAKZ,SAAd,EAAyB;AACvBC,MAAAA,aAAa,CAACP,EAAD,EAAK,EAAL,CAAb;AACAc,MAAAA,aAAa,CAACd,EAAD,EAAKkB,KAAL,CAAb;AACD,KAlCuE,CAoCxE;;;AACA,QAAIE,WAAW,GAAGrB,OAAO,CAACsB,UAA1B;;AACA,QAAID,WAAW,KAAKd,SAApB,EAA+B;AAC7BC,MAAAA,aAAa,CAACP,EAAD,EAAK,EAAL,CAAb;AACAc,MAAAA,aAAa,CAACd,EAAD,EAAKoB,WAAL,CAAb;AACD,KAzCuE,CA2CxE;;;AACA,QAAIE,MAAM,GAAGvB,OAAO,CAACwB,KAArB;;AACA,QAAID,MAAM,KAAKhB,SAAf,EAA0B;AACxBC,MAAAA,aAAa,CAACP,EAAD,EAAK,EAAL,CAAb;AACAc,MAAAA,aAAa,CAACd,EAAD,EAAKe,SAAS,CAACO,MAAD,CAAd,CAAb;AACD,KAhDuE,CAkDxE;;;AACA,QAAIE,GAAG,GAAGzB,OAAO,CAAC0B,EAAlB;;AACA,QAAID,GAAG,KAAKlB,SAAZ,EAAuB;AACrBC,MAAAA,aAAa,CAACP,EAAD,EAAK,EAAL,CAAb;AACAc,MAAAA,aAAa,CAACd,EAAD,EAAKe,SAAS,CAACS,GAAD,CAAd,CAAb;AACD;AACF;;AAEM,WAASE,kBAAT,CAA4BC,MAA5B,EAA8D;AACnE,WAAOC,mBAAmB,CAACC,cAAc,CAACF,MAAD,CAAf,CAA1B;AACD;;AAED,WAASC,mBAAT,CAA6B5B,EAA7B,EAA2D;AACzD,QAAID,OAAqB,GAAG,EAA5B;;AAEA+B,IAAAA,cAAc,EAAE,OAAO,CAACC,OAAO,CAAC/B,EAAD,CAAf,EAAqB;AACnC,UAAIgC,GAAG,GAAGC,YAAY,CAACjC,EAAD,CAAtB;;AAEA,cAAQgC,GAAG,KAAK,CAAhB;AACE,aAAK,CAAL;AACE,gBAAMF,cAAN;AAEF;;AACA,aAAK,CAAL;AAAQ;AACN/B,YAAAA,OAAO,CAACM,GAAR,GAAc6B,SAAS,CAAClC,EAAD;AAAK;AAAe,gBAApB,CAAvB;AACA;AACD;AAED;;AACA,aAAK,CAAL;AAAQ;AACND,YAAAA,OAAO,CAACW,MAAR,GAAiByB,UAAU,CAACnC,EAAD,EAAKiC,YAAY,CAACjC,EAAD,CAAjB,CAA3B;AACA;AACD;AAED;;AACA,aAAK,CAAL;AAAQ;AACND,YAAAA,OAAO,CAACc,OAAR,GAAkBoB,YAAY,CAACjC,EAAD,CAA9B;AACA;AACD;AAED;;AACA,aAAK,CAAL;AAAQ;AACND,YAAAA,OAAO,CAACkB,KAAR,GAAgBmB,YAAY,CAACpC,EAAD;AAAK;AAAe,iBAApB,CAA5B;AACA;AACD;AAED;;AACA,aAAK,CAAL;AAAQ;AACND,YAAAA,OAAO,CAACoB,IAAR,GAAeiB,YAAY,CAACpC,EAAD;AAAK;AAAe,iBAApB,CAA3B;AACA;AACD;AAED;;AACA,aAAK,CAAL;AAAQ;AACND,YAAAA,OAAO,CAACsB,UAAR,GAAqBe,YAAY,CAACpC,EAAD;AAAK;AAAe,iBAApB,CAAjC;AACA;AACD;AAED;;AACA,aAAK,CAAL;AAAQ;AACND,YAAAA,OAAO,CAACwB,KAAR,GAAgBU,YAAY,CAACjC,EAAD,CAA5B;AACA;AACD;AAED;;AACA,aAAK,CAAL;AAAQ;AACND,YAAAA,OAAO,CAAC0B,EAAR,GAAaQ,YAAY,CAACjC,EAAD,CAAzB;AACA;AACD;;AAED;AACEqC,UAAAA,gBAAgB,CAACrC,EAAD,EAAKgC,GAAG,GAAG,CAAX,CAAhB;AArDJ;AAuDD;;AAED,WAAOjC,OAAP;AACD;;AAgBM,WAASuC,iBAAT,CAA2BvC,OAA3B,EAA6D;AAClE,QAAIC,EAAE,GAAGC,aAAa,EAAtB;;AACAsC,IAAAA,kBAAkB,CAACxC,OAAD,EAAUC,EAAV,CAAlB;;AACA,WAAOG,YAAY,CAACH,EAAD,CAAnB;AACD;;AAED,WAASuC,kBAAT,CAA4BxC,OAA5B,EAAkDC,EAAlD,EAAwE;AACtE;AACA,QAAIwC,OAAO,GAAGzC,OAAO,CAAC0C,MAAtB;;AACA,QAAID,OAAO,KAAKlC,SAAhB,EAA2B;AACzBC,MAAAA,aAAa,CAACP,EAAD,EAAK,CAAL,CAAb;AACAO,MAAAA,aAAa,CAACP,EAAD,EAAK0C,iBAAiB,CAACF,OAAD,CAAtB,CAAb;AACD,KANqE,CAQtE;;;AACA,QAAIG,UAAU,GAAG5C,OAAO,CAAC6C,SAAzB;;AACA,QAAID,UAAU,KAAKrC,SAAnB,EAA8B;AAC5BC,MAAAA,aAAa,CAACP,EAAD,EAAK,EAAL,CAAb;AACAW,MAAAA,WAAW,CAACX,EAAD,EAAK2C,UAAL,CAAX;AACD,KAbqE,CAetE;;;AACA,QAAIE,QAAQ,GAAG9C,OAAO,CAAC+C,OAAvB;;AACA,QAAID,QAAQ,KAAKvC,SAAjB,EAA4B;AAC1BC,MAAAA,aAAa,CAACP,EAAD,EAAK,EAAL,CAAb;AACAW,MAAAA,WAAW,CAACX,EAAD,EAAK6C,QAAL,CAAX;AACD,KApBqE,CAsBtE;;;AACA,QAAIE,WAAW,GAAGhD,OAAO,CAACiD,UAA1B;;AACA,QAAID,WAAW,KAAKzC,SAApB,EAA+B;AAC7BC,MAAAA,aAAa,CAACP,EAAD,EAAK,EAAL,CAAb;AACAO,MAAAA,aAAa,CAACP,EAAD,EAAK+C,WAAL,CAAb;AACD,KA3BqE,CA6BtE;;;AACA,QAAIE,eAAe,GAAGlD,OAAO,CAACmD,cAA9B;;AACA,QAAID,eAAe,KAAK3C,SAAxB,EAAmC;AACjCC,MAAAA,aAAa,CAACP,EAAD,EAAK,EAAL,CAAb;AACAW,MAAAA,WAAW,CAACX,EAAD,EAAKiD,eAAL,CAAX;AACD,KAlCqE,CAoCtE;;;AACA,QAAIE,YAAY,GAAGpD,OAAO,CAACqD,WAA3B;;AACA,QAAID,YAAY,KAAK7C,SAArB,EAAgC;AAC9BC,MAAAA,aAAa,CAACP,EAAD,EAAK,EAAL,CAAb;AACAO,MAAAA,aAAa,CAACP,EAAD,EAAKmD,YAAL,CAAb;AACD,KAzCqE,CA2CtE;;;AACA,QAAIE,gBAAgB,GAAGtD,OAAO,CAACuD,eAA/B;;AACA,QAAID,gBAAgB,KAAK/C,SAAzB,EAAoC;AAClCC,MAAAA,aAAa,CAACP,EAAD,EAAK,EAAL,CAAb;AACAc,MAAAA,aAAa,CAACd,EAAD,EAAKqD,gBAAL,CAAb;AACD,KAhDqE,CAkDtE;;;AACA,QAAIE,cAAc,GAAGxD,OAAO,CAACyD,aAA7B;;AACA,QAAID,cAAc,KAAKjD,SAAvB,EAAkC;AAChCC,MAAAA,aAAa,CAACP,EAAD,EAAK,EAAL,CAAb;AACAW,MAAAA,WAAW,CAACX,EAAD,EAAKuD,cAAL,CAAX;AACD,KAvDqE,CAyDtE;;;AACA,QAAIE,iBAAiB,GAAG1D,OAAO,CAAC2D,gBAAhC;;AACA,QAAID,iBAAiB,KAAKnD,SAA1B,EAAqC;AACnCC,MAAAA,aAAa,CAACP,EAAD,EAAK,EAAL,CAAb;AACAO,MAAAA,aAAa,CAACP,EAAD,EAAKyD,iBAAL,CAAb;AACD,KA9DqE,CAgEtE;;;AACA,QAAIE,kBAAkB,GAAG5D,OAAO,CAAC6D,iBAAjC;;AACA,QAAID,kBAAkB,KAAKrD,SAA3B,EAAsC;AACpCC,MAAAA,aAAa,CAACP,EAAD,EAAK,EAAL,CAAb;AACAO,MAAAA,aAAa,CAACP,EAAD,EAAK2D,kBAAL,CAAb;AACD,KArEqE,CAuEtE;;;AACA,QAAIE,SAAS,GAAG9D,OAAO,CAAC+D,QAAxB;;AACA,QAAID,SAAS,KAAKvD,SAAlB,EAA6B;AAC3BC,MAAAA,aAAa,CAACP,EAAD,EAAK,EAAL,CAAb;AACAc,MAAAA,aAAa,CAACd,EAAD,EAAK6D,SAAL,CAAb;AACD;AACF;;AAEM,WAASE,iBAAT,CAA2BpC,MAA3B,EAA4D;AACjE,WAAOqC,kBAAkB,CAACnC,cAAc,CAACF,MAAD,CAAf,CAAzB;AACD;;AAED,WAASqC,kBAAT,CAA4BhE,EAA5B,EAAyD;AACvD,QAAID,OAAoB,GAAG,EAA3B;;AAEA+B,IAAAA,cAAc,EAAE,OAAO,CAACC,OAAO,CAAC/B,EAAD,CAAf,EAAqB;AACnC,UAAIgC,GAAG,GAAGC,YAAY,CAACjC,EAAD,CAAtB;;AAEA,cAAQgC,GAAG,KAAK,CAAhB;AACE,aAAK,CAAL;AACE,gBAAMF,cAAN;AAEF;;AACA,aAAK,CAAL;AAAQ;AACN/B,YAAAA,OAAO,CAAC0C,MAAR,GAAiBwB,iBAAiB,CAAChC,YAAY,CAACjC,EAAD,CAAb,CAAlC;AACA;AACD;AAED;;AACA,aAAK,CAAL;AAAQ;AACND,YAAAA,OAAO,CAAC6C,SAAR,GAAoBT,UAAU,CAACnC,EAAD,EAAKiC,YAAY,CAACjC,EAAD,CAAjB,CAA9B;AACA;AACD;AAED;;AACA,aAAK,CAAL;AAAQ;AACND,YAAAA,OAAO,CAAC+C,OAAR,GAAkBX,UAAU,CAACnC,EAAD,EAAKiC,YAAY,CAACjC,EAAD,CAAjB,CAA5B;AACA;AACD;AAED;;AACA,aAAK,CAAL;AAAQ;AACND,YAAAA,OAAO,CAACiD,UAAR,GAAqBf,YAAY,CAACjC,EAAD,CAAZ,KAAqB,CAA1C;AACA;AACD;AAED;;AACA,aAAK,CAAL;AAAQ;AACND,YAAAA,OAAO,CAACmD,cAAR,GAAyBf,UAAU,CAACnC,EAAD,EAAKiC,YAAY,CAACjC,EAAD,CAAjB,CAAnC;AACA;AACD;AAED;;AACA,aAAK,CAAL;AAAQ;AACND,YAAAA,OAAO,CAACqD,WAAR,GAAsBnB,YAAY,CAACjC,EAAD,CAAZ,KAAqB,CAA3C;AACA;AACD;AAED;;AACA,aAAK,CAAL;AAAQ;AACND,YAAAA,OAAO,CAACuD,eAAR,GAA0BlB,YAAY,CAACpC,EAAD;AAAK;AAAe,gBAApB,CAAtC;AACA;AACD;AAED;;AACA,aAAK,CAAL;AAAQ;AACND,YAAAA,OAAO,CAACyD,aAAR,GAAwBrB,UAAU,CAACnC,EAAD,EAAKiC,YAAY,CAACjC,EAAD,CAAjB,CAAlC;AACA;AACD;AAED;;AACA,aAAK,CAAL;AAAQ;AACND,YAAAA,OAAO,CAAC2D,gBAAR,GAA2BzB,YAAY,CAACjC,EAAD,CAAZ,KAAqB,CAAhD;AACA;AACD;AAED;;AACA,aAAK,EAAL;AAAS;AACPD,YAAAA,OAAO,CAAC6D,iBAAR,GAA4B3B,YAAY,CAACjC,EAAD,CAAZ,KAAqB,CAAjD;AACA;AACD;AAED;;AACA,aAAK,EAAL;AAAS;AACPD,YAAAA,OAAO,CAAC+D,QAAR,GAAmB1B,YAAY,CAACpC,EAAD;AAAK;AAAe,gBAApB,CAA/B;AACA;AACD;;AAED;AACEqC,UAAAA,gBAAgB,CAACrC,EAAD,EAAKgC,GAAG,GAAG,CAAX,CAAhB;AAvEJ;AAyED;;AAED,WAAOjC,OAAP;AACD;;AAMM,WAASmE,gBAAT,CAA0BnE,OAA1B,EAA2D;AAChE,QAAIC,EAAE,GAAGC,aAAa,EAAtB;;AACAkE,IAAAA,iBAAiB,CAACpE,OAAD,EAAUC,EAAV,CAAjB;;AACA,WAAOG,YAAY,CAACH,EAAD,CAAnB;AACD;;AAED,WAASmE,iBAAT,CAA2BpE,OAA3B,EAAgDC,EAAhD,EAAsE;AACpE;AACA,QAAIoE,KAAK,GAAGrE,OAAO,CAACsE,IAApB;;AACA,QAAID,KAAK,KAAK9D,SAAd,EAAyB;AACvBC,MAAAA,aAAa,CAACP,EAAD,EAAK,EAAL,CAAb;AACAO,MAAAA,aAAa,CAACP,EAAD,EAAKoE,KAAK,CAACE,MAAX,CAAb,EAAiCC,UAAU,CAACvE,EAAD,EAAKoE,KAAL,CAA3C;AACD;AACF;;AAEM,WAASI,gBAAT,CAA0B7C,MAA1B,EAA0D;AAC/D,WAAO8C,iBAAiB,CAAC5C,cAAc,CAACF,MAAD,CAAf,CAAxB;AACD;;AAED,WAAS8C,iBAAT,CAA2BzE,EAA3B,EAAuD;AACrD,QAAID,OAAmB,GAAG,EAA1B;;AAEA+B,IAAAA,cAAc,EAAE,OAAO,CAACC,OAAO,CAAC/B,EAAD,CAAf,EAAqB;AACnC,UAAIgC,GAAG,GAAGC,YAAY,CAACjC,EAAD,CAAtB;;AAEA,cAAQgC,GAAG,KAAK,CAAhB;AACE,aAAK,CAAL;AACE,gBAAMF,cAAN;AAEF;;AACA,aAAK,CAAL;AAAQ;AACN/B,YAAAA,OAAO,CAACsE,IAAR,GAAeK,SAAS,CAAC1E,EAAD,EAAKiC,YAAY,CAACjC,EAAD,CAAjB,CAAxB;AACA;AACD;;AAED;AACEqC,UAAAA,gBAAgB,CAACrC,EAAD,EAAKgC,GAAG,GAAG,CAAX,CAAhB;AAXJ;AAaD;;AAED,WAAOjC,OAAP;AACD;;AAMM,WAAS4E,mBAAT,CAA6B5E,OAA7B,EAAiE;AACtE,QAAIC,EAAE,GAAGC,aAAa,EAAtB;;AACA2E,IAAAA,oBAAoB,CAAC7E,OAAD,EAAUC,EAAV,CAApB;;AACA,WAAOG,YAAY,CAACH,EAAD,CAAnB;AACD;;AAED,WAAS4E,oBAAT,CAA8B7E,OAA9B,EAAsDC,EAAtD,EAA4E;AAC1E;AACA,QAAI6E,QAAQ,GAAG9E,OAAO,CAAC+E,OAAvB;;AACA,QAAID,QAAQ,KAAKvE,SAAjB,EAA4B;AAC1BC,MAAAA,aAAa,CAACP,EAAD,EAAK,CAAL,CAAb;AACAc,MAAAA,aAAa,CAACd,EAAD,EAAKe,SAAS,CAAC8D,QAAD,CAAd,CAAb;AACD;AACF;;AAEM,WAASE,mBAAT,CAA6BpD,MAA7B,EAAgE;AACrE,WAAOqD,oBAAoB,CAACnD,cAAc,CAACF,MAAD,CAAf,CAA3B;AACD;;AAED,WAASqD,oBAAT,CAA8BhF,EAA9B,EAA6D;AAC3D,QAAID,OAAsB,GAAG,EAA7B;;AAEA+B,IAAAA,cAAc,EAAE,OAAO,CAACC,OAAO,CAAC/B,EAAD,CAAf,EAAqB;AACnC,UAAIgC,GAAG,GAAGC,YAAY,CAACjC,EAAD,CAAtB;;AAEA,cAAQgC,GAAG,KAAK,CAAhB;AACE,aAAK,CAAL;AACE,gBAAMF,cAAN;AAEF;;AACA,aAAK,CAAL;AAAQ;AACN/B,YAAAA,OAAO,CAAC+E,OAAR,GAAkB7C,YAAY,CAACjC,EAAD,CAA9B;AACA;AACD;;AAED;AACEqC,UAAAA,gBAAgB,CAACrC,EAAD,EAAKgC,GAAG,GAAG,CAAX,CAAhB;AAXJ;AAaD;;AAED,WAAOjC,OAAP;AACD;;AASM,WAASkF,mBAAT,CAA6BlF,OAA7B,EAAiE;AACtE,QAAIC,EAAE,GAAGC,aAAa,EAAtB;;AACAiF,IAAAA,oBAAoB,CAACnF,OAAD,EAAUC,EAAV,CAApB;;AACA,WAAOG,YAAY,CAACH,EAAD,CAAnB;AACD;;AAED,WAASkF,oBAAT,CAA8BnF,OAA9B,EAAsDC,EAAtD,EAA4E;AAC1E;AACA,QAAImF,MAAM,GAAGpF,OAAO,CAACqF,KAArB;;AACA,QAAID,MAAM,KAAK7E,SAAf,EAA0B;AACxBC,MAAAA,aAAa,CAACP,EAAD,EAAK,CAAL,CAAb;AACAc,MAAAA,aAAa,CAACd,EAAD,EAAKe,SAAS,CAACoE,MAAD,CAAd,CAAb;AACD,KANyE,CAQ1E;;;AACA,QAAIE,KAAK,GAAGtF,OAAO,CAACuF,IAApB;;AACA,QAAID,KAAK,KAAK/E,SAAd,EAAyB;AACvBC,MAAAA,aAAa,CAACP,EAAD,EAAK,EAAL,CAAb;AACAW,MAAAA,WAAW,CAACX,EAAD,EAAKqF,KAAL,CAAX;AACD,KAbyE,CAe1E;;;AACA,QAAIE,SAAS,GAAGxF,OAAO,CAACyF,QAAxB;;AACA,QAAID,SAAS,KAAKjF,SAAlB,EAA6B;AAC3BC,MAAAA,aAAa,CAACP,EAAD,EAAK,EAAL,CAAb;AACAc,MAAAA,aAAa,CAACd,EAAD,EAAKe,SAAS,CAACwE,SAAD,CAAd,CAAb;AACD,KApByE,CAsB1E;;;AACA,QAAIE,WAAW,GAAG1F,OAAO,CAAC2F,KAA1B;;AACA,QAAID,WAAW,KAAKnF,SAApB,EAA+B;AAC7B,WAAK,IAAIqF,KAAT,IAAkBF,WAAlB,EAA+B;AAC7BlF,QAAAA,aAAa,CAACP,EAAD,EAAK,EAAL,CAAb;AACA,YAAI4F,MAAM,GAAG3F,aAAa,EAA1B;;AACA2E,QAAAA,oBAAoB,CAACe,KAAD,EAAQC,MAAR,CAApB;;AACArF,QAAAA,aAAa,CAACP,EAAD,EAAK4F,MAAM,CAACC,KAAZ,CAAb;AACAC,QAAAA,eAAe,CAAC9F,EAAD,EAAK4F,MAAL,CAAf;AACAG,QAAAA,cAAc,CAACH,MAAD,CAAd;AACD;AACF;AACF;;AAEM,WAASI,mBAAT,CAA6BrE,MAA7B,EAAgE;AACrE,WAAOsE,oBAAoB,CAACpE,cAAc,CAACF,MAAD,CAAf,CAA3B;AACD;;AAED,WAASsE,oBAAT,CAA8BjG,EAA9B,EAA6D;AAC3D,QAAID,OAAsB,GAAG,EAA7B;;AAEA+B,IAAAA,cAAc,EAAE,OAAO,CAACC,OAAO,CAAC/B,EAAD,CAAf,EAAqB;AACnC,UAAIgC,GAAG,GAAGC,YAAY,CAACjC,EAAD,CAAtB;;AAEA,cAAQgC,GAAG,KAAK,CAAhB;AACE,aAAK,CAAL;AACE,gBAAMF,cAAN;AAEF;;AACA,aAAK,CAAL;AAAQ;AACN/B,YAAAA,OAAO,CAACqF,KAAR,GAAgBnD,YAAY,CAACjC,EAAD,CAA5B;AACA;AACD;AAED;;AACA,aAAK,CAAL;AAAQ;AACND,YAAAA,OAAO,CAACuF,IAAR,GAAenD,UAAU,CAACnC,EAAD,EAAKiC,YAAY,CAACjC,EAAD,CAAjB,CAAzB;AACA;AACD;AAED;;AACA,aAAK,CAAL;AAAQ;AACND,YAAAA,OAAO,CAACyF,QAAR,GAAmBvD,YAAY,CAACjC,EAAD,CAA/B;AACA;AACD;AAED;;AACA,aAAK,CAAL;AAAQ;AACN,gBAAI6F,KAAK,GAAGK,mBAAmB,CAAClG,EAAD,CAA/B;AACA,gBAAImG,MAAM,GAAGpG,OAAO,CAAC2F,KAAR,KAAkB3F,OAAO,CAAC2F,KAAR,GAAgB,EAAlC,CAAb;AACAS,YAAAA,MAAM,CAACC,IAAP,CAAYpB,oBAAoB,CAAChF,EAAD,CAAhC;AACAA,YAAAA,EAAE,CAAC6F,KAAH,GAAWA,KAAX;AACA;AACD;;AAED;AACExD,UAAAA,gBAAgB,CAACrC,EAAD,EAAKgC,GAAG,GAAG,CAAX,CAAhB;AAhCJ;AAkCD;;AAED,WAAOjC,OAAP;AACD;;AAWM,WAASsG,cAAT,CAAwBtG,OAAxB,EAAuD;AAC5D,QAAIC,EAAE,GAAGC,aAAa,EAAtB;;AACAqG,IAAAA,eAAe,CAACvG,OAAD,EAAUC,EAAV,CAAf;;AACA,WAAOG,YAAY,CAACH,EAAD,CAAnB;AACD;;AAED,WAASsG,eAAT,CAAyBvG,OAAzB,EAA4CC,EAA5C,EAAkE;AAChE;AACA,QAAI6E,QAAQ,GAAG9E,OAAO,CAAC+E,OAAvB;;AACA,QAAID,QAAQ,KAAKvE,SAAjB,EAA4B;AAC1BC,MAAAA,aAAa,CAACP,EAAD,EAAK,CAAL,CAAb;AACAc,MAAAA,aAAa,CAACd,EAAD,EAAKe,SAAS,CAAC8D,QAAD,CAAd,CAAb;AACD,KAN+D,CAQhE;;;AACA,QAAI0B,YAAY,GAAGxG,OAAO,CAACyG,WAA3B;;AACA,QAAID,YAAY,KAAKjG,SAArB,EAAgC;AAC9BC,MAAAA,aAAa,CAACP,EAAD,EAAK,EAAL,CAAb;AACAO,MAAAA,aAAa,CAACP,EAAD,EAAKuG,YAAL,CAAb;AACD,KAb+D,CAehE;;;AACA,QAAIE,aAAa,GAAG1G,OAAO,CAAC2G,YAA5B;;AACA,QAAID,aAAa,KAAKnG,SAAtB,EAAiC;AAC/BC,MAAAA,aAAa,CAACP,EAAD,EAAK,EAAL,CAAb;AACAO,MAAAA,aAAa,CAACP,EAAD,EAAKyG,aAAL,CAAb;AACD,KApB+D,CAsBhE;;;AACA,QAAIE,KAAK,GAAG5G,OAAO,CAAC6G,IAApB;;AACA,QAAID,KAAK,KAAKrG,SAAd,EAAyB;AACvBC,MAAAA,aAAa,CAACP,EAAD,EAAK,EAAL,CAAb;AACAQ,MAAAA,UAAU,CAACR,EAAD,EAAK2G,KAAL,CAAV;AACD,KA3B+D,CA6BhE;;;AACA,QAAIE,IAAI,GAAG9G,OAAO,CAAC+G,GAAnB;;AACA,QAAID,IAAI,KAAKvG,SAAb,EAAwB;AACtBC,MAAAA,aAAa,CAACP,EAAD,EAAK,EAAL,CAAb;AACAc,MAAAA,aAAa,CAACd,EAAD,EAAKe,SAAS,CAAC8F,IAAD,CAAd,CAAb;AACD,KAlC+D,CAoChE;;;AACA,QAAIvF,MAAM,GAAGvB,OAAO,CAACwB,KAArB;;AACA,QAAID,MAAM,KAAKhB,SAAf,EAA0B;AACxBC,MAAAA,aAAa,CAACP,EAAD,EAAK,EAAL,CAAb;AACAc,MAAAA,aAAa,CAACd,EAAD,EAAKe,SAAS,CAACO,MAAD,CAAd,CAAb;AACD;AACF;;AAEM,WAASyF,cAAT,CAAwBpF,MAAxB,EAAsD;AAC3D,WAAOqF,eAAe,CAACnF,cAAc,CAACF,MAAD,CAAf,CAAtB;AACD;;AAED,WAASqF,eAAT,CAAyBhH,EAAzB,EAAmD;AACjD,QAAID,OAAiB,GAAG,EAAxB;;AAEA+B,IAAAA,cAAc,EAAE,OAAO,CAACC,OAAO,CAAC/B,EAAD,CAAf,EAAqB;AACnC,UAAIgC,GAAG,GAAGC,YAAY,CAACjC,EAAD,CAAtB;;AAEA,cAAQgC,GAAG,KAAK,CAAhB;AACE,aAAK,CAAL;AACE,gBAAMF,cAAN;AAEF;;AACA,aAAK,CAAL;AAAQ;AACN/B,YAAAA,OAAO,CAAC+E,OAAR,GAAkB7C,YAAY,CAACjC,EAAD,CAA9B;AACA;AACD;AAED;;AACA,aAAK,CAAL;AAAQ;AACND,YAAAA,OAAO,CAACyG,WAAR,GAAsBvE,YAAY,CAACjC,EAAD,CAAZ,KAAqB,CAA3C;AACA;AACD;AAED;;AACA,aAAK,CAAL;AAAQ;AACND,YAAAA,OAAO,CAAC2G,YAAR,GAAuBzE,YAAY,CAACjC,EAAD,CAAZ,KAAqB,CAA5C;AACA;AACD;AAED;;AACA,aAAK,CAAL;AAAQ;AACND,YAAAA,OAAO,CAAC6G,IAAR,GAAe1E,SAAS,CAAClC,EAAD;AAAK;AAAe,gBAApB,CAAxB;AACA;AACD;AAED;;AACA,aAAK,CAAL;AAAQ;AACND,YAAAA,OAAO,CAAC+G,GAAR,GAAc7E,YAAY,CAACjC,EAAD,CAA1B;AACA;AACD;AAED;;AACA,aAAK,CAAL;AAAQ;AACND,YAAAA,OAAO,CAACwB,KAAR,GAAgBU,YAAY,CAACjC,EAAD,CAA5B;AACA;AACD;;AAED;AACEqC,UAAAA,gBAAgB,CAACrC,EAAD,EAAKgC,GAAG,GAAG,CAAX,CAAhB;AAzCJ;AA2CD;;AAED,WAAOjC,OAAP;AACD;;AAQM,WAASkH,cAAT,CAAwBlH,OAAxB,EAAuD;AAC5D,QAAIC,EAAE,GAAGC,aAAa,EAAtB;;AACAiH,IAAAA,eAAe,CAACnH,OAAD,EAAUC,EAAV,CAAf;;AACA,WAAOG,YAAY,CAACH,EAAD,CAAnB;AACD;;AAED,WAASkH,eAAT,CAAyBnH,OAAzB,EAA4CC,EAA5C,EAAkE;AAChE;AACA,QAAImH,QAAQ,GAAGpH,OAAO,CAACqH,OAAvB;;AACA,QAAID,QAAQ,KAAK7G,SAAjB,EAA4B;AAC1BC,MAAAA,aAAa,CAACP,EAAD,EAAK,CAAL,CAAb;AACAc,MAAAA,aAAa,CAACd,EAAD,EAAKe,SAAS,CAACoG,QAAD,CAAd,CAAb;AACD,KAN+D,CAQhE;;;AACA,QAAIZ,YAAY,GAAGxG,OAAO,CAACyG,WAA3B;;AACA,QAAID,YAAY,KAAKjG,SAArB,EAAgC;AAC9BC,MAAAA,aAAa,CAACP,EAAD,EAAK,EAAL,CAAb;AACAO,MAAAA,aAAa,CAACP,EAAD,EAAKuG,YAAL,CAAb;AACD,KAb+D,CAehE;;;AACA,QAAIE,aAAa,GAAG1G,OAAO,CAAC2G,YAA5B;;AACA,QAAID,aAAa,KAAKnG,SAAtB,EAAiC;AAC/BC,MAAAA,aAAa,CAACP,EAAD,EAAK,EAAL,CAAb;AACAO,MAAAA,aAAa,CAACP,EAAD,EAAKyG,aAAL,CAAb;AACD;AACF;;AAEM,WAASY,cAAT,CAAwB1F,MAAxB,EAAsD;AAC3D,WAAO2F,eAAe,CAACzF,cAAc,CAACF,MAAD,CAAf,CAAtB;AACD;;AAED,WAAS2F,eAAT,CAAyBtH,EAAzB,EAAmD;AACjD,QAAID,OAAiB,GAAG,EAAxB;;AAEA+B,IAAAA,cAAc,EAAE,OAAO,CAACC,OAAO,CAAC/B,EAAD,CAAf,EAAqB;AACnC,UAAIgC,GAAG,GAAGC,YAAY,CAACjC,EAAD,CAAtB;;AAEA,cAAQgC,GAAG,KAAK,CAAhB;AACE,aAAK,CAAL;AACE,gBAAMF,cAAN;AAEF;;AACA,aAAK,CAAL;AAAQ;AACN/B,YAAAA,OAAO,CAACqH,OAAR,GAAkBnF,YAAY,CAACjC,EAAD,CAA9B;AACA;AACD;AAED;;AACA,aAAK,CAAL;AAAQ;AACND,YAAAA,OAAO,CAACyG,WAAR,GAAsBvE,YAAY,CAACjC,EAAD,CAAZ,KAAqB,CAA3C;AACA;AACD;AAED;;AACA,aAAK,CAAL;AAAQ;AACND,YAAAA,OAAO,CAAC2G,YAAR,GAAuBzE,YAAY,CAACjC,EAAD,CAAZ,KAAqB,CAA5C;AACA;AACD;;AAED;AACEqC,UAAAA,gBAAgB,CAACrC,EAAD,EAAKgC,GAAG,GAAG,CAAX,CAAhB;AAvBJ;AAyBD;;AAED,WAAOjC,OAAP;AACD;;AAOM,WAASwH,mBAAT,CAA6BxH,OAA7B,EAAiE;AACtE,QAAIC,EAAE,GAAGC,aAAa,EAAtB;;AACAuH,IAAAA,oBAAoB,CAACzH,OAAD,EAAUC,EAAV,CAApB;;AACA,WAAOG,YAAY,CAACH,EAAD,CAAnB;AACD;;AAED,WAASwH,oBAAT,CAA8BzH,OAA9B,EAAsDC,EAAtD,EAA4E;AAC1E;AACA,QAAIyH,YAAY,GAAG1H,OAAO,CAAC2H,MAA3B;;AACA,QAAID,YAAY,KAAKnH,SAArB,EAAgC;AAC9B,WAAK,IAAIqF,KAAT,IAAkB8B,YAAlB,EAAgC;AAC9BlH,QAAAA,aAAa,CAACP,EAAD,EAAK,EAAL,CAAb;AACA,YAAI4F,MAAM,GAAG3F,aAAa,EAA1B;;AACAiF,QAAAA,oBAAoB,CAACS,KAAD,EAAQC,MAAR,CAApB;;AACArF,QAAAA,aAAa,CAACP,EAAD,EAAK4F,MAAM,CAACC,KAAZ,CAAb;AACAC,QAAAA,eAAe,CAAC9F,EAAD,EAAK4F,MAAL,CAAf;AACAG,QAAAA,cAAc,CAACH,MAAD,CAAd;AACD;AACF,KAZyE,CAc1E;;;AACA,QAAI+B,cAAc,GAAG5H,OAAO,CAAC6H,aAA7B;;AACA,QAAID,cAAc,KAAKrH,SAAvB,EAAkC;AAChCC,MAAAA,aAAa,CAACP,EAAD,EAAK,EAAL,CAAb;AACA6H,MAAAA,mBAAmB,CAAC7H,EAAD,EAAK2H,cAAL,CAAnB;AACD;AACF;;AAEM,WAASG,mBAAT,CAA6BnG,MAA7B,EAAgE;AACrE,WAAOoG,oBAAoB,CAAClG,cAAc,CAACF,MAAD,CAAf,CAA3B;AACD;;AAED,WAASoG,oBAAT,CAA8B/H,EAA9B,EAA6D;AAC3D,QAAID,OAAsB,GAAG,EAA7B;;AAEA+B,IAAAA,cAAc,EAAE,OAAO,CAACC,OAAO,CAAC/B,EAAD,CAAf,EAAqB;AACnC,UAAIgC,GAAG,GAAGC,YAAY,CAACjC,EAAD,CAAtB;;AAEA,cAAQgC,GAAG,KAAK,CAAhB;AACE,aAAK,CAAL;AACE,gBAAMF,cAAN;AAEF;;AACA,aAAK,CAAL;AAAQ;AACN,gBAAI+D,KAAK,GAAGK,mBAAmB,CAAClG,EAAD,CAA/B;AACA,gBAAImG,MAAM,GAAGpG,OAAO,CAAC2H,MAAR,KAAmB3H,OAAO,CAAC2H,MAAR,GAAiB,EAApC,CAAb;AACAvB,YAAAA,MAAM,CAACC,IAAP,CAAYH,oBAAoB,CAACjG,EAAD,CAAhC;AACAA,YAAAA,EAAE,CAAC6F,KAAH,GAAWA,KAAX;AACA;AACD;AAED;;AACA,aAAK,CAAL;AAAQ;AACN9F,YAAAA,OAAO,CAAC6H,aAAR,GAAwBI,kBAAkB,CAAChI,EAAD,CAA1C;AACA;AACD;;AAED;AACEqC,UAAAA,gBAAgB,CAACrC,EAAD,EAAKgC,GAAG,GAAG,CAAX,CAAhB;AApBJ;AAsBD;;AAED,WAAOjC,OAAP;AACD;;AAOM,WAASkI,kBAAT,CAA4BlI,OAA5B,EAA+D;AACpE,QAAIC,EAAE,GAAGC,aAAa,EAAtB;;AACAiI,IAAAA,mBAAmB,CAACnI,OAAD,EAAUC,EAAV,CAAnB;;AACA,WAAOG,YAAY,CAACH,EAAD,CAAnB;AACD;;AAED,WAASkI,mBAAT,CAA6BnI,OAA7B,EAAoDC,EAApD,EAA0E;AACxE;AACA,QAAI6E,QAAQ,GAAG9E,OAAO,CAAC+E,OAAvB;;AACA,QAAID,QAAQ,KAAKvE,SAAjB,EAA4B;AAC1BC,MAAAA,aAAa,CAACP,EAAD,EAAK,CAAL,CAAb;AACAc,MAAAA,aAAa,CAACd,EAAD,EAAKe,SAAS,CAAC8D,QAAD,CAAd,CAAb;AACD,KANuE,CAQxE;;;AACA,QAAIvD,MAAM,GAAGvB,OAAO,CAACwB,KAArB;;AACA,QAAID,MAAM,KAAKhB,SAAf,EAA0B;AACxBC,MAAAA,aAAa,CAACP,EAAD,EAAK,EAAL,CAAb;AACAc,MAAAA,aAAa,CAACd,EAAD,EAAKe,SAAS,CAACO,MAAD,CAAd,CAAb;AACD;AACF;;AAEM,WAAS6G,kBAAT,CAA4BxG,MAA5B,EAA8D;AACnE,WAAOyG,mBAAmB,CAACvG,cAAc,CAACF,MAAD,CAAf,CAA1B;AACD;;AAED,WAASyG,mBAAT,CAA6BpI,EAA7B,EAA2D;AACzD,QAAID,OAAqB,GAAG,EAA5B;;AAEA+B,IAAAA,cAAc,EAAE,OAAO,CAACC,OAAO,CAAC/B,EAAD,CAAf,EAAqB;AACnC,UAAIgC,GAAG,GAAGC,YAAY,CAACjC,EAAD,CAAtB;;AAEA,cAAQgC,GAAG,KAAK,CAAhB;AACE,aAAK,CAAL;AACE,gBAAMF,cAAN;AAEF;;AACA,aAAK,CAAL;AAAQ;AACN/B,YAAAA,OAAO,CAAC+E,OAAR,GAAkB7C,YAAY,CAACjC,EAAD,CAA9B;AACA;AACD;AAED;;AACA,aAAK,CAAL;AAAQ;AACND,YAAAA,OAAO,CAACwB,KAAR,GAAgBU,YAAY,CAACjC,EAAD,CAA5B;AACA;AACD;;AAED;AACEqC,UAAAA,gBAAgB,CAACrC,EAAD,EAAKgC,GAAG,GAAG,CAAX,CAAhB;AAjBJ;AAmBD;;AAED,WAAOjC,OAAP;AACD;;AAaM,WAASsI,uBAAT,CAAiCtI,OAAjC,EAAyE;AAC9E,QAAIC,EAAE,GAAGC,aAAa,EAAtB;;AACAqI,IAAAA,wBAAwB,CAACvI,OAAD,EAAUC,EAAV,CAAxB;;AACA,WAAOG,YAAY,CAACH,EAAD,CAAnB;AACD;;AAED,WAASsI,wBAAT,CAAkCvI,OAAlC,EAA8DC,EAA9D,EAAoF;AAClF;AACA,QAAIuI,QAAQ,GAAGxI,OAAO,CAACyI,OAAvB;;AACA,QAAID,QAAQ,KAAKjI,SAAjB,EAA4B;AAC1BC,MAAAA,aAAa,CAACP,EAAD,EAAK,CAAL,CAAb;AACAQ,MAAAA,UAAU,CAACR,EAAD,EAAKuI,QAAL,CAAV;AACD,KANiF,CAQlF;;;AACA,QAAIE,KAAK,GAAG1I,OAAO,CAAC2I,IAApB;;AACA,QAAID,KAAK,KAAKnI,SAAd,EAAyB;AACvBC,MAAAA,aAAa,CAACP,EAAD,EAAK,EAAL,CAAb;AACAO,MAAAA,aAAa,CAACP,EAAD,EAAK2I,eAAe,CAACF,KAAD,CAApB,CAAb;AACD,KAbiF,CAelF;;;AACA,QAAIG,OAAO,GAAG7I,OAAO,CAAC8I,MAAtB;;AACA,QAAID,OAAO,KAAKtI,SAAhB,EAA2B;AACzBC,MAAAA,aAAa,CAACP,EAAD,EAAK,EAAL,CAAb;AACAc,MAAAA,aAAa,CAACd,EAAD,EAAKe,SAAS,CAAC6H,OAAD,CAAd,CAAb;AACD,KApBiF,CAsBlF;;;AACA,QAAIE,WAAW,GAAG/I,OAAO,CAACgJ,UAA1B;;AACA,QAAID,WAAW,KAAKxI,SAApB,EAA+B;AAC7BC,MAAAA,aAAa,CAACP,EAAD,EAAK,EAAL,CAAb;AACAO,MAAAA,aAAa,CAACP,EAAD,EAAK8I,WAAL,CAAb;AACD,KA3BiF,CA6BlF;;;AACA,QAAIE,WAAW,GAAGjJ,OAAO,CAACkJ,UAA1B;;AACA,QAAID,WAAW,KAAK1I,SAApB,EAA+B;AAC7BC,MAAAA,aAAa,CAACP,EAAD,EAAK,EAAL,CAAb;AACAO,MAAAA,aAAa,CAACP,EAAD,EAAKgJ,WAAL,CAAb;AACD,KAlCiF,CAoClF;;;AACA,QAAIE,IAAI,GAAGnJ,OAAO,CAACoJ,GAAnB;;AACA,QAAID,IAAI,KAAK5I,SAAb,EAAwB;AACtBC,MAAAA,aAAa,CAACP,EAAD,EAAK,EAAL,CAAb;AACAW,MAAAA,WAAW,CAACX,EAAD,EAAKkJ,IAAL,CAAX;AACD,KAzCiF,CA2ClF;;;AACA,QAAIE,WAAW,GAAGrJ,OAAO,CAACsJ,UAA1B;;AACA,QAAID,WAAW,KAAK9I,SAApB,EAA+B;AAC7BC,MAAAA,aAAa,CAACP,EAAD,EAAK,EAAL,CAAb;AACAO,MAAAA,aAAa,CAACP,EAAD,EAAKoJ,WAAL,CAAb;AACD,KAhDiF,CAkDlF;;;AACA,QAAIE,YAAY,GAAGvJ,OAAO,CAACwJ,WAA3B;;AACA,QAAID,YAAY,KAAKhJ,SAArB,EAAgC;AAC9BC,MAAAA,aAAa,CAACP,EAAD,EAAK,EAAL,CAAb;AACAO,MAAAA,aAAa,CAACP,EAAD,EAAKsJ,YAAY,CAAChF,MAAlB,CAAb,EAAwCC,UAAU,CAACvE,EAAD,EAAKsJ,YAAL,CAAlD;AACD;AACF;;AAEM,WAASE,uBAAT,CAAiC7H,MAAjC,EAAwE;AAC7E,WAAO8H,wBAAwB,CAAC5H,cAAc,CAACF,MAAD,CAAf,CAA/B;AACD;;AAED,WAAS8H,wBAAT,CAAkCzJ,EAAlC,EAAqE;AACnE,QAAID,OAA0B,GAAG,EAAjC;;AAEA+B,IAAAA,cAAc,EAAE,OAAO,CAACC,OAAO,CAAC/B,EAAD,CAAf,EAAqB;AACnC,UAAIgC,GAAG,GAAGC,YAAY,CAACjC,EAAD,CAAtB;;AAEA,cAAQgC,GAAG,KAAK,CAAhB;AACE,aAAK,CAAL;AACE,gBAAMF,cAAN;AAEF;;AACA,aAAK,CAAL;AAAQ;AACN/B,YAAAA,OAAO,CAACyI,OAAR,GAAkBtG,SAAS,CAAClC,EAAD;AAAK;AAAe,gBAApB,CAA3B;AACA;AACD;AAED;;AACA,aAAK,CAAL;AAAQ;AACND,YAAAA,OAAO,CAAC2I,IAAR,GAAegB,eAAe,CAACzH,YAAY,CAACjC,EAAD,CAAb,CAA9B;AACA;AACD;AAED;;AACA,aAAK,CAAL;AAAQ;AACND,YAAAA,OAAO,CAAC8I,MAAR,GAAiB5G,YAAY,CAACjC,EAAD,CAA7B;AACA;AACD;AAED;;AACA,aAAK,CAAL;AAAQ;AACND,YAAAA,OAAO,CAACgJ,UAAR,GAAqB9G,YAAY,CAACjC,EAAD,CAAZ,KAAqB,CAA1C;AACA;AACD;AAED;;AACA,aAAK,CAAL;AAAQ;AACND,YAAAA,OAAO,CAACkJ,UAAR,GAAqBhH,YAAY,CAACjC,EAAD,CAAZ,KAAqB,CAA1C;AACA;AACD;AAED;;AACA,aAAK,CAAL;AAAQ;AACND,YAAAA,OAAO,CAACoJ,GAAR,GAAchH,UAAU,CAACnC,EAAD,EAAKiC,YAAY,CAACjC,EAAD,CAAjB,CAAxB;AACA;AACD;AAED;;AACA,aAAK,CAAL;AAAQ;AACND,YAAAA,OAAO,CAACsJ,UAAR,GAAqBpH,YAAY,CAACjC,EAAD,CAAZ,KAAqB,CAA1C;AACA;AACD;AAED;;AACA,aAAK,CAAL;AAAQ;AACND,YAAAA,OAAO,CAACwJ,WAAR,GAAsB7E,SAAS,CAAC1E,EAAD,EAAKiC,YAAY,CAACjC,EAAD,CAAjB,CAA/B;AACA;AACD;;AAED;AACEqC,UAAAA,gBAAgB,CAACrC,EAAD,EAAKgC,GAAG,GAAG,CAAX,CAAhB;AArDJ;AAuDD;;AAED,WAAOjC,OAAP;AACD;;AAQM,WAAS4J,kBAAT,CAA4B5J,OAA5B,EAA+D;AACpE,QAAIC,EAAE,GAAGC,aAAa,EAAtB;;AACA2J,IAAAA,mBAAmB,CAAC7J,OAAD,EAAUC,EAAV,CAAnB;;AACA,WAAOG,YAAY,CAACH,EAAD,CAAnB;AACD;;AAED,WAAS4J,mBAAT,CAA6B7J,OAA7B,EAAoDC,EAApD,EAA0E;AACxE;AACA,QAAI6J,OAAO,GAAG9J,OAAO,CAAC+J,MAAtB;;AACA,QAAID,OAAO,KAAKvJ,SAAhB,EAA2B;AACzBC,MAAAA,aAAa,CAACP,EAAD,EAAK,CAAL,CAAb;AACAO,MAAAA,aAAa,CAACP,EAAD,EAAK+J,sBAAsB,CAACF,OAAD,CAA3B,CAAb;AACD,KANuE,CAQxE;;;AACA,QAAIG,YAAY,GAAGjK,OAAO,CAACkK,WAA3B;;AACA,QAAID,YAAY,KAAK1J,SAArB,EAAgC;AAC9BC,MAAAA,aAAa,CAACP,EAAD,EAAK,EAAL,CAAb;AACAO,MAAAA,aAAa,CAACP,EAAD,EAAKgK,YAAL,CAAb;AACD,KAbuE,CAexE;;;AACA,QAAIE,WAAW,GAAGnK,OAAO,CAACoK,UAA1B;;AACA,QAAID,WAAW,KAAK5J,SAApB,EAA+B;AAC7BC,MAAAA,aAAa,CAACP,EAAD,EAAK,EAAL,CAAb;AACA,UAAI4F,MAAM,GAAG3F,aAAa,EAA1B;;AACAqI,MAAAA,wBAAwB,CAAC4B,WAAD,EAActE,MAAd,CAAxB;;AACArF,MAAAA,aAAa,CAACP,EAAD,EAAK4F,MAAM,CAACC,KAAZ,CAAb;AACAC,MAAAA,eAAe,CAAC9F,EAAD,EAAK4F,MAAL,CAAf;AACAG,MAAAA,cAAc,CAACH,MAAD,CAAd;AACD;AACF;;AAEM,WAASwE,kBAAT,CAA4BzI,MAA5B,EAA8D;AACnE,WAAO0I,mBAAmB,CAACxI,cAAc,CAACF,MAAD,CAAf,CAA1B;AACD;;AAED,WAAS0I,mBAAT,CAA6BrK,EAA7B,EAA2D;AACzD,QAAID,OAAqB,GAAG,EAA5B;;AAEA+B,IAAAA,cAAc,EAAE,OAAO,CAACC,OAAO,CAAC/B,EAAD,CAAf,EAAqB;AACnC,UAAIgC,GAAG,GAAGC,YAAY,CAACjC,EAAD,CAAtB;;AAEA,cAAQgC,GAAG,KAAK,CAAhB;AACE,aAAK,CAAL;AACE,gBAAMF,cAAN;AAEF;;AACA,aAAK,CAAL;AAAQ;AACN/B,YAAAA,OAAO,CAAC+J,MAAR,GAAiBQ,sBAAsB,CAACrI,YAAY,CAACjC,EAAD,CAAb,CAAvC;AACA;AACD;AAED;;AACA,aAAK,CAAL;AAAQ;AACND,YAAAA,OAAO,CAACkK,WAAR,GAAsBhI,YAAY,CAACjC,EAAD,CAAZ,KAAqB,CAA3C;AACA;AACD;AAED;;AACA,aAAK,CAAL;AAAQ;AACN,gBAAI6F,KAAK,GAAGK,mBAAmB,CAAClG,EAAD,CAA/B;AACAD,YAAAA,OAAO,CAACoK,UAAR,GAAqBV,wBAAwB,CAACzJ,EAAD,CAA7C;AACAA,YAAAA,EAAE,CAAC6F,KAAH,GAAWA,KAAX;AACA;AACD;;AAED;AACExD,UAAAA,gBAAgB,CAACrC,EAAD,EAAKgC,GAAG,GAAG,CAAX,CAAhB;AAzBJ;AA2BD;;AAED,WAAOjC,OAAP;AACD;;AAcM,WAASwK,gBAAT,CAA0BxK,OAA1B,EAA2D;AAChE,QAAIC,EAAE,GAAGC,aAAa,EAAtB;;AACAuK,IAAAA,iBAAiB,CAACzK,OAAD,EAAUC,EAAV,CAAjB;;AACA,WAAOG,YAAY,CAACH,EAAD,CAAnB;AACD;;AAED,WAASwK,iBAAT,CAA2BzK,OAA3B,EAAgDC,EAAhD,EAAsE;AACpE;AACA,QAAII,IAAI,GAAGL,OAAO,CAACM,GAAnB;;AACA,QAAID,IAAI,KAAKE,SAAb,EAAwB;AACtBC,MAAAA,aAAa,CAACP,EAAD,EAAK,CAAL,CAAb;AACAQ,MAAAA,UAAU,CAACR,EAAD,EAAKI,IAAL,CAAV;AACD,KANmE,CAQpE;;;AACA,QAAIK,OAAO,GAAGV,OAAO,CAACW,MAAtB;;AACA,QAAID,OAAO,KAAKH,SAAhB,EAA2B;AACzBC,MAAAA,aAAa,CAACP,EAAD,EAAK,EAAL,CAAb;AACAW,MAAAA,WAAW,CAACX,EAAD,EAAKS,OAAL,CAAX;AACD,KAbmE,CAepE;;;AACA,QAAI4E,KAAK,GAAGtF,OAAO,CAACuF,IAApB;;AACA,QAAID,KAAK,KAAK/E,SAAd,EAAyB;AACvBC,MAAAA,aAAa,CAACP,EAAD,EAAK,EAAL,CAAb;AACAW,MAAAA,WAAW,CAACX,EAAD,EAAKqF,KAAL,CAAX;AACD,KApBmE,CAsBpE;;;AACA,QAAIoF,QAAQ,GAAG1K,OAAO,CAAC2K,OAAvB;;AACA,QAAID,QAAQ,KAAKnK,SAAjB,EAA4B;AAC1BC,MAAAA,aAAa,CAACP,EAAD,EAAK,EAAL,CAAb;AACAc,MAAAA,aAAa,CAACd,EAAD,EAAKe,SAAS,CAAC0J,QAAD,CAAd,CAAb;AACD,KA3BmE,CA6BpE;;;AACA,QAAIE,SAAS,GAAG5K,OAAO,CAAC6K,QAAxB;;AACA,QAAID,SAAS,KAAKrK,SAAlB,EAA6B;AAC3BC,MAAAA,aAAa,CAACP,EAAD,EAAK,EAAL,CAAb;AACA6H,MAAAA,mBAAmB,CAAC7H,EAAD,EAAK2K,SAAL,CAAnB;AACD,KAlCmE,CAoCpE;;;AACA,QAAIE,MAAM,GAAG9K,OAAO,CAAC+K,KAArB;;AACA,QAAID,MAAM,KAAKvK,SAAf,EAA0B;AACxBC,MAAAA,aAAa,CAACP,EAAD,EAAK,EAAL,CAAb;AACA+K,MAAAA,SAAS,CAAC/K,EAAD,EAAK6K,MAAM,GAAG,CAAH,GAAO,CAAlB,CAAT;AACD,KAzCmE,CA2CpE;;;AACA,QAAI1D,QAAQ,GAAGpH,OAAO,CAACqH,OAAvB;;AACA,QAAID,QAAQ,KAAK7G,SAAjB,EAA4B;AAC1BC,MAAAA,aAAa,CAACP,EAAD,EAAK,EAAL,CAAb;AACAc,MAAAA,aAAa,CAACd,EAAD,EAAKe,SAAS,CAACoG,QAAD,CAAd,CAAb;AACD,KAhDmE,CAkDpE;;;AACA,QAAI1B,WAAW,GAAG1F,OAAO,CAAC2F,KAA1B;;AACA,QAAID,WAAW,KAAKnF,SAApB,EAA+B;AAC7B,WAAK,IAAIqF,KAAT,IAAkBF,WAAlB,EAA+B;AAC7BlF,QAAAA,aAAa,CAACP,EAAD,EAAK,EAAL,CAAb;AACA,YAAI4F,MAAM,GAAG3F,aAAa,EAA1B;;AACAiI,QAAAA,mBAAmB,CAACvC,KAAD,EAAQC,MAAR,CAAnB;;AACArF,QAAAA,aAAa,CAACP,EAAD,EAAK4F,MAAM,CAACC,KAAZ,CAAb;AACAC,QAAAA,eAAe,CAAC9F,EAAD,EAAK4F,MAAL,CAAf;AACAG,QAAAA,cAAc,CAACH,MAAD,CAAd;AACD;AACF,KA7DmE,CA+DpE;;;AACA,QAAIwD,WAAW,GAAGrJ,OAAO,CAACsJ,UAA1B;;AACA,QAAID,WAAW,KAAK9I,SAApB,EAA+B;AAC7BC,MAAAA,aAAa,CAACP,EAAD,EAAK,EAAL,CAAb;AACAO,MAAAA,aAAa,CAACP,EAAD,EAAKoJ,WAAL,CAAb;AACD;AACF;;AAEM,WAAS4B,gBAAT,CAA0BrJ,MAA1B,EAA0D;AAC/D,WAAOsJ,iBAAiB,CAACpJ,cAAc,CAACF,MAAD,CAAf,CAAxB;AACD;;AAED,WAASsJ,iBAAT,CAA2BjL,EAA3B,EAAuD;AACrD,QAAID,OAAmB,GAAG,EAA1B;;AAEA+B,IAAAA,cAAc,EAAE,OAAO,CAACC,OAAO,CAAC/B,EAAD,CAAf,EAAqB;AACnC,UAAIgC,GAAG,GAAGC,YAAY,CAACjC,EAAD,CAAtB;;AAEA,cAAQgC,GAAG,KAAK,CAAhB;AACE,aAAK,CAAL;AACE,gBAAMF,cAAN;AAEF;;AACA,aAAK,CAAL;AAAQ;AACN/B,YAAAA,OAAO,CAACM,GAAR,GAAc6B,SAAS,CAAClC,EAAD;AAAK;AAAe,gBAApB,CAAvB;AACA;AACD;AAED;;AACA,aAAK,CAAL;AAAQ;AACND,YAAAA,OAAO,CAACW,MAAR,GAAiByB,UAAU,CAACnC,EAAD,EAAKiC,YAAY,CAACjC,EAAD,CAAjB,CAA3B;AACA;AACD;AAED;;AACA,aAAK,CAAL;AAAQ;AACND,YAAAA,OAAO,CAACuF,IAAR,GAAenD,UAAU,CAACnC,EAAD,EAAKiC,YAAY,CAACjC,EAAD,CAAjB,CAAzB;AACA;AACD;AAED;;AACA,aAAK,CAAL;AAAQ;AACND,YAAAA,OAAO,CAAC2K,OAAR,GAAkBzI,YAAY,CAACjC,EAAD,CAA9B;AACA;AACD;AAED;;AACA,aAAK,CAAL;AAAQ;AACND,YAAAA,OAAO,CAAC6K,QAAR,GAAmB5C,kBAAkB,CAAChI,EAAD,CAArC;AACA;AACD;AAED;;AACA,aAAK,CAAL;AAAQ;AACND,YAAAA,OAAO,CAAC+K,KAAR,GAAgB,CAAC,CAACI,QAAQ,CAAClL,EAAD,CAA1B;AACA;AACD;AAED;;AACA,aAAK,CAAL;AAAQ;AACND,YAAAA,OAAO,CAACqH,OAAR,GAAkBnF,YAAY,CAACjC,EAAD,CAA9B;AACA;AACD;AAED;;AACA,aAAK,CAAL;AAAQ;AACN,gBAAI6F,KAAK,GAAGK,mBAAmB,CAAClG,EAAD,CAA/B;AACA,gBAAImG,MAAM,GAAGpG,OAAO,CAAC2F,KAAR,KAAkB3F,OAAO,CAAC2F,KAAR,GAAgB,EAAlC,CAAb;AACAS,YAAAA,MAAM,CAACC,IAAP,CAAYgC,mBAAmB,CAACpI,EAAD,CAA/B;AACAA,YAAAA,EAAE,CAAC6F,KAAH,GAAWA,KAAX;AACA;AACD;AAED;;AACA,aAAK,CAAL;AAAQ;AACN9F,YAAAA,OAAO,CAACsJ,UAAR,GAAqBpH,YAAY,CAACjC,EAAD,CAAZ,KAAqB,CAA1C;AACA;AACD;;AAED;AACEqC,UAAAA,gBAAgB,CAACrC,EAAD,EAAKgC,GAAG,GAAG,CAAX,CAAhB;AA9DJ;AAgED;;AAED,WAAOjC,OAAP;AACD;;AAcM,WAASoL,cAAT,CAAwBpL,OAAxB,EAAuD;AAC5D,QAAIC,EAAE,GAAGC,aAAa,EAAtB;;AACAmL,IAAAA,eAAe,CAACrL,OAAD,EAAUC,EAAV,CAAf;;AACA,WAAOG,YAAY,CAACH,EAAD,CAAnB;AACD;;AAED,WAASoL,eAAT,CAAyBrL,OAAzB,EAA4CC,EAA5C,EAAkE;AAChE;AACA,QAAIuI,QAAQ,GAAGxI,OAAO,CAACyI,OAAvB;;AACA,QAAID,QAAQ,KAAKjI,SAAjB,EAA4B;AAC1BC,MAAAA,aAAa,CAACP,EAAD,EAAK,CAAL,CAAb;AACAQ,MAAAA,UAAU,CAACR,EAAD,EAAKuI,QAAL,CAAV;AACD,KAN+D,CAQhE;;;AACA,QAAIS,WAAW,GAAGjJ,OAAO,CAACkJ,UAA1B;;AACA,QAAID,WAAW,KAAK1I,SAApB,EAA+B;AAC7BC,MAAAA,aAAa,CAACP,EAAD,EAAK,EAAL,CAAb;AACAO,MAAAA,aAAa,CAACP,EAAD,EAAKgJ,WAAL,CAAb;AACD,KAb+D,CAehE;;;AACA,QAAIqC,QAAQ,GAAGtL,OAAO,CAACuL,OAAvB;;AACA,QAAID,QAAQ,KAAK/K,SAAjB,EAA4B;AAC1BC,MAAAA,aAAa,CAACP,EAAD,EAAK,EAAL,CAAb;AACAO,MAAAA,aAAa,CAACP,EAAD,EAAKqL,QAAL,CAAb;AACD,KApB+D,CAsBhE;;;AACA,QAAIzC,OAAO,GAAG7I,OAAO,CAAC8I,MAAtB;;AACA,QAAID,OAAO,KAAKtI,SAAhB,EAA2B;AACzBC,MAAAA,aAAa,CAACP,EAAD,EAAK,EAAL,CAAb;AACAc,MAAAA,aAAa,CAACd,EAAD,EAAKe,SAAS,CAAC6H,OAAD,CAAd,CAAb;AACD,KA3B+D,CA6BhE;;;AACA,QAAIH,KAAK,GAAG1I,OAAO,CAAC2I,IAApB;;AACA,QAAID,KAAK,KAAKnI,SAAd,EAAyB;AACvBC,MAAAA,aAAa,CAACP,EAAD,EAAK,EAAL,CAAb;AACAO,MAAAA,aAAa,CAACP,EAAD,EAAK2I,eAAe,CAACF,KAAD,CAApB,CAAb;AACD,KAlC+D,CAoChE;;;AACA,QAAI8C,WAAW,GAAGxL,OAAO,CAACyL,UAA1B;;AACA,QAAID,WAAW,KAAKjL,SAApB,EAA+B;AAC7BC,MAAAA,aAAa,CAACP,EAAD,EAAK,EAAL,CAAb;AACAO,MAAAA,aAAa,CAACP,EAAD,EAAKuL,WAAL,CAAb;AACD,KAzC+D,CA2ChE;;;AACA,QAAIzC,WAAW,GAAG/I,OAAO,CAACgJ,UAA1B;;AACA,QAAID,WAAW,KAAKxI,SAApB,EAA+B;AAC7BC,MAAAA,aAAa,CAACP,EAAD,EAAK,EAAL,CAAb;AACAO,MAAAA,aAAa,CAACP,EAAD,EAAK8I,WAAL,CAAb;AACD,KAhD+D,CAkDhE;;;AACA,QAAII,IAAI,GAAGnJ,OAAO,CAACoJ,GAAnB;;AACA,QAAID,IAAI,KAAK5I,SAAb,EAAwB;AACtBC,MAAAA,aAAa,CAACP,EAAD,EAAK,EAAL,CAAb;AACAW,MAAAA,WAAW,CAACX,EAAD,EAAKkJ,IAAL,CAAX;AACD,KAvD+D,CAyDhE;;;AACA,QAAIuC,aAAa,GAAG1L,OAAO,CAAC2L,OAA5B;;AACA,QAAID,aAAa,KAAKnL,SAAtB,EAAiC;AAC/B,WAAK,IAAIqF,KAAT,IAAkB8F,aAAlB,EAAiC;AAC/BlL,QAAAA,aAAa,CAACP,EAAD,EAAK,EAAL,CAAb;AACA,YAAI4F,MAAM,GAAG3F,aAAa,EAA1B;;AACAuK,QAAAA,iBAAiB,CAAC7E,KAAD,EAAQC,MAAR,CAAjB;;AACArF,QAAAA,aAAa,CAACP,EAAD,EAAK4F,MAAM,CAACC,KAAZ,CAAb;AACAC,QAAAA,eAAe,CAAC9F,EAAD,EAAK4F,MAAL,CAAf;AACAG,QAAAA,cAAc,CAACH,MAAD,CAAd;AACD;AACF;AACF;;AAEM,WAAS+F,cAAT,CAAwBhK,MAAxB,EAAsD;AAC3D,WAAOiK,eAAe,CAAC/J,cAAc,CAACF,MAAD,CAAf,CAAtB;AACD;;AAED,WAASiK,eAAT,CAAyB5L,EAAzB,EAAmD;AACjD,QAAID,OAAiB,GAAG,EAAxB;;AAEA+B,IAAAA,cAAc,EAAE,OAAO,CAACC,OAAO,CAAC/B,EAAD,CAAf,EAAqB;AACnC,UAAIgC,GAAG,GAAGC,YAAY,CAACjC,EAAD,CAAtB;;AAEA,cAAQgC,GAAG,KAAK,CAAhB;AACE,aAAK,CAAL;AACE,gBAAMF,cAAN;AAEF;;AACA,aAAK,CAAL;AAAQ;AACN/B,YAAAA,OAAO,CAACyI,OAAR,GAAkBtG,SAAS,CAAClC,EAAD;AAAK;AAAe,gBAApB,CAA3B;AACA;AACD;AAED;;AACA,aAAK,CAAL;AAAQ;AACND,YAAAA,OAAO,CAACkJ,UAAR,GAAqBhH,YAAY,CAACjC,EAAD,CAAZ,KAAqB,CAA1C;AACA;AACD;AAED;;AACA,aAAK,CAAL;AAAQ;AACND,YAAAA,OAAO,CAACuL,OAAR,GAAkBrJ,YAAY,CAACjC,EAAD,CAAZ,KAAqB,CAAvC;AACA;AACD;AAED;;AACA,aAAK,CAAL;AAAQ;AACND,YAAAA,OAAO,CAAC8I,MAAR,GAAiB5G,YAAY,CAACjC,EAAD,CAA7B;AACA;AACD;AAED;;AACA,aAAK,CAAL;AAAQ;AACND,YAAAA,OAAO,CAAC2I,IAAR,GAAegB,eAAe,CAACzH,YAAY,CAACjC,EAAD,CAAb,CAA9B;AACA;AACD;AAED;;AACA,aAAK,CAAL;AAAQ;AACND,YAAAA,OAAO,CAACyL,UAAR,GAAqBvJ,YAAY,CAACjC,EAAD,CAAZ,KAAqB,CAA1C;AACA;AACD;AAED;;AACA,aAAK,CAAL;AAAQ;AACND,YAAAA,OAAO,CAACgJ,UAAR,GAAqB9G,YAAY,CAACjC,EAAD,CAAZ,KAAqB,CAA1C;AACA;AACD;AAED;;AACA,aAAK,CAAL;AAAQ;AACND,YAAAA,OAAO,CAACoJ,GAAR,GAAchH,UAAU,CAACnC,EAAD,EAAKiC,YAAY,CAACjC,EAAD,CAAjB,CAAxB;AACA;AACD;AAED;;AACA,aAAK,CAAL;AAAQ;AACN,gBAAI6F,KAAK,GAAGK,mBAAmB,CAAClG,EAAD,CAA/B;AACA,gBAAImG,MAAM,GAAGpG,OAAO,CAAC2L,OAAR,KAAoB3L,OAAO,CAAC2L,OAAR,GAAkB,EAAtC,CAAb;AACAvF,YAAAA,MAAM,CAACC,IAAP,CAAY6E,iBAAiB,CAACjL,EAAD,CAA7B;AACAA,YAAAA,EAAE,CAAC6F,KAAH,GAAWA,KAAX;AACA;AACD;;AAED;AACExD,UAAAA,gBAAgB,CAACrC,EAAD,EAAKgC,GAAG,GAAG,CAAX,CAAhB;AA9DJ;AAgED;;AAED,WAAOjC,OAAP;AACD;;AASM,WAAS8L,oBAAT,CAA8B9L,OAA9B,EAAmE;AACxE,QAAIC,EAAE,GAAGC,aAAa,EAAtB;;AACA6L,IAAAA,qBAAqB,CAAC/L,OAAD,EAAUC,EAAV,CAArB;;AACA,WAAOG,YAAY,CAACH,EAAD,CAAnB;AACD;;AAED,WAAS8L,qBAAT,CAA+B/L,OAA/B,EAAwDC,EAAxD,EAA8E;AAC5E;AACA,QAAII,IAAI,GAAGL,OAAO,CAACM,GAAnB;;AACA,QAAID,IAAI,KAAKE,SAAb,EAAwB;AACtBC,MAAAA,aAAa,CAACP,EAAD,EAAK,CAAL,CAAb;AACAQ,MAAAA,UAAU,CAACR,EAAD,EAAKI,IAAL,CAAV;AACD,KAN2E,CAQ5E;;;AACA,QAAIqK,QAAQ,GAAG1K,OAAO,CAAC2K,OAAvB;;AACA,QAAID,QAAQ,KAAKnK,SAAjB,EAA4B;AAC1BC,MAAAA,aAAa,CAACP,EAAD,EAAK,EAAL,CAAb;AACAc,MAAAA,aAAa,CAACd,EAAD,EAAKe,SAAS,CAAC0J,QAAD,CAAd,CAAb;AACD,KAb2E,CAe5E;;;AACA,QAAII,MAAM,GAAG9K,OAAO,CAAC+K,KAArB;;AACA,QAAID,MAAM,KAAKvK,SAAf,EAA0B;AACxBC,MAAAA,aAAa,CAACP,EAAD,EAAK,EAAL,CAAb;AACA+K,MAAAA,SAAS,CAAC/K,EAAD,EAAK6K,MAAM,GAAG,CAAH,GAAO,CAAlB,CAAT;AACD,KApB2E,CAsB5E;;;AACA,QAAIkB,MAAM,GAAGhM,OAAO,CAACiM,KAArB;;AACA,QAAID,MAAM,KAAKzL,SAAf,EAA0B;AACxBC,MAAAA,aAAa,CAACP,EAAD,EAAK,EAAL,CAAb;AACAc,MAAAA,aAAa,CAACd,EAAD,EAAKe,SAAS,CAACgL,MAAD,CAAd,CAAb;AACD;AACF;;AAEM,WAASE,oBAAT,CAA8BtK,MAA9B,EAAkE;AACvE,WAAOuK,qBAAqB,CAACrK,cAAc,CAACF,MAAD,CAAf,CAA5B;AACD;;AAED,WAASuK,qBAAT,CAA+BlM,EAA/B,EAA+D;AAC7D,QAAID,OAAuB,GAAG,EAA9B;;AAEA+B,IAAAA,cAAc,EAAE,OAAO,CAACC,OAAO,CAAC/B,EAAD,CAAf,EAAqB;AACnC,UAAIgC,GAAG,GAAGC,YAAY,CAACjC,EAAD,CAAtB;;AAEA,cAAQgC,GAAG,KAAK,CAAhB;AACE,aAAK,CAAL;AACE,gBAAMF,cAAN;AAEF;;AACA,aAAK,CAAL;AAAQ;AACN/B,YAAAA,OAAO,CAACM,GAAR,GAAc6B,SAAS,CAAClC,EAAD;AAAK;AAAe,gBAApB,CAAvB;AACA;AACD;AAED;;AACA,aAAK,CAAL;AAAQ;AACND,YAAAA,OAAO,CAAC2K,OAAR,GAAkBzI,YAAY,CAACjC,EAAD,CAA9B;AACA;AACD;AAED;;AACA,aAAK,CAAL;AAAQ;AACND,YAAAA,OAAO,CAAC+K,KAAR,GAAgB,CAAC,CAACI,QAAQ,CAAClL,EAAD,CAA1B;AACA;AACD;AAED;;AACA,aAAK,CAAL;AAAQ;AACND,YAAAA,OAAO,CAACiM,KAAR,GAAgB/J,YAAY,CAACjC,EAAD,CAA5B;AACA;AACD;;AAED;AACEqC,UAAAA,gBAAgB,CAACrC,EAAD,EAAKgC,GAAG,GAAG,CAAX,CAAhB;AA7BJ;AA+BD;;AAED,WAAOjC,OAAP;AACD;;AAaM,WAASoM,gBAAT,CAA0BpM,OAA1B,EAA2D;AAChE,QAAIC,EAAE,GAAGC,aAAa,EAAtB;;AACAmM,IAAAA,iBAAiB,CAACrM,OAAD,EAAUC,EAAV,CAAjB;;AACA,WAAOG,YAAY,CAACH,EAAD,CAAnB;AACD;;AAED,WAASoM,iBAAT,CAA2BrM,OAA3B,EAAgDC,EAAhD,EAAsE;AACpE;AACA,QAAIuI,QAAQ,GAAGxI,OAAO,CAACyI,OAAvB;;AACA,QAAID,QAAQ,KAAKjI,SAAjB,EAA4B;AAC1BC,MAAAA,aAAa,CAACP,EAAD,EAAK,CAAL,CAAb;AACAQ,MAAAA,UAAU,CAACR,EAAD,EAAKuI,QAAL,CAAV;AACD,KANmE,CAQpE;;;AACA,QAAIE,KAAK,GAAG1I,OAAO,CAAC2I,IAApB;;AACA,QAAID,KAAK,KAAKnI,SAAd,EAAyB;AACvBC,MAAAA,aAAa,CAACP,EAAD,EAAK,EAAL,CAAb;AACAO,MAAAA,aAAa,CAACP,EAAD,EAAK2I,eAAe,CAACF,KAAD,CAApB,CAAb;AACD,KAbmE,CAepE;;;AACA,QAAIG,OAAO,GAAG7I,OAAO,CAAC8I,MAAtB;;AACA,QAAID,OAAO,KAAKtI,SAAhB,EAA2B;AACzBC,MAAAA,aAAa,CAACP,EAAD,EAAK,EAAL,CAAb;AACAc,MAAAA,aAAa,CAACd,EAAD,EAAKe,SAAS,CAAC6H,OAAD,CAAd,CAAb;AACD,KApBmE,CAsBpE;;;AACA,QAAIyD,WAAW,GAAGtM,OAAO,CAACuM,UAA1B;;AACA,QAAID,WAAW,KAAK/L,SAApB,EAA+B;AAC7BC,MAAAA,aAAa,CAACP,EAAD,EAAK,EAAL,CAAb;AACAO,MAAAA,aAAa,CAACP,EAAD,EAAKqM,WAAL,CAAb;AACD,KA3BmE,CA6BpE;;;AACA,QAAIE,SAAS,GAAGxM,OAAO,CAACyM,QAAxB;;AACA,QAAID,SAAS,KAAKjM,SAAlB,EAA6B;AAC3BC,MAAAA,aAAa,CAACP,EAAD,EAAK,EAAL,CAAb;AACAO,MAAAA,aAAa,CAACP,EAAD,EAAKuM,SAAL,CAAb;AACD,KAlCmE,CAoCpE;;;AACA,QAAIE,SAAS,GAAG1M,OAAO,CAAC2M,QAAxB;;AACA,QAAID,SAAS,KAAKnM,SAAlB,EAA6B;AAC3BC,MAAAA,aAAa,CAACP,EAAD,EAAK,EAAL,CAAb;AACAc,MAAAA,aAAa,CAACd,EAAD,EAAKe,SAAS,CAAC0L,SAAD,CAAd,CAAb;AACD,KAzCmE,CA2CpE;;;AACA,QAAIE,OAAO,GAAG5M,OAAO,CAAC6M,MAAtB;;AACA,QAAID,OAAO,KAAKrM,SAAhB,EAA2B;AACzBC,MAAAA,aAAa,CAACP,EAAD,EAAK,EAAL,CAAb;AACAO,MAAAA,aAAa,CAACP,EAAD,EAAK6M,iBAAiB,CAACF,OAAD,CAAtB,CAAb;AACD,KAhDmE,CAkDpE;;;AACA,QAAIG,UAAU,GAAG/M,OAAO,CAACgN,IAAzB;;AACA,QAAID,UAAU,KAAKxM,SAAnB,EAA8B;AAC5B,WAAK,IAAIqF,KAAT,IAAkBmH,UAAlB,EAA8B;AAC5BvM,QAAAA,aAAa,CAACP,EAAD,EAAK,EAAL,CAAb;AACA,YAAI4F,MAAM,GAAG3F,aAAa,EAA1B;;AACA6L,QAAAA,qBAAqB,CAACnG,KAAD,EAAQC,MAAR,CAArB;;AACArF,QAAAA,aAAa,CAACP,EAAD,EAAK4F,MAAM,CAACC,KAAZ,CAAb;AACAC,QAAAA,eAAe,CAAC9F,EAAD,EAAK4F,MAAL,CAAf;AACAG,QAAAA,cAAc,CAACH,MAAD,CAAd;AACD;AACF;AACF;;AAEM,WAASoH,gBAAT,CAA0BrL,MAA1B,EAA0D;AAC/D,WAAOsL,iBAAiB,CAACpL,cAAc,CAACF,MAAD,CAAf,CAAxB;AACD;;AAED,WAASsL,iBAAT,CAA2BjN,EAA3B,EAAuD;AACrD,QAAID,OAAmB,GAAG,EAA1B;;AAEA+B,IAAAA,cAAc,EAAE,OAAO,CAACC,OAAO,CAAC/B,EAAD,CAAf,EAAqB;AACnC,UAAIgC,GAAG,GAAGC,YAAY,CAACjC,EAAD,CAAtB;;AAEA,cAAQgC,GAAG,KAAK,CAAhB;AACE,aAAK,CAAL;AACE,gBAAMF,cAAN;AAEF;;AACA,aAAK,CAAL;AAAQ;AACN/B,YAAAA,OAAO,CAACyI,OAAR,GAAkBtG,SAAS,CAAClC,EAAD;AAAK;AAAe,gBAApB,CAA3B;AACA;AACD;AAED;;AACA,aAAK,CAAL;AAAQ;AACND,YAAAA,OAAO,CAAC2I,IAAR,GAAegB,eAAe,CAACzH,YAAY,CAACjC,EAAD,CAAb,CAA9B;AACA;AACD;AAED;;AACA,aAAK,CAAL;AAAQ;AACND,YAAAA,OAAO,CAAC8I,MAAR,GAAiB5G,YAAY,CAACjC,EAAD,CAA7B;AACA;AACD;AAED;;AACA,aAAK,CAAL;AAAQ;AACND,YAAAA,OAAO,CAACuM,UAAR,GAAqBrK,YAAY,CAACjC,EAAD,CAAZ,KAAqB,CAA1C;AACA;AACD;AAED;;AACA,aAAK,CAAL;AAAQ;AACND,YAAAA,OAAO,CAACyM,QAAR,GAAmBvK,YAAY,CAACjC,EAAD,CAAZ,KAAqB,CAAxC;AACA;AACD;AAED;;AACA,aAAK,CAAL;AAAQ;AACND,YAAAA,OAAO,CAAC2M,QAAR,GAAmBzK,YAAY,CAACjC,EAAD,CAA/B;AACA;AACD;AAED;;AACA,aAAK,CAAL;AAAQ;AACND,YAAAA,OAAO,CAAC6M,MAAR,GAAiBM,iBAAiB,CAACjL,YAAY,CAACjC,EAAD,CAAb,CAAlC;AACA;AACD;AAED;;AACA,aAAK,CAAL;AAAQ;AACN,gBAAI6F,KAAK,GAAGK,mBAAmB,CAAClG,EAAD,CAA/B;AACA,gBAAImG,MAAM,GAAGpG,OAAO,CAACgN,IAAR,KAAiBhN,OAAO,CAACgN,IAAR,GAAe,EAAhC,CAAb;AACA5G,YAAAA,MAAM,CAACC,IAAP,CAAY8F,qBAAqB,CAAClM,EAAD,CAAjC;AACAA,YAAAA,EAAE,CAAC6F,KAAH,GAAWA,KAAX;AACA;AACD;;AAED;AACExD,UAAAA,gBAAgB,CAACrC,EAAD,EAAKgC,GAAG,GAAG,CAAX,CAAhB;AAxDJ;AA0DD;;AAED,WAAOjC,OAAP;AACD;;AASM,WAASoN,0BAAT,CAAoCpN,OAApC,EAA+E;AACpF,QAAIC,EAAE,GAAGC,aAAa,EAAtB;;AACAmN,IAAAA,2BAA2B,CAACrN,OAAD,EAAUC,EAAV,CAA3B;;AACA,WAAOG,YAAY,CAACH,EAAD,CAAnB;AACD;;AAED,WAASoN,2BAAT,CAAqCrN,OAArC,EAAoEC,EAApE,EAA0F;AACxF;AACA,QAAIwC,OAAO,GAAGzC,OAAO,CAAC0C,MAAtB;;AACA,QAAID,OAAO,KAAKlC,SAAhB,EAA2B;AACzBC,MAAAA,aAAa,CAACP,EAAD,EAAK,EAAL,CAAb;AACAc,MAAAA,aAAa,CAACd,EAAD,EAAKe,SAAS,CAACyB,OAAD,CAAd,CAAb;AACD,KANuF,CAQxF;;;AACA,QAAIG,UAAU,GAAG5C,OAAO,CAAC6C,SAAzB;;AACA,QAAID,UAAU,KAAKrC,SAAnB,EAA8B;AAC5BC,MAAAA,aAAa,CAACP,EAAD,EAAK,EAAL,CAAb;AACAW,MAAAA,WAAW,CAACX,EAAD,EAAK2C,UAAL,CAAX;AACD,KAbuF,CAexF;;;AACA,QAAIE,QAAQ,GAAG9C,OAAO,CAAC+C,OAAvB;;AACA,QAAID,QAAQ,KAAKvC,SAAjB,EAA4B;AAC1BC,MAAAA,aAAa,CAACP,EAAD,EAAK,EAAL,CAAb;AACAW,MAAAA,WAAW,CAACX,EAAD,EAAK6C,QAAL,CAAX;AACD,KApBuF,CAsBxF;;;AACA,QAAIE,WAAW,GAAGhD,OAAO,CAACiD,UAA1B;;AACA,QAAID,WAAW,KAAKzC,SAApB,EAA+B;AAC7BC,MAAAA,aAAa,CAACP,EAAD,EAAK,EAAL,CAAb;AACAO,MAAAA,aAAa,CAACP,EAAD,EAAK+C,WAAL,CAAb;AACD;AACF;;AAEM,WAASsK,0BAAT,CAAoC1L,MAApC,EAA8E;AACnF,WAAO2L,2BAA2B,CAACzL,cAAc,CAACF,MAAD,CAAf,CAAlC;AACD;;AAED,WAAS2L,2BAAT,CAAqCtN,EAArC,EAA2E;AACzE,QAAID,OAA6B,GAAG,EAApC;;AAEA+B,IAAAA,cAAc,EAAE,OAAO,CAACC,OAAO,CAAC/B,EAAD,CAAf,EAAqB;AACnC,UAAIgC,GAAG,GAAGC,YAAY,CAACjC,EAAD,CAAtB;;AAEA,cAAQgC,GAAG,KAAK,CAAhB;AACE,aAAK,CAAL;AACE,gBAAMF,cAAN;AAEF;;AACA,aAAK,CAAL;AAAQ;AACN/B,YAAAA,OAAO,CAAC0C,MAAR,GAAiBR,YAAY,CAACjC,EAAD,CAA7B;AACA;AACD;AAED;;AACA,aAAK,CAAL;AAAQ;AACND,YAAAA,OAAO,CAAC6C,SAAR,GAAoBT,UAAU,CAACnC,EAAD,EAAKiC,YAAY,CAACjC,EAAD,CAAjB,CAA9B;AACA;AACD;AAED;;AACA,aAAK,CAAL;AAAQ;AACND,YAAAA,OAAO,CAAC+C,OAAR,GAAkBX,UAAU,CAACnC,EAAD,EAAKiC,YAAY,CAACjC,EAAD,CAAjB,CAA5B;AACA;AACD;AAED;;AACA,aAAK,CAAL;AAAQ;AACND,YAAAA,OAAO,CAACiD,UAAR,GAAqBf,YAAY,CAACjC,EAAD,CAAZ,KAAqB,CAA1C;AACA;AACD;;AAED;AACEqC,UAAAA,gBAAgB,CAACrC,EAAD,EAAKgC,GAAG,GAAG,CAAX,CAAhB;AA7BJ;AA+BD;;AAED,WAAOjC,OAAP;AACD;;AAOM,WAASwN,wBAAT,CAAkCxN,OAAlC,EAA2E;AAChF,QAAIC,EAAE,GAAGC,aAAa,EAAtB;;AACAuN,IAAAA,yBAAyB,CAACzN,OAAD,EAAUC,EAAV,CAAzB;;AACA,WAAOG,YAAY,CAACH,EAAD,CAAnB;AACD;;AAED,WAASwN,yBAAT,CAAmCzN,OAAnC,EAAgEC,EAAhE,EAAsF;AACpF;AACA,QAAI2K,SAAS,GAAG5K,OAAO,CAAC6K,QAAxB;;AACA,QAAID,SAAS,KAAKrK,SAAlB,EAA6B;AAC3BC,MAAAA,aAAa,CAACP,EAAD,EAAK,CAAL,CAAb;AACA6H,MAAAA,mBAAmB,CAAC7H,EAAD,EAAK2K,SAAL,CAAnB;AACD,KANmF,CAQpF;;;AACA,QAAI8C,MAAM,GAAG1N,OAAO,CAAC2N,KAArB;;AACA,QAAID,MAAM,KAAKnN,SAAf,EAA0B;AACxBC,MAAAA,aAAa,CAACP,EAAD,EAAK,EAAL,CAAb;AACAO,MAAAA,aAAa,CAACP,EAAD,EAAKyN,MAAM,CAACnJ,MAAZ,CAAb,EAAkCC,UAAU,CAACvE,EAAD,EAAKyN,MAAL,CAA5C;AACD;AACF;;AAEM,WAASE,wBAAT,CAAkChM,MAAlC,EAA0E;AAC/E,WAAOiM,yBAAyB,CAAC/L,cAAc,CAACF,MAAD,CAAf,CAAhC;AACD;;AAED,WAASiM,yBAAT,CAAmC5N,EAAnC,EAAuE;AACrE,QAAID,OAA2B,GAAG,EAAlC;;AAEA+B,IAAAA,cAAc,EAAE,OAAO,CAACC,OAAO,CAAC/B,EAAD,CAAf,EAAqB;AACnC,UAAIgC,GAAG,GAAGC,YAAY,CAACjC,EAAD,CAAtB;;AAEA,cAAQgC,GAAG,KAAK,CAAhB;AACE,aAAK,CAAL;AACE,gBAAMF,cAAN;AAEF;;AACA,aAAK,CAAL;AAAQ;AACN/B,YAAAA,OAAO,CAAC6K,QAAR,GAAmB5C,kBAAkB,CAAChI,EAAD,CAArC;AACA;AACD;AAED;;AACA,aAAK,CAAL;AAAQ;AACND,YAAAA,OAAO,CAAC2N,KAAR,GAAgBhJ,SAAS,CAAC1E,EAAD,EAAKiC,YAAY,CAACjC,EAAD,CAAjB,CAAzB;AACA;AACD;;AAED;AACEqC,UAAAA,gBAAgB,CAACrC,EAAD,EAAKgC,GAAG,GAAG,CAAX,CAAhB;AAjBJ;AAmBD;;AAED,WAAOjC,OAAP;AACD;;AAOM,WAAS8N,oBAAT,CAA8B9N,OAA9B,EAAmE;AACxE,QAAIC,EAAE,GAAGC,aAAa,EAAtB;;AACA6N,IAAAA,qBAAqB,CAAC/N,OAAD,EAAUC,EAAV,CAArB;;AACA,WAAOG,YAAY,CAACH,EAAD,CAAnB;AACD;;AAED,WAAS8N,qBAAT,CAA+B/N,OAA/B,EAAwDC,EAAxD,EAA8E;AAC5E;AACA,QAAI+N,UAAU,GAAGhO,OAAO,CAACiO,SAAzB;;AACA,QAAID,UAAU,KAAKzN,SAAnB,EAA8B;AAC5BC,MAAAA,aAAa,CAACP,EAAD,EAAK,CAAL,CAAb;AACAO,MAAAA,aAAa,CAACP,EAAD,EAAK+N,UAAL,CAAb;AACD,KAN2E,CAQ5E;;;AACA,QAAIN,MAAM,GAAG1N,OAAO,CAAC2N,KAArB;;AACA,QAAID,MAAM,KAAKnN,SAAf,EAA0B;AACxBC,MAAAA,aAAa,CAACP,EAAD,EAAK,EAAL,CAAb;AACA,UAAI4F,MAAM,GAAG3F,aAAa,EAA1B;;AACAuN,MAAAA,yBAAyB,CAACC,MAAD,EAAS7H,MAAT,CAAzB;;AACArF,MAAAA,aAAa,CAACP,EAAD,EAAK4F,MAAM,CAACC,KAAZ,CAAb;AACAC,MAAAA,eAAe,CAAC9F,EAAD,EAAK4F,MAAL,CAAf;AACAG,MAAAA,cAAc,CAACH,MAAD,CAAd;AACD;AACF;;AAEM,WAASqI,oBAAT,CAA8BtM,MAA9B,EAAkE;AACvE,WAAOuM,qBAAqB,CAACrM,cAAc,CAACF,MAAD,CAAf,CAA5B;AACD;;AAED,WAASuM,qBAAT,CAA+BlO,EAA/B,EAA+D;AAC7D,QAAID,OAAuB,GAAG,EAA9B;;AAEA+B,IAAAA,cAAc,EAAE,OAAO,CAACC,OAAO,CAAC/B,EAAD,CAAf,EAAqB;AACnC,UAAIgC,GAAG,GAAGC,YAAY,CAACjC,EAAD,CAAtB;;AAEA,cAAQgC,GAAG,KAAK,CAAhB;AACE,aAAK,CAAL;AACE,gBAAMF,cAAN;AAEF;;AACA,aAAK,CAAL;AAAQ;AACN/B,YAAAA,OAAO,CAACiO,SAAR,GAAoB/L,YAAY,CAACjC,EAAD,CAAZ,KAAqB,CAAzC;AACA;AACD;AAED;;AACA,aAAK,CAAL;AAAQ;AACN,gBAAI6F,KAAK,GAAGK,mBAAmB,CAAClG,EAAD,CAA/B;AACAD,YAAAA,OAAO,CAAC2N,KAAR,GAAgBE,yBAAyB,CAAC5N,EAAD,CAAzC;AACAA,YAAAA,EAAE,CAAC6F,KAAH,GAAWA,KAAX;AACA;AACD;;AAED;AACExD,UAAAA,gBAAgB,CAACrC,EAAD,EAAKgC,GAAG,GAAG,CAAX,CAAhB;AAnBJ;AAqBD;;AAED,WAAOjC,OAAP;AACD;;AAcD,WAASmG,mBAAT,CAA6BlG,EAA7B,EAAqD;AACnD,QAAIsE,MAAM,GAAGrC,YAAY,CAACjC,EAAD,CAAzB;AACA,QAAI6F,KAAK,GAAG7F,EAAE,CAAC6F,KAAf;AACA7F,IAAAA,EAAE,CAAC6F,KAAH,GAAW7F,EAAE,CAACmO,MAAH,GAAY7J,MAAvB;AACA,WAAOuB,KAAP;AACD;;AAED,WAASxD,gBAAT,CAA0BrC,EAA1B,EAA0CoO,IAA1C,EAA8D;AAC5D,YAAQA,IAAR;AACE,WAAK,CAAL;AAAQ,eAAOlD,QAAQ,CAAClL,EAAD,CAAR,GAAe,IAAtB,EAA4B,CAAG;;AAAC;;AACxC,WAAK,CAAL;AAAQqO,QAAAA,IAAI,CAACrO,EAAD,EAAKiC,YAAY,CAACjC,EAAD,CAAjB,CAAJ;AAA4B;;AACpC,WAAK,CAAL;AAAQqO,QAAAA,IAAI,CAACrO,EAAD,EAAK,CAAL,CAAJ;AAAa;;AACrB,WAAK,CAAL;AAAQqO,QAAAA,IAAI,CAACrO,EAAD,EAAK,CAAL,CAAJ;AAAa;;AACrB;AAAS,cAAM,IAAIsO,KAAJ,CAAU,yBAAyBF,IAAnC,CAAN;AALX;AAOD;;AAED,WAASG,YAAT,CAAsB5I,KAAtB,EAA2C;AACzC,WAAO;AACL6I,MAAAA,GAAG,EAAE7I,KAAK,CAAC8I,UAAN,CAAiB,CAAjB,IAAuB9I,KAAK,CAAC8I,UAAN,CAAiB,CAAjB,KAAuB,EAD9C;AAELC,MAAAA,IAAI,EAAE/I,KAAK,CAAC8I,UAAN,CAAiB,CAAjB,IAAuB9I,KAAK,CAAC8I,UAAN,CAAiB,CAAjB,KAAuB,EAF/C;AAGLE,MAAAA,QAAQ,EAAE;AAHL,KAAP;AAKD;;AAED,WAASC,YAAT,CAAsBjJ,KAAtB,EAA2C;AACzC,QAAI6I,GAAG,GAAG7I,KAAK,CAAC6I,GAAhB;AACA,QAAIE,IAAI,GAAG/I,KAAK,CAAC+I,IAAjB;AACA,WAAOG,MAAM,CAACC,YAAP,CACLN,GAAG,GAAG,MADD,EAELA,GAAG,KAAK,EAFH,EAGLE,IAAI,GAAG,MAHF,EAILA,IAAI,KAAK,EAJJ,CAAP;AAKD,G,CAED;AACA;;;AAQA,WAAS3N,SAAT,CAAmB4E,KAAnB,EAAwC;AACtCA,IAAAA,KAAK,IAAI,CAAT;AACA,WAAO;AACL6I,MAAAA,GAAG,EAAE7I,KADA;AAEL+I,MAAAA,IAAI,EAAE/I,KAAK,IAAI,EAFV;AAGLgJ,MAAAA,QAAQ,EAAEhJ,KAAK,IAAI;AAHd,KAAP;AAKD;;AAID,WAAS1F,aAAT,GAAqC;AACnC,UAAMD,EAAE,GAAG+O,OAAO,CAACC,GAAR,EAAX;AACA,QAAI,CAAChP,EAAL,EAAS,OAAO;AAAEiP,MAAAA,KAAK,EAAE,IAAIC,UAAJ,CAAe,EAAf,CAAT;AAA6Bf,MAAAA,MAAM,EAAE,CAArC;AAAwCtI,MAAAA,KAAK,EAAE;AAA/C,KAAP;AACT7F,IAAAA,EAAE,CAACmO,MAAH,GAAYnO,EAAE,CAAC6F,KAAH,GAAW,CAAvB;AACA,WAAO7F,EAAP;AACD;;AAED,WAAS+F,cAAT,CAAwB/F,EAAxB,EAA8C;AAC5C+O,IAAAA,OAAO,CAAC3I,IAAR,CAAapG,EAAb;AACD;;AAED,WAAS6B,cAAT,CAAwBoN,KAAxB,EAAuD;AACrD,WAAO;AAAEA,MAAAA,KAAF;AAASd,MAAAA,MAAM,EAAE,CAAjB;AAAoBtI,MAAAA,KAAK,EAAEoJ,KAAK,CAAC3K;AAAjC,KAAP;AACD;;AAED,WAASnE,YAAT,CAAsBH,EAAtB,EAAkD;AAChD,QAAIiP,KAAK,GAAGjP,EAAE,CAACiP,KAAf;AACA,QAAIpJ,KAAK,GAAG7F,EAAE,CAAC6F,KAAf;AACA,WAAOoJ,KAAK,CAAC3K,MAAN,KAAiBuB,KAAjB,GAAyBoJ,KAAzB,GAAiCA,KAAK,CAACE,QAAN,CAAe,CAAf,EAAkBtJ,KAAlB,CAAxC;AACD;;AAED,WAASwI,IAAT,CAAcrO,EAAd,EAA8BmO,MAA9B,EAAoD;AAClD,QAAInO,EAAE,CAACmO,MAAH,GAAYA,MAAZ,GAAqBnO,EAAE,CAAC6F,KAA5B,EAAmC;AACjC,YAAM,IAAIyI,KAAJ,CAAU,iBAAV,CAAN;AACD;;AACDtO,IAAAA,EAAE,CAACmO,MAAH,IAAaA,MAAb;AACD;;AAED,WAASpM,OAAT,CAAiB/B,EAAjB,EAA0C;AACxC,WAAOA,EAAE,CAACmO,MAAH,IAAanO,EAAE,CAAC6F,KAAvB;AACD;;AAED,WAASuJ,IAAT,CAAcpP,EAAd,EAA8BqP,KAA9B,EAAqD;AACnD,QAAIJ,KAAK,GAAGjP,EAAE,CAACiP,KAAf;AACA,QAAId,MAAM,GAAGnO,EAAE,CAACmO,MAAhB;AACA,QAAItI,KAAK,GAAG7F,EAAE,CAAC6F,KAAf;AACA,QAAIyJ,WAAW,GAAGnB,MAAM,GAAGkB,KAA3B;;AACA,QAAIC,WAAW,GAAGL,KAAK,CAAC3K,MAAxB,EAAgC;AAC9B,UAAIiL,QAAQ,GAAG,IAAIL,UAAJ,CAAeI,WAAW,GAAG,CAA7B,CAAf;AACAC,MAAAA,QAAQ,CAACC,GAAT,CAAaP,KAAb;AACAjP,MAAAA,EAAE,CAACiP,KAAH,GAAWM,QAAX;AACD;;AACDvP,IAAAA,EAAE,CAACmO,MAAH,GAAYmB,WAAZ;;AACA,QAAIA,WAAW,GAAGzJ,KAAlB,EAAyB;AACvB7F,MAAAA,EAAE,CAAC6F,KAAH,GAAWyJ,WAAX;AACD;;AACD,WAAOnB,MAAP;AACD;;AAED,WAASsB,OAAT,CAAiBzP,EAAjB,EAAiCqP,KAAjC,EAAwD;AACtD,QAAIlB,MAAM,GAAGnO,EAAE,CAACmO,MAAhB;;AACA,QAAIA,MAAM,GAAGkB,KAAT,GAAiBrP,EAAE,CAAC6F,KAAxB,EAA+B;AAC7B,YAAM,IAAIyI,KAAJ,CAAU,iBAAV,CAAN;AACD;;AACDtO,IAAAA,EAAE,CAACmO,MAAH,IAAakB,KAAb;AACA,WAAOlB,MAAP;AACD;;AAED,WAASzJ,SAAT,CAAmB1E,EAAnB,EAAmCqP,KAAnC,EAA8D;AAC5D,QAAIlB,MAAM,GAAGsB,OAAO,CAACzP,EAAD,EAAKqP,KAAL,CAApB;AACA,WAAOrP,EAAE,CAACiP,KAAH,CAASE,QAAT,CAAkBhB,MAAlB,EAA0BA,MAAM,GAAGkB,KAAnC,CAAP;AACD;;AAED,WAAS9K,UAAT,CAAoBvE,EAApB,EAAoC0P,MAApC,EAA8D;AAC5D,QAAIvB,MAAM,GAAGiB,IAAI,CAACpP,EAAD,EAAK0P,MAAM,CAACpL,MAAZ,CAAjB;AACAtE,IAAAA,EAAE,CAACiP,KAAH,CAASO,GAAT,CAAaE,MAAb,EAAqBvB,MAArB;AACD;;AAED,WAAShM,UAAT,CAAoBnC,EAApB,EAAoCqP,KAApC,EAA2D;AACzD;AACA,QAAIlB,MAAM,GAAGsB,OAAO,CAACzP,EAAD,EAAKqP,KAAL,CAApB;AACA,QAAIP,YAAY,GAAGD,MAAM,CAACC,YAA1B;AACA,QAAIG,KAAK,GAAGjP,EAAE,CAACiP,KAAf;AACA,QAAIU,OAAO,GAAG,QAAd;AACA,QAAIC,IAAI,GAAG,EAAX;;AAEA,SAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGR,KAApB,EAA2BQ,CAAC,EAA5B,EAAgC;AAC9B,UAAIC,EAAE,GAAGb,KAAK,CAACY,CAAC,GAAG1B,MAAL,CAAd;AAAA,UAA4B4B,EAA5B;AAAA,UAAwCC,EAAxC;AAAA,UAAoDC,EAApD;AAAA,UAAgEC,CAAhE,CAD8B,CAG9B;;AACA,UAAI,CAACJ,EAAE,GAAG,IAAN,MAAgB,CAApB,EAAuB;AACrBF,QAAAA,IAAI,IAAId,YAAY,CAACgB,EAAD,CAApB;AACD,OAFD,CAIA;AAJA,WAKK,IAAI,CAACA,EAAE,GAAG,IAAN,MAAgB,IAApB,EAA0B;AAC7B,YAAID,CAAC,GAAG,CAAJ,IAASR,KAAb,EAAoBO,IAAI,IAAID,OAAR,CAApB,KACK;AACHI,UAAAA,EAAE,GAAGd,KAAK,CAACY,CAAC,GAAG1B,MAAJ,GAAa,CAAd,CAAV;AACA,cAAI,CAAC4B,EAAE,GAAG,IAAN,MAAgB,IAApB,EAA0BH,IAAI,IAAID,OAAR,CAA1B,KACK;AACHO,YAAAA,CAAC,GAAI,CAACJ,EAAE,GAAG,IAAN,KAAe,CAAhB,GAAsBC,EAAE,GAAG,IAA/B;AACA,gBAAIG,CAAC,GAAG,IAAR,EAAcN,IAAI,IAAID,OAAR,CAAd,KACK;AACHC,cAAAA,IAAI,IAAId,YAAY,CAACoB,CAAD,CAApB;AACAL,cAAAA,CAAC;AACF;AACF;AACF;AACF,OAdI,CAgBL;AAhBK,WAiBA,IAAI,CAACC,EAAE,GAAG,IAAN,KAAe,IAAnB,EAAyB;AAC5B,YAAID,CAAC,GAAG,CAAJ,IAASR,KAAb,EAAoBO,IAAI,IAAID,OAAR,CAApB,KACK;AACHI,UAAAA,EAAE,GAAGd,KAAK,CAACY,CAAC,GAAG1B,MAAJ,GAAa,CAAd,CAAV;AACA6B,UAAAA,EAAE,GAAGf,KAAK,CAACY,CAAC,GAAG1B,MAAJ,GAAa,CAAd,CAAV;AACA,cAAI,CAAC,CAAC4B,EAAE,GAAIC,EAAE,IAAI,CAAb,IAAmB,MAApB,MAAgC,MAApC,EAA4CJ,IAAI,IAAID,OAAR,CAA5C,KACK;AACHO,YAAAA,CAAC,GAAI,CAACJ,EAAE,GAAG,IAAN,KAAe,EAAhB,GAAuB,CAACC,EAAE,GAAG,IAAN,KAAe,CAAtC,GAA4CC,EAAE,GAAG,IAArD;AACA,gBAAIE,CAAC,GAAG,MAAJ,IAAeA,CAAC,IAAI,MAAL,IAAeA,CAAC,IAAI,MAAvC,EAAgDN,IAAI,IAAID,OAAR,CAAhD,KACK;AACHC,cAAAA,IAAI,IAAId,YAAY,CAACoB,CAAD,CAApB;AACAL,cAAAA,CAAC,IAAI,CAAL;AACD;AACF;AACF;AACF,OAfI,CAiBL;AAjBK,WAkBA,IAAI,CAACC,EAAE,GAAG,IAAN,KAAe,IAAnB,EAAyB;AAC5B,YAAID,CAAC,GAAG,CAAJ,IAASR,KAAb,EAAoBO,IAAI,IAAID,OAAR,CAApB,KACK;AACHI,UAAAA,EAAE,GAAGd,KAAK,CAACY,CAAC,GAAG1B,MAAJ,GAAa,CAAd,CAAV;AACA6B,UAAAA,EAAE,GAAGf,KAAK,CAACY,CAAC,GAAG1B,MAAJ,GAAa,CAAd,CAAV;AACA8B,UAAAA,EAAE,GAAGhB,KAAK,CAACY,CAAC,GAAG1B,MAAJ,GAAa,CAAd,CAAV;AACA,cAAI,CAAC,CAAC4B,EAAE,GAAIC,EAAE,IAAI,CAAZ,GAAkBC,EAAE,IAAI,EAAzB,IAAgC,QAAjC,MAA+C,QAAnD,EAA6DL,IAAI,IAAID,OAAR,CAA7D,KACK;AACHO,YAAAA,CAAC,GAAI,CAACJ,EAAE,GAAG,IAAN,KAAe,IAAhB,GAAyB,CAACC,EAAE,GAAG,IAAN,KAAe,IAAxC,GAAiD,CAACC,EAAE,GAAG,IAAN,KAAe,IAAhE,GAAyEC,EAAE,GAAG,IAAlF;AACA,gBAAIC,CAAC,GAAG,OAAJ,IAAeA,CAAC,GAAG,QAAvB,EAAiCN,IAAI,IAAID,OAAR,CAAjC,KACK;AACHO,cAAAA,CAAC,IAAI,OAAL;AACAN,cAAAA,IAAI,IAAId,YAAY,CAAC,CAACoB,CAAC,IAAI,EAAN,IAAY,MAAb,EAAqB,CAACA,CAAC,GAAG,KAAL,IAAc,MAAnC,CAApB;AACAL,cAAAA,CAAC,IAAI,CAAL;AACD;AACF;AACF;AACF,OAjBI,MAmBAD,IAAI,IAAID,OAAR;AACN;;AAED,WAAOC,IAAP;AACD;;AAED,WAASjP,WAAT,CAAqBX,EAArB,EAAqC4P,IAArC,EAAyD;AACvD;AACA,QAAIO,CAAC,GAAGP,IAAI,CAACtL,MAAb;AACA,QAAI8L,SAAS,GAAG,CAAhB,CAHuD,CAKvD;;AACA,SAAK,IAAIP,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGM,CAApB,EAAuBN,CAAC,EAAxB,EAA4B;AAC1B,UAAIK,CAAC,GAAGN,IAAI,CAACnB,UAAL,CAAgBoB,CAAhB,CAAR;;AACA,UAAIK,CAAC,IAAI,MAAL,IAAeA,CAAC,IAAI,MAApB,IAA8BL,CAAC,GAAG,CAAJ,GAAQM,CAA1C,EAA6C;AAC3CD,QAAAA,CAAC,GAAG,CAACA,CAAC,IAAI,EAAN,IAAYN,IAAI,CAACnB,UAAL,CAAgB,EAAEoB,CAAlB,CAAZ,GAAmC,SAAvC;AACD;;AACDO,MAAAA,SAAS,IAAIF,CAAC,GAAG,IAAJ,GAAW,CAAX,GAAeA,CAAC,GAAG,KAAJ,GAAY,CAAZ,GAAgBA,CAAC,GAAG,OAAJ,GAAc,CAAd,GAAkB,CAA9D;AACD;;AACD3P,IAAAA,aAAa,CAACP,EAAD,EAAKoQ,SAAL,CAAb;AAEA,QAAIjC,MAAM,GAAGiB,IAAI,CAACpP,EAAD,EAAKoQ,SAAL,CAAjB;AACA,QAAInB,KAAK,GAAGjP,EAAE,CAACiP,KAAf,CAhBuD,CAkBvD;;AACA,SAAK,IAAIY,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGM,CAApB,EAAuBN,CAAC,EAAxB,EAA4B;AAC1B,UAAIK,CAAC,GAAGN,IAAI,CAACnB,UAAL,CAAgBoB,CAAhB,CAAR;;AACA,UAAIK,CAAC,IAAI,MAAL,IAAeA,CAAC,IAAI,MAApB,IAA8BL,CAAC,GAAG,CAAJ,GAAQM,CAA1C,EAA6C;AAC3CD,QAAAA,CAAC,GAAG,CAACA,CAAC,IAAI,EAAN,IAAYN,IAAI,CAACnB,UAAL,CAAgB,EAAEoB,CAAlB,CAAZ,GAAmC,SAAvC;AACD;;AACD,UAAIK,CAAC,GAAG,IAAR,EAAc;AACZjB,QAAAA,KAAK,CAACd,MAAM,EAAP,CAAL,GAAkB+B,CAAlB;AACD,OAFD,MAEO;AACL,YAAIA,CAAC,GAAG,KAAR,EAAe;AACbjB,UAAAA,KAAK,CAACd,MAAM,EAAP,CAAL,GAAoB+B,CAAC,IAAI,CAAN,GAAW,IAAZ,GAAoB,IAAtC;AACD,SAFD,MAEO;AACL,cAAIA,CAAC,GAAG,OAAR,EAAiB;AACfjB,YAAAA,KAAK,CAACd,MAAM,EAAP,CAAL,GAAoB+B,CAAC,IAAI,EAAN,GAAY,IAAb,GAAqB,IAAvC;AACD,WAFD,MAEO;AACLjB,YAAAA,KAAK,CAACd,MAAM,EAAP,CAAL,GAAoB+B,CAAC,IAAI,EAAN,GAAY,IAAb,GAAqB,IAAvC;AACAjB,YAAAA,KAAK,CAACd,MAAM,EAAP,CAAL,GAAoB+B,CAAC,IAAI,EAAN,GAAY,IAAb,GAAqB,IAAvC;AACD;;AACDjB,UAAAA,KAAK,CAACd,MAAM,EAAP,CAAL,GAAoB+B,CAAC,IAAI,CAAN,GAAW,IAAZ,GAAoB,IAAtC;AACD;;AACDjB,QAAAA,KAAK,CAACd,MAAM,EAAP,CAAL,GAAmB+B,CAAC,GAAG,IAAL,GAAa,IAA/B;AACD;AACF;AACF;;AAED,WAASpK,eAAT,CAAyB9F,EAAzB,EAAyC0P,MAAzC,EAAmE;AACjE,QAAIvB,MAAM,GAAGiB,IAAI,CAACpP,EAAD,EAAK0P,MAAM,CAAC7J,KAAZ,CAAjB;AACA,QAAIwK,IAAI,GAAGrQ,EAAE,CAACiP,KAAd;AACA,QAAIqB,EAAE,GAAGZ,MAAM,CAACT,KAAhB,CAHiE,CAKjE;;AACA,SAAK,IAAIY,CAAC,GAAG,CAAR,EAAWM,CAAC,GAAGT,MAAM,CAAC7J,KAA3B,EAAkCgK,CAAC,GAAGM,CAAtC,EAAyCN,CAAC,EAA1C,EAA8C;AAC5CQ,MAAAA,IAAI,CAACR,CAAC,GAAG1B,MAAL,CAAJ,GAAmBmC,EAAE,CAACT,CAAD,CAArB;AACD;AACF;;AAED,WAAS3E,QAAT,CAAkBlL,EAAlB,EAA0C;AACxC,WAAOA,EAAE,CAACiP,KAAH,CAASQ,OAAO,CAACzP,EAAD,EAAK,CAAL,CAAhB,CAAP;AACD;;AAED,WAAS+K,SAAT,CAAmB/K,EAAnB,EAAmC2F,KAAnC,EAAwD;AACtD,QAAIwI,MAAM,GAAGiB,IAAI,CAACpP,EAAD,EAAK,CAAL,CAAjB;AACAA,IAAAA,EAAE,CAACiP,KAAH,CAASd,MAAT,IAAmBxI,KAAnB;AACD;;AAED,WAAS4K,SAAT,CAAmBvQ,EAAnB,EAA2C;AACzC,QAAImO,MAAM,GAAGsB,OAAO,CAACzP,EAAD,EAAK,CAAL,CAApB;AACA,QAAIiP,KAAK,GAAGjP,EAAE,CAACiP,KAAf,CAFyC,CAIzC;;AACAuB,IAAAA,MAAM,CAAC,CAAD,CAAN,GAAYvB,KAAK,CAACd,MAAM,EAAP,CAAjB;AACAqC,IAAAA,MAAM,CAAC,CAAD,CAAN,GAAYvB,KAAK,CAACd,MAAM,EAAP,CAAjB;AACAqC,IAAAA,MAAM,CAAC,CAAD,CAAN,GAAYvB,KAAK,CAACd,MAAM,EAAP,CAAjB;AACAqC,IAAAA,MAAM,CAAC,CAAD,CAAN,GAAYvB,KAAK,CAACd,MAAM,EAAP,CAAjB;AACA,WAAOsC,GAAG,CAAC,CAAD,CAAV;AACD;;AAED,WAASC,UAAT,CAAoB1Q,EAApB,EAAoC2F,KAApC,EAAyD;AACvD,QAAIwI,MAAM,GAAGiB,IAAI,CAACpP,EAAD,EAAK,CAAL,CAAjB;AACA,QAAIiP,KAAK,GAAGjP,EAAE,CAACiP,KAAf;AACAwB,IAAAA,GAAG,CAAC,CAAD,CAAH,GAAS9K,KAAT,CAHuD,CAKvD;;AACAsJ,IAAAA,KAAK,CAACd,MAAM,EAAP,CAAL,GAAkBqC,MAAM,CAAC,CAAD,CAAxB;AACAvB,IAAAA,KAAK,CAACd,MAAM,EAAP,CAAL,GAAkBqC,MAAM,CAAC,CAAD,CAAxB;AACAvB,IAAAA,KAAK,CAACd,MAAM,EAAP,CAAL,GAAkBqC,MAAM,CAAC,CAAD,CAAxB;AACAvB,IAAAA,KAAK,CAACd,MAAM,EAAP,CAAL,GAAkBqC,MAAM,CAAC,CAAD,CAAxB;AACD;;AAED,WAASG,UAAT,CAAoB3Q,EAApB,EAA4C;AAC1C,QAAImO,MAAM,GAAGsB,OAAO,CAACzP,EAAD,EAAK,CAAL,CAApB;AACA,QAAIiP,KAAK,GAAGjP,EAAE,CAACiP,KAAf,CAF0C,CAI1C;;AACA2B,IAAAA,MAAM,CAAC,CAAD,CAAN,GAAY3B,KAAK,CAACd,MAAM,EAAP,CAAjB;AACAyC,IAAAA,MAAM,CAAC,CAAD,CAAN,GAAY3B,KAAK,CAACd,MAAM,EAAP,CAAjB;AACAyC,IAAAA,MAAM,CAAC,CAAD,CAAN,GAAY3B,KAAK,CAACd,MAAM,EAAP,CAAjB;AACAyC,IAAAA,MAAM,CAAC,CAAD,CAAN,GAAY3B,KAAK,CAACd,MAAM,EAAP,CAAjB;AACAyC,IAAAA,MAAM,CAAC,CAAD,CAAN,GAAY3B,KAAK,CAACd,MAAM,EAAP,CAAjB;AACAyC,IAAAA,MAAM,CAAC,CAAD,CAAN,GAAY3B,KAAK,CAACd,MAAM,EAAP,CAAjB;AACAyC,IAAAA,MAAM,CAAC,CAAD,CAAN,GAAY3B,KAAK,CAACd,MAAM,EAAP,CAAjB;AACAyC,IAAAA,MAAM,CAAC,CAAD,CAAN,GAAY3B,KAAK,CAACd,MAAM,EAAP,CAAjB;AACA,WAAO0C,GAAG,CAAC,CAAD,CAAV;AACD;;AAED,WAASC,WAAT,CAAqB9Q,EAArB,EAAqC2F,KAArC,EAA0D;AACxD,QAAIwI,MAAM,GAAGiB,IAAI,CAACpP,EAAD,EAAK,CAAL,CAAjB;AACA,QAAIiP,KAAK,GAAGjP,EAAE,CAACiP,KAAf;AACA4B,IAAAA,GAAG,CAAC,CAAD,CAAH,GAASlL,KAAT,CAHwD,CAKxD;;AACAsJ,IAAAA,KAAK,CAACd,MAAM,EAAP,CAAL,GAAkByC,MAAM,CAAC,CAAD,CAAxB;AACA3B,IAAAA,KAAK,CAACd,MAAM,EAAP,CAAL,GAAkByC,MAAM,CAAC,CAAD,CAAxB;AACA3B,IAAAA,KAAK,CAACd,MAAM,EAAP,CAAL,GAAkByC,MAAM,CAAC,CAAD,CAAxB;AACA3B,IAAAA,KAAK,CAACd,MAAM,EAAP,CAAL,GAAkByC,MAAM,CAAC,CAAD,CAAxB;AACA3B,IAAAA,KAAK,CAACd,MAAM,EAAP,CAAL,GAAkByC,MAAM,CAAC,CAAD,CAAxB;AACA3B,IAAAA,KAAK,CAACd,MAAM,EAAP,CAAL,GAAkByC,MAAM,CAAC,CAAD,CAAxB;AACA3B,IAAAA,KAAK,CAACd,MAAM,EAAP,CAAL,GAAkByC,MAAM,CAAC,CAAD,CAAxB;AACA3B,IAAAA,KAAK,CAACd,MAAM,EAAP,CAAL,GAAkByC,MAAM,CAAC,CAAD,CAAxB;AACD;;AAED,WAASG,SAAT,CAAmB/Q,EAAnB,EAA2C;AACzC,QAAImO,MAAM,GAAGsB,OAAO,CAACzP,EAAD,EAAK,CAAL,CAApB;AACA,QAAIiP,KAAK,GAAGjP,EAAE,CAACiP,KAAf;AACA,WACEA,KAAK,CAACd,MAAD,CAAL,GACCc,KAAK,CAACd,MAAM,GAAG,CAAV,CAAL,IAAqB,CADtB,GAECc,KAAK,CAACd,MAAM,GAAG,CAAV,CAAL,IAAqB,EAFtB,GAGCc,KAAK,CAACd,MAAM,GAAG,CAAV,CAAL,IAAqB,EAJxB;AAMD;;AAED,WAAS6C,UAAT,CAAoBhR,EAApB,EAAoC2F,KAApC,EAAyD;AACvD,QAAIwI,MAAM,GAAGiB,IAAI,CAACpP,EAAD,EAAK,CAAL,CAAjB;AACA,QAAIiP,KAAK,GAAGjP,EAAE,CAACiP,KAAf;AACAA,IAAAA,KAAK,CAACd,MAAD,CAAL,GAAgBxI,KAAhB;AACAsJ,IAAAA,KAAK,CAACd,MAAM,GAAG,CAAV,CAAL,GAAoBxI,KAAK,IAAI,CAA7B;AACAsJ,IAAAA,KAAK,CAACd,MAAM,GAAG,CAAV,CAAL,GAAoBxI,KAAK,IAAI,EAA7B;AACAsJ,IAAAA,KAAK,CAACd,MAAM,GAAG,CAAV,CAAL,GAAoBxI,KAAK,IAAI,EAA7B;AACD;;AAED,WAASzD,SAAT,CAAmBlC,EAAnB,EAAmC2O,QAAnC,EAA4D;AAC1D,WAAO;AACLH,MAAAA,GAAG,EAAEuC,SAAS,CAAC/Q,EAAD,CADT;AAEL0O,MAAAA,IAAI,EAAEqC,SAAS,CAAC/Q,EAAD,CAFV;AAGL2O,MAAAA;AAHK,KAAP;AAKD;;AAED,WAASnO,UAAT,CAAoBR,EAApB,EAAoC2F,KAApC,EAAuD;AACrDqL,IAAAA,UAAU,CAAChR,EAAD,EAAK2F,KAAK,CAAC6I,GAAX,CAAV;AACAwC,IAAAA,UAAU,CAAChR,EAAD,EAAK2F,KAAK,CAAC+I,IAAX,CAAV;AACD;;AAED,WAASzM,YAAT,CAAsBjC,EAAtB,EAA8C;AAC5C,QAAIkQ,CAAC,GAAG,CAAR;AACA,QAAIvK,KAAK,GAAG,CAAZ;AACA,QAAIsL,CAAJ;;AACA,OAAG;AACDA,MAAAA,CAAC,GAAG/F,QAAQ,CAAClL,EAAD,CAAZ;AACA,UAAIkQ,CAAC,GAAG,EAAR,EAAYvK,KAAK,IAAI,CAACsL,CAAC,GAAG,IAAL,KAAcf,CAAvB;AACZA,MAAAA,CAAC,IAAI,CAAL;AACD,KAJD,QAISe,CAAC,GAAG,IAJb;;AAKA,WAAOtL,KAAP;AACD;;AAED,WAASpF,aAAT,CAAuBP,EAAvB,EAAuC2F,KAAvC,EAA4D;AAC1DA,IAAAA,KAAK,MAAM,CAAX;;AACA,WAAOA,KAAK,IAAI,IAAhB,EAAsB;AACpBoF,MAAAA,SAAS,CAAC/K,EAAD,EAAM2F,KAAK,GAAG,IAAT,GAAiB,IAAtB,CAAT;AACAA,MAAAA,KAAK,MAAM,CAAX;AACD;;AACDoF,IAAAA,SAAS,CAAC/K,EAAD,EAAK2F,KAAL,CAAT;AACD;;AAED,WAASvD,YAAT,CAAsBpC,EAAtB,EAAsC2O,QAAtC,EAA+D;AAC7D,QAAIuC,KAAK,GAAG,CAAZ;AACA,QAAIC,KAAK,GAAG,CAAZ;AACA,QAAIC,KAAK,GAAG,CAAZ;AACA,QAAIH,CAAJ;AAEAA,IAAAA,CAAC,GAAG/F,QAAQ,CAAClL,EAAD,CAAZ;AAAkBkR,IAAAA,KAAK,GAAID,CAAC,GAAG,IAAb;;AAAoB,QAAIA,CAAC,GAAG,IAAR,EAAc;AAClDA,MAAAA,CAAC,GAAG/F,QAAQ,CAAClL,EAAD,CAAZ;AAAkBkR,MAAAA,KAAK,IAAI,CAACD,CAAC,GAAG,IAAL,KAAc,CAAvB;;AAA0B,UAAIA,CAAC,GAAG,IAAR,EAAc;AACxDA,QAAAA,CAAC,GAAG/F,QAAQ,CAAClL,EAAD,CAAZ;AAAkBkR,QAAAA,KAAK,IAAI,CAACD,CAAC,GAAG,IAAL,KAAc,EAAvB;;AAA2B,YAAIA,CAAC,GAAG,IAAR,EAAc;AACzDA,UAAAA,CAAC,GAAG/F,QAAQ,CAAClL,EAAD,CAAZ;AAAkBkR,UAAAA,KAAK,IAAI,CAACD,CAAC,GAAG,IAAL,KAAc,EAAvB;;AAA2B,cAAIA,CAAC,GAAG,IAAR,EAAc;AAEzDA,YAAAA,CAAC,GAAG/F,QAAQ,CAAClL,EAAD,CAAZ;AAAkBmR,YAAAA,KAAK,GAAIF,CAAC,GAAG,IAAb;;AAAoB,gBAAIA,CAAC,GAAG,IAAR,EAAc;AAClDA,cAAAA,CAAC,GAAG/F,QAAQ,CAAClL,EAAD,CAAZ;AAAkBmR,cAAAA,KAAK,IAAI,CAACF,CAAC,GAAG,IAAL,KAAc,CAAvB;;AAA0B,kBAAIA,CAAC,GAAG,IAAR,EAAc;AACxDA,gBAAAA,CAAC,GAAG/F,QAAQ,CAAClL,EAAD,CAAZ;AAAkBmR,gBAAAA,KAAK,IAAI,CAACF,CAAC,GAAG,IAAL,KAAc,EAAvB;;AAA2B,oBAAIA,CAAC,GAAG,IAAR,EAAc;AACzDA,kBAAAA,CAAC,GAAG/F,QAAQ,CAAClL,EAAD,CAAZ;AAAkBmR,kBAAAA,KAAK,IAAI,CAACF,CAAC,GAAG,IAAL,KAAc,EAAvB;;AAA2B,sBAAIA,CAAC,GAAG,IAAR,EAAc;AAEzDA,oBAAAA,CAAC,GAAG/F,QAAQ,CAAClL,EAAD,CAAZ;AAAkBoR,oBAAAA,KAAK,GAAIH,CAAC,GAAG,IAAb;;AAAoB,wBAAIA,CAAC,GAAG,IAAR,EAAc;AAClDA,sBAAAA,CAAC,GAAG/F,QAAQ,CAAClL,EAAD,CAAZ;AAAkBoR,sBAAAA,KAAK,IAAI,CAACH,CAAC,GAAG,IAAL,KAAc,CAAvB;AACnB;AACF;AACF;AACF;AACF;AACF;AACF;AACF;AACF;;AAED,WAAO;AACLzC,MAAAA,GAAG,EAAE0C,KAAK,GAAIC,KAAK,IAAI,EADlB;AAELzC,MAAAA,IAAI,EAAGyC,KAAK,KAAK,CAAX,GAAiBC,KAAK,IAAI,EAF3B;AAGLzC,MAAAA;AAHK,KAAP;AAKD;;AAED,WAAS7N,aAAT,CAAuBd,EAAvB,EAAuC2F,KAAvC,EAA0D;AACxD,QAAIuL,KAAK,GAAGvL,KAAK,CAAC6I,GAAN,KAAc,CAA1B;AACA,QAAI2C,KAAK,GAAG,CAAExL,KAAK,CAAC6I,GAAN,KAAc,EAAf,GAAsB7I,KAAK,CAAC+I,IAAN,IAAc,CAArC,MAA6C,CAAzD;AACA,QAAI0C,KAAK,GAAGzL,KAAK,CAAC+I,IAAN,KAAe,EAA3B,CAHwD,CAKxD;;AACA,QAAI2C,IAAI,GACND,KAAK,KAAK,CAAV,GACED,KAAK,KAAK,CAAV,GACED,KAAK,GAAG,KAAK,EAAb,GACEA,KAAK,GAAG,KAAK,CAAb,GAAiB,CAAjB,GAAqB,CADvB,GAEEA,KAAK,GAAG,KAAK,EAAb,GAAkB,CAAlB,GAAsB,CAH1B,GAIEC,KAAK,GAAG,KAAK,EAAb,GACEA,KAAK,GAAG,KAAK,CAAb,GAAiB,CAAjB,GAAqB,CADvB,GAEEA,KAAK,GAAG,KAAK,EAAb,GAAkB,CAAlB,GAAsB,CAP5B,GAQEC,KAAK,GAAG,KAAK,CAAb,GAAiB,CAAjB,GAAqB,EATzB;AAWA,QAAIjD,MAAM,GAAGiB,IAAI,CAACpP,EAAD,EAAKqR,IAAL,CAAjB;AACA,QAAIpC,KAAK,GAAGjP,EAAE,CAACiP,KAAf;;AAEA,YAAQoC,IAAR;AACE,WAAK,EAAL;AAASpC,QAAAA,KAAK,CAACd,MAAM,GAAG,CAAV,CAAL,GAAqBiD,KAAK,KAAK,CAAX,GAAgB,IAApC;;AACT,WAAK,CAAL;AAAQnC,QAAAA,KAAK,CAACd,MAAM,GAAG,CAAV,CAAL,GAAoBkD,IAAI,KAAK,CAAT,GAAaD,KAAK,GAAG,IAArB,GAA4BA,KAAK,GAAG,IAAxD;;AACR,WAAK,CAAL;AAAQnC,QAAAA,KAAK,CAACd,MAAM,GAAG,CAAV,CAAL,GAAoBkD,IAAI,KAAK,CAAT,GAAcF,KAAK,KAAK,EAAX,GAAiB,IAA9B,GAAsCA,KAAK,KAAK,EAAX,GAAiB,IAA1E;;AACR,WAAK,CAAL;AAAQlC,QAAAA,KAAK,CAACd,MAAM,GAAG,CAAV,CAAL,GAAoBkD,IAAI,KAAK,CAAT,GAAcF,KAAK,KAAK,EAAX,GAAiB,IAA9B,GAAsCA,KAAK,KAAK,EAAX,GAAiB,IAA1E;;AACR,WAAK,CAAL;AAAQlC,QAAAA,KAAK,CAACd,MAAM,GAAG,CAAV,CAAL,GAAoBkD,IAAI,KAAK,CAAT,GAAcF,KAAK,KAAK,CAAX,GAAgB,IAA7B,GAAqCA,KAAK,KAAK,CAAX,GAAgB,IAAxE;;AACR,WAAK,CAAL;AAAQlC,QAAAA,KAAK,CAACd,MAAM,GAAG,CAAV,CAAL,GAAoBkD,IAAI,KAAK,CAAT,GAAaF,KAAK,GAAG,IAArB,GAA4BA,KAAK,GAAG,IAAxD;;AACR,WAAK,CAAL;AAAQlC,QAAAA,KAAK,CAACd,MAAM,GAAG,CAAV,CAAL,GAAoBkD,IAAI,KAAK,CAAT,GAAcH,KAAK,KAAK,EAAX,GAAiB,IAA9B,GAAsCA,KAAK,KAAK,EAAX,GAAiB,IAA1E;;AACR,WAAK,CAAL;AAAQjC,QAAAA,KAAK,CAACd,MAAM,GAAG,CAAV,CAAL,GAAoBkD,IAAI,KAAK,CAAT,GAAcH,KAAK,KAAK,EAAX,GAAiB,IAA9B,GAAsCA,KAAK,KAAK,EAAX,GAAiB,IAA1E;;AACR,WAAK,CAAL;AAAQjC,QAAAA,KAAK,CAACd,MAAM,GAAG,CAAV,CAAL,GAAoBkD,IAAI,KAAK,CAAT,GAAcH,KAAK,KAAK,CAAX,GAAgB,IAA7B,GAAqCA,KAAK,KAAK,CAAX,GAAgB,IAAxE;;AACR,WAAK,CAAL;AAAQjC,QAAAA,KAAK,CAACd,MAAD,CAAL,GAAgBkD,IAAI,KAAK,CAAT,GAAaH,KAAK,GAAG,IAArB,GAA4BA,KAAK,GAAG,IAApD;AAVV;AAYD;;AAED,WAASlJ,kBAAT,CAA4BhI,EAA5B,EAAoD;AAClD,QAAI2F,KAAK,GAAG1D,YAAY,CAACjC,EAAD,CAAxB,CADkD,CAGlD;;AACA,WAAQ2F,KAAK,KAAK,CAAX,GAAgB,EAAEA,KAAK,GAAG,CAAV,CAAvB;AACD;;AAED,WAASkC,mBAAT,CAA6B7H,EAA7B,EAA6C2F,KAA7C,EAAkE;AAChE;AACApF,IAAAA,aAAa,CAACP,EAAD,EAAM2F,KAAK,IAAI,CAAV,GAAgBA,KAAK,IAAI,EAA9B,CAAb;AACD;;AAED,WAAS2L,kBAAT,CAA4BtR,EAA5B,EAAkD;AAChD,QAAI2F,KAAK,GAAGvD,YAAY,CAACpC,EAAD;AAAK;AAAe,SAApB,CAAxB;AACA,QAAIwO,GAAG,GAAG7I,KAAK,CAAC6I,GAAhB;AACA,QAAIE,IAAI,GAAG/I,KAAK,CAAC+I,IAAjB;AACA,QAAI6C,IAAI,GAAG,EAAE/C,GAAG,GAAG,CAAR,CAAX,CAJgD,CAMhD;;AACA,WAAO;AACLA,MAAAA,GAAG,EAAE,CAAEA,GAAG,KAAK,CAAT,GAAeE,IAAI,IAAI,EAAxB,IAA+B6C,IAD/B;AAEL7C,MAAAA,IAAI,EAAGA,IAAI,KAAK,CAAV,GAAe6C,IAFhB;AAGL5C,MAAAA,QAAQ,EAAE;AAHL,KAAP;AAKD;;AAED,WAAS6C,mBAAT,CAA6BxR,EAA7B,EAA6C2F,KAA7C,EAAgE;AAC9D,QAAI6I,GAAG,GAAG7I,KAAK,CAAC6I,GAAhB;AACA,QAAIE,IAAI,GAAG/I,KAAK,CAAC+I,IAAjB;AACA,QAAI6C,IAAI,GAAG7C,IAAI,IAAI,EAAnB,CAH8D,CAK9D;;AACA5N,IAAAA,aAAa,CAACd,EAAD,EAAK;AAChBwO,MAAAA,GAAG,EAAGA,GAAG,IAAI,CAAR,GAAa+C,IADF;AAEhB7C,MAAAA,IAAI,EAAE,CAAEA,IAAI,IAAI,CAAT,GAAeF,GAAG,KAAK,EAAxB,IAA+B+C,IAFrB;AAGhB5C,MAAAA,QAAQ,EAAE;AAHM,KAAL,CAAb;AAKD;;;wBA7yEe7O,kB;wBAgEA4B,kB;uBAoFAY,iB;uBAqFAyB,iB;sBA4FAG,gB;sBAeAM,gB;yBAgCAG,mB;yBAeAI,mB;yBAmCAE,mB;yBA0CAe,mB;oBA0DAK,c;oBAkDAU,c;oBAgEAE,c;oBA6BAI,c;yBA6CAE,mB;yBA4BAO,mB;wBA0CAG,kB;wBAsBAE,kB;6BA6CAE,uB;6BAgEAmB,uB;wBA4EAG,kB;wBAiCAS,kB;sBAsDAG,gB;sBA6EAS,gB;oBA2FAG,c;oBA6EAQ,c;0BAsFAE,oB;0BAoCAI,oB;sBAyDAE,gB;sBAsEAa,gB;gCAgFAG,0B;gCAoCAE,0B;8BAmDAE,wB;8BAsBAI,wB;0BAuCAE,oB;0BA0BAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gCAtkEHwD,c,GAA4C;AACvDC,QAAAA,QAAQ,EAAE,CAD6C;AAEvDC,QAAAA,cAAc,EAAE,CAFuC;AAGvDC,QAAAA,iBAAiB,EAAE,CAHoC;AAIvDC,QAAAA,SAAS,EAAE,CAJ4C;AAKvDC,QAAAA,gBAAgB,EAAE,CALqC;AAMvDC,QAAAA,aAAa,EAAE,CANwC;AAOvDC,QAAAA,YAAY,EAAE,CAPyC;AAQvDC,QAAAA,aAAa,EAAE,CARwC;AASvDC,QAAAA,OAAO,EAAE,CAT8C;AAUvDC,QAAAA,cAAc,EAAE,CAVuC;AAWvDC,QAAAA,aAAa,EAAE,EAXwC;AAYvDC,QAAAA,YAAY,EAAE,EAZyC;AAavDC,QAAAA,UAAU,EAAE,EAb2C;AAcvDC,QAAAA,aAAa,EAAE,IAdwC;AAevDC,QAAAA,SAAS,EAAE,IAf4C;AAgBvDC,QAAAA,YAAY,EAAE,IAhByC;AAiBvDC,QAAAA,OAAO,EAAE,IAjB8C;AAkBvDC,QAAAA,WAAW,EAAE,IAlB0C;AAmBvDC,QAAAA,UAAU,EAAE,IAnB2C;AAoBvDC,QAAAA,eAAe,EAAE,IApBsC;AAqBvDC,QAAAA,sBAAsB,EAAE,KArB+B;AAsBvDC,QAAAA,sBAAsB,EAAE,KAtB+B;AAuBvDC,QAAAA,uBAAuB,EAAE,KAvB8B;AAwBvDC,QAAAA,eAAe,EAAE,KAxBsC;AAyBvDC,QAAAA,uBAAuB,EAAE,KAzB8B;AA0BvDC,QAAAA,eAAe,EAAE,KA1BsC;AA2BvDC,QAAAA,wBAAwB,EAAE,KA3B6B;AA4BvDC,QAAAA,uBAAuB,EAAE,KA5B8B;AA6BvDC,QAAAA,wBAAwB,EAAE,KA7B6B;AA8BvDC,QAAAA,2BAA2B,EAAE,KA9B0B;AA+BvDC,QAAAA,2BAA2B,EAAE,KA/B0B;AAgCvDC,QAAAA,yBAAyB,EAAE,KAhC4B;AAiCvDC,QAAAA,2BAA2B,EAAE,KAjC0B;AAkCvDC,QAAAA,mBAAmB,EAAE,KAlCkC;AAmCvDC,QAAAA,8BAA8B,EAAE,KAnCuB;AAoCvDC,QAAAA,qBAAqB,EAAE,KApCgC;AAqCvDC,QAAAA,qBAAqB,EAAE,KArCgC;AAsCvDC,QAAAA,kBAAkB,EAAE,KAtCmC;AAuCvDC,QAAAA,uBAAuB,EAAE,KAvC8B;AAwCvDC,QAAAA,6BAA6B,EAAE,KAxCwB;AAyCvDC,QAAAA,uBAAuB,EAAE,KAzC8B;AA0CvDC,QAAAA,sBAAsB,EAAE,KA1C+B;AA2CvDC,QAAAA,8BAA8B,EAAE,KA3CuB;AA4CvDC,QAAAA,8CAA8C,EAAE,KA5CO;AA6CvDC,QAAAA,6BAA6B,EAAE,KA7CwB;AA8CvDC,QAAAA,WAAW,EAAE,KA9C0C;AA+CvDC,QAAAA,mBAAmB,EAAE,KA/CkC;AAgDvDC,QAAAA,cAAc,EAAE,KAhDuC;AAiDvDC,QAAAA,cAAc,EAAE,KAjDuC;AAkDvDC,QAAAA,iBAAiB,EAAE,KAlDoC;AAmDvDC,QAAAA,cAAc,EAAE,KAnDuC;AAoDvDC,QAAAA,eAAe,EAAE,KApDsC;AAqDvDC,QAAAA,cAAc,EAAE,KArDuC;AAsDvDC,QAAAA,cAAc,EAAE,KAtDuC;AAuDvDC,QAAAA,YAAY,EAAE,KAvDyC;AAwDvDC,QAAAA,WAAW,EAAE,KAxD0C;AAyDvDC,QAAAA,cAAc,EAAE,KAzDuC;AA0DvDC,QAAAA,uBAAuB,EAAE,KA1D8B;AA2DvDC,QAAAA,gBAAgB,EAAE,KA3DqC;AA4DvDC,QAAAA,gBAAgB,EAAE,KA5DqC;AA6DvDC,QAAAA,cAAc,EAAE,KA7DuC;AA8DvDC,QAAAA,4BAA4B,EAAE,KA9DyB;AA+DvDC,QAAAA,kBAAkB,EAAE;AA/DmC,O;;gCAkE5CC,c,GAA8C;AACzD,WAAGC,QAAQ,CAAChE,QAD6C;AAEzD,WAAGgE,QAAQ,CAAC/D,cAF6C;AAGzD,WAAG+D,QAAQ,CAAC9D,iBAH6C;AAIzD,WAAG8D,QAAQ,CAAC7D,SAJ6C;AAKzD,WAAG6D,QAAQ,CAAC5D,gBAL6C;AAMzD,WAAG4D,QAAQ,CAAC3D,aAN6C;AAOzD,WAAG2D,QAAQ,CAAC1D,YAP6C;AAQzD,WAAG0D,QAAQ,CAACzD,aAR6C;AASzD,WAAGyD,QAAQ,CAACxD,OAT6C;AAUzD,WAAGwD,QAAQ,CAACvD,cAV6C;AAWzD,YAAIuD,QAAQ,CAACtD,aAX4C;AAYzD,YAAIsD,QAAQ,CAACrD,YAZ4C;AAazD,YAAIqD,QAAQ,CAACpD,UAb4C;AAczD,cAAMoD,QAAQ,CAACnD,aAd0C;AAezD,cAAMmD,QAAQ,CAAClD,SAf0C;AAgBzD,cAAMkD,QAAQ,CAACjD,YAhB0C;AAiBzD,cAAMiD,QAAQ,CAAChD,OAjB0C;AAkBzD,cAAMgD,QAAQ,CAAC/C,WAlB0C;AAmBzD,cAAM+C,QAAQ,CAAC9C,UAnB0C;AAoBzD,cAAM8C,QAAQ,CAAC7C,eApB0C;AAqBzD,eAAO6C,QAAQ,CAAC5C,sBArByC;AAsBzD,eAAO4C,QAAQ,CAAC3C,sBAtByC;AAuBzD,eAAO2C,QAAQ,CAAC1C,uBAvByC;AAwBzD,eAAO0C,QAAQ,CAACzC,eAxByC;AAyBzD,eAAOyC,QAAQ,CAACxC,uBAzByC;AA0BzD,eAAOwC,QAAQ,CAACvC,eA1ByC;AA2BzD,eAAOuC,QAAQ,CAACtC,wBA3ByC;AA4BzD,eAAOsC,QAAQ,CAACrC,uBA5ByC;AA6BzD,eAAOqC,QAAQ,CAACpC,wBA7ByC;AA8BzD,eAAOoC,QAAQ,CAACnC,2BA9ByC;AA+BzD,eAAOmC,QAAQ,CAAClC,2BA/ByC;AAgCzD,eAAOkC,QAAQ,CAACjC,yBAhCyC;AAiCzD,eAAOiC,QAAQ,CAAChC,2BAjCyC;AAkCzD,eAAOgC,QAAQ,CAAC/B,mBAlCyC;AAmCzD,eAAO+B,QAAQ,CAAC9B,8BAnCyC;AAoCzD,eAAO8B,QAAQ,CAAC7B,qBApCyC;AAqCzD,eAAO6B,QAAQ,CAAC5B,qBArCyC;AAsCzD,eAAO4B,QAAQ,CAAC3B,kBAtCyC;AAuCzD,eAAO2B,QAAQ,CAAC1B,uBAvCyC;AAwCzD,eAAO0B,QAAQ,CAACzB,6BAxCyC;AAyCzD,eAAOyB,QAAQ,CAACxB,uBAzCyC;AA0CzD,eAAOwB,QAAQ,CAACvB,sBA1CyC;AA2CzD,eAAOuB,QAAQ,CAACtB,8BA3CyC;AA4CzD,eAAOsB,QAAQ,CAACrB,8CA5CyC;AA6CzD,eAAOqB,QAAQ,CAACpB,6BA7CyC;AA8CzD,eAAOoB,QAAQ,CAACnB,WA9CyC;AA+CzD,eAAOmB,QAAQ,CAAClB,mBA/CyC;AAgDzD,eAAOkB,QAAQ,CAACjB,cAhDyC;AAiDzD,eAAOiB,QAAQ,CAAChB,cAjDyC;AAkDzD,eAAOgB,QAAQ,CAACf,iBAlDyC;AAmDzD,eAAOe,QAAQ,CAACd,cAnDyC;AAoDzD,eAAOc,QAAQ,CAACb,eApDyC;AAqDzD,eAAOa,QAAQ,CAACZ,cArDyC;AAsDzD,eAAOY,QAAQ,CAACX,cAtDyC;AAuDzD,eAAOW,QAAQ,CAACV,YAvDyC;AAwDzD,eAAOU,QAAQ,CAACT,WAxDyC;AAyDzD,eAAOS,QAAQ,CAACR,cAzDyC;AA0DzD,eAAOQ,QAAQ,CAACP,uBA1DyC;AA2DzD,eAAOO,QAAQ,CAACN,gBA3DyC;AA4DzD,eAAOM,QAAQ,CAACL,gBA5DyC;AA6DzD,eAAOK,QAAQ,CAACJ,cA7DyC;AA8DzD,eAAOI,QAAQ,CAACH,4BA9DyC;AA+DzD,eAAOG,QAAQ,CAACF;AA/DyC,O;;;;;;;;;;;;oCA4E9CG,kB,GAAgD;AAC3DC,QAAAA,WAAW,EAAE,CAD8C;AAE3DC,QAAAA,UAAU,EAAE,CAF+C;AAG3DC,QAAAA,UAAU,EAAE,CAH+C;AAI3DC,QAAAA,gBAAgB,EAAE,CAJyC;AAK3DC,QAAAA,cAAc,EAAE,CAL2C;AAM3DC,QAAAA,YAAY,EAAE,CAN6C;AAO3DC,QAAAA,eAAe,EAAE;AAP0C,O;;oCAUhDC,kB,GAAsD;AACjE,WAAGC,YAAY,CAACR,WADiD;AAEjE,WAAGQ,YAAY,CAACP,UAFiD;AAGjE,WAAGO,YAAY,CAACN,UAHiD;AAIjE,WAAGM,YAAY,CAACL,gBAJiD;AAKjE,WAAGK,YAAY,CAACJ,cALiD;AAMjE,WAAGI,YAAY,CAACH,YANiD;AAOjE,WAAGG,YAAY,CAACF;AAPiD,O;;;;;;;;;wCAiBtDG,sB,GAAoD;AAC/DC,QAAAA,iBAAiB,EAAE,CAD4C;AAE/DC,QAAAA,eAAe,EAAE,CAF8C;AAG/DC,QAAAA,eAAe,EAAE,CAH8C;AAI/DC,QAAAA,eAAe,EAAE;AAJ8C,O;;wCAOpDC,sB,GAA8D;AACzE,WAAGC,gBAAgB,CAACL,iBADqD;AAEzE,WAAGK,gBAAgB,CAACJ,eAFqD;AAGzE,WAAGI,gBAAgB,CAACH,eAHqD;AAIzE,WAAGG,gBAAgB,CAACF;AAJqD,O;;;;;;;;mCAa9D/T,iB,GAA+C;AAC1DkU,QAAAA,WAAW,EAAE,CAD6C;AAE1DC,QAAAA,WAAW,EAAE,CAF6C;AAG1DC,QAAAA,aAAa,EAAE;AAH2C,O;;mCAM/C7S,iB,GAAoD;AAC/D,WAAG8S,WAAW,CAACH,WADgD;AAE/D,WAAGG,WAAW,CAACF,WAFgD;AAG/D,WAAGE,WAAW,CAACD;AAHgD,O;;;;;;;;;iCAapDnO,e,GAA6C;AACxDqO,QAAAA,IAAI,EAAE,CADkD;AAExDC,QAAAA,IAAI,EAAE,CAFkD;AAGxDC,QAAAA,IAAI,EAAE,CAHkD;AAIxDC,QAAAA,IAAI,EAAE;AAJkD,O;;iCAO7CzN,e,GAAgD;AAC3D,WAAG0N,SAAS,CAACJ,IAD8C;AAE3D,WAAGI,SAAS,CAACH,IAF8C;AAG3D,WAAGG,SAAS,CAACF,IAH8C;AAI3D,WAAGE,SAAS,CAACD;AAJ8C,O;;;;;;;;0CAahDE,wB,GAAsD;AACjEC,QAAAA,IAAI,EAAE,CAD2D;AAEjEC,QAAAA,QAAQ,EAAE,CAFuD;AAGjEC,QAAAA,QAAQ,EAAE;AAHuD,O;;0CAMtDC,wB,GAAkE;AAC7E,WAAGC,kBAAkB,CAACJ,IADuD;AAE7E,WAAGI,kBAAkB,CAACH,QAFuD;AAG7E,WAAGG,kBAAkB,CAACF;AAHuD,O;;;;;;;;;mCAalE3K,iB,GAA+C;AAC1DmK,QAAAA,IAAI,EAAE,CADoD;AAE1DW,QAAAA,OAAO,EAAE,CAFiD;AAG1DC,QAAAA,QAAQ,EAAE,CAHgD;AAI1DC,QAAAA,IAAI,EAAE;AAJoD,O;;mCAO/C3K,iB,GAAoD;AAC/D,WAAG4K,WAAW,CAACd,IADgD;AAE/D,WAAGc,WAAW,CAACH,OAFgD;AAG/D,WAAGG,WAAW,CAACF,QAHgD;AAI/D,WAAGE,WAAW,CAACD;AAJgD,O;;;;;;;;;;wCAepD9N,sB,GAAoD;AAC/DiN,QAAAA,IAAI,EAAE,CADyD;AAE/De,QAAAA,OAAO,EAAE,CAFsD;AAG/DC,QAAAA,cAAc,EAAE,CAH+C;AAI/DC,QAAAA,WAAW,EAAE,CAJkD;AAK/DC,QAAAA,WAAW,EAAE;AALkD,O;;wCAQpD5N,sB,GAA8D;AACzE,WAAG6N,gBAAgB,CAACnB,IADqD;AAEzE,WAAGmB,gBAAgB,CAACJ,OAFqD;AAGzE,WAAGI,gBAAgB,CAACH,cAHqD;AAIzE,WAAGG,gBAAgB,CAACF,WAJqD;AAKzE,WAAGE,gBAAgB,CAACD;AALqD,O;;AAu4DvEzH,MAAAA,G,GAAM,IAAI2H,YAAJ,CAAiB,CAAjB,C;AACN5H,MAAAA,M,GAAS,IAAItB,UAAJ,CAAeuB,GAAG,CAACf,MAAnB,C;AAETmB,MAAAA,G,GAAM,IAAIwH,YAAJ,CAAiB,CAAjB,C;AACNzH,MAAAA,M,GAAS,IAAI1B,UAAJ,CAAe2B,GAAG,CAACnB,MAAnB,C;AAWTX,MAAAA,O,GAAwB,E", "sourcesContent": ["export const enum RET_CODE {\n  NO_ERROR = \"NO_ERROR\",\n  INVALID_OPENID = \"INVALID_OPENID\",\n  NOT_IN_WHITE_LIST = \"NOT_IN_WHITE_LIST\",\n  NOT_RIGHT = \"NOT_RIGHT\",\n  INVALID_PASSWORD = \"INVALID_PASSWORD\",\n  INVALID_INPUT = \"INVALID_INPUT\",\n  SYSTEM_ERROR = \"SYSTEM_ERROR\",\n  INVALID_TOKEN = \"INVALID_TOKEN\",\n  RELOGIN = \"RELOGIN\",\n  NAME_TOO_SHORT = \"NAME_TOO_SHORT\",\n  NAME_TOO_LONG = \"NAME_TOO_LONG\",\n  NAME_INVALID = \"NAME_INVALID\",\n  NOT_AUTHED = \"NOT_AUTHED\",\n  DATA_TOO_LONG = \"DATA_TOO_LONG\",\n  NOT_EXIST = \"NOT_EXIST\",\n  TOO_FREQUENT = \"TOO_FREQUENT\",\n  TIMEOUT = \"TIMEOUT\",\n  INVALID_SEQ = \"INVALID_SEQ\",\n  IN_PROCESS = \"IN_PROCESS\",\n  NOT_FREE_STATUS = \"NOT_FREE_STATUS\",\n  CLIENT_VERSION_TOO_OLD = \"CLIENT_VERSION_TOO_OLD\",\n  CLIENT_VERSION_TOO_NEW = \"CLIENT_VERSION_TOO_NEW\",\n  CLIENT_VERSION_BACK_OLD = \"CLIENT_VERSION_BACK_OLD\",\n  CARD_NOT_UNLOCK = \"CARD_NOT_UNLOCK\",\n  DEFAULT_CARDGROUP_NOSET = \"DEFAULT_CARDGROUP_NOSET\",\n  CARD_NOT_CONFIG = \"CARD_NOT_CONFIG\",\n  CARD_GROUP_INDEX_INVALID = \"CARD_GROUP_INDEX_INVALID\",\n  CARD_GROUP_NAME_INVALID = \"CARD_GROUP_NAME_INVALID\",\n  CARD_GROUP_NAME_TOO_LONG = \"CARD_GROUP_NAME_TOO_LONG\",\n  CARD_GROUP_FORCE_ID_INVALID = \"CARD_GROUP_FORCE_ID_INVALID\",\n  CARD_GROUP_INDEX_NOT_CONFIG = \"CARD_GROUP_INDEX_NOT_CONFIG\",\n  CARD_GROUP_CARD_DUPLICATE = \"CARD_GROUP_CARD_DUPLICATE\",\n  CARD_GROUP_CARD_NUM_INVALID = \"CARD_GROUP_CARD_NUM_INVALID\",\n  CUSTOM_ROOM_IS_FULL = \"CUSTOM_ROOM_IS_FULL\",\n  CUSTOM_ROOM_POSITION_NOT_EMPTY = \"CUSTOM_ROOM_POSITION_NOT_EMPTY\",\n  CUSTOM_ROOM_CAMP_FULL = \"CUSTOM_ROOM_CAMP_FULL\",\n  CUSTOM_ROOM_NOT_EXIST = \"CUSTOM_ROOM_NOT_EXIST\",\n  NOT_IN_CUSTOM_ROOM = \"NOT_IN_CUSTOM_ROOM\",\n  CUSTOM_ROOM_IN_FIGHTING = \"CUSTOM_ROOM_IN_FIGHTING\",\n  CUSTOM_ROOM_PLAYER_NOT_ENOUGH = \"CUSTOM_ROOM_PLAYER_NOT_ENOUGH\",\n  CUSTOM_ROOM_NOT_CREATOR = \"CUSTOM_ROOM_NOT_CREATOR\",\n  ALREADY_IN_CUSTOM_ROOM = \"ALREADY_IN_CUSTOM_ROOM\",\n  CUSTOM_ROOM_HAS_OFFLINE_PLAYER = \"CUSTOM_ROOM_HAS_OFFLINE_PLAYER\",\n  CUSTOM_ROOM_CHANGE_POS_WITH_PLAYER_NOT_SUPPORT = \"CUSTOM_ROOM_CHANGE_POS_WITH_PLAYER_NOT_SUPPORT\",\n  CUSTOM_ROOM_VERSION_NOT_MATCH = \"CUSTOM_ROOM_VERSION_NOT_MATCH\",\n  NOT_IN_GAME = \"NOT_IN_GAME\",\n  GAME_NOT_IN_PREPARE = \"GAME_NOT_IN_PREPARE\",\n  GAME_HAS_READY = \"GAME_HAS_READY\",\n  GAME_NOT_READY = \"GAME_NOT_READY\",\n  GAME_CANNOT_READY = \"GAME_CANNOT_READY\",\n  ROOM_NOT_EXIST = \"ROOM_NOT_EXIST\",\n  ALREADY_IN_ROOM = \"ALREADY_IN_ROOM\",\n  ROOM_NUM_LIMIT = \"ROOM_NUM_LIMIT\",\n  ROOM_HAS_START = \"ROOM_HAS_START\",\n  ROOM_IS_FULL = \"ROOM_IS_FULL\",\n  NOT_IN_ROOM = \"NOT_IN_ROOM\",\n  ROOM_SIDE_FULL = \"ROOM_SIDE_FULL\",\n  ROOM_POSITION_NOT_EMPTY = \"ROOM_POSITION_NOT_EMPTY\",\n  ROOM_NOT_CORRECT = \"ROOM_NOT_CORRECT\",\n  ROOM_NOT_CREATOR = \"ROOM_NOT_CREATOR\",\n  ROOM_NOT_READY = \"ROOM_NOT_READY\",\n  ROOM_CANNOT_SET_OTHER_PLAYER = \"ROOM_CANNOT_SET_OTHER_PLAYER\",\n  ROOM_CANNOT_ADD_AI = \"ROOM_CANNOT_ADD_AI\",\n}\n\nexport const encodeRET_CODE: { [key: string]: number } = {\n  NO_ERROR: 0,\n  INVALID_OPENID: 1,\n  NOT_IN_WHITE_LIST: 2,\n  NOT_RIGHT: 3,\n  INVALID_PASSWORD: 4,\n  INVALID_INPUT: 5,\n  SYSTEM_ERROR: 6,\n  INVALID_TOKEN: 7,\n  RELOGIN: 8,\n  NAME_TOO_SHORT: 9,\n  NAME_TOO_LONG: 10,\n  NAME_INVALID: 11,\n  NOT_AUTHED: 12,\n  DATA_TOO_LONG: 1002,\n  NOT_EXIST: 1003,\n  TOO_FREQUENT: 1004,\n  TIMEOUT: 1005,\n  INVALID_SEQ: 1006,\n  IN_PROCESS: 1007,\n  NOT_FREE_STATUS: 1008,\n  CLIENT_VERSION_TOO_OLD: 10007,\n  CLIENT_VERSION_TOO_NEW: 10008,\n  CLIENT_VERSION_BACK_OLD: 10009,\n  CARD_NOT_UNLOCK: 10107,\n  DEFAULT_CARDGROUP_NOSET: 10110,\n  CARD_NOT_CONFIG: 10111,\n  CARD_GROUP_INDEX_INVALID: 10112,\n  CARD_GROUP_NAME_INVALID: 10113,\n  CARD_GROUP_NAME_TOO_LONG: 10114,\n  CARD_GROUP_FORCE_ID_INVALID: 10115,\n  CARD_GROUP_INDEX_NOT_CONFIG: 10116,\n  CARD_GROUP_CARD_DUPLICATE: 10117,\n  CARD_GROUP_CARD_NUM_INVALID: 10118,\n  CUSTOM_ROOM_IS_FULL: 10201,\n  CUSTOM_ROOM_POSITION_NOT_EMPTY: 10202,\n  CUSTOM_ROOM_CAMP_FULL: 10203,\n  CUSTOM_ROOM_NOT_EXIST: 10204,\n  NOT_IN_CUSTOM_ROOM: 10205,\n  CUSTOM_ROOM_IN_FIGHTING: 10206,\n  CUSTOM_ROOM_PLAYER_NOT_ENOUGH: 10207,\n  CUSTOM_ROOM_NOT_CREATOR: 10208,\n  ALREADY_IN_CUSTOM_ROOM: 10209,\n  CUSTOM_ROOM_HAS_OFFLINE_PLAYER: 10210,\n  CUSTOM_ROOM_CHANGE_POS_WITH_PLAYER_NOT_SUPPORT: 10211,\n  CUSTOM_ROOM_VERSION_NOT_MATCH: 10212,\n  NOT_IN_GAME: 10501,\n  GAME_NOT_IN_PREPARE: 10506,\n  GAME_HAS_READY: 10507,\n  GAME_NOT_READY: 10511,\n  GAME_CANNOT_READY: 10512,\n  ROOM_NOT_EXIST: 10701,\n  ALREADY_IN_ROOM: 10702,\n  ROOM_NUM_LIMIT: 10703,\n  ROOM_HAS_START: 10704,\n  ROOM_IS_FULL: 10705,\n  NOT_IN_ROOM: 10706,\n  ROOM_SIDE_FULL: 10707,\n  ROOM_POSITION_NOT_EMPTY: 10708,\n  ROOM_NOT_CORRECT: 10709,\n  ROOM_NOT_CREATOR: 10710,\n  ROOM_NOT_READY: 10711,\n  ROOM_CANNOT_SET_OTHER_PLAYER: 10712,\n  ROOM_CANNOT_ADD_AI: 10713,\n};\n\nexport const decodeRET_CODE: { [key: number]: RET_CODE } = {\n  0: RET_CODE.NO_ERROR,\n  1: RET_CODE.INVALID_OPENID,\n  2: RET_CODE.NOT_IN_WHITE_LIST,\n  3: RET_CODE.NOT_RIGHT,\n  4: RET_CODE.INVALID_PASSWORD,\n  5: RET_CODE.INVALID_INPUT,\n  6: RET_CODE.SYSTEM_ERROR,\n  7: RET_CODE.INVALID_TOKEN,\n  8: RET_CODE.RELOGIN,\n  9: RET_CODE.NAME_TOO_SHORT,\n  10: RET_CODE.NAME_TOO_LONG,\n  11: RET_CODE.NAME_INVALID,\n  12: RET_CODE.NOT_AUTHED,\n  1002: RET_CODE.DATA_TOO_LONG,\n  1003: RET_CODE.NOT_EXIST,\n  1004: RET_CODE.TOO_FREQUENT,\n  1005: RET_CODE.TIMEOUT,\n  1006: RET_CODE.INVALID_SEQ,\n  1007: RET_CODE.IN_PROCESS,\n  1008: RET_CODE.NOT_FREE_STATUS,\n  10007: RET_CODE.CLIENT_VERSION_TOO_OLD,\n  10008: RET_CODE.CLIENT_VERSION_TOO_NEW,\n  10009: RET_CODE.CLIENT_VERSION_BACK_OLD,\n  10107: RET_CODE.CARD_NOT_UNLOCK,\n  10110: RET_CODE.DEFAULT_CARDGROUP_NOSET,\n  10111: RET_CODE.CARD_NOT_CONFIG,\n  10112: RET_CODE.CARD_GROUP_INDEX_INVALID,\n  10113: RET_CODE.CARD_GROUP_NAME_INVALID,\n  10114: RET_CODE.CARD_GROUP_NAME_TOO_LONG,\n  10115: RET_CODE.CARD_GROUP_FORCE_ID_INVALID,\n  10116: RET_CODE.CARD_GROUP_INDEX_NOT_CONFIG,\n  10117: RET_CODE.CARD_GROUP_CARD_DUPLICATE,\n  10118: RET_CODE.CARD_GROUP_CARD_NUM_INVALID,\n  10201: RET_CODE.CUSTOM_ROOM_IS_FULL,\n  10202: RET_CODE.CUSTOM_ROOM_POSITION_NOT_EMPTY,\n  10203: RET_CODE.CUSTOM_ROOM_CAMP_FULL,\n  10204: RET_CODE.CUSTOM_ROOM_NOT_EXIST,\n  10205: RET_CODE.NOT_IN_CUSTOM_ROOM,\n  10206: RET_CODE.CUSTOM_ROOM_IN_FIGHTING,\n  10207: RET_CODE.CUSTOM_ROOM_PLAYER_NOT_ENOUGH,\n  10208: RET_CODE.CUSTOM_ROOM_NOT_CREATOR,\n  10209: RET_CODE.ALREADY_IN_CUSTOM_ROOM,\n  10210: RET_CODE.CUSTOM_ROOM_HAS_OFFLINE_PLAYER,\n  10211: RET_CODE.CUSTOM_ROOM_CHANGE_POS_WITH_PLAYER_NOT_SUPPORT,\n  10212: RET_CODE.CUSTOM_ROOM_VERSION_NOT_MATCH,\n  10501: RET_CODE.NOT_IN_GAME,\n  10506: RET_CODE.GAME_NOT_IN_PREPARE,\n  10507: RET_CODE.GAME_HAS_READY,\n  10511: RET_CODE.GAME_NOT_READY,\n  10512: RET_CODE.GAME_CANNOT_READY,\n  10701: RET_CODE.ROOM_NOT_EXIST,\n  10702: RET_CODE.ALREADY_IN_ROOM,\n  10703: RET_CODE.ROOM_NUM_LIMIT,\n  10704: RET_CODE.ROOM_HAS_START,\n  10705: RET_CODE.ROOM_IS_FULL,\n  10706: RET_CODE.NOT_IN_ROOM,\n  10707: RET_CODE.ROOM_SIDE_FULL,\n  10708: RET_CODE.ROOM_POSITION_NOT_EMPTY,\n  10709: RET_CODE.ROOM_NOT_CORRECT,\n  10710: RET_CODE.ROOM_NOT_CREATOR,\n  10711: RET_CODE.ROOM_NOT_READY,\n  10712: RET_CODE.ROOM_CANNOT_SET_OTHER_PLAYER,\n  10713: RET_CODE.ROOM_CANNOT_ADD_AI,\n};\n\nexport const enum ACCOUNT_TYPE {\n  ACCOUNT_RAW = \"ACCOUNT_RAW\",\n  ACCOUNT_QQ = \"ACCOUNT_QQ\",\n  ACCOUNT_WX = \"ACCOUNT_WX\",\n  ACCOUNT_FACEBOOK = \"ACCOUNT_FACEBOOK\",\n  ACCOUNT_GOOGLE = \"ACCOUNT_GOOGLE\",\n  ACCOUNT_MAIL = \"ACCOUNT_MAIL\",\n  ACCOUNT_APPLEID = \"ACCOUNT_APPLEID\",\n}\n\nexport const encodeACCOUNT_TYPE: { [key: string]: number } = {\n  ACCOUNT_RAW: 0,\n  ACCOUNT_QQ: 1,\n  ACCOUNT_WX: 2,\n  ACCOUNT_FACEBOOK: 3,\n  ACCOUNT_GOOGLE: 4,\n  ACCOUNT_MAIL: 5,\n  ACCOUNT_APPLEID: 6,\n};\n\nexport const decodeACCOUNT_TYPE: { [key: number]: ACCOUNT_TYPE } = {\n  0: ACCOUNT_TYPE.ACCOUNT_RAW,\n  1: ACCOUNT_TYPE.ACCOUNT_QQ,\n  2: ACCOUNT_TYPE.ACCOUNT_WX,\n  3: ACCOUNT_TYPE.ACCOUNT_FACEBOOK,\n  4: ACCOUNT_TYPE.ACCOUNT_GOOGLE,\n  5: ACCOUNT_TYPE.ACCOUNT_MAIL,\n  6: ACCOUNT_TYPE.ACCOUNT_APPLEID,\n};\n\nexport const enum AREA_STATUS_TYPE {\n  AREA_STATUSCLOSED = \"AREA_STATUSCLOSED\",\n  AREA_STATUSFREE = \"AREA_STATUSFREE\",\n  AREA_STATUSHIGH = \"AREA_STATUSHIGH\",\n  AREA_STATUSFULL = \"AREA_STATUSFULL\",\n}\n\nexport const encodeAREA_STATUS_TYPE: { [key: string]: number } = {\n  AREA_STATUSCLOSED: 0,\n  AREA_STATUSFREE: 1,\n  AREA_STATUSHIGH: 2,\n  AREA_STATUSFULL: 3,\n};\n\nexport const decodeAREA_STATUS_TYPE: { [key: number]: AREA_STATUS_TYPE } = {\n  0: AREA_STATUS_TYPE.AREA_STATUSCLOSED,\n  1: AREA_STATUS_TYPE.AREA_STATUSFREE,\n  2: AREA_STATUS_TYPE.AREA_STATUSHIGH,\n  3: AREA_STATUS_TYPE.AREA_STATUSFULL,\n};\n\nexport const enum GENDER_TYPE {\n  GENDER_NONE = \"GENDER_NONE\",\n  GENDER_MALE = \"GENDER_MALE\",\n  GENDER_FEMALE = \"GENDER_FEMALE\",\n}\n\nexport const encodeGENDER_TYPE: { [key: string]: number } = {\n  GENDER_NONE: 0,\n  GENDER_MALE: 1,\n  GENDER_FEMALE: 2,\n};\n\nexport const decodeGENDER_TYPE: { [key: number]: GENDER_TYPE } = {\n  0: GENDER_TYPE.GENDER_NONE,\n  1: GENDER_TYPE.GENDER_MALE,\n  2: GENDER_TYPE.GENDER_FEMALE,\n};\n\nexport const enum GAME_MODE {\n  NONE = \"NONE\",\n  P1V1 = \"P1V1\",\n  P2V2 = \"P2V2\",\n  P4V4 = \"P4V4\",\n}\n\nexport const encodeGAME_MODE: { [key: string]: number } = {\n  NONE: 0,\n  P1V1: 1,\n  P2V2: 2,\n  P4V4: 3,\n};\n\nexport const decodeGAME_MODE: { [key: number]: GAME_MODE } = {\n  0: GAME_MODE.NONE,\n  1: GAME_MODE.P1V1,\n  2: GAME_MODE.P2V2,\n  3: GAME_MODE.P4V4,\n};\n\nexport const enum CUSTOM_ROOM_STATUS {\n  OPEN = \"OPEN\",\n  CREATING = \"CREATING\",\n  FIGHTING = \"FIGHTING\",\n}\n\nexport const encodeCUSTOM_ROOM_STATUS: { [key: string]: number } = {\n  OPEN: 0,\n  CREATING: 2,\n  FIGHTING: 3,\n};\n\nexport const decodeCUSTOM_ROOM_STATUS: { [key: number]: CUSTOM_ROOM_STATUS } = {\n  0: CUSTOM_ROOM_STATUS.OPEN,\n  2: CUSTOM_ROOM_STATUS.CREATING,\n  3: CUSTOM_ROOM_STATUS.FIGHTING,\n};\n\nexport const enum GAME_RESULT {\n  NONE = \"NONE\",\n  RED_WIN = \"RED_WIN\",\n  BLUE_WIN = \"BLUE_WIN\",\n  DRAW = \"DRAW\",\n}\n\nexport const encodeGAME_RESULT: { [key: string]: number } = {\n  NONE: 0,\n  RED_WIN: 1,\n  BLUE_WIN: 2,\n  DRAW: 3,\n};\n\nexport const decodeGAME_RESULT: { [key: number]: GAME_RESULT } = {\n  0: GAME_RESULT.NONE,\n  1: GAME_RESULT.RED_WIN,\n  2: GAME_RESULT.BLUE_WIN,\n  3: GAME_RESULT.DRAW,\n};\n\nexport const enum ROLE_GAME_STATUS {\n  NONE = \"NONE\",\n  IN_TEAM = \"IN_TEAM\",\n  IN_CUSTOM_ROOM = \"IN_CUSTOM_ROOM\",\n  IN_FIGHTING = \"IN_FIGHTING\",\n  IN_GUILDING = \"IN_GUILDING\",\n}\n\nexport const encodeROLE_GAME_STATUS: { [key: string]: number } = {\n  NONE: 0,\n  IN_TEAM: 1,\n  IN_CUSTOM_ROOM: 2,\n  IN_FIGHTING: 3,\n  IN_GUILDING: 4,\n};\n\nexport const decodeROLE_GAME_STATUS: { [key: number]: ROLE_GAME_STATUS } = {\n  0: ROLE_GAME_STATUS.NONE,\n  1: ROLE_GAME_STATUS.IN_TEAM,\n  2: ROLE_GAME_STATUS.IN_CUSTOM_ROOM,\n  3: ROLE_GAME_STATUS.IN_FIGHTING,\n  4: ROLE_GAME_STATUS.IN_GUILDING,\n};\n\nexport interface RoleBaseAttr {\n  uin?: Long;\n  openid?: string;\n  area_id?: number;\n  money?: Long;\n  gold?: Long;\n  game_money?: Long;\n  level?: number;\n  xp?: number;\n}\n\nexport function encodeRoleBaseAttr(message: RoleBaseAttr): Uint8Array {\n  let bb = popByteBuffer();\n  _encodeRoleBaseAttr(message, bb);\n  return toUint8Array(bb);\n}\n\nfunction _encodeRoleBaseAttr(message: RoleBaseAttr, bb: ByteBuffer): void {\n  // optional fixed64 uin = 1;\n  let $uin = message.uin;\n  if ($uin !== undefined) {\n    writeVarint32(bb, 9);\n    writeInt64(bb, $uin);\n  }\n\n  // optional string openid = 2;\n  let $openid = message.openid;\n  if ($openid !== undefined) {\n    writeVarint32(bb, 18);\n    writeString(bb, $openid);\n  }\n\n  // optional int32 area_id = 3;\n  let $area_id = message.area_id;\n  if ($area_id !== undefined) {\n    writeVarint32(bb, 24);\n    writeVarint64(bb, intToLong($area_id));\n  }\n\n  // optional int64 money = 4;\n  let $money = message.money;\n  if ($money !== undefined) {\n    writeVarint32(bb, 32);\n    writeVarint64(bb, $money);\n  }\n\n  // optional int64 gold = 5;\n  let $gold = message.gold;\n  if ($gold !== undefined) {\n    writeVarint32(bb, 40);\n    writeVarint64(bb, $gold);\n  }\n\n  // optional int64 game_money = 6;\n  let $game_money = message.game_money;\n  if ($game_money !== undefined) {\n    writeVarint32(bb, 48);\n    writeVarint64(bb, $game_money);\n  }\n\n  // optional int32 level = 7;\n  let $level = message.level;\n  if ($level !== undefined) {\n    writeVarint32(bb, 56);\n    writeVarint64(bb, intToLong($level));\n  }\n\n  // optional int32 xp = 8;\n  let $xp = message.xp;\n  if ($xp !== undefined) {\n    writeVarint32(bb, 64);\n    writeVarint64(bb, intToLong($xp));\n  }\n}\n\nexport function decodeRoleBaseAttr(binary: Uint8Array): RoleBaseAttr {\n  return _decodeRoleBaseAttr(wrapByteBuffer(binary));\n}\n\nfunction _decodeRoleBaseAttr(bb: ByteBuffer): RoleBaseAttr {\n  let message: RoleBaseAttr = {} as any;\n\n  end_of_message: while (!isAtEnd(bb)) {\n    let tag = readVarint32(bb);\n\n    switch (tag >>> 3) {\n      case 0:\n        break end_of_message;\n\n      // optional fixed64 uin = 1;\n      case 1: {\n        message.uin = readInt64(bb, /* unsigned */ true);\n        break;\n      }\n\n      // optional string openid = 2;\n      case 2: {\n        message.openid = readString(bb, readVarint32(bb));\n        break;\n      }\n\n      // optional int32 area_id = 3;\n      case 3: {\n        message.area_id = readVarint32(bb);\n        break;\n      }\n\n      // optional int64 money = 4;\n      case 4: {\n        message.money = readVarint64(bb, /* unsigned */ false);\n        break;\n      }\n\n      // optional int64 gold = 5;\n      case 5: {\n        message.gold = readVarint64(bb, /* unsigned */ false);\n        break;\n      }\n\n      // optional int64 game_money = 6;\n      case 6: {\n        message.game_money = readVarint64(bb, /* unsigned */ false);\n        break;\n      }\n\n      // optional int32 level = 7;\n      case 7: {\n        message.level = readVarint32(bb);\n        break;\n      }\n\n      // optional int32 xp = 8;\n      case 8: {\n        message.xp = readVarint32(bb);\n        break;\n      }\n\n      default:\n        skipUnknownField(bb, tag & 7);\n    }\n  }\n\n  return message;\n}\n\nexport interface RoleExtAttr {\n  gender?: GENDER_TYPE;\n  nick_name?: string;\n  picture?: string;\n  client_ver?: number;\n  client_ver_str?: string;\n  create_time?: number;\n  last_login_time?: Long;\n  last_login_ip?: string;\n  last_logout_time?: number;\n  total_online_time?: number;\n  bit_flag?: Long;\n}\n\nexport function encodeRoleExtAttr(message: RoleExtAttr): Uint8Array {\n  let bb = popByteBuffer();\n  _encodeRoleExtAttr(message, bb);\n  return toUint8Array(bb);\n}\n\nfunction _encodeRoleExtAttr(message: RoleExtAttr, bb: ByteBuffer): void {\n  // optional GENDER_TYPE gender = 1;\n  let $gender = message.gender;\n  if ($gender !== undefined) {\n    writeVarint32(bb, 8);\n    writeVarint32(bb, encodeGENDER_TYPE[$gender]);\n  }\n\n  // optional string nick_name = 2;\n  let $nick_name = message.nick_name;\n  if ($nick_name !== undefined) {\n    writeVarint32(bb, 18);\n    writeString(bb, $nick_name);\n  }\n\n  // optional string picture = 3;\n  let $picture = message.picture;\n  if ($picture !== undefined) {\n    writeVarint32(bb, 26);\n    writeString(bb, $picture);\n  }\n\n  // optional uint32 client_ver = 4;\n  let $client_ver = message.client_ver;\n  if ($client_ver !== undefined) {\n    writeVarint32(bb, 32);\n    writeVarint32(bb, $client_ver);\n  }\n\n  // optional string client_ver_str = 5;\n  let $client_ver_str = message.client_ver_str;\n  if ($client_ver_str !== undefined) {\n    writeVarint32(bb, 42);\n    writeString(bb, $client_ver_str);\n  }\n\n  // optional uint32 create_time = 6;\n  let $create_time = message.create_time;\n  if ($create_time !== undefined) {\n    writeVarint32(bb, 48);\n    writeVarint32(bb, $create_time);\n  }\n\n  // optional uint64 last_login_time = 7;\n  let $last_login_time = message.last_login_time;\n  if ($last_login_time !== undefined) {\n    writeVarint32(bb, 56);\n    writeVarint64(bb, $last_login_time);\n  }\n\n  // optional string last_login_ip = 8;\n  let $last_login_ip = message.last_login_ip;\n  if ($last_login_ip !== undefined) {\n    writeVarint32(bb, 66);\n    writeString(bb, $last_login_ip);\n  }\n\n  // optional uint32 last_logout_time = 9;\n  let $last_logout_time = message.last_logout_time;\n  if ($last_logout_time !== undefined) {\n    writeVarint32(bb, 72);\n    writeVarint32(bb, $last_logout_time);\n  }\n\n  // optional uint32 total_online_time = 10;\n  let $total_online_time = message.total_online_time;\n  if ($total_online_time !== undefined) {\n    writeVarint32(bb, 80);\n    writeVarint32(bb, $total_online_time);\n  }\n\n  // optional uint64 bit_flag = 11;\n  let $bit_flag = message.bit_flag;\n  if ($bit_flag !== undefined) {\n    writeVarint32(bb, 88);\n    writeVarint64(bb, $bit_flag);\n  }\n}\n\nexport function decodeRoleExtAttr(binary: Uint8Array): RoleExtAttr {\n  return _decodeRoleExtAttr(wrapByteBuffer(binary));\n}\n\nfunction _decodeRoleExtAttr(bb: ByteBuffer): RoleExtAttr {\n  let message: RoleExtAttr = {} as any;\n\n  end_of_message: while (!isAtEnd(bb)) {\n    let tag = readVarint32(bb);\n\n    switch (tag >>> 3) {\n      case 0:\n        break end_of_message;\n\n      // optional GENDER_TYPE gender = 1;\n      case 1: {\n        message.gender = decodeGENDER_TYPE[readVarint32(bb)];\n        break;\n      }\n\n      // optional string nick_name = 2;\n      case 2: {\n        message.nick_name = readString(bb, readVarint32(bb));\n        break;\n      }\n\n      // optional string picture = 3;\n      case 3: {\n        message.picture = readString(bb, readVarint32(bb));\n        break;\n      }\n\n      // optional uint32 client_ver = 4;\n      case 4: {\n        message.client_ver = readVarint32(bb) >>> 0;\n        break;\n      }\n\n      // optional string client_ver_str = 5;\n      case 5: {\n        message.client_ver_str = readString(bb, readVarint32(bb));\n        break;\n      }\n\n      // optional uint32 create_time = 6;\n      case 6: {\n        message.create_time = readVarint32(bb) >>> 0;\n        break;\n      }\n\n      // optional uint64 last_login_time = 7;\n      case 7: {\n        message.last_login_time = readVarint64(bb, /* unsigned */ true);\n        break;\n      }\n\n      // optional string last_login_ip = 8;\n      case 8: {\n        message.last_login_ip = readString(bb, readVarint32(bb));\n        break;\n      }\n\n      // optional uint32 last_logout_time = 9;\n      case 9: {\n        message.last_logout_time = readVarint32(bb) >>> 0;\n        break;\n      }\n\n      // optional uint32 total_online_time = 10;\n      case 10: {\n        message.total_online_time = readVarint32(bb) >>> 0;\n        break;\n      }\n\n      // optional uint64 bit_flag = 11;\n      case 11: {\n        message.bit_flag = readVarint64(bb, /* unsigned */ true);\n        break;\n      }\n\n      default:\n        skipUnknownField(bb, tag & 7);\n    }\n  }\n\n  return message;\n}\n\nexport interface ClientData {\n  data?: Uint8Array;\n}\n\nexport function encodeClientData(message: ClientData): Uint8Array {\n  let bb = popByteBuffer();\n  _encodeClientData(message, bb);\n  return toUint8Array(bb);\n}\n\nfunction _encodeClientData(message: ClientData, bb: ByteBuffer): void {\n  // optional bytes data = 1;\n  let $data = message.data;\n  if ($data !== undefined) {\n    writeVarint32(bb, 10);\n    writeVarint32(bb, $data.length), writeBytes(bb, $data);\n  }\n}\n\nexport function decodeClientData(binary: Uint8Array): ClientData {\n  return _decodeClientData(wrapByteBuffer(binary));\n}\n\nfunction _decodeClientData(bb: ByteBuffer): ClientData {\n  let message: ClientData = {} as any;\n\n  end_of_message: while (!isAtEnd(bb)) {\n    let tag = readVarint32(bb);\n\n    switch (tag >>> 3) {\n      case 0:\n        break end_of_message;\n\n      // optional bytes data = 1;\n      case 1: {\n        message.data = readBytes(bb, readVarint32(bb));\n        break;\n      }\n\n      default:\n        skipUnknownField(bb, tag & 7);\n    }\n  }\n\n  return message;\n}\n\nexport interface CardGroupItem {\n  card_id?: number;\n}\n\nexport function encodeCardGroupItem(message: CardGroupItem): Uint8Array {\n  let bb = popByteBuffer();\n  _encodeCardGroupItem(message, bb);\n  return toUint8Array(bb);\n}\n\nfunction _encodeCardGroupItem(message: CardGroupItem, bb: ByteBuffer): void {\n  // optional int32 card_id = 1;\n  let $card_id = message.card_id;\n  if ($card_id !== undefined) {\n    writeVarint32(bb, 8);\n    writeVarint64(bb, intToLong($card_id));\n  }\n}\n\nexport function decodeCardGroupItem(binary: Uint8Array): CardGroupItem {\n  return _decodeCardGroupItem(wrapByteBuffer(binary));\n}\n\nfunction _decodeCardGroupItem(bb: ByteBuffer): CardGroupItem {\n  let message: CardGroupItem = {} as any;\n\n  end_of_message: while (!isAtEnd(bb)) {\n    let tag = readVarint32(bb);\n\n    switch (tag >>> 3) {\n      case 0:\n        break end_of_message;\n\n      // optional int32 card_id = 1;\n      case 1: {\n        message.card_id = readVarint32(bb);\n        break;\n      }\n\n      default:\n        skipUnknownField(bb, tag & 7);\n    }\n  }\n\n  return message;\n}\n\nexport interface CardGroupInfo {\n  index?: number;\n  name?: string;\n  force_id?: number;\n  cards?: CardGroupItem[];\n}\n\nexport function encodeCardGroupInfo(message: CardGroupInfo): Uint8Array {\n  let bb = popByteBuffer();\n  _encodeCardGroupInfo(message, bb);\n  return toUint8Array(bb);\n}\n\nfunction _encodeCardGroupInfo(message: CardGroupInfo, bb: ByteBuffer): void {\n  // optional int32 index = 1;\n  let $index = message.index;\n  if ($index !== undefined) {\n    writeVarint32(bb, 8);\n    writeVarint64(bb, intToLong($index));\n  }\n\n  // optional string name = 2;\n  let $name = message.name;\n  if ($name !== undefined) {\n    writeVarint32(bb, 18);\n    writeString(bb, $name);\n  }\n\n  // optional int32 force_id = 3;\n  let $force_id = message.force_id;\n  if ($force_id !== undefined) {\n    writeVarint32(bb, 24);\n    writeVarint64(bb, intToLong($force_id));\n  }\n\n  // repeated CardGroupItem cards = 4;\n  let array$cards = message.cards;\n  if (array$cards !== undefined) {\n    for (let value of array$cards) {\n      writeVarint32(bb, 34);\n      let nested = popByteBuffer();\n      _encodeCardGroupItem(value, nested);\n      writeVarint32(bb, nested.limit);\n      writeByteBuffer(bb, nested);\n      pushByteBuffer(nested);\n    }\n  }\n}\n\nexport function decodeCardGroupInfo(binary: Uint8Array): CardGroupInfo {\n  return _decodeCardGroupInfo(wrapByteBuffer(binary));\n}\n\nfunction _decodeCardGroupInfo(bb: ByteBuffer): CardGroupInfo {\n  let message: CardGroupInfo = {} as any;\n\n  end_of_message: while (!isAtEnd(bb)) {\n    let tag = readVarint32(bb);\n\n    switch (tag >>> 3) {\n      case 0:\n        break end_of_message;\n\n      // optional int32 index = 1;\n      case 1: {\n        message.index = readVarint32(bb);\n        break;\n      }\n\n      // optional string name = 2;\n      case 2: {\n        message.name = readString(bb, readVarint32(bb));\n        break;\n      }\n\n      // optional int32 force_id = 3;\n      case 3: {\n        message.force_id = readVarint32(bb);\n        break;\n      }\n\n      // repeated CardGroupItem cards = 4;\n      case 4: {\n        let limit = pushTemporaryLength(bb);\n        let values = message.cards || (message.cards = []);\n        values.push(_decodeCardGroupItem(bb));\n        bb.limit = limit;\n        break;\n      }\n\n      default:\n        skipUnknownField(bb, tag & 7);\n    }\n  }\n\n  return message;\n}\n\nexport interface CardInfo {\n  card_id?: number;\n  unlock_time?: number;\n  expired_time?: number;\n  guid?: Long;\n  num?: number;\n  level?: number;\n}\n\nexport function encodeCardInfo(message: CardInfo): Uint8Array {\n  let bb = popByteBuffer();\n  _encodeCardInfo(message, bb);\n  return toUint8Array(bb);\n}\n\nfunction _encodeCardInfo(message: CardInfo, bb: ByteBuffer): void {\n  // optional int32 card_id = 1;\n  let $card_id = message.card_id;\n  if ($card_id !== undefined) {\n    writeVarint32(bb, 8);\n    writeVarint64(bb, intToLong($card_id));\n  }\n\n  // optional uint32 unlock_time = 2;\n  let $unlock_time = message.unlock_time;\n  if ($unlock_time !== undefined) {\n    writeVarint32(bb, 16);\n    writeVarint32(bb, $unlock_time);\n  }\n\n  // optional uint32 expired_time = 3;\n  let $expired_time = message.expired_time;\n  if ($expired_time !== undefined) {\n    writeVarint32(bb, 24);\n    writeVarint32(bb, $expired_time);\n  }\n\n  // optional fixed64 guid = 4;\n  let $guid = message.guid;\n  if ($guid !== undefined) {\n    writeVarint32(bb, 33);\n    writeInt64(bb, $guid);\n  }\n\n  // optional int32 num = 5;\n  let $num = message.num;\n  if ($num !== undefined) {\n    writeVarint32(bb, 40);\n    writeVarint64(bb, intToLong($num));\n  }\n\n  // optional int32 level = 6;\n  let $level = message.level;\n  if ($level !== undefined) {\n    writeVarint32(bb, 48);\n    writeVarint64(bb, intToLong($level));\n  }\n}\n\nexport function decodeCardInfo(binary: Uint8Array): CardInfo {\n  return _decodeCardInfo(wrapByteBuffer(binary));\n}\n\nfunction _decodeCardInfo(bb: ByteBuffer): CardInfo {\n  let message: CardInfo = {} as any;\n\n  end_of_message: while (!isAtEnd(bb)) {\n    let tag = readVarint32(bb);\n\n    switch (tag >>> 3) {\n      case 0:\n        break end_of_message;\n\n      // optional int32 card_id = 1;\n      case 1: {\n        message.card_id = readVarint32(bb);\n        break;\n      }\n\n      // optional uint32 unlock_time = 2;\n      case 2: {\n        message.unlock_time = readVarint32(bb) >>> 0;\n        break;\n      }\n\n      // optional uint32 expired_time = 3;\n      case 3: {\n        message.expired_time = readVarint32(bb) >>> 0;\n        break;\n      }\n\n      // optional fixed64 guid = 4;\n      case 4: {\n        message.guid = readInt64(bb, /* unsigned */ true);\n        break;\n      }\n\n      // optional int32 num = 5;\n      case 5: {\n        message.num = readVarint32(bb);\n        break;\n      }\n\n      // optional int32 level = 6;\n      case 6: {\n        message.level = readVarint32(bb);\n        break;\n      }\n\n      default:\n        skipUnknownField(bb, tag & 7);\n    }\n  }\n\n  return message;\n}\n\nexport interface HeroInfo {\n  hero_id?: number;\n  unlock_time?: number;\n  expired_time?: number;\n}\n\nexport function encodeHeroInfo(message: HeroInfo): Uint8Array {\n  let bb = popByteBuffer();\n  _encodeHeroInfo(message, bb);\n  return toUint8Array(bb);\n}\n\nfunction _encodeHeroInfo(message: HeroInfo, bb: ByteBuffer): void {\n  // optional int32 hero_id = 1;\n  let $hero_id = message.hero_id;\n  if ($hero_id !== undefined) {\n    writeVarint32(bb, 8);\n    writeVarint64(bb, intToLong($hero_id));\n  }\n\n  // optional uint32 unlock_time = 2;\n  let $unlock_time = message.unlock_time;\n  if ($unlock_time !== undefined) {\n    writeVarint32(bb, 16);\n    writeVarint32(bb, $unlock_time);\n  }\n\n  // optional uint32 expired_time = 3;\n  let $expired_time = message.expired_time;\n  if ($expired_time !== undefined) {\n    writeVarint32(bb, 24);\n    writeVarint32(bb, $expired_time);\n  }\n}\n\nexport function decodeHeroInfo(binary: Uint8Array): HeroInfo {\n  return _decodeHeroInfo(wrapByteBuffer(binary));\n}\n\nfunction _decodeHeroInfo(bb: ByteBuffer): HeroInfo {\n  let message: HeroInfo = {} as any;\n\n  end_of_message: while (!isAtEnd(bb)) {\n    let tag = readVarint32(bb);\n\n    switch (tag >>> 3) {\n      case 0:\n        break end_of_message;\n\n      // optional int32 hero_id = 1;\n      case 1: {\n        message.hero_id = readVarint32(bb);\n        break;\n      }\n\n      // optional uint32 unlock_time = 2;\n      case 2: {\n        message.unlock_time = readVarint32(bb) >>> 0;\n        break;\n      }\n\n      // optional uint32 expired_time = 3;\n      case 3: {\n        message.expired_time = readVarint32(bb) >>> 0;\n        break;\n      }\n\n      default:\n        skipUnknownField(bb, tag & 7);\n    }\n  }\n\n  return message;\n}\n\nexport interface CardGroupList {\n  groups?: CardGroupInfo[];\n  default_group?: number;\n}\n\nexport function encodeCardGroupList(message: CardGroupList): Uint8Array {\n  let bb = popByteBuffer();\n  _encodeCardGroupList(message, bb);\n  return toUint8Array(bb);\n}\n\nfunction _encodeCardGroupList(message: CardGroupList, bb: ByteBuffer): void {\n  // repeated CardGroupInfo groups = 1;\n  let array$groups = message.groups;\n  if (array$groups !== undefined) {\n    for (let value of array$groups) {\n      writeVarint32(bb, 10);\n      let nested = popByteBuffer();\n      _encodeCardGroupInfo(value, nested);\n      writeVarint32(bb, nested.limit);\n      writeByteBuffer(bb, nested);\n      pushByteBuffer(nested);\n    }\n  }\n\n  // optional sint32 default_group = 2;\n  let $default_group = message.default_group;\n  if ($default_group !== undefined) {\n    writeVarint32(bb, 16);\n    writeVarint32ZigZag(bb, $default_group);\n  }\n}\n\nexport function decodeCardGroupList(binary: Uint8Array): CardGroupList {\n  return _decodeCardGroupList(wrapByteBuffer(binary));\n}\n\nfunction _decodeCardGroupList(bb: ByteBuffer): CardGroupList {\n  let message: CardGroupList = {} as any;\n\n  end_of_message: while (!isAtEnd(bb)) {\n    let tag = readVarint32(bb);\n\n    switch (tag >>> 3) {\n      case 0:\n        break end_of_message;\n\n      // repeated CardGroupInfo groups = 1;\n      case 1: {\n        let limit = pushTemporaryLength(bb);\n        let values = message.groups || (message.groups = []);\n        values.push(_decodeCardGroupInfo(bb));\n        bb.limit = limit;\n        break;\n      }\n\n      // optional sint32 default_group = 2;\n      case 2: {\n        message.default_group = readVarint32ZigZag(bb);\n        break;\n      }\n\n      default:\n        skipUnknownField(bb, tag & 7);\n    }\n  }\n\n  return message;\n}\n\nexport interface GameCardInfo {\n  card_id?: number;\n  level?: number;\n}\n\nexport function encodeGameCardInfo(message: GameCardInfo): Uint8Array {\n  let bb = popByteBuffer();\n  _encodeGameCardInfo(message, bb);\n  return toUint8Array(bb);\n}\n\nfunction _encodeGameCardInfo(message: GameCardInfo, bb: ByteBuffer): void {\n  // optional int32 card_id = 1;\n  let $card_id = message.card_id;\n  if ($card_id !== undefined) {\n    writeVarint32(bb, 8);\n    writeVarint64(bb, intToLong($card_id));\n  }\n\n  // optional int32 level = 2;\n  let $level = message.level;\n  if ($level !== undefined) {\n    writeVarint32(bb, 16);\n    writeVarint64(bb, intToLong($level));\n  }\n}\n\nexport function decodeGameCardInfo(binary: Uint8Array): GameCardInfo {\n  return _decodeGameCardInfo(wrapByteBuffer(binary));\n}\n\nfunction _decodeGameCardInfo(bb: ByteBuffer): GameCardInfo {\n  let message: GameCardInfo = {} as any;\n\n  end_of_message: while (!isAtEnd(bb)) {\n    let tag = readVarint32(bb);\n\n    switch (tag >>> 3) {\n      case 0:\n        break end_of_message;\n\n      // optional int32 card_id = 1;\n      case 1: {\n        message.card_id = readVarint32(bb);\n        break;\n      }\n\n      // optional int32 level = 2;\n      case 2: {\n        message.level = readVarint32(bb);\n        break;\n      }\n\n      default:\n        skipUnknownField(bb, tag & 7);\n    }\n  }\n\n  return message;\n}\n\nexport interface RoleGameStartInfo {\n  game_id?: Long;\n  mode?: GAME_MODE;\n  map_id?: number;\n  gamesvr_id?: number;\n  begin_time?: number;\n  url?: string;\n  session_id?: number;\n  session_key?: Uint8Array;\n}\n\nexport function encodeRoleGameStartInfo(message: RoleGameStartInfo): Uint8Array {\n  let bb = popByteBuffer();\n  _encodeRoleGameStartInfo(message, bb);\n  return toUint8Array(bb);\n}\n\nfunction _encodeRoleGameStartInfo(message: RoleGameStartInfo, bb: ByteBuffer): void {\n  // optional fixed64 game_id = 1;\n  let $game_id = message.game_id;\n  if ($game_id !== undefined) {\n    writeVarint32(bb, 9);\n    writeInt64(bb, $game_id);\n  }\n\n  // optional GAME_MODE mode = 2;\n  let $mode = message.mode;\n  if ($mode !== undefined) {\n    writeVarint32(bb, 16);\n    writeVarint32(bb, encodeGAME_MODE[$mode]);\n  }\n\n  // optional int32 map_id = 3;\n  let $map_id = message.map_id;\n  if ($map_id !== undefined) {\n    writeVarint32(bb, 24);\n    writeVarint64(bb, intToLong($map_id));\n  }\n\n  // optional uint32 gamesvr_id = 4;\n  let $gamesvr_id = message.gamesvr_id;\n  if ($gamesvr_id !== undefined) {\n    writeVarint32(bb, 32);\n    writeVarint32(bb, $gamesvr_id);\n  }\n\n  // optional uint32 begin_time = 5;\n  let $begin_time = message.begin_time;\n  if ($begin_time !== undefined) {\n    writeVarint32(bb, 40);\n    writeVarint32(bb, $begin_time);\n  }\n\n  // optional string url = 6;\n  let $url = message.url;\n  if ($url !== undefined) {\n    writeVarint32(bb, 50);\n    writeString(bb, $url);\n  }\n\n  // optional uint32 session_id = 7;\n  let $session_id = message.session_id;\n  if ($session_id !== undefined) {\n    writeVarint32(bb, 56);\n    writeVarint32(bb, $session_id);\n  }\n\n  // optional bytes session_key = 8;\n  let $session_key = message.session_key;\n  if ($session_key !== undefined) {\n    writeVarint32(bb, 66);\n    writeVarint32(bb, $session_key.length), writeBytes(bb, $session_key);\n  }\n}\n\nexport function decodeRoleGameStartInfo(binary: Uint8Array): RoleGameStartInfo {\n  return _decodeRoleGameStartInfo(wrapByteBuffer(binary));\n}\n\nfunction _decodeRoleGameStartInfo(bb: ByteBuffer): RoleGameStartInfo {\n  let message: RoleGameStartInfo = {} as any;\n\n  end_of_message: while (!isAtEnd(bb)) {\n    let tag = readVarint32(bb);\n\n    switch (tag >>> 3) {\n      case 0:\n        break end_of_message;\n\n      // optional fixed64 game_id = 1;\n      case 1: {\n        message.game_id = readInt64(bb, /* unsigned */ true);\n        break;\n      }\n\n      // optional GAME_MODE mode = 2;\n      case 2: {\n        message.mode = decodeGAME_MODE[readVarint32(bb)];\n        break;\n      }\n\n      // optional int32 map_id = 3;\n      case 3: {\n        message.map_id = readVarint32(bb);\n        break;\n      }\n\n      // optional uint32 gamesvr_id = 4;\n      case 4: {\n        message.gamesvr_id = readVarint32(bb) >>> 0;\n        break;\n      }\n\n      // optional uint32 begin_time = 5;\n      case 5: {\n        message.begin_time = readVarint32(bb) >>> 0;\n        break;\n      }\n\n      // optional string url = 6;\n      case 6: {\n        message.url = readString(bb, readVarint32(bb));\n        break;\n      }\n\n      // optional uint32 session_id = 7;\n      case 7: {\n        message.session_id = readVarint32(bb) >>> 0;\n        break;\n      }\n\n      // optional bytes session_key = 8;\n      case 8: {\n        message.session_key = readBytes(bb, readVarint32(bb));\n        break;\n      }\n\n      default:\n        skipUnknownField(bb, tag & 7);\n    }\n  }\n\n  return message;\n}\n\nexport interface RoleGameInfo {\n  status?: ROLE_GAME_STATUS;\n  status_time?: number;\n  start_info?: RoleGameStartInfo;\n}\n\nexport function encodeRoleGameInfo(message: RoleGameInfo): Uint8Array {\n  let bb = popByteBuffer();\n  _encodeRoleGameInfo(message, bb);\n  return toUint8Array(bb);\n}\n\nfunction _encodeRoleGameInfo(message: RoleGameInfo, bb: ByteBuffer): void {\n  // optional ROLE_GAME_STATUS status = 1;\n  let $status = message.status;\n  if ($status !== undefined) {\n    writeVarint32(bb, 8);\n    writeVarint32(bb, encodeROLE_GAME_STATUS[$status]);\n  }\n\n  // optional uint32 status_time = 2;\n  let $status_time = message.status_time;\n  if ($status_time !== undefined) {\n    writeVarint32(bb, 16);\n    writeVarint32(bb, $status_time);\n  }\n\n  // optional RoleGameStartInfo start_info = 3;\n  let $start_info = message.start_info;\n  if ($start_info !== undefined) {\n    writeVarint32(bb, 26);\n    let nested = popByteBuffer();\n    _encodeRoleGameStartInfo($start_info, nested);\n    writeVarint32(bb, nested.limit);\n    writeByteBuffer(bb, nested);\n    pushByteBuffer(nested);\n  }\n}\n\nexport function decodeRoleGameInfo(binary: Uint8Array): RoleGameInfo {\n  return _decodeRoleGameInfo(wrapByteBuffer(binary));\n}\n\nfunction _decodeRoleGameInfo(bb: ByteBuffer): RoleGameInfo {\n  let message: RoleGameInfo = {} as any;\n\n  end_of_message: while (!isAtEnd(bb)) {\n    let tag = readVarint32(bb);\n\n    switch (tag >>> 3) {\n      case 0:\n        break end_of_message;\n\n      // optional ROLE_GAME_STATUS status = 1;\n      case 1: {\n        message.status = decodeROLE_GAME_STATUS[readVarint32(bb)];\n        break;\n      }\n\n      // optional uint32 status_time = 2;\n      case 2: {\n        message.status_time = readVarint32(bb) >>> 0;\n        break;\n      }\n\n      // optional RoleGameStartInfo start_info = 3;\n      case 3: {\n        let limit = pushTemporaryLength(bb);\n        message.start_info = _decodeRoleGameStartInfo(bb);\n        bb.limit = limit;\n        break;\n      }\n\n      default:\n        skipUnknownField(bb, tag & 7);\n    }\n  }\n\n  return message;\n}\n\nexport interface GamePlayer {\n  uin?: Long;\n  openid?: string;\n  name?: string;\n  camp_id?: number;\n  position?: number;\n  is_ai?: boolean;\n  hero_id?: number;\n  cards?: GameCardInfo[];\n  session_id?: number;\n}\n\nexport function encodeGamePlayer(message: GamePlayer): Uint8Array {\n  let bb = popByteBuffer();\n  _encodeGamePlayer(message, bb);\n  return toUint8Array(bb);\n}\n\nfunction _encodeGamePlayer(message: GamePlayer, bb: ByteBuffer): void {\n  // optional fixed64 uin = 1;\n  let $uin = message.uin;\n  if ($uin !== undefined) {\n    writeVarint32(bb, 9);\n    writeInt64(bb, $uin);\n  }\n\n  // optional string openid = 2;\n  let $openid = message.openid;\n  if ($openid !== undefined) {\n    writeVarint32(bb, 18);\n    writeString(bb, $openid);\n  }\n\n  // optional string name = 3;\n  let $name = message.name;\n  if ($name !== undefined) {\n    writeVarint32(bb, 26);\n    writeString(bb, $name);\n  }\n\n  // optional int32 camp_id = 4;\n  let $camp_id = message.camp_id;\n  if ($camp_id !== undefined) {\n    writeVarint32(bb, 32);\n    writeVarint64(bb, intToLong($camp_id));\n  }\n\n  // optional sint32 position = 5;\n  let $position = message.position;\n  if ($position !== undefined) {\n    writeVarint32(bb, 40);\n    writeVarint32ZigZag(bb, $position);\n  }\n\n  // optional bool is_ai = 6;\n  let $is_ai = message.is_ai;\n  if ($is_ai !== undefined) {\n    writeVarint32(bb, 48);\n    writeByte(bb, $is_ai ? 1 : 0);\n  }\n\n  // optional int32 hero_id = 7;\n  let $hero_id = message.hero_id;\n  if ($hero_id !== undefined) {\n    writeVarint32(bb, 56);\n    writeVarint64(bb, intToLong($hero_id));\n  }\n\n  // repeated GameCardInfo cards = 8;\n  let array$cards = message.cards;\n  if (array$cards !== undefined) {\n    for (let value of array$cards) {\n      writeVarint32(bb, 66);\n      let nested = popByteBuffer();\n      _encodeGameCardInfo(value, nested);\n      writeVarint32(bb, nested.limit);\n      writeByteBuffer(bb, nested);\n      pushByteBuffer(nested);\n    }\n  }\n\n  // optional uint32 session_id = 9;\n  let $session_id = message.session_id;\n  if ($session_id !== undefined) {\n    writeVarint32(bb, 72);\n    writeVarint32(bb, $session_id);\n  }\n}\n\nexport function decodeGamePlayer(binary: Uint8Array): GamePlayer {\n  return _decodeGamePlayer(wrapByteBuffer(binary));\n}\n\nfunction _decodeGamePlayer(bb: ByteBuffer): GamePlayer {\n  let message: GamePlayer = {} as any;\n\n  end_of_message: while (!isAtEnd(bb)) {\n    let tag = readVarint32(bb);\n\n    switch (tag >>> 3) {\n      case 0:\n        break end_of_message;\n\n      // optional fixed64 uin = 1;\n      case 1: {\n        message.uin = readInt64(bb, /* unsigned */ true);\n        break;\n      }\n\n      // optional string openid = 2;\n      case 2: {\n        message.openid = readString(bb, readVarint32(bb));\n        break;\n      }\n\n      // optional string name = 3;\n      case 3: {\n        message.name = readString(bb, readVarint32(bb));\n        break;\n      }\n\n      // optional int32 camp_id = 4;\n      case 4: {\n        message.camp_id = readVarint32(bb);\n        break;\n      }\n\n      // optional sint32 position = 5;\n      case 5: {\n        message.position = readVarint32ZigZag(bb);\n        break;\n      }\n\n      // optional bool is_ai = 6;\n      case 6: {\n        message.is_ai = !!readByte(bb);\n        break;\n      }\n\n      // optional int32 hero_id = 7;\n      case 7: {\n        message.hero_id = readVarint32(bb);\n        break;\n      }\n\n      // repeated GameCardInfo cards = 8;\n      case 8: {\n        let limit = pushTemporaryLength(bb);\n        let values = message.cards || (message.cards = []);\n        values.push(_decodeGameCardInfo(bb));\n        bb.limit = limit;\n        break;\n      }\n\n      // optional uint32 session_id = 9;\n      case 9: {\n        message.session_id = readVarint32(bb) >>> 0;\n        break;\n      }\n\n      default:\n        skipUnknownField(bb, tag & 7);\n    }\n  }\n\n  return message;\n}\n\nexport interface GameInfo {\n  game_id?: Long;\n  begin_time?: number;\n  version?: number;\n  map_id?: number;\n  mode?: GAME_MODE;\n  random_num?: number;\n  gamesvr_id?: number;\n  url?: string;\n  players?: GamePlayer[];\n}\n\nexport function encodeGameInfo(message: GameInfo): Uint8Array {\n  let bb = popByteBuffer();\n  _encodeGameInfo(message, bb);\n  return toUint8Array(bb);\n}\n\nfunction _encodeGameInfo(message: GameInfo, bb: ByteBuffer): void {\n  // optional fixed64 game_id = 1;\n  let $game_id = message.game_id;\n  if ($game_id !== undefined) {\n    writeVarint32(bb, 9);\n    writeInt64(bb, $game_id);\n  }\n\n  // optional uint32 begin_time = 2;\n  let $begin_time = message.begin_time;\n  if ($begin_time !== undefined) {\n    writeVarint32(bb, 16);\n    writeVarint32(bb, $begin_time);\n  }\n\n  // optional uint32 version = 3;\n  let $version = message.version;\n  if ($version !== undefined) {\n    writeVarint32(bb, 24);\n    writeVarint32(bb, $version);\n  }\n\n  // optional int32 map_id = 4;\n  let $map_id = message.map_id;\n  if ($map_id !== undefined) {\n    writeVarint32(bb, 32);\n    writeVarint64(bb, intToLong($map_id));\n  }\n\n  // optional GAME_MODE mode = 5;\n  let $mode = message.mode;\n  if ($mode !== undefined) {\n    writeVarint32(bb, 40);\n    writeVarint32(bb, encodeGAME_MODE[$mode]);\n  }\n\n  // optional uint32 random_num = 6;\n  let $random_num = message.random_num;\n  if ($random_num !== undefined) {\n    writeVarint32(bb, 48);\n    writeVarint32(bb, $random_num);\n  }\n\n  // optional uint32 gamesvr_id = 7;\n  let $gamesvr_id = message.gamesvr_id;\n  if ($gamesvr_id !== undefined) {\n    writeVarint32(bb, 56);\n    writeVarint32(bb, $gamesvr_id);\n  }\n\n  // optional string url = 8;\n  let $url = message.url;\n  if ($url !== undefined) {\n    writeVarint32(bb, 66);\n    writeString(bb, $url);\n  }\n\n  // repeated GamePlayer players = 9;\n  let array$players = message.players;\n  if (array$players !== undefined) {\n    for (let value of array$players) {\n      writeVarint32(bb, 74);\n      let nested = popByteBuffer();\n      _encodeGamePlayer(value, nested);\n      writeVarint32(bb, nested.limit);\n      writeByteBuffer(bb, nested);\n      pushByteBuffer(nested);\n    }\n  }\n}\n\nexport function decodeGameInfo(binary: Uint8Array): GameInfo {\n  return _decodeGameInfo(wrapByteBuffer(binary));\n}\n\nfunction _decodeGameInfo(bb: ByteBuffer): GameInfo {\n  let message: GameInfo = {} as any;\n\n  end_of_message: while (!isAtEnd(bb)) {\n    let tag = readVarint32(bb);\n\n    switch (tag >>> 3) {\n      case 0:\n        break end_of_message;\n\n      // optional fixed64 game_id = 1;\n      case 1: {\n        message.game_id = readInt64(bb, /* unsigned */ true);\n        break;\n      }\n\n      // optional uint32 begin_time = 2;\n      case 2: {\n        message.begin_time = readVarint32(bb) >>> 0;\n        break;\n      }\n\n      // optional uint32 version = 3;\n      case 3: {\n        message.version = readVarint32(bb) >>> 0;\n        break;\n      }\n\n      // optional int32 map_id = 4;\n      case 4: {\n        message.map_id = readVarint32(bb);\n        break;\n      }\n\n      // optional GAME_MODE mode = 5;\n      case 5: {\n        message.mode = decodeGAME_MODE[readVarint32(bb)];\n        break;\n      }\n\n      // optional uint32 random_num = 6;\n      case 6: {\n        message.random_num = readVarint32(bb) >>> 0;\n        break;\n      }\n\n      // optional uint32 gamesvr_id = 7;\n      case 7: {\n        message.gamesvr_id = readVarint32(bb) >>> 0;\n        break;\n      }\n\n      // optional string url = 8;\n      case 8: {\n        message.url = readString(bb, readVarint32(bb));\n        break;\n      }\n\n      // repeated GamePlayer players = 9;\n      case 9: {\n        let limit = pushTemporaryLength(bb);\n        let values = message.players || (message.players = []);\n        values.push(_decodeGamePlayer(bb));\n        bb.limit = limit;\n        break;\n      }\n\n      default:\n        skipUnknownField(bb, tag & 7);\n    }\n  }\n\n  return message;\n}\n\nexport interface GamePlayerStat {\n  uin?: Long;\n  camp_id?: number;\n  is_ai?: boolean;\n  score?: number;\n}\n\nexport function encodeGamePlayerStat(message: GamePlayerStat): Uint8Array {\n  let bb = popByteBuffer();\n  _encodeGamePlayerStat(message, bb);\n  return toUint8Array(bb);\n}\n\nfunction _encodeGamePlayerStat(message: GamePlayerStat, bb: ByteBuffer): void {\n  // optional fixed64 uin = 1;\n  let $uin = message.uin;\n  if ($uin !== undefined) {\n    writeVarint32(bb, 9);\n    writeInt64(bb, $uin);\n  }\n\n  // optional int32 camp_id = 2;\n  let $camp_id = message.camp_id;\n  if ($camp_id !== undefined) {\n    writeVarint32(bb, 16);\n    writeVarint64(bb, intToLong($camp_id));\n  }\n\n  // optional bool is_ai = 3;\n  let $is_ai = message.is_ai;\n  if ($is_ai !== undefined) {\n    writeVarint32(bb, 24);\n    writeByte(bb, $is_ai ? 1 : 0);\n  }\n\n  // optional int32 score = 4;\n  let $score = message.score;\n  if ($score !== undefined) {\n    writeVarint32(bb, 32);\n    writeVarint64(bb, intToLong($score));\n  }\n}\n\nexport function decodeGamePlayerStat(binary: Uint8Array): GamePlayerStat {\n  return _decodeGamePlayerStat(wrapByteBuffer(binary));\n}\n\nfunction _decodeGamePlayerStat(bb: ByteBuffer): GamePlayerStat {\n  let message: GamePlayerStat = {} as any;\n\n  end_of_message: while (!isAtEnd(bb)) {\n    let tag = readVarint32(bb);\n\n    switch (tag >>> 3) {\n      case 0:\n        break end_of_message;\n\n      // optional fixed64 uin = 1;\n      case 1: {\n        message.uin = readInt64(bb, /* unsigned */ true);\n        break;\n      }\n\n      // optional int32 camp_id = 2;\n      case 2: {\n        message.camp_id = readVarint32(bb);\n        break;\n      }\n\n      // optional bool is_ai = 3;\n      case 3: {\n        message.is_ai = !!readByte(bb);\n        break;\n      }\n\n      // optional int32 score = 4;\n      case 4: {\n        message.score = readVarint32(bb);\n        break;\n      }\n\n      default:\n        skipUnknownField(bb, tag & 7);\n    }\n  }\n\n  return message;\n}\n\nexport interface GameResult {\n  game_id?: Long;\n  mode?: GAME_MODE;\n  map_id?: number;\n  start_time?: number;\n  end_time?: number;\n  duration?: number;\n  result?: GAME_RESULT;\n  stat?: GamePlayerStat[];\n}\n\nexport function encodeGameResult(message: GameResult): Uint8Array {\n  let bb = popByteBuffer();\n  _encodeGameResult(message, bb);\n  return toUint8Array(bb);\n}\n\nfunction _encodeGameResult(message: GameResult, bb: ByteBuffer): void {\n  // optional fixed64 game_id = 1;\n  let $game_id = message.game_id;\n  if ($game_id !== undefined) {\n    writeVarint32(bb, 9);\n    writeInt64(bb, $game_id);\n  }\n\n  // optional GAME_MODE mode = 2;\n  let $mode = message.mode;\n  if ($mode !== undefined) {\n    writeVarint32(bb, 16);\n    writeVarint32(bb, encodeGAME_MODE[$mode]);\n  }\n\n  // optional int32 map_id = 3;\n  let $map_id = message.map_id;\n  if ($map_id !== undefined) {\n    writeVarint32(bb, 24);\n    writeVarint64(bb, intToLong($map_id));\n  }\n\n  // optional uint32 start_time = 4;\n  let $start_time = message.start_time;\n  if ($start_time !== undefined) {\n    writeVarint32(bb, 32);\n    writeVarint32(bb, $start_time);\n  }\n\n  // optional uint32 end_time = 5;\n  let $end_time = message.end_time;\n  if ($end_time !== undefined) {\n    writeVarint32(bb, 40);\n    writeVarint32(bb, $end_time);\n  }\n\n  // optional int32 duration = 6;\n  let $duration = message.duration;\n  if ($duration !== undefined) {\n    writeVarint32(bb, 48);\n    writeVarint64(bb, intToLong($duration));\n  }\n\n  // optional GAME_RESULT result = 7;\n  let $result = message.result;\n  if ($result !== undefined) {\n    writeVarint32(bb, 56);\n    writeVarint32(bb, encodeGAME_RESULT[$result]);\n  }\n\n  // repeated GamePlayerStat stat = 8;\n  let array$stat = message.stat;\n  if (array$stat !== undefined) {\n    for (let value of array$stat) {\n      writeVarint32(bb, 66);\n      let nested = popByteBuffer();\n      _encodeGamePlayerStat(value, nested);\n      writeVarint32(bb, nested.limit);\n      writeByteBuffer(bb, nested);\n      pushByteBuffer(nested);\n    }\n  }\n}\n\nexport function decodeGameResult(binary: Uint8Array): GameResult {\n  return _decodeGameResult(wrapByteBuffer(binary));\n}\n\nfunction _decodeGameResult(bb: ByteBuffer): GameResult {\n  let message: GameResult = {} as any;\n\n  end_of_message: while (!isAtEnd(bb)) {\n    let tag = readVarint32(bb);\n\n    switch (tag >>> 3) {\n      case 0:\n        break end_of_message;\n\n      // optional fixed64 game_id = 1;\n      case 1: {\n        message.game_id = readInt64(bb, /* unsigned */ true);\n        break;\n      }\n\n      // optional GAME_MODE mode = 2;\n      case 2: {\n        message.mode = decodeGAME_MODE[readVarint32(bb)];\n        break;\n      }\n\n      // optional int32 map_id = 3;\n      case 3: {\n        message.map_id = readVarint32(bb);\n        break;\n      }\n\n      // optional uint32 start_time = 4;\n      case 4: {\n        message.start_time = readVarint32(bb) >>> 0;\n        break;\n      }\n\n      // optional uint32 end_time = 5;\n      case 5: {\n        message.end_time = readVarint32(bb) >>> 0;\n        break;\n      }\n\n      // optional int32 duration = 6;\n      case 6: {\n        message.duration = readVarint32(bb);\n        break;\n      }\n\n      // optional GAME_RESULT result = 7;\n      case 7: {\n        message.result = decodeGAME_RESULT[readVarint32(bb)];\n        break;\n      }\n\n      // repeated GamePlayerStat stat = 8;\n      case 8: {\n        let limit = pushTemporaryLength(bb);\n        let values = message.stat || (message.stat = []);\n        values.push(_decodeGamePlayerStat(bb));\n        bb.limit = limit;\n        break;\n      }\n\n      default:\n        skipUnknownField(bb, tag & 7);\n    }\n  }\n\n  return message;\n}\n\nexport interface RoleSimpleDataOthers {\n  gender?: number;\n  nick_name?: string;\n  picture?: string;\n  client_ver?: number;\n}\n\nexport function encodeRoleSimpleDataOthers(message: RoleSimpleDataOthers): Uint8Array {\n  let bb = popByteBuffer();\n  _encodeRoleSimpleDataOthers(message, bb);\n  return toUint8Array(bb);\n}\n\nfunction _encodeRoleSimpleDataOthers(message: RoleSimpleDataOthers, bb: ByteBuffer): void {\n  // optional int32 gender = 2;\n  let $gender = message.gender;\n  if ($gender !== undefined) {\n    writeVarint32(bb, 16);\n    writeVarint64(bb, intToLong($gender));\n  }\n\n  // optional string nick_name = 3;\n  let $nick_name = message.nick_name;\n  if ($nick_name !== undefined) {\n    writeVarint32(bb, 26);\n    writeString(bb, $nick_name);\n  }\n\n  // optional string picture = 4;\n  let $picture = message.picture;\n  if ($picture !== undefined) {\n    writeVarint32(bb, 34);\n    writeString(bb, $picture);\n  }\n\n  // optional uint32 client_ver = 5;\n  let $client_ver = message.client_ver;\n  if ($client_ver !== undefined) {\n    writeVarint32(bb, 40);\n    writeVarint32(bb, $client_ver);\n  }\n}\n\nexport function decodeRoleSimpleDataOthers(binary: Uint8Array): RoleSimpleDataOthers {\n  return _decodeRoleSimpleDataOthers(wrapByteBuffer(binary));\n}\n\nfunction _decodeRoleSimpleDataOthers(bb: ByteBuffer): RoleSimpleDataOthers {\n  let message: RoleSimpleDataOthers = {} as any;\n\n  end_of_message: while (!isAtEnd(bb)) {\n    let tag = readVarint32(bb);\n\n    switch (tag >>> 3) {\n      case 0:\n        break end_of_message;\n\n      // optional int32 gender = 2;\n      case 2: {\n        message.gender = readVarint32(bb);\n        break;\n      }\n\n      // optional string nick_name = 3;\n      case 3: {\n        message.nick_name = readString(bb, readVarint32(bb));\n        break;\n      }\n\n      // optional string picture = 4;\n      case 4: {\n        message.picture = readString(bb, readVarint32(bb));\n        break;\n      }\n\n      // optional uint32 client_ver = 5;\n      case 5: {\n        message.client_ver = readVarint32(bb) >>> 0;\n        break;\n      }\n\n      default:\n        skipUnknownField(bb, tag & 7);\n    }\n  }\n\n  return message;\n}\n\nexport interface GameFrameInputUnit {\n  position?: number;\n  input?: Uint8Array;\n}\n\nexport function encodeGameFrameInputUnit(message: GameFrameInputUnit): Uint8Array {\n  let bb = popByteBuffer();\n  _encodeGameFrameInputUnit(message, bb);\n  return toUint8Array(bb);\n}\n\nfunction _encodeGameFrameInputUnit(message: GameFrameInputUnit, bb: ByteBuffer): void {\n  // optional sint32 position = 1;\n  let $position = message.position;\n  if ($position !== undefined) {\n    writeVarint32(bb, 8);\n    writeVarint32ZigZag(bb, $position);\n  }\n\n  // optional bytes input = 2;\n  let $input = message.input;\n  if ($input !== undefined) {\n    writeVarint32(bb, 18);\n    writeVarint32(bb, $input.length), writeBytes(bb, $input);\n  }\n}\n\nexport function decodeGameFrameInputUnit(binary: Uint8Array): GameFrameInputUnit {\n  return _decodeGameFrameInputUnit(wrapByteBuffer(binary));\n}\n\nfunction _decodeGameFrameInputUnit(bb: ByteBuffer): GameFrameInputUnit {\n  let message: GameFrameInputUnit = {} as any;\n\n  end_of_message: while (!isAtEnd(bb)) {\n    let tag = readVarint32(bb);\n\n    switch (tag >>> 3) {\n      case 0:\n        break end_of_message;\n\n      // optional sint32 position = 1;\n      case 1: {\n        message.position = readVarint32ZigZag(bb);\n        break;\n      }\n\n      // optional bytes input = 2;\n      case 2: {\n        message.input = readBytes(bb, readVarint32(bb));\n        break;\n      }\n\n      default:\n        skipUnknownField(bb, tag & 7);\n    }\n  }\n\n  return message;\n}\n\nexport interface GameFrameInput {\n  frame_seq?: number;\n  input?: GameFrameInputUnit;\n}\n\nexport function encodeGameFrameInput(message: GameFrameInput): Uint8Array {\n  let bb = popByteBuffer();\n  _encodeGameFrameInput(message, bb);\n  return toUint8Array(bb);\n}\n\nfunction _encodeGameFrameInput(message: GameFrameInput, bb: ByteBuffer): void {\n  // optional uint32 frame_seq = 1;\n  let $frame_seq = message.frame_seq;\n  if ($frame_seq !== undefined) {\n    writeVarint32(bb, 8);\n    writeVarint32(bb, $frame_seq);\n  }\n\n  // optional GameFrameInputUnit input = 2;\n  let $input = message.input;\n  if ($input !== undefined) {\n    writeVarint32(bb, 18);\n    let nested = popByteBuffer();\n    _encodeGameFrameInputUnit($input, nested);\n    writeVarint32(bb, nested.limit);\n    writeByteBuffer(bb, nested);\n    pushByteBuffer(nested);\n  }\n}\n\nexport function decodeGameFrameInput(binary: Uint8Array): GameFrameInput {\n  return _decodeGameFrameInput(wrapByteBuffer(binary));\n}\n\nfunction _decodeGameFrameInput(bb: ByteBuffer): GameFrameInput {\n  let message: GameFrameInput = {} as any;\n\n  end_of_message: while (!isAtEnd(bb)) {\n    let tag = readVarint32(bb);\n\n    switch (tag >>> 3) {\n      case 0:\n        break end_of_message;\n\n      // optional uint32 frame_seq = 1;\n      case 1: {\n        message.frame_seq = readVarint32(bb) >>> 0;\n        break;\n      }\n\n      // optional GameFrameInputUnit input = 2;\n      case 2: {\n        let limit = pushTemporaryLength(bb);\n        message.input = _decodeGameFrameInputUnit(bb);\n        bb.limit = limit;\n        break;\n      }\n\n      default:\n        skipUnknownField(bb, tag & 7);\n    }\n  }\n\n  return message;\n}\n\nexport interface Long {\n  low: number;\n  high: number;\n  unsigned: boolean;\n}\n\ninterface ByteBuffer {\n  bytes: Uint8Array;\n  offset: number;\n  limit: number;\n}\n\nfunction pushTemporaryLength(bb: ByteBuffer): number {\n  let length = readVarint32(bb);\n  let limit = bb.limit;\n  bb.limit = bb.offset + length;\n  return limit;\n}\n\nfunction skipUnknownField(bb: ByteBuffer, type: number): void {\n  switch (type) {\n    case 0: while (readByte(bb) & 0x80) { } break;\n    case 2: skip(bb, readVarint32(bb)); break;\n    case 5: skip(bb, 4); break;\n    case 1: skip(bb, 8); break;\n    default: throw new Error(\"Unimplemented type: \" + type);\n  }\n}\n\nfunction stringToLong(value: string): Long {\n  return {\n    low: value.charCodeAt(0) | (value.charCodeAt(1) << 16),\n    high: value.charCodeAt(2) | (value.charCodeAt(3) << 16),\n    unsigned: false,\n  };\n}\n\nfunction longToString(value: Long): string {\n  let low = value.low;\n  let high = value.high;\n  return String.fromCharCode(\n    low & 0xFFFF,\n    low >>> 16,\n    high & 0xFFFF,\n    high >>> 16);\n}\n\n// The code below was modified from https://github.com/protobufjs/bytebuffer.js\n// which is under the Apache License 2.0.\n\nlet f32 = new Float32Array(1);\nlet f32_u8 = new Uint8Array(f32.buffer);\n\nlet f64 = new Float64Array(1);\nlet f64_u8 = new Uint8Array(f64.buffer);\n\nfunction intToLong(value: number): Long {\n  value |= 0;\n  return {\n    low: value,\n    high: value >> 31,\n    unsigned: value >= 0,\n  };\n}\n\nlet bbStack: ByteBuffer[] = [];\n\nfunction popByteBuffer(): ByteBuffer {\n  const bb = bbStack.pop();\n  if (!bb) return { bytes: new Uint8Array(64), offset: 0, limit: 0 };\n  bb.offset = bb.limit = 0;\n  return bb;\n}\n\nfunction pushByteBuffer(bb: ByteBuffer): void {\n  bbStack.push(bb);\n}\n\nfunction wrapByteBuffer(bytes: Uint8Array): ByteBuffer {\n  return { bytes, offset: 0, limit: bytes.length };\n}\n\nfunction toUint8Array(bb: ByteBuffer): Uint8Array {\n  let bytes = bb.bytes;\n  let limit = bb.limit;\n  return bytes.length === limit ? bytes : bytes.subarray(0, limit);\n}\n\nfunction skip(bb: ByteBuffer, offset: number): void {\n  if (bb.offset + offset > bb.limit) {\n    throw new Error('Skip past limit');\n  }\n  bb.offset += offset;\n}\n\nfunction isAtEnd(bb: ByteBuffer): boolean {\n  return bb.offset >= bb.limit;\n}\n\nfunction grow(bb: ByteBuffer, count: number): number {\n  let bytes = bb.bytes;\n  let offset = bb.offset;\n  let limit = bb.limit;\n  let finalOffset = offset + count;\n  if (finalOffset > bytes.length) {\n    let newBytes = new Uint8Array(finalOffset * 2);\n    newBytes.set(bytes);\n    bb.bytes = newBytes;\n  }\n  bb.offset = finalOffset;\n  if (finalOffset > limit) {\n    bb.limit = finalOffset;\n  }\n  return offset;\n}\n\nfunction advance(bb: ByteBuffer, count: number): number {\n  let offset = bb.offset;\n  if (offset + count > bb.limit) {\n    throw new Error('Read past limit');\n  }\n  bb.offset += count;\n  return offset;\n}\n\nfunction readBytes(bb: ByteBuffer, count: number): Uint8Array {\n  let offset = advance(bb, count);\n  return bb.bytes.subarray(offset, offset + count);\n}\n\nfunction writeBytes(bb: ByteBuffer, buffer: Uint8Array): void {\n  let offset = grow(bb, buffer.length);\n  bb.bytes.set(buffer, offset);\n}\n\nfunction readString(bb: ByteBuffer, count: number): string {\n  // Sadly a hand-coded UTF8 decoder is much faster than subarray+TextDecoder in V8\n  let offset = advance(bb, count);\n  let fromCharCode = String.fromCharCode;\n  let bytes = bb.bytes;\n  let invalid = '\\uFFFD';\n  let text = '';\n\n  for (let i = 0; i < count; i++) {\n    let c1 = bytes[i + offset], c2: number, c3: number, c4: number, c: number;\n\n    // 1 byte\n    if ((c1 & 0x80) === 0) {\n      text += fromCharCode(c1);\n    }\n\n    // 2 bytes\n    else if ((c1 & 0xE0) === 0xC0) {\n      if (i + 1 >= count) text += invalid;\n      else {\n        c2 = bytes[i + offset + 1];\n        if ((c2 & 0xC0) !== 0x80) text += invalid;\n        else {\n          c = ((c1 & 0x1F) << 6) | (c2 & 0x3F);\n          if (c < 0x80) text += invalid;\n          else {\n            text += fromCharCode(c);\n            i++;\n          }\n        }\n      }\n    }\n\n    // 3 bytes\n    else if ((c1 & 0xF0) == 0xE0) {\n      if (i + 2 >= count) text += invalid;\n      else {\n        c2 = bytes[i + offset + 1];\n        c3 = bytes[i + offset + 2];\n        if (((c2 | (c3 << 8)) & 0xC0C0) !== 0x8080) text += invalid;\n        else {\n          c = ((c1 & 0x0F) << 12) | ((c2 & 0x3F) << 6) | (c3 & 0x3F);\n          if (c < 0x0800 || (c >= 0xD800 && c <= 0xDFFF)) text += invalid;\n          else {\n            text += fromCharCode(c);\n            i += 2;\n          }\n        }\n      }\n    }\n\n    // 4 bytes\n    else if ((c1 & 0xF8) == 0xF0) {\n      if (i + 3 >= count) text += invalid;\n      else {\n        c2 = bytes[i + offset + 1];\n        c3 = bytes[i + offset + 2];\n        c4 = bytes[i + offset + 3];\n        if (((c2 | (c3 << 8) | (c4 << 16)) & 0xC0C0C0) !== 0x808080) text += invalid;\n        else {\n          c = ((c1 & 0x07) << 0x12) | ((c2 & 0x3F) << 0x0C) | ((c3 & 0x3F) << 0x06) | (c4 & 0x3F);\n          if (c < 0x10000 || c > 0x10FFFF) text += invalid;\n          else {\n            c -= 0x10000;\n            text += fromCharCode((c >> 10) + 0xD800, (c & 0x3FF) + 0xDC00);\n            i += 3;\n          }\n        }\n      }\n    }\n\n    else text += invalid;\n  }\n\n  return text;\n}\n\nfunction writeString(bb: ByteBuffer, text: string): void {\n  // Sadly a hand-coded UTF8 encoder is much faster than TextEncoder+set in V8\n  let n = text.length;\n  let byteCount = 0;\n\n  // Write the byte count first\n  for (let i = 0; i < n; i++) {\n    let c = text.charCodeAt(i);\n    if (c >= 0xD800 && c <= 0xDBFF && i + 1 < n) {\n      c = (c << 10) + text.charCodeAt(++i) - 0x35FDC00;\n    }\n    byteCount += c < 0x80 ? 1 : c < 0x800 ? 2 : c < 0x10000 ? 3 : 4;\n  }\n  writeVarint32(bb, byteCount);\n\n  let offset = grow(bb, byteCount);\n  let bytes = bb.bytes;\n\n  // Then write the bytes\n  for (let i = 0; i < n; i++) {\n    let c = text.charCodeAt(i);\n    if (c >= 0xD800 && c <= 0xDBFF && i + 1 < n) {\n      c = (c << 10) + text.charCodeAt(++i) - 0x35FDC00;\n    }\n    if (c < 0x80) {\n      bytes[offset++] = c;\n    } else {\n      if (c < 0x800) {\n        bytes[offset++] = ((c >> 6) & 0x1F) | 0xC0;\n      } else {\n        if (c < 0x10000) {\n          bytes[offset++] = ((c >> 12) & 0x0F) | 0xE0;\n        } else {\n          bytes[offset++] = ((c >> 18) & 0x07) | 0xF0;\n          bytes[offset++] = ((c >> 12) & 0x3F) | 0x80;\n        }\n        bytes[offset++] = ((c >> 6) & 0x3F) | 0x80;\n      }\n      bytes[offset++] = (c & 0x3F) | 0x80;\n    }\n  }\n}\n\nfunction writeByteBuffer(bb: ByteBuffer, buffer: ByteBuffer): void {\n  let offset = grow(bb, buffer.limit);\n  let from = bb.bytes;\n  let to = buffer.bytes;\n\n  // This for loop is much faster than subarray+set on V8\n  for (let i = 0, n = buffer.limit; i < n; i++) {\n    from[i + offset] = to[i];\n  }\n}\n\nfunction readByte(bb: ByteBuffer): number {\n  return bb.bytes[advance(bb, 1)];\n}\n\nfunction writeByte(bb: ByteBuffer, value: number): void {\n  let offset = grow(bb, 1);\n  bb.bytes[offset] = value;\n}\n\nfunction readFloat(bb: ByteBuffer): number {\n  let offset = advance(bb, 4);\n  let bytes = bb.bytes;\n\n  // Manual copying is much faster than subarray+set in V8\n  f32_u8[0] = bytes[offset++];\n  f32_u8[1] = bytes[offset++];\n  f32_u8[2] = bytes[offset++];\n  f32_u8[3] = bytes[offset++];\n  return f32[0];\n}\n\nfunction writeFloat(bb: ByteBuffer, value: number): void {\n  let offset = grow(bb, 4);\n  let bytes = bb.bytes;\n  f32[0] = value;\n\n  // Manual copying is much faster than subarray+set in V8\n  bytes[offset++] = f32_u8[0];\n  bytes[offset++] = f32_u8[1];\n  bytes[offset++] = f32_u8[2];\n  bytes[offset++] = f32_u8[3];\n}\n\nfunction readDouble(bb: ByteBuffer): number {\n  let offset = advance(bb, 8);\n  let bytes = bb.bytes;\n\n  // Manual copying is much faster than subarray+set in V8\n  f64_u8[0] = bytes[offset++];\n  f64_u8[1] = bytes[offset++];\n  f64_u8[2] = bytes[offset++];\n  f64_u8[3] = bytes[offset++];\n  f64_u8[4] = bytes[offset++];\n  f64_u8[5] = bytes[offset++];\n  f64_u8[6] = bytes[offset++];\n  f64_u8[7] = bytes[offset++];\n  return f64[0];\n}\n\nfunction writeDouble(bb: ByteBuffer, value: number): void {\n  let offset = grow(bb, 8);\n  let bytes = bb.bytes;\n  f64[0] = value;\n\n  // Manual copying is much faster than subarray+set in V8\n  bytes[offset++] = f64_u8[0];\n  bytes[offset++] = f64_u8[1];\n  bytes[offset++] = f64_u8[2];\n  bytes[offset++] = f64_u8[3];\n  bytes[offset++] = f64_u8[4];\n  bytes[offset++] = f64_u8[5];\n  bytes[offset++] = f64_u8[6];\n  bytes[offset++] = f64_u8[7];\n}\n\nfunction readInt32(bb: ByteBuffer): number {\n  let offset = advance(bb, 4);\n  let bytes = bb.bytes;\n  return (\n    bytes[offset] |\n    (bytes[offset + 1] << 8) |\n    (bytes[offset + 2] << 16) |\n    (bytes[offset + 3] << 24)\n  );\n}\n\nfunction writeInt32(bb: ByteBuffer, value: number): void {\n  let offset = grow(bb, 4);\n  let bytes = bb.bytes;\n  bytes[offset] = value;\n  bytes[offset + 1] = value >> 8;\n  bytes[offset + 2] = value >> 16;\n  bytes[offset + 3] = value >> 24;\n}\n\nfunction readInt64(bb: ByteBuffer, unsigned: boolean): Long {\n  return {\n    low: readInt32(bb),\n    high: readInt32(bb),\n    unsigned,\n  };\n}\n\nfunction writeInt64(bb: ByteBuffer, value: Long): void {\n  writeInt32(bb, value.low);\n  writeInt32(bb, value.high);\n}\n\nfunction readVarint32(bb: ByteBuffer): number {\n  let c = 0;\n  let value = 0;\n  let b: number;\n  do {\n    b = readByte(bb);\n    if (c < 32) value |= (b & 0x7F) << c;\n    c += 7;\n  } while (b & 0x80);\n  return value;\n}\n\nfunction writeVarint32(bb: ByteBuffer, value: number): void {\n  value >>>= 0;\n  while (value >= 0x80) {\n    writeByte(bb, (value & 0x7f) | 0x80);\n    value >>>= 7;\n  }\n  writeByte(bb, value);\n}\n\nfunction readVarint64(bb: ByteBuffer, unsigned: boolean): Long {\n  let part0 = 0;\n  let part1 = 0;\n  let part2 = 0;\n  let b: number;\n\n  b = readByte(bb); part0 = (b & 0x7F); if (b & 0x80) {\n    b = readByte(bb); part0 |= (b & 0x7F) << 7; if (b & 0x80) {\n      b = readByte(bb); part0 |= (b & 0x7F) << 14; if (b & 0x80) {\n        b = readByte(bb); part0 |= (b & 0x7F) << 21; if (b & 0x80) {\n\n          b = readByte(bb); part1 = (b & 0x7F); if (b & 0x80) {\n            b = readByte(bb); part1 |= (b & 0x7F) << 7; if (b & 0x80) {\n              b = readByte(bb); part1 |= (b & 0x7F) << 14; if (b & 0x80) {\n                b = readByte(bb); part1 |= (b & 0x7F) << 21; if (b & 0x80) {\n\n                  b = readByte(bb); part2 = (b & 0x7F); if (b & 0x80) {\n                    b = readByte(bb); part2 |= (b & 0x7F) << 7;\n                  }\n                }\n              }\n            }\n          }\n        }\n      }\n    }\n  }\n\n  return {\n    low: part0 | (part1 << 28),\n    high: (part1 >>> 4) | (part2 << 24),\n    unsigned,\n  };\n}\n\nfunction writeVarint64(bb: ByteBuffer, value: Long): void {\n  let part0 = value.low >>> 0;\n  let part1 = ((value.low >>> 28) | (value.high << 4)) >>> 0;\n  let part2 = value.high >>> 24;\n\n  // ref: src/google/protobuf/io/coded_stream.cc\n  let size =\n    part2 === 0 ?\n      part1 === 0 ?\n        part0 < 1 << 14 ?\n          part0 < 1 << 7 ? 1 : 2 :\n          part0 < 1 << 21 ? 3 : 4 :\n        part1 < 1 << 14 ?\n          part1 < 1 << 7 ? 5 : 6 :\n          part1 < 1 << 21 ? 7 : 8 :\n      part2 < 1 << 7 ? 9 : 10;\n\n  let offset = grow(bb, size);\n  let bytes = bb.bytes;\n\n  switch (size) {\n    case 10: bytes[offset + 9] = (part2 >>> 7) & 0x01;\n    case 9: bytes[offset + 8] = size !== 9 ? part2 | 0x80 : part2 & 0x7F;\n    case 8: bytes[offset + 7] = size !== 8 ? (part1 >>> 21) | 0x80 : (part1 >>> 21) & 0x7F;\n    case 7: bytes[offset + 6] = size !== 7 ? (part1 >>> 14) | 0x80 : (part1 >>> 14) & 0x7F;\n    case 6: bytes[offset + 5] = size !== 6 ? (part1 >>> 7) | 0x80 : (part1 >>> 7) & 0x7F;\n    case 5: bytes[offset + 4] = size !== 5 ? part1 | 0x80 : part1 & 0x7F;\n    case 4: bytes[offset + 3] = size !== 4 ? (part0 >>> 21) | 0x80 : (part0 >>> 21) & 0x7F;\n    case 3: bytes[offset + 2] = size !== 3 ? (part0 >>> 14) | 0x80 : (part0 >>> 14) & 0x7F;\n    case 2: bytes[offset + 1] = size !== 2 ? (part0 >>> 7) | 0x80 : (part0 >>> 7) & 0x7F;\n    case 1: bytes[offset] = size !== 1 ? part0 | 0x80 : part0 & 0x7F;\n  }\n}\n\nfunction readVarint32ZigZag(bb: ByteBuffer): number {\n  let value = readVarint32(bb);\n\n  // ref: src/google/protobuf/wire_format_lite.h\n  return (value >>> 1) ^ -(value & 1);\n}\n\nfunction writeVarint32ZigZag(bb: ByteBuffer, value: number): void {\n  // ref: src/google/protobuf/wire_format_lite.h\n  writeVarint32(bb, (value << 1) ^ (value >> 31));\n}\n\nfunction readVarint64ZigZag(bb: ByteBuffer): Long {\n  let value = readVarint64(bb, /* unsigned */ false);\n  let low = value.low;\n  let high = value.high;\n  let flip = -(low & 1);\n\n  // ref: src/google/protobuf/wire_format_lite.h\n  return {\n    low: ((low >>> 1) | (high << 31)) ^ flip,\n    high: (high >>> 1) ^ flip,\n    unsigned: false,\n  };\n}\n\nfunction writeVarint64ZigZag(bb: ByteBuffer, value: Long): void {\n  let low = value.low;\n  let high = value.high;\n  let flip = high >> 31;\n\n  // ref: src/google/protobuf/wire_format_lite.h\n  writeVarint64(bb, {\n    low: (low << 1) ^ flip,\n    high: ((high << 1) | (low >>> 31)) ^ flip,\n    unsigned: false,\n  });\n}\n"]}