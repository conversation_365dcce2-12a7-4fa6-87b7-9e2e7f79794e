{"version": 3, "sources": ["file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/MainUI.ts"], "names": ["_decorator", "director", "Component", "<PERSON><PERSON>", "ccclass", "property", "MainUI", "start", "enterButton", "node", "on", "EventType", "CLICK", "onEnterButtonClick", "loadScene", "update", "deltaTime"], "mappings": ";;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,Q,OAAAA,Q;AAAUC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,M,OAAAA,M;;;;;;;;;OACpC;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBL,U;;wBAGjBM,M,WADZF,OAAO,CAAC,QAAD,C,UAEHC,QAAQ,CAACF,MAAD,C,2BAFb,MACaG,MADb,SAC4BJ,SAD5B,CACsC;AAAA;AAAA;;AAAA;AAAA;;AAIlCK,QAAAA,KAAK,GAAG;AACJ,eAAKC,WAAL,CAAiBC,IAAjB,CAAsBC,EAAtB,CAAyBP,MAAM,CAACQ,SAAP,CAAiBC,KAA1C,EAAiD,KAAKC,kBAAtD,EAA0E,IAA1E;AACH;;AACDA,QAAAA,kBAAkB,GAAG;AACjBZ,UAAAA,QAAQ,CAACa,SAAT,CAAmB,MAAnB;AACH;;AAEDC,QAAAA,MAAM,CAACC,SAAD,EAAoB,CAEzB;;AAbiC,O;;;;;iBAEZ,I", "sourcesContent": ["import { _decorator, director, Component, But<PERSON>} from 'cc';\nconst { ccclass, property } = _decorator;\n\n@ccclass('MainUI')\nexport class MainUI extends Component {\n    @property(Button)\n    enterButton: Button = null!;\n\n    start() {\n        this.enterButton.node.on(Button.EventType.CLICK, this.onEnterButtonClick, this);\n    }\n    onEnterButtonClick() {\n        director.loadScene(\"Game\");\n    }\n\n    update(deltaTime: number) {\n        \n    }\n}\n\n"]}