System.register(["__unresolved_0", "cc", "cc/env", "__unresolved_1"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Component, EDITOR, quickSetupGizmos, GizmoManager, _dec, _class, _class2, _descriptor, _descriptor2, _crd, ccclass, property, GizmoSetup;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfquickSetupGizmos(extras) {
    _reporterNs.report("quickSetupGizmos", "./index", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGizmoManager(extras) {
    _reporterNs.report("GizmoManager", "./index", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Component = _cc.Component;
    }, function (_ccEnv) {
      EDITOR = _ccEnv.EDITOR;
    }, function (_unresolved_2) {
      quickSetupGizmos = _unresolved_2.quickSetupGizmos;
      GizmoManager = _unresolved_2.GizmoManager;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "30d878nseRGip2X74dAnGCp", "GizmoSetup", undefined);

      __checkObsolete__(['_decorator', 'Component', 'Node', 'director']);

      ({
        ccclass,
        property
      } = _decorator);
      /**
       * Helper component to automatically setup gizmos in a scene
       * Add this to any node in your scene to automatically initialize the gizmo system
       */

      _export("GizmoSetup", GizmoSetup = (_dec = ccclass('GizmoSetup'), _dec(_class = (_class2 = class GizmoSetup extends Component {
        constructor() {
          super(...arguments);

          _initializerDefineProperty(this, "autoSetup", _descriptor, this);

          _initializerDefineProperty(this, "setupInPlayMode", _descriptor2, this);
        }

        onLoad() {
          if (!EDITOR && !this.setupInPlayMode) return;

          if (this.autoSetup) {
            this.setupGizmos();
          }
        }
        /**
         * Setup the gizmo system
         */


        setupGizmos() {
          // Check if gizmo manager already exists
          var existingManager = (_crd && GizmoManager === void 0 ? (_reportPossibleCrUseOfGizmoManager({
            error: Error()
          }), GizmoManager) : GizmoManager).getInstance();

          if (existingManager) {
            console.log('GizmoSetup: Gizmo manager already exists, skipping setup');
            return;
          } // Setup gizmos


          var gizmoNode = (_crd && quickSetupGizmos === void 0 ? (_reportPossibleCrUseOfquickSetupGizmos({
            error: Error()
          }), quickSetupGizmos) : quickSetupGizmos)();

          if (gizmoNode) {
            console.log('GizmoSetup: Gizmo system initialized successfully');
          } else {
            console.error('GizmoSetup: Failed to initialize gizmo system');
          }
        }
        /**
         * Manual setup method that can be called from editor or code
         */


        static setupGizmosForScene() {
          var setup = new GizmoSetup();
          setup.setupGizmos();
        }

      }, (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "autoSetup", [property], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return true;
        }
      }), _descriptor2 = _applyDecoratedDescriptor(_class2.prototype, "setupInPlayMode", [property], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return false;
        }
      })), _class2)) || _class));
      /**
       * Example usage:
       * 
       * 1. Add GizmoSetup component to any node in your scene
       * 2. Set autoSetup to true (default)
       * 3. The gizmo system will be automatically initialized when the scene loads
       * 
       * Or call manually:
       * ```typescript
       * import { GizmoSetup } from "./Game/gizmos/GizmoSetup";
       * 
       * // In your game initialization code
       * GizmoSetup.setupGizmosForScene();
       * ```
       */


      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=c28552905bb24483ff88966e741df96e1577ddc1.js.map