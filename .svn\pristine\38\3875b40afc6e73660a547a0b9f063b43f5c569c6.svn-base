import { _decorator, Component, Node, Vec3, Rect } from 'cc';
import { ILevelData, SubLevel, Spawner, Path, MapLayer } from '../core/LevelData';
import { EditMode, SelectionInfo, EditorSettings, IDGenerator } from '../core/Types';
import { JsonLevelSerializer, SerializerFactory } from '../core/LevelSerializer';
import { LevelManager } from '../runtime/LevelManager';
import { CameraManager } from '../runtime/CameraManager';
import { PathManager } from '../runtime/PathManager';
import { SpawnerSystem } from '../runtime/SpawnerSystem';
import { MapSystem } from '../runtime/MapSystem';
import { EventGraphManager } from '../events/EventSystem';
import { EditorGizmos } from './EditorGizmos';
import { PathEditor } from './PathEditor';

const { ccclass, property } = _decorator;

/**
 * Main Level Editor component that orchestrates all systems
 */
@ccclass('LevelEditor')
export class LevelEditor extends Component {
    // System components
    @property({ type: LevelManager })
    public levelManager: LevelManager | null = null;

    @property({ type: CameraManager })
    public cameraManager: CameraManager | null = null;

    @property({ type: PathManager })
    public pathManager: PathManager | null = null;

    @property({ type: SpawnerSystem })
    public spawnerSystem: SpawnerSystem | null = null;

    @property({ type: MapSystem })
    public mapSystem: MapSystem | null = null;

    @property({ type: EventGraphManager })
    public eventManager: EventGraphManager | null = null;

    @property({ type: EditorGizmos })
    public editorGizmos: EditorGizmos | null = null;

    @property({ type: PathEditor })
    public pathEditor: PathEditor | null = null;
    
    // Editor state
    private currentLevel: ILevelData | null = null;
    private selectedSubLevel: SubLevel | null = null;
    private currentEditMode: EditMode = EditMode.Select;
    private selection: SelectionInfo = { type: 'none', objectId: '' };
    private isPreviewMode: boolean = false;
    
    // Editor settings
    private editorSettings: EditorSettings = {
        gridEnabled: true,
        gridSize: 50,
        snapToGrid: false,
        gizmoSize: 8,
        showGizmoLabels: true,
        gizmoColors: {} as any,
        showPreview: true,
        previewSpeed: 1.0,
        showDebugInfo: false,
        autoSave: true,
        autoSaveInterval: 300, // 5 minutes
        backupCount: 5,
        maxVisibleSubLevels: 3,
        preloadDistance: 1000,
        unloadDelay: 5.0
    };
    
    // Events
    public onLevelLoaded?: (level: ILevelData) => void;
    public onLevelSaved?: (level: ILevelData) => void;
    public onSelectionChanged?: (selection: SelectionInfo) => void;
    public onEditModeChanged?: (mode: EditMode) => void;
    
    protected onLoad(): void {
        this.initializeSystems();
        this.setupEventHandlers();
    }
    
    protected start(): void {
        console.log('LevelEditor initialized');
        this.createNewLevel();
    }
    
    /**
     * Initialize all subsystems
     */
    private initializeSystems(): void {
        // Get or create system components
        if (!this.levelManager) {
            this.levelManager = this.getComponent(LevelManager) || this.addComponent(LevelManager);
        }
        
        if (!this.cameraManager) {
            this.cameraManager = this.getComponent(CameraManager) || this.addComponent(CameraManager);
        }
        
        if (!this.pathManager) {
            this.pathManager = this.getComponent(PathManager) || this.addComponent(PathManager);
        }
        
        if (!this.spawnerSystem) {
            this.spawnerSystem = this.getComponent(SpawnerSystem) || this.addComponent(SpawnerSystem);
        }
        
        if (!this.mapSystem) {
            this.mapSystem = this.getComponent(MapSystem) || this.addComponent(MapSystem);
        }
        
        if (!this.eventManager) {
            this.eventManager = this.getComponent(EventGraphManager) || this.addComponent(EventGraphManager);
        }
        
        if (!this.editorGizmos) {
            this.editorGizmos = this.getComponent(EditorGizmos) || this.addComponent(EditorGizmos);
        }
        
        if (!this.pathEditor) {
            this.pathEditor = this.getComponent(PathEditor) || this.addComponent(PathEditor);
        }
        
        console.log('All editor systems initialized');
    }
    
    /**
     * Setup event handlers between systems
     */
    private setupEventHandlers(): void {
        // Level Manager events
        if (this.levelManager) {
            this.levelManager.onLevelLoaded = this.onLevelLoadedInternal.bind(this);
            this.levelManager.onSubLevelActivated = this.onSubLevelActivated.bind(this);
            this.levelManager.onSubLevelDeactivated = this.onSubLevelDeactivated.bind(this);
        }
        
        // Camera Manager events
        if (this.cameraManager) {
            this.cameraManager.onPositionChanged = this.onCameraPositionChanged.bind(this);
        }
        
        // Editor Gizmos events
        if (this.editorGizmos) {
            this.editorGizmos.onHandleSelected = this.onGizmoHandleSelected.bind(this);
            this.editorGizmos.onHandleMoved = this.onGizmoHandleMoved.bind(this);
        }
    }
    
    /**
     * Create a new level
     */
    public createNewLevel(): void {
        const newLevel: ILevelData = {
            metadata: {
                name: 'New Level',
                version: '1.0.0',
                duration: 300, // 5 minutes
                difficulty: 1,
                description: 'A new level created with the Level Editor',
                author: 'Level Editor',
                createdAt: new Date().toISOString(),
                modifiedAt: new Date().toISOString()
            },
            subLevels: [],
            globalEvents: {
                nodes: new Map(),
                connections: [],
                triggers: []
            },
            cameraSettings: {
                viewportSize: { width: 1920, height: 1080 },
                scrollSpeed: 100,
                smoothing: 5.0
            }
        };
        
        // Create a default sublevel
        const defaultSubLevel = this.createDefaultSubLevel();
        newLevel.subLevels.push(defaultSubLevel);
        
        this.loadLevel(newLevel);
        console.log('New level created');
    }
    
    /**
     * Load a level from data
     */
    public async loadLevel(levelData: ILevelData): Promise<void> {
        try {
            this.currentLevel = levelData;
            
            // Load level into systems
            if (this.levelManager) {
                await this.levelManager.loadLevel(levelData);
            }
            
            // Initialize camera
            if (this.cameraManager) {
                this.cameraManager.initializeCamera(levelData.cameraSettings);
            }
            
            // Load paths
            if (this.pathManager) {
                levelData.subLevels.forEach(subLevel => {
                    subLevel.paths.forEach(path => {
                        this.pathManager!.addPath(path);
                    });
                });
            }
            
            // Load event graph
            if (this.eventManager) {
                this.eventManager.loadEventGraph(levelData.globalEvents);
            }
            
            // Select first sublevel
            if (levelData.subLevels.length > 0) {
                this.selectSubLevel(levelData.subLevels[0]);
            }
            
            // Refresh gizmos
            this.refreshGizmos();
            
            console.log(`Level loaded: ${levelData.metadata.name}`);
        } catch (error) {
            console.error('Failed to load level:', error);
            throw error;
        }
    }
    
    /**
     * Save the current level
     */
    public saveLevel(filePath?: string): string {
        if (!this.currentLevel) {
            throw new Error('No level to save');
        }
        
        // Update modification time
        this.currentLevel.metadata.modifiedAt = new Date().toISOString();
        
        // Serialize level
        const serializer = SerializerFactory.createSerializer('json');
        const serializedData = serializer.serialize(this.currentLevel);
        
        // Trigger save event
        this.onLevelSaved?.(this.currentLevel);
        
        console.log(`Level saved: ${this.currentLevel.metadata.name}`);
        return serializedData as string;
    }
    
    /**
     * Load level from file
     */
    public loadLevelFromFile(fileData: string): void {
        try {
            const serializer = SerializerFactory.createSerializer('json');
            const levelData = serializer.deserialize(fileData);
            this.loadLevel(levelData);
        } catch (error) {
            console.error('Failed to load level from file:', error);
            throw error;
        }
    }
    
    /**
     * Set edit mode
     */
    public setEditMode(mode: EditMode): void {
        this.currentEditMode = mode;
        this.onEditModeChanged?.(mode);
        
        // Update editor state based on mode
        switch (mode) {
            case EditMode.EditPath:
                if (this.pathEditor && this.selection.type === 'path') {
                    const path = this.getSelectedPath();
                    if (path) {
                        this.pathEditor.setPath(path);
                    }
                }
                break;
                
            case EditMode.Select:
                if (this.pathEditor) {
                    this.pathEditor.clearPath();
                }
                break;
        }
        
        this.refreshGizmos();
        console.log(`Edit mode changed to: ${mode}`);
    }
    
    /**
     * Select a SubLevel
     */
    public selectSubLevel(subLevel: SubLevel): void {
        this.selectedSubLevel = subLevel;
        this.setSelection('sublevel', subLevel.id);
        this.refreshGizmos();
        console.log(`SubLevel selected: ${subLevel.name}`);
    }
    
    /**
     * Add a new SubLevel
     */
    public addSubLevel(bounds: Rect): SubLevel {
        if (!this.currentLevel) {
            throw new Error('No level loaded');
        }
        
        const subLevel: SubLevel = {
            id: IDGenerator.generate('sublevel'),
            name: `SubLevel ${this.currentLevel.subLevels.length + 1}`,
            bounds: bounds,
            maps: [],
            spawners: [],
            paths: [],
            events: {
                nodes: new Map(),
                connections: [],
                triggers: []
            },
            connections: []
        };
        
        this.currentLevel.subLevels.push(subLevel);
        this.selectSubLevel(subLevel);
        
        console.log(`SubLevel added: ${subLevel.name}`);
        return subLevel;
    }
    
    /**
     * Add a spawner to the current SubLevel
     */
    public addSpawner(position: Vec3, prefabPath: string): Spawner | null {
        if (!this.selectedSubLevel || !this.spawnerSystem) {
            console.warn('No SubLevel selected or SpawnerSystem not available');
            return null;
        }
        
        const spawner = this.spawnerSystem.createSpawner(
            `Spawner ${this.selectedSubLevel.spawners.length + 1}`,
            position,
            prefabPath
        );
        
        this.selectedSubLevel.spawners.push(spawner);
        this.spawnerSystem.registerSpawner(spawner);
        
        this.refreshGizmos();
        console.log(`Spawner added: ${spawner.name}`);
        return spawner;
    }
    
    /**
     * Add a path to the current SubLevel
     */
    public addPath(startPosition: Vec3): Path | null {
        if (!this.selectedSubLevel || !this.pathManager) {
            console.warn('No SubLevel selected or PathManager not available');
            return null;
        }
        
        const path = this.pathManager.createPath(
            `Path ${this.selectedSubLevel.paths.length + 1}`,
            startPosition
        );
        
        this.selectedSubLevel.paths.push(path);
        
        this.refreshGizmos();
        console.log(`Path added: ${path.name}`);
        return path;
    }
    
    /**
     * Start level preview
     */
    public startPreview(): void {
        if (!this.currentLevel || !this.levelManager || !this.eventManager) {
            console.warn('Cannot start preview: missing level or systems');
            return;
        }
        
        this.isPreviewMode = true;
        
        // Start systems
        this.eventManager.startExecution();
        if (this.spawnerSystem) {
            this.spawnerSystem.activateAllSpawners();
        }
        
        // Hide editor gizmos
        if (this.editorGizmos) {
            this.editorGizmos.node.active = false;
        }
        
        console.log('Level preview started');
    }
    
    /**
     * Stop level preview
     */
    public stopPreview(): void {
        this.isPreviewMode = false;
        
        // Stop systems
        if (this.eventManager) {
            this.eventManager.stopExecution();
        }
        if (this.spawnerSystem) {
            this.spawnerSystem.deactivateAllSpawners();
        }
        
        // Show editor gizmos
        if (this.editorGizmos) {
            this.editorGizmos.node.active = true;
        }
        
        this.refreshGizmos();
        console.log('Level preview stopped');
    }
    
    /**
     * Refresh all gizmos
     */
    private refreshGizmos(): void {
        if (!this.editorGizmos || this.isPreviewMode) return;
        
        this.editorGizmos.clearGizmos();
        
        // Draw camera viewport
        this.editorGizmos.drawCameraViewport();
        
        // Draw selected SubLevel
        if (this.selectedSubLevel) {
            this.editorGizmos.drawSubLevelGizmos(this.selectedSubLevel);
            
            // Draw spawners
            this.selectedSubLevel.spawners.forEach(spawner => {
                this.editorGizmos!.drawSpawnerGizmos(spawner);
            });
            
            // Draw paths
            this.selectedSubLevel.paths.forEach(path => {
                this.editorGizmos!.drawPathGizmos(path);
            });
            
            // Draw map layers
            this.selectedSubLevel.maps.forEach(mapLayer => {
                this.editorGizmos!.drawMapLayerGizmos(mapLayer);
            });
        }
    }
    
    /**
     * Set current selection
     */
    private setSelection(type: SelectionInfo['type'], objectId: string, additionalData?: any): void {
        this.selection = {
            type,
            objectId,
            subLevelId: this.selectedSubLevel?.id,
            additionalData
        };
        
        this.onSelectionChanged?.(this.selection);
    }
    
    /**
     * Get selected path
     */
    private getSelectedPath(): Path | null {
        if (this.selection.type !== 'path' || !this.selectedSubLevel) return null;
        
        return this.selectedSubLevel.paths.find(p => p.id === this.selection.objectId) || null;
    }
    
    /**
     * Create default SubLevel
     */
    private createDefaultSubLevel(): SubLevel {
        return {
            id: IDGenerator.generate('sublevel'),
            name: 'Main SubLevel',
            bounds: new Rect(-1000, -1000, 2000, 2000),
            maps: [],
            spawners: [],
            paths: [],
            events: {
                nodes: new Map(),
                connections: [],
                triggers: []
            },
            connections: []
        };
    }
    
    // Event handlers
    private onLevelLoadedInternal(level: ILevelData): void {
        this.onLevelLoaded?.(level);
    }
    
    private onSubLevelActivated(subLevel: SubLevel): void {
        console.log(`SubLevel activated: ${subLevel.name}`);
    }
    
    private onSubLevelDeactivated(subLevel: SubLevel): void {
        console.log(`SubLevel deactivated: ${subLevel.name}`);
    }
    
    private onCameraPositionChanged(position: Vec3, viewport: Rect): void {
        // Update gizmos when camera moves
        this.refreshGizmos();
    }
    
    private onGizmoHandleSelected(handle: any): void {
        console.log(`Gizmo handle selected: ${handle.id}`);
        // Handle gizmo selection
    }
    
    private onGizmoHandleMoved(handle: any, newPosition: Vec3): void {
        console.log(`Gizmo handle moved: ${handle.id} to ${newPosition.toString()}`);
        // Handle gizmo movement
    }
    
    // Getters
    /**
     * Get current level data
     */
    public getCurrentLevel(): ILevelData | null {
        return this.currentLevel;
    }
    
    public getSelectedSubLevel(): SubLevel | null {
        return this.selectedSubLevel;
    }
    
    public getCurrentEditMode(): EditMode {
        return this.currentEditMode;
    }
    
    public getSelection(): SelectionInfo {
        return this.selection;
    }
    
    public isInPreviewMode(): boolean {
        return this.isPreviewMode;
    }
    
    public getEditorSettings(): EditorSettings {
        return this.editorSettings;
    }
}

