/**
 * CrazyStorm 2.0 Integration for Cocos Creator
 * Core Type Definitions
 * 
 * This file contains all the TypeScript interfaces and enums that mirror
 * the CrazyStorm 2.0 data structures for seamless integration.
 */

import { Vec2, Color, Enum } from 'cc';

// ============================================================================
// CORE INTERFACES
// ============================================================================

/**
 * Root file structure representing a complete CrazyStorm project
 */
export interface CrazyStormFile {
    version: string;
    fileResourceIndex: number;
    particleIndex: number;
    particleSystems: ParticleSystem[];
    images: FileResource[];
    sounds: FileResource[];
    globals: VariableResource[];
}

/**
 * A particle system containing multiple layers and custom particle types
 */
export interface ParticleSystem {
    name: string;
    customTypeIndex: number;
    layerIndex: number;
    customTypes: ParticleType[];
    layers: Layer[];
    componentIndex: { [key: string]: number };
}

/**
 * A layer represents a timeline-based container for components
 */
export interface Layer {
    name: string;
    visible: boolean;
    color: LayerColor;
    beginFrame: number;
    totalFrame: number;
    components: Component[];
}

/**
 * Base component interface - all CrazyStorm components inherit from this
 */
export interface Component {
    specificType: ComponentType;
    id: number;
    name: string;
    properties: { [key: string]: PropertyValue };
    componentData: ComponentData;
    variables: VariableResource[];
    componentEventGroups: EventGroup[];
    children: Component[];
    parentId?: number;
    bindingTargetId?: number;
}

/**
 * Component runtime data
 */
export interface ComponentData {
    layerFrame: number;
    currentFrame: number;
    beginFrame: number;
    totalFrame: number;
    position: Vec2;
    speed: number;
    speedAngle: number;
    acspeed: number;
    acspeedAngle: number;
    visibility: boolean;
}

/**
 * Property value that can be either a constant or an expression
 */
export interface PropertyValue {
    expression: boolean;
    value: string | number;
}

/**
 * Emitter-specific data structure
 */
export interface EmitterData {
    emitPosition: Vec2;
    emitCount: number;
    emitCycle: number;
    emitAngle: number;
    emitRange: number;
    emitRadius: number;
}

/**
 * Base particle data structure
 */
export interface ParticleBaseData {
    maxLife: number;
    pcurrentFrame: number;
    pposition: Vec2;
    widthScale: number;
    rgb: Color;
    mass: number;
    opacity: number;
    pspeed: number;
    pspeedAngle: number;
    pacspeed: number;
    pacspeedAngle: number;
    protation: number;
    blendType: BlendType;
    killOutside: boolean;
    collision: boolean;
    ignoreMask: boolean;
    ignoreRebound: boolean;
    ignoreForce: boolean;
    fogEffect: boolean;
    fadeEffect: boolean;
}

/**
 * Curve particle specific data
 */
export interface CurveParticleData {
    length: number;
}

/**
 * Force field specific data
 */
export interface ForceFieldData {
    halfWidth: number;
    halfHeight: number;
    fieldShape: FieldShape;
    reach: Reach;
    targetName: string;
    force: number;
    direction: number;
    forceType: ForceType;
}

/**
 * Event group for handling particle events
 */
export interface EventGroup {
    name: string;
    events: ParticleEvent[];
}

/**
 * Individual particle event
 */
export interface ParticleEvent {
    type: string;
    condition: string;
    action: string;
}

/**
 * Resource definitions
 */
export interface Resource {
    label: string;
}

export interface VariableResource extends Resource {
    value: number;
}

export interface FileResource extends Resource {
    id: number;
    relativePath: string;
}

/**
 * Particle type definition
 */
export interface ParticleType {
    id: number;
    name: string;
    imageId: number;
    radius: number;
    textureRect: { x: number; y: number; width: number; height: number };
}

// ============================================================================
// ENUMS
// ============================================================================

export enum ComponentType {
    CurveEmitter = "CurveEmitter",
    MultiEmitter = "MultiEmitter", 
    ForceField = "ForceField",
    EventField = "EventField",
    Rebounder = "Rebounder"
}

export enum LayerColor {
    Red = "Red",
    Green = "Green", 
    Blue = "Blue",
    Yellow = "Yellow",
    Purple = "Purple",
    Cyan = "Cyan"
}

export enum BlendType {
    AlphaBlend = "AlphaBlend",
    Additive = "Additive",
    Subtractive = "Subtractive",
    Multiply = "Multiply"
}

export enum FieldShape {
    Rectangle = "Rectangle",
    Circle = "Circle"
}

export enum Reach {
    All = "All",
    Layer = "Layer", 
    Name = "Name"
}

export enum ForceType {
    Direction = "Direction",
    Inner = "Inner",
    Outer = "Outer"
}

// ============================================================================
// SPECIALIZED COMPONENT INTERFACES
// ============================================================================

export interface CurveEmitterComponent extends Component {
    specificType: ComponentType.CurveEmitter;
    emitter: {
        emitterData: EmitterData;
        particleBase: {
            type: number;
            properties: { [key: string]: PropertyValue };
            particleBaseData: ParticleBaseData;
            curveParticle: {
                curveParticleData: CurveParticleData;
            };
        };
        particleEventGroups: EventGroup[];
    };
}

export interface MultiEmitterComponent extends Component {
    specificType: ComponentType.MultiEmitter;
    emitter: {
        emitterData: EmitterData;
        particleBase: {
            type: number;
            properties: { [key: string]: PropertyValue };
            particleBaseData: ParticleBaseData;
        };
        particleEventGroups: EventGroup[];
    };
}

export interface ForceFieldComponent extends Component {
    specificType: ComponentType.ForceField;
    forceFieldData: ForceFieldData;
}

export interface EventFieldComponent extends Component {
    specificType: ComponentType.EventField;
    // Event field specific properties will be added based on CrazyStorm implementation
}

export interface RebounderComponent extends Component {
    specificType: ComponentType.Rebounder;
    // Rebounder specific properties will be added based on CrazyStorm implementation
}

// ============================================================================
// UTILITY TYPES
// ============================================================================

/**
 * Union type for all component types
 */
export type AnyComponent = CurveEmitterComponent | MultiEmitterComponent | ForceFieldComponent | EventFieldComponent | RebounderComponent;

/**
 * Parser configuration options
 */
export interface ParserConfig {
    validateStructure: boolean;
    optimizeForMobile: boolean;
    debugMode: boolean;
}

/**
 * Runtime particle instance
 */
export interface RuntimeParticle {
    id: number;
    alive: boolean;
    position: Vec2;
    velocity: Vec2;
    acceleration: Vec2;
    rotation: number;
    scale: number;
    opacity: number;
    color: Color;
    currentFrame: number;
    maxLife: number;
    particleType: ParticleType;
    emitterId: number;
    layerId: number;
}

// ============================================================================
// ENUM REGISTRATIONS FOR COCOS CREATOR
// ============================================================================

// Register enums with Cocos Creator for property decorators
Enum(ComponentType);
Enum(LayerColor);
Enum(BlendType);
Enum(FieldShape);
Enum(Reach);
Enum(ForceType);
