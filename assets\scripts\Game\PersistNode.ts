import { _decorator, Component, Node, game, Prefab, SpriteFrame, AudioClip } from 'cc';
import { AnimFactory } from './factroy/AnimFactory';
import { EnemyBulletFactory } from './factroy/EnemyBulletFactory';
import { EnemyFactory } from './factroy/EnemyFactory';
import { GameFactory } from './factroy/GameFactory';
import { GoodsFactory } from './factroy/GoodsFactory';
import { PlayerBulletFactory } from './factroy/PlayerBulletFactory';
const { ccclass, property } = _decorator;

@ccclass('PersistNode')
export class PersistNode extends Component {

    @property(Prefab)
    playerBulletPreb: Prefab = null;    //敌机子弹预制体

    @property(Prefab)
    enemyPreb: Prefab = null;       //敌机预制体

    @property(Prefab)
    enemyBulletPreb = null;     //敌机子弹预制体

    @property(Prefab)
    animPreb = null;     //敌机爆炸动画预制体

    @property(Prefab)
    goodsPreb = null;     //物资预制体

    @property(SpriteFrame)
    bloodGoods: SpriteFrame = null;   //加血道具图片

    @property(SpriteFrame)
    lightGoods: SpriteFrame = null;   //激光道具图片

    @property(SpriteFrame)
    missileGoods: SpriteFrame = null;   //导弹道具图片

    @property(SpriteFrame)
    normalBullet: SpriteFrame = null;   //普通子弹图片

    @property(SpriteFrame)
    lightBullet: SpriteFrame = null;    //激光子弹图片

    @property(SpriteFrame)
    missileBullet: SpriteFrame = null;  //导弹子弹图片

    @property(SpriteFrame)
    enemy1: SpriteFrame = null;     //enemy1图片

    @property(SpriteFrame)
    enemy2: SpriteFrame = null;     //enemy2图片

    @property(SpriteFrame)
    enemybullet1: SpriteFrame = null;     //enemy1子弹图片

    @property(SpriteFrame)
    enemybullet2: SpriteFrame = null;     //enemy2子弹图片

    @property
    normalBulletSpeed: number = 0;   //普通子弹发射时间间隔

    @property
    lightBulletSpeed: number = 0;   //激光子弹发射时间间隔

    @property
    missileBulletSpeed: number = 0;   //导弹子弹发射时间间隔

    @property
    normalBulletMoveSpeed: number = 0;    //普通子弹移动速度

    @property
    lightBulletMoveSpeed: number = 0;    //激光子弹移动速度

    @property
    missileBulletMoveSpeed: number = 0;    //激光子弹移动速度

    @property
    enemyBullet1MoveSpeed: number = 0;      //敌机子弹1的移动速度

    @property
    enemyBullet2MoveSpeed: number = 0;      //敌机子弹1的移动速度

    @property
    enemy1MinProduceTime: number = 0;    //enemy1出现时间的间隔的下限

    @property
    enemy1MaxProduceTime: number = 0;   //enemy1出现时间的间隔的上限

    @property
    enemy2MinProduceTime: number = 0;    //enemy2出现时间的间隔的下限

    @property
    enemy2MaxProduceTime: number = 0;   //enemy2出现时间的间隔的上限

    @property
    bloodGoodsMinProduceTime: number = 0;    //加血物资出现时间的间隔的下限

    @property
    bloodGoodsMaxProduceTime: number = 0;   //加血物资出现时间的间隔的上限

    @property
    lightGoodsMinProduceTime: number = 0;    //激光物资出现时间的间隔的下限

    @property
    lightGoodsMaxProduceTime: number = 0;   //激光物资出现时间的间隔的上限

    @property
    missileGoodsMinProduceTime: number = 0;    //导弹物资出现时间的间隔的下限

    @property
    missileGoodsMaxProduceTime: number = 0;   //导弹物资出现时间的间隔的上限

    @property
    enemy1MoveSpeed: number = 0;    //敌机1的移动速度

    @property
    enemy2MoveSpeed: number = 0;    //敌机1的移动速度

    @property
    enemy1ShootSpeed: number = 0;   //敌机1发射子弹时间间隔

    @property
    enemy2ShootSpeed: number = 0;   //敌机2发射子弹时间间隔

    @property
    planeTotalBlood: number = 0;         //玩家总血量

    @property
    enemyTotalBlood: number = 0;    //敌机总血量

    @property
    enemyBullet1ReduceBlood: number = 0;        //当被敌机子弹一打中后掉多少血

    @property
    enemyBullet2ReduceBlood: number = 0;        //当被敌机子弹二打中后掉多少血

    @property
    playerNormalReduce: number = 0;         //被普通子弹击中，敌机掉多少血

    @property
    playerLightReduce: number = 0;         //被激光子弹击中，敌机掉多少血

    @property
    playerMissileReduce: number = 0;         //导弹子弹击中，敌机掉多少血

    @property
    enemyContactPlayerReduce: number = 0;  //敌机碰到玩家，玩家掉多少血

    @property
    bloodGoodsMoveSpeed: number = 0;    //加血物资移动速度

    @property
    lightGoodsMoveSpeed: number = 0;      //激光物资移动速度

    @property
    missileGoodsMoveSpeed: number = 0;      //导弹物资移动速度

    @property(AudioClip)
    bulletAudioClip: AudioClip = null;      //普通子弹声音

    @property(AudioClip)
    lightAudioClip: AudioClip = null;      //激光子弹声音

    @property(AudioClip)
    missileAudioClip: AudioClip = null;      //导弹子弹声音

    @property(AudioClip)
    boomAudioClip: AudioClip = null;      //敌机爆炸声音

    @property(AudioClip)
    gameOverAudioClip: AudioClip = null;      //游戏结束声音

    playerBulletFactory: GameFactory = null;    //玩家子弹工厂

    enemyFactory: GameFactory = null;       //敌机工厂

    enemyBulletFactory: GameFactory = null;  //敌机子弹工厂

    goodsFactory: GameFactory = null;  //敌机子弹工厂

    animFactory: AnimFactory = null; //动画工厂

    onLoad() {
        game.addPersistRootNode(this.node);    //将PersistNode设置为常驻节点

        this.playerBulletFactory = new PlayerBulletFactory(); //把玩家子弹工厂建在持久节点上

        this.enemyFactory = new EnemyFactory(); //把敌机工厂建在持久节点上

        this.enemyBulletFactory = new EnemyBulletFactory(); //把敌机子弹工厂建在持久节点上

        this.goodsFactory = new GoodsFactory(); //把物资工厂建在持久节点上

        this.animFactory = new AnimFactory();
    }

}

