{"version": 3, "sources": ["file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/GoodsFactory.ts"], "names": ["GoodsFactory", "_decorator", "instantiate", "Global", "Goods", "PersistNode", "GameFactory", "ccclass", "property", "createProduct", "productType", "goodsTemp", "productPool", "size", "get", "persistNode", "getComponent", "goodsPreb", "BLOOD_GOODS", "init", "bloodGoods", "LIGHT_GOODS", "lightGoods", "MISSILE_GOODS", "missileGoods"], "mappings": ";;;iJAOaA,Y;;;;;;;;;;;;;;;;;;;;;;;;;;;AAPJC,MAAAA,U,OAAAA,U;AAA6BC,MAAAA,W,OAAAA,W;;AAC7BC,MAAAA,M,iBAAAA,M;;AACAC,MAAAA,K,iBAAAA,K;;AACAC,MAAAA,W,iBAAAA,W;;AACAC,MAAAA,W,iBAAAA,W;;;;;;;;;OACH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBP,U;;8BAEjBD,Y,GAAN,MAAMA,YAAN;AAAA;AAAA,sCAAuC;AAGnCS,QAAAA,aAAa,CAACC,WAAD,EAA4B;AAC5C,cAAIC,SAAe,GAAG,IAAtB;;AAEA,cAAG,KAAKC,WAAL,CAAiBC,IAAjB,KAA0B,CAA7B,EAAgC;AAC5BF,YAAAA,SAAS,GAAG,KAAKC,WAAL,CAAiBE,GAAjB,EAAZ,CAD4B,CACS;AACxC,WAFD,MAEO;AACHH,YAAAA,SAAS,GAAGT,WAAW,CAAC,KAAKa,WAAL,CAAiBC,YAAjB;AAAA;AAAA,4CAA2CC,SAA5C,CAAvB,CADG,CAC6E;AACnF;;AAED,kBAAOP,WAAP;AACI,iBAAK;AAAA;AAAA,kCAAOQ,WAAZ;AACIP,cAAAA,SAAS,CAACK,YAAV;AAAA;AAAA,kCAA8BG,IAA9B,CAAmCT,WAAnC,EAAgD,KAAKK,WAAL,CAAiBC,YAAjB;AAAA;AAAA,8CAA2CI,UAA3F;AACA;;AACJ,iBAAK;AAAA;AAAA,kCAAOC,WAAZ;AACIV,cAAAA,SAAS,CAACK,YAAV;AAAA;AAAA,kCAA8BG,IAA9B,CAAmCT,WAAnC,EAAgD,KAAKK,WAAL,CAAiBC,YAAjB;AAAA;AAAA,8CAA2CM,UAA3F;AACA;;AACJ,iBAAK;AAAA;AAAA,kCAAOC,aAAZ;AACIZ,cAAAA,SAAS,CAACK,YAAV;AAAA;AAAA,kCAA8BG,IAA9B,CAAmCT,WAAnC,EAAgD,KAAKK,WAAL,CAAiBC,YAAjB;AAAA;AAAA,8CAA2CQ,YAA3F;AACA;AATR;;AAYA,iBAAOb,SAAP;AACH;;AAzByC,O", "sourcesContent": ["import { _decorator, Component, Node, instantiate } from 'cc';\nimport { Global } from '../Global';\nimport { Goods } from '../Goods';\nimport { PersistNode } from '../PersistNode';\nimport { GameFactory } from './GameFactory';\nconst { ccclass, property } = _decorator;\n\nexport class GoodsFactory extends GameFactory {\n\n\n    public createProduct(productType: string): Node {\n        let goodsTemp: Node = null;\n\n        if(this.productPool.size() > 0) {\n            goodsTemp = this.productPool.get();  //如果池里有物资，就直接拿来用\n        } else {\n            goodsTemp = instantiate(this.persistNode.getComponent(PersistNode).goodsPreb);  //从常驻节点拿到预制体原料\n        }\n\n        switch(productType) {\n            case Global.BLOOD_GOODS:\n                goodsTemp.getComponent(Goods).init(productType, this.persistNode.getComponent(PersistNode).bloodGoods);\n                break;\n            case Global.LIGHT_GOODS:\n                goodsTemp.getComponent(Goods).init(productType, this.persistNode.getComponent(PersistNode).lightGoods);\n                break;\n            case Global.MISSILE_GOODS:\n                goodsTemp.getComponent(Goods).init(productType, this.persistNode.getComponent(PersistNode).missileGoods);\n                break;\n        }\n\n        return goodsTemp;\n    }\n\n}\n\n"]}