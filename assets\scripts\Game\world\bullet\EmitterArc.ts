import { _decorator } from 'cc';
import { Emitter } from './Emitter';
const { ccclass, property } = _decorator;

@ccclass('EmitterArc')
export class EmitterArc extends Emitter {

    // 发射角度
    @property
    angle: number = 90;

    // 发射弧度
    @property
    arc: number = 0;

    // 发射半径
    @property
    radius: number = 0;

    /**
     * Implementation of emitBullet for arc-based emission
     */
    emitBullet(): void {
        if (!this.bulletPrefab || this.count <= 0) {
            return;
        }

        // TODO: Implement bullet emission logic
        // This will be implemented when the bullet system is ready
        console.log(`EmitterArc: Emitting ${this.count} bullets in arc pattern`);

        for (let i = 0; i < this.count; i++) {
            const direction = this.getDirection(i);
            // TODO: Create and configure bullet instance
            console.log(`Bullet ${i}: direction (${direction.x.toFixed(2)}, ${direction.y.toFixed(2)})`);
        }
    }

    /**
     * Calculate the direction for a bullet at the given index
     * @param index The index of the bullet (0 to count-1)
     * @returns Direction vector {x, y}
     */
    getDirection(index: number): { x: number, y: number } {
        // 计算发射方向
        const angleOffset = this.count > 1 ? (this.arc / (this.count - 1)) * index - this.arc / 2 : 0;
        const radian = (this.angle + angleOffset) * (Math.PI / 180);
        return {
            x: Math.cos(radian),
            y: Math.sin(radian)
        };
    }

    /**
     * Get the spawn position for a bullet at the given index
     * @param index The index of the bullet (0 to count-1)
     * @returns Position offset from emitter center
     */
    getSpawnPosition(index: number): { x: number, y: number } {
        if (this.radius <= 0) {
            return { x: 0, y: 0 };
        }

        const direction = this.getDirection(index);
        return {
            x: direction.x * this.radius,
            y: direction.y * this.radius
        };
    }
}