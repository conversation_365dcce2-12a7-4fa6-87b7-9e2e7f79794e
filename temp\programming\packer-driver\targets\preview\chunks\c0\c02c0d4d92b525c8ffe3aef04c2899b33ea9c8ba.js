System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, Color, GizmoDrawer, RegisterGizmoDrawer, GizmoUtils, EmitterArc, _dec, _class, _crd, EmitterArcGizmo;

  function _reportPossibleCrUseOfGizmoDrawer(extras) {
    _reporterNs.report("GizmoDrawer", "./GizmoDrawer", _context.meta, extras);
  }

  function _reportPossibleCrUseOfRegisterGizmoDrawer(extras) {
    _reporterNs.report("RegisterGizmoDrawer", "./GizmoDrawer", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGizmoUtils(extras) {
    _reporterNs.report("GizmoUtils", "./GizmoUtils", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEmitterArc(extras) {
    _reporterNs.report("EmitterArc", "../world/bullet/EmitterArc", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      Color = _cc.Color;
    }, function (_unresolved_2) {
      GizmoDrawer = _unresolved_2.GizmoDrawer;
      RegisterGizmoDrawer = _unresolved_2.RegisterGizmoDrawer;
    }, function (_unresolved_3) {
      GizmoUtils = _unresolved_3.GizmoUtils;
    }, function (_unresolved_4) {
      EmitterArc = _unresolved_4.EmitterArc;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "3c748JAhQlND5BFXS223daj", "EmitterArcGizmo", undefined);

      __checkObsolete__(['_decorator', 'Graphics', 'Color', 'Node']);

      /**
       * Gizmo drawer for EmitterArc components
       * Draws visual debugging information for arc-based bullet emitters
       */
      _export("EmitterArcGizmo", EmitterArcGizmo = (_dec = _crd && RegisterGizmoDrawer === void 0 ? (_reportPossibleCrUseOfRegisterGizmoDrawer({
        error: Error()
      }), RegisterGizmoDrawer) : RegisterGizmoDrawer, _dec(_class = class EmitterArcGizmo extends (_crd && GizmoDrawer === void 0 ? (_reportPossibleCrUseOfGizmoDrawer({
        error: Error()
      }), GizmoDrawer) : GizmoDrawer) {
        constructor() {
          super(...arguments);
          this.componentType = _crd && EmitterArc === void 0 ? (_reportPossibleCrUseOfEmitterArc({
            error: Error()
          }), EmitterArc) : EmitterArc;
          this.drawerName = "EmitterArcGizmo";
          // Gizmo display options
          this.showRadius = true;
          this.showDirections = true;
          this.showCenter = true;
          this.showArc = true;
          // Colors
          this.radiusColor = Color.GRAY;
          this.directionColor = Color.RED;
          this.centerColor = Color.WHITE;
          this.arcColor = Color.YELLOW;
          // Display settings
          this.speedScale = 1.0;
          this.arrowSize = 8;
          this.centerSize = 8;
        }

        drawGizmos(emitter, graphics, node) {
          // Get world position for drawing
          var worldPos = node.worldPosition;
          var worldX = worldPos.x;
          var worldY = worldPos.y; // Draw center point

          if (this.showCenter) {
            this.drawCenter(graphics, worldX, worldY);
          } // Draw radius circle


          if (this.showRadius && emitter.radius > 0) {
            this.drawRadius(graphics, worldX, worldY, emitter.radius);
          } // Draw arc indicator


          if (this.showArc && emitter.arc > 0) {
            this.drawArcIndicator(graphics, emitter, worldX, worldY);
          } // Draw direction arrows


          if (this.showDirections && emitter.count > 0) {
            this.drawDirections(graphics, emitter, worldX, worldY);
          }
        }

        drawCenter(graphics, worldX, worldY) {
          (_crd && GizmoUtils === void 0 ? (_reportPossibleCrUseOfGizmoUtils({
            error: Error()
          }), GizmoUtils) : GizmoUtils).drawCross(graphics, worldX, worldY, this.centerSize, this.centerColor);
        }

        drawRadius(graphics, worldX, worldY, radius) {
          (_crd && GizmoUtils === void 0 ? (_reportPossibleCrUseOfGizmoUtils({
            error: Error()
          }), GizmoUtils) : GizmoUtils).drawCircle(graphics, worldX, worldY, radius, this.radiusColor, false);
        }

        drawArcIndicator(graphics, emitter, worldX, worldY) {
          if (emitter.arc <= 0) return;
          graphics.strokeColor = this.arcColor;
          graphics.lineWidth = 2; // Convert angle and arc to radians

          var baseAngleRad = (emitter.angle + 90) * Math.PI / 180; // +90 because 0 degrees is up in Cocos

          var arcRad = emitter.arc * Math.PI / 180;
          var startAngle = baseAngleRad - arcRad / 2;
          var endAngle = baseAngleRad + arcRad / 2; // Draw arc at radius distance

          var arcRadius = Math.max(emitter.radius, 30); // Minimum radius for visibility
          // Draw arc

          var segments = Math.max(8, Math.floor(emitter.arc / 5)); // More segments for larger arcs

          for (var i = 0; i <= segments; i++) {
            var angle = startAngle + (endAngle - startAngle) * (i / segments);
            var x = worldX + Math.cos(angle) * arcRadius;
            var y = worldY + Math.sin(angle) * arcRadius;

            if (i === 0) {
              graphics.moveTo(x, y);
            } else {
              graphics.lineTo(x, y);
            }
          } // Draw arc end indicators


          var startX = worldX + Math.cos(startAngle) * arcRadius;
          var startY = worldY + Math.sin(startAngle) * arcRadius;
          var endX = worldX + Math.cos(endAngle) * arcRadius;
          var endY = worldY + Math.sin(endAngle) * arcRadius; // Lines from center to arc ends

          graphics.moveTo(worldX, worldY);
          graphics.lineTo(startX, startY);
          graphics.moveTo(worldX, worldY);
          graphics.lineTo(endX, endY);
          graphics.stroke();
        }

        drawDirections(graphics, emitter, worldX, worldY) {
          var baseLength = 30;
          var speedFactor = emitter.speedMultiplier || 1;
          var arrowLength = Math.max(baseLength, baseLength * speedFactor * this.speedScale);

          for (var i = 0; i < emitter.count; i++) {
            var direction = emitter.getDirection(i);
            var spawnPos = emitter.getSpawnPosition(i); // Start position (at spawn position relative to world position)

            var startX = worldX + spawnPos.x;
            var startY = worldY + spawnPos.y; // End position (direction from spawn position)

            var endX = startX + direction.x * arrowLength;
            var endY = startY + direction.y * arrowLength; // Draw arrow

            (_crd && GizmoUtils === void 0 ? (_reportPossibleCrUseOfGizmoUtils({
              error: Error()
            }), GizmoUtils) : GizmoUtils).drawArrow(graphics, startX, startY, endX, endY, this.directionColor, this.arrowSize);
          }
        }

        getPriority() {
          return 10; // Draw emitter gizmos with medium priority
        }
        /**
         * Configure display options
         */


        configure(options) {
          if (options.showRadius !== undefined) this.showRadius = options.showRadius;
          if (options.showDirections !== undefined) this.showDirections = options.showDirections;
          if (options.showCenter !== undefined) this.showCenter = options.showCenter;
          if (options.showArc !== undefined) this.showArc = options.showArc;
          if (options.radiusColor !== undefined) this.radiusColor = options.radiusColor;
          if (options.directionColor !== undefined) this.directionColor = options.directionColor;
          if (options.centerColor !== undefined) this.centerColor = options.centerColor;
          if (options.arcColor !== undefined) this.arcColor = options.arcColor;
          if (options.speedScale !== undefined) this.speedScale = options.speedScale;
          if (options.arrowSize !== undefined) this.arrowSize = options.arrowSize;
          if (options.centerSize !== undefined) this.centerSize = options.centerSize;
        }

      }) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=c02c0d4d92b525c8ffe3aef04c2899b33ea9c8ba.js.map