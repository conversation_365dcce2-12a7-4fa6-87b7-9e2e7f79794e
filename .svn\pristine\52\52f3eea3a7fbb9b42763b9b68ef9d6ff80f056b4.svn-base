import { _decorator, Component, Node, Sprite<PERSON>rame, Sprite, Vec3, find, ProgressBar, Collider2D, Contact2DType, IPhysics2DContact, Label, log, director, AudioSource, Animation } from 'cc';
import { EnemyBullet } from './EnemyBullet';
import { GameFactory } from './factroy/GameFactory';
import { Global } from './Global';
import { PersistNode } from './PersistNode';
import { Player } from './Player';
const { ccclass, property } = _decorator;

@ccclass('Enemy')
export class Enemy extends Component {
    
    enemyType: string = null;

    curPos: Vec3 = null;

    enemyFactory: GameFactory = null;
    enemyBulletFactory: GameFactory = null;

    persistNode: Node = null;

    enemy1MoveSpeed: number = 0;    //敌机1的移动速度

    enemy2MoveSpeed: number = 0;    //敌机1的移动速度

    enemy1ShootTimer: number = 0;   //敌机1发射子弹计时器

    enemy2ShootTimer: number = 0;   //敌机2发射子弹计时器

    enemy1ShootSpeed: number = 0;   //敌机1发射子弹时间间隔

    enemy2ShootSpeed: number = 0;   //敌机2发射子弹时间间隔

    enemyTotalBlood: number = 0;    //敌机总血量

    enemyBlood: number = 0;         //敌机当前血量

    enemyContactPlayerReduce: number = 0;  //敌机碰到玩家，玩家掉多少血

    onLoad() {
        this.persistNode = find("PersistNode");
        this.enemyFactory = this.persistNode.getComponent(PersistNode).enemyFactory;
        this.enemyBulletFactory = this.persistNode.getComponent(PersistNode).enemyBulletFactory;

        this.enemy1MoveSpeed = this.persistNode.getComponent(PersistNode).enemy1MoveSpeed;
        this.enemy2MoveSpeed = this.persistNode.getComponent(PersistNode).enemy2MoveSpeed;

        this.enemy1ShootSpeed = this.persistNode.getComponent(PersistNode).enemy1ShootSpeed;
        this.enemy2ShootSpeed = this.persistNode.getComponent(PersistNode).enemy2ShootSpeed;

        this.enemyTotalBlood = this.persistNode.getComponent(PersistNode).enemyTotalBlood;
        this.enemyBlood = this.enemyTotalBlood; 

        this.enemyContactPlayerReduce = this.persistNode.getComponent(PersistNode).enemyContactPlayerReduce;

        let collider = this.node.getComponent(Collider2D);

        if (collider) {
            collider.on(Contact2DType.BEGIN_CONTACT, this.onBeginContact, this);
        }

    }

     onBeginContact(selfCollider: Collider2D, otherCollider: Collider2D, contact: IPhysics2DContact | null) {
        if (otherCollider.tag == 1) {
            otherCollider.getComponent(Player).planeBlood -= this.enemyContactPlayerReduce;
            otherCollider.node.getChildByName("Blood").getComponent(ProgressBar).progress = otherCollider.getComponent(Player).planeBlood / otherCollider.getComponent(Player).planeTotalBlood;
            
            if (this.enemyType == Global.ENEMY_1) {
                Global.SCORE += 20;
            } else {
                Global.SCORE += 40;
            }
            find("Canvas/Score").getComponent(Label).string = "Score:" + Global.SCORE.toString();

            //添加动画节点
            let anim = this.persistNode.getComponent(PersistNode).animFactory.createAnim();
            anim.setPosition(this.node.getPosition());
            find("Canvas").addChild(anim);
            anim.getComponent(Animation).play();    //播放动画

            this.enemyFactory.recycleProduct(this.node);    //敌机消失
            this.persistNode.getComponent(AudioSource).playOneShot(this.persistNode.getComponent(PersistNode).boomAudioClip);

            if (otherCollider.getComponent(Player).planeBlood <= 0) {
                director.loadScene("Main");
                this.persistNode.getComponent(AudioSource).playOneShot(this.persistNode.getComponent(PersistNode).gameOverAudioClip);
            }
        }
    }

    /**
     * 敌机初始化函数
     */
    init(enemyType: string, spriteFrame: SpriteFrame) {
        this.enemyType = enemyType;
        this.node.getComponent(Sprite).spriteFrame = spriteFrame;
        this.enemyBlood = this.enemyTotalBlood;     //敌机满血复活
        this.node.getChildByName("EnemyBlood").getComponent(ProgressBar).progress = 1;
    }

    update(deltaTime: number) {
        if (this.enemyType == Global.ENEMY_1) {     //敌机1相关操作
            this.enem1Move(deltaTime);          //敌机1移动

            //敌机1发射子弹
            this.enemy1ShootTimer += deltaTime;
            if (this.enemy1ShootTimer > this.enemy1ShootSpeed) {
                this.enemy1Shoot();
                this.enemy1ShootTimer = 0;
            }
            
        } else if (this.enemyType == Global.ENEMY_2) {      //敌机2相关操作
            this.enem2Move(deltaTime);

            //敌机2发射子弹
            this.enemy2ShootTimer += deltaTime;
            if (this.enemy2ShootTimer > this.enemy2ShootSpeed) {
                this.enemy2Shoot();
                this.enemy2ShootTimer = 0;
            }
        }
    }

    /**
     * 敌机2发射子弹
     */
     enemy2Shoot() {
        let posBegin: Vec3 = new Vec3();    //定义子弹开始的位置
        let enemyBullet: Node = null;

        enemyBullet = this.enemyBulletFactory.createProduct(Global.ENEMY_BULLET_2);  //制作子弹
        this.node.parent.addChild(enemyBullet);        //添加节点到画布

        this.curPos = this.node.getPosition();      //得到敌机机当前位置
        posBegin.x = this.curPos.x; 
        posBegin.y = this.curPos.y - 50;        //设置到机头位置
        enemyBullet.setPosition(posBegin);
    }

    /**
     * 敌机1发射子弹
     */
    enemy1Shoot() {
        let posBegin: Vec3 = new Vec3();    //定义子弹开始的位置
        let enemyBullet: Node = null;

        enemyBullet = this.enemyBulletFactory.createProduct(Global.ENEMY_BULLET_1);  //制作子弹
        this.node.parent.addChild(enemyBullet);        //添加节点到画布

        this.curPos = this.node.getPosition();      //得到敌机机当前位置
        posBegin.x = this.curPos.x; 
        posBegin.y = this.curPos.y - 50;        //设置到机头位置
        enemyBullet.setPosition(posBegin);
    }

    /**
     * 敌机1移动
     */
    enem1Move(deltaTime: number) {
        this.curPos = this.node.getPosition();
        this.curPos.y -= this.enemy1MoveSpeed * deltaTime;
        this.node.setPosition(this.curPos);

        if (this.curPos.y < -Global.HEIGHT / 2) {
            this.enemyFactory.recycleProduct(this.node);
        }
    }

    /**
     * 敌机2移动
     */
     enem2Move(deltaTime: number) {
        this.curPos = this.node.getPosition();
        this.curPos.y -= this.enemy2MoveSpeed * deltaTime;
        this.node.setPosition(this.curPos);

        if (this.curPos.y < -Global.HEIGHT / 2) {
            this.enemyFactory.recycleProduct(this.node);
        }
    }
}

