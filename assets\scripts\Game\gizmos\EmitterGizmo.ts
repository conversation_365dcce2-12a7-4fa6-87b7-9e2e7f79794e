import { _decorator, Graphics, Color, Node } from 'cc';
import { GizmoDrawer } from './GizmoDrawer';
import { GizmoUtils } from './GizmoUtils';
import { Emitter } from '../world/bullet/Emitter';

/**
 * Gizmo drawer for base Emitter components
 * Draws visual debugging information for basic bullet emitters
 */
export class EmitterGizmo extends GizmoDrawer<Emitter> {
    
    public readonly componentType = Emitter;
    public readonly drawerName = "EmitterGizmo";
    
    // Gizmo display options
    public showDirections: boolean = true;
    public showCenter: boolean = true;
    
    // Colors
    public directionColor: Color = Color.RED;
    public centerColor: Color = Color.WHITE;
    
    // Display settings
    public speedScale: number = 1.0;
    public arrowSize: number = 8;
    public centerSize: number = 8;
    
    public drawGizmos(emitter: Emitter, graphics: Graphics, node: Node): void {
        // Get world position for drawing
        const worldPos = node.worldPosition;

        // Draw center point
        if (this.showCenter) {
            this.drawCenter(graphics, worldPos.x, worldPos.y);
        }

        // Draw direction arrows
        if (this.showDirections && emitter.count > 0) {
            this.drawDirections(graphics, emitter, worldPos.x, worldPos.y);
        }
    }
    
    private drawCenter(graphics: Graphics, worldX: number, worldY: number): void {
        GizmoUtils.drawCross(graphics, worldX, worldY, this.centerSize, this.centerColor);
    }

    private drawDirections(graphics: Graphics, emitter: Emitter, worldX: number, worldY: number): void {
        const baseLength = 30;
        const speedFactor = emitter.speedMultiplier || 1;
        const arrowLength = Math.max(baseLength, baseLength * speedFactor * this.speedScale);

        for (let i = 0; i < emitter.count; i++) {
            // For basic emitters, just draw simple forward direction (up)
            const dirX = 0;
            const dirY = 1;

            const startX = worldX;
            const startY = worldY;
            const endX = worldX + dirX * arrowLength;
            const endY = worldY + dirY * arrowLength;

            // Draw arrow
            GizmoUtils.drawArrow(graphics, startX, startY, endX, endY, this.directionColor, this.arrowSize);
        }
    }
    
    public getPriority(): number {
        return 5; // Draw base emitter gizmos with lower priority than EmitterArc
    }
    
    /**
     * Configure display options
     */
    public configure(options: {
        showDirections?: boolean;
        showCenter?: boolean;
        directionColor?: Color;
        centerColor?: Color;
        speedScale?: number;
        arrowSize?: number;
        centerSize?: number;
    }): void {
        if (options.showDirections !== undefined) this.showDirections = options.showDirections;
        if (options.showCenter !== undefined) this.showCenter = options.showCenter;
        if (options.directionColor !== undefined) this.directionColor = options.directionColor;
        if (options.centerColor !== undefined) this.centerColor = options.centerColor;
        if (options.speedScale !== undefined) this.speedScale = options.speedScale;
        if (options.arrowSize !== undefined) this.arrowSize = options.arrowSize;
        if (options.centerSize !== undefined) this.centerSize = options.centerSize;
    }
}
