import { _decorator, Graphics, Color, Node } from 'cc';
import { GizmoDrawer } from './GizmoDrawer';
import { Emitter } from '../world/bullet/Emitter';

/**
 * Gizmo drawer for base Emitter components
 * Draws visual debugging information for basic bullet emitters
 */
export class EmitterGizmo extends GizmoDrawer<Emitter> {
    
    public readonly componentType = Emitter;
    public readonly drawerName = "EmitterGizmo";
    
    // Gizmo display options
    public showDirections: boolean = true;
    public showCenter: boolean = true;
    
    // Colors
    public directionColor: Color = Color.RED;
    public centerColor: Color = Color.WHITE;
    
    // Display settings
    public speedScale: number = 1.0;
    public arrowSize: number = 8;
    public centerSize: number = 8;
    
    public drawGizmos(emitter: Emitter, graphics: Graphics, node: Node): void {
        // Set transform to match the emitter's node
        const worldPos = node.worldPosition;
        const worldRot = node.worldRotation;
        const worldScale = node.worldScale;
        
        // Save current transform
        graphics.save();
        
        // Apply node transform
        graphics.translate(worldPos.x, worldPos.y);
        graphics.rotate(worldRot.z);
        graphics.scale(worldScale.x, worldScale.y);
        
        // Draw center point
        if (this.showCenter) {
            this.drawCenter(graphics);
        }
        
        // Draw direction arrows
        if (this.showDirections && emitter.count > 0) {
            this.drawDirections(graphics, emitter);
        }
        
        // Restore transform
        graphics.restore();
    }
    
    private drawCenter(graphics: Graphics): void {
        this.drawCross(graphics, 0, 0, this.centerSize, this.centerColor);
    }
    
    private drawDirections(graphics: Graphics, emitter: Emitter): void {
        const baseLength = 30;
        const speedFactor = emitter.speedMultiplier || 1;
        const arrowLength = Math.max(baseLength, baseLength * speedFactor * this.speedScale);
        
        for (let i = 0; i < emitter.count; i++) {
            // For basic emitters, just draw simple forward direction (up)
            const dirX = 0;
            const dirY = 1;
            
            const startX = 0;
            const startY = 0;
            const endX = dirX * arrowLength;
            const endY = dirY * arrowLength;
            
            // Draw arrow
            this.drawArrow(graphics, startX, startY, endX, endY, this.directionColor, this.arrowSize);
        }
    }
    
    public getPriority(): number {
        return 5; // Draw base emitter gizmos with lower priority than EmitterArc
    }
    
    /**
     * Configure display options
     */
    public configure(options: {
        showDirections?: boolean;
        showCenter?: boolean;
        directionColor?: Color;
        centerColor?: Color;
        speedScale?: number;
        arrowSize?: number;
        centerSize?: number;
    }): void {
        if (options.showDirections !== undefined) this.showDirections = options.showDirections;
        if (options.showCenter !== undefined) this.showCenter = options.showCenter;
        if (options.directionColor !== undefined) this.directionColor = options.directionColor;
        if (options.centerColor !== undefined) this.centerColor = options.centerColor;
        if (options.speedScale !== undefined) this.speedScale = options.speedScale;
        if (options.arrowSize !== undefined) this.arrowSize = options.arrowSize;
        if (options.centerSize !== undefined) this.centerSize = options.centerSize;
    }
}
