{"version": 3, "sources": ["file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Anim.ts"], "names": ["_decorator", "Component", "find", "PersistNode", "ccclass", "property", "<PERSON><PERSON>", "animFactory", "onLoad", "getComponent", "recycle", "recycleProduct", "node"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAiBC,MAAAA,I,OAAAA,I;;AAE7BC,MAAAA,W,iBAAAA,W;;;;;;;;;OACH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBL,U;;sBAGjBM,I,WADZF,OAAO,CAAC,MAAD,C,gBAAR,MACaE,IADb,SAC0BL,SAD1B,CACoC;AAAA;AAAA;AAAA,eAChCM,WADgC,GACL,IADK;AAAA;;AAGhCC,QAAAA,MAAM,GAAE;AACJ,eAAKD,WAAL,GAAmBL,IAAI,CAAC,aAAD,CAAJ,CAAoBO,YAApB;AAAA;AAAA,0CAA8CF,WAAjE;AACH;;AAEDG,QAAAA,OAAO,GAAG;AACN,eAAKH,WAAL,CAAiBI,cAAjB,CAAgC,KAAKC,IAArC;AACH;;AAT+B,O", "sourcesContent": ["import { _decorator, Component, Node, find } from 'cc';\nimport { AnimFactory } from './factroy/AnimFactory';\nimport { PersistNode } from './PersistNode';\nconst { ccclass, property } = _decorator;\n\n@ccclass('Anim')\nexport class Anim extends Component {\n    animFactory: AnimFactory = null;\n\n    onLoad(){\n        this.animFactory = find(\"PersistNode\").getComponent(PersistNode).animFactory;\n    }\n    \n    recycle() {\n        this.animFactory.recycleProduct(this.node);\n    }\n}\n\n"]}