{"version": 3, "sources": ["file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Background.ts"], "names": ["_decorator", "Component", "Global", "ccclass", "property", "Background", "curPos", "update", "deltaTime", "node", "getPosition", "y", "bgSpeed", "HEIGHT", "setPosition"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;;AACZC,MAAAA,M,iBAAAA,M;;;;;;;;;OACH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBJ,U;;4BAGjBK,U,WADZF,OAAO,CAAC,YAAD,C,2BAAR,MACaE,UADb,SACgCJ,SADhC,CAC0C;AAAA;AAAA;;AAAA;;AAAA,eAKtCK,MALsC,GAKvB,IALuB;AAAA;;AAOtCC,QAAAA,MAAM,CAACC,SAAD,EAAoB;AACtB,eAAKF,MAAL,GAAc,KAAKG,IAAL,CAAUC,WAAV,EAAd;AACA,eAAKJ,MAAL,CAAYK,CAAZ,IAAiB,KAAKC,OAAL,GAAeJ,SAAhC;;AAEA,cAAI,KAAKF,MAAL,CAAYK,CAAZ,GAAgB,CAAC;AAAA;AAAA,gCAAOE,MAA5B,EAAoC;AAChC,iBAAKP,MAAL,CAAYK,CAAZ,GAAgB,CAAhB;AACH;;AAED,eAAKF,IAAL,CAAUK,WAAV,CAAsB,KAAKR,MAA3B;AACH;;AAhBqC,O,0EAErCF,Q;;;;;iBACiB,E", "sourcesContent": ["import { _decorator, Component, Node, Vec3 } from 'cc';\nimport { Global } from './Global';\nconst { ccclass, property } = _decorator;\n\n@ccclass('Background')\nexport class Background extends Component {\n    \n    @property\n    bgSpeed: number = 50;\n\n    curPos: Vec3 = null;\n\n    update(deltaTime: number) {\n        this.curPos = this.node.getPosition();\n        this.curPos.y -= this.bgSpeed * deltaTime;\n\n        if (this.curPos.y < -Global.HEIGHT) {\n            this.curPos.y = 0;\n        }\n\n        this.node.setPosition(this.curPos);\n    }\n}\n\n"]}