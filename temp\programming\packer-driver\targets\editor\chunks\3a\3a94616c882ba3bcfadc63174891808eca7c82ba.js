System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, instantiate, PersistNode, GameFactory, AnimFactory, _crd, ccclass, property;

  function _reportPossibleCrUseOfPersistNode(extras) {
    _reporterNs.report("PersistNode", "../PersistNode", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameFactory(extras) {
    _reporterNs.report("GameFactory", "./GameFactory", _context.meta, extras);
  }

  _export("AnimFactory", void 0);

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      instantiate = _cc.instantiate;
    }, function (_unresolved_2) {
      PersistNode = _unresolved_2.PersistNode;
    }, function (_unresolved_3) {
      GameFactory = _unresolved_3.GameFactory;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "cdb00KhobFCxZm9ADJCP2dT", "AnimFactory", undefined);

      __checkObsolete__(['_decorator', 'Component', 'Node', 'instantiate']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("AnimFactory", AnimFactory = class AnimFactory extends (_crd && GameFactory === void 0 ? (_reportPossibleCrUseOfGameFactory({
        error: Error()
      }), GameFactory) : GameFactory) {
        createAnim() {
          let animTemp = null;

          if (this.productPool.size() > 0) {
            animTemp = this.productPool.get(); //如果池里有敌机，就直接拿来用
          } else {
            animTemp = instantiate(this.persistNode.getComponent(_crd && PersistNode === void 0 ? (_reportPossibleCrUseOfPersistNode({
              error: Error()
            }), PersistNode) : PersistNode).animPreb); //从常驻节点拿到预制体原料
          }

          return animTemp;
        }

      });

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=3a94616c882ba3bcfadc63174891808eca7c82ba.js.map