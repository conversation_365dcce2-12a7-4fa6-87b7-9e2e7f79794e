/**
 * CrazyStorm 2.0 Integration for Cocos Creator
 * Mobile Optimizer
 * 
 * This system provides mobile-specific optimizations including
 * LOD (Level of Detail), culling, and adaptive quality settings.
 */

import { Vec2, sys, log, warn } from 'cc';
import { DeviceTier, getPerformanceMonitor } from './PerformanceMonitor';
import { getPoolManager } from './ObjectPool';

/**
 * Quality settings for different performance levels
 */
export interface QualitySettings {
    maxParticles: number;
    particleScale: number;
    emissionRate: number;
    maxTrailLength: number;
    enableComplexEffects: boolean;
    enableParticleCollision: boolean;
    cullingDistance: number;
    updateFrequency: number; // Updates per second
}

/**
 * LOD (Level of Detail) configuration
 */
export interface LODConfig {
    distances: number[]; // Distance thresholds
    particleReduction: number[]; // Particle count multipliers for each LOD level
    qualityReduction: number[]; // Quality multipliers for each LOD level
}

/**
 * Culling bounds
 */
export interface CullingBounds {
    left: number;
    right: number;
    top: number;
    bottom: number;
    margin: number; // Extra margin for smooth transitions
}

/**
 * Mobile optimizer class
 */
export class MobileOptimizer {
    private static instance: MobileOptimizer;
    
    private qualitySettings: QualitySettings;
    private lodConfig: LODConfig;
    private cullingBounds: CullingBounds;
    private isEnabled: boolean = true;
    private adaptiveQuality: boolean = true;
    
    // Performance tracking
    private lastQualityAdjustment: number = 0;
    private qualityAdjustmentCooldown: number = 5000; // 5 seconds
    private performanceHistory: number[] = [];
    private maxHistorySize: number = 10;
    
    // Camera/viewport tracking
    private cameraPosition: Vec2 = new Vec2(0, 0);
    private viewportSize: Vec2 = new Vec2(1920, 1080);

    private constructor() {
        this.initializeSettings();
        this.setupCullingBounds();
        this.setupLODConfig();
    }

    /**
     * Get singleton instance
     */
    public static getInstance(): MobileOptimizer {
        if (!MobileOptimizer.instance) {
            MobileOptimizer.instance = new MobileOptimizer();
        }
        return MobileOptimizer.instance;
    }

    /**
     * Initialize optimizer
     */
    public initialize(): void {
        const deviceTier = getPerformanceMonitor().getDeviceTier();
        this.adjustSettingsForDevice(deviceTier);
        
        // Start performance monitoring if on mobile
        if (sys.isMobile) {
            this.startAdaptiveQuality();
        }
        
        log('MobileOptimizer: Initialized for device tier:', deviceTier);
    }

    /**
     * Update optimizer (call this every frame)
     */
    public update(deltaTime: number): void {
        if (!this.isEnabled) {
            return;
        }

        // Update adaptive quality
        if (this.adaptiveQuality) {
            this.updateAdaptiveQuality();
        }

        // Update culling bounds based on camera
        this.updateCullingBounds();
    }

    /**
     * Check if a position should be culled
     */
    public shouldCull(position: Vec2): boolean {
        return position.x < this.cullingBounds.left ||
               position.x > this.cullingBounds.right ||
               position.y < this.cullingBounds.top ||
               position.y > this.cullingBounds.bottom;
    }

    /**
     * Get LOD level for a distance from camera
     */
    public getLODLevel(distanceFromCamera: number): number {
        for (let i = 0; i < this.lodConfig.distances.length; i++) {
            if (distanceFromCamera <= this.lodConfig.distances[i]) {
                return i;
            }
        }
        return this.lodConfig.distances.length - 1;
    }

    /**
     * Get particle count multiplier for LOD level
     */
    public getParticleMultiplier(lodLevel: number): number {
        if (lodLevel >= 0 && lodLevel < this.lodConfig.particleReduction.length) {
            return this.lodConfig.particleReduction[lodLevel];
        }
        return 1.0;
    }

    /**
     * Get quality multiplier for LOD level
     */
    public getQualityMultiplier(lodLevel: number): number {
        if (lodLevel >= 0 && lodLevel < this.lodConfig.qualityReduction.length) {
            return this.lodConfig.qualityReduction[lodLevel];
        }
        return 1.0;
    }

    /**
     * Optimize particle count based on performance
     */
    public optimizeParticleCount(requestedCount: number, position: Vec2): number {
        // Apply culling
        if (this.shouldCull(position)) {
            return 0;
        }

        // Apply LOD
        const distance = this.getDistanceFromCamera(position);
        const lodLevel = this.getLODLevel(distance);
        const lodMultiplier = this.getParticleMultiplier(lodLevel);

        // Apply quality settings
        const qualityMultiplier = this.qualitySettings.emissionRate;

        // Calculate final count
        let optimizedCount = Math.floor(requestedCount * lodMultiplier * qualityMultiplier);

        // Enforce maximum
        optimizedCount = Math.min(optimizedCount, this.qualitySettings.maxParticles);

        return optimizedCount;
    }

    /**
     * Check if complex effects should be enabled at position
     */
    public shouldEnableComplexEffects(position: Vec2): boolean {
        if (!this.qualitySettings.enableComplexEffects) {
            return false;
        }

        // Disable complex effects for distant objects
        const distance = this.getDistanceFromCamera(position);
        const lodLevel = this.getLODLevel(distance);
        
        return lodLevel <= 1; // Only enable for close and medium distance
    }

    /**
     * Get optimized trail length
     */
    public getOptimizedTrailLength(requestedLength: number, position: Vec2): number {
        const distance = this.getDistanceFromCamera(position);
        const lodLevel = this.getLODLevel(distance);
        const qualityMultiplier = this.getQualityMultiplier(lodLevel);
        
        const optimizedLength = Math.floor(requestedLength * qualityMultiplier);
        return Math.min(optimizedLength, this.qualitySettings.maxTrailLength);
    }

    /**
     * Set camera position for culling and LOD calculations
     */
    public setCameraPosition(position: Vec2): void {
        this.cameraPosition.set(position.x, position.y);
    }

    /**
     * Set viewport size
     */
    public setViewportSize(size: Vec2): void {
        this.viewportSize.set(size.x, size.y);
        this.setupCullingBounds();
    }

    /**
     * Get current quality settings
     */
    public getQualitySettings(): QualitySettings {
        return { ...this.qualitySettings };
    }

    /**
     * Set quality settings
     */
    public setQualitySettings(settings: Partial<QualitySettings>): void {
        this.qualitySettings = { ...this.qualitySettings, ...settings };
        log('MobileOptimizer: Updated quality settings');
    }

    /**
     * Enable/disable adaptive quality
     */
    public setAdaptiveQuality(enabled: boolean): void {
        this.adaptiveQuality = enabled;
        log(`MobileOptimizer: Adaptive quality ${enabled ? 'enabled' : 'disabled'}`);
    }

    /**
     * Force quality adjustment
     */
    public adjustQuality(direction: 'up' | 'down'): void {
        const adjustment = direction === 'up' ? 1.1 : 0.9;
        
        this.qualitySettings.maxParticles = Math.floor(this.qualitySettings.maxParticles * adjustment);
        this.qualitySettings.emissionRate *= adjustment;
        this.qualitySettings.maxTrailLength = Math.floor(this.qualitySettings.maxTrailLength * adjustment);
        
        // Clamp values
        this.qualitySettings.maxParticles = Math.max(100, Math.min(5000, this.qualitySettings.maxParticles));
        this.qualitySettings.emissionRate = Math.max(0.1, Math.min(2.0, this.qualitySettings.emissionRate));
        this.qualitySettings.maxTrailLength = Math.max(5, Math.min(50, this.qualitySettings.maxTrailLength));
        
        log(`MobileOptimizer: Quality adjusted ${direction}`);
    }

    /**
     * Get optimization statistics
     */
    public getStats(): {
        isEnabled: boolean;
        adaptiveQuality: boolean;
        currentQuality: QualitySettings;
        lodConfig: LODConfig;
        cullingBounds: CullingBounds;
        performanceHistory: number[];
    } {
        return {
            isEnabled: this.isEnabled,
            adaptiveQuality: this.adaptiveQuality,
            currentQuality: this.qualitySettings,
            lodConfig: this.lodConfig,
            cullingBounds: this.cullingBounds,
            performanceHistory: [...this.performanceHistory]
        };
    }

    // Private methods

    /**
     * Initialize default settings
     */
    private initializeSettings(): void {
        this.qualitySettings = {
            maxParticles: 1000,
            particleScale: 1.0,
            emissionRate: 1.0,
            maxTrailLength: 20,
            enableComplexEffects: true,
            enableParticleCollision: true,
            cullingDistance: 2000,
            updateFrequency: 60
        };
    }

    /**
     * Setup culling bounds
     */
    private setupCullingBounds(): void {
        const margin = 200; // Extra margin for smooth transitions
        this.cullingBounds = {
            left: this.cameraPosition.x - this.viewportSize.x / 2 - margin,
            right: this.cameraPosition.x + this.viewportSize.x / 2 + margin,
            top: this.cameraPosition.y - this.viewportSize.y / 2 - margin,
            bottom: this.cameraPosition.y + this.viewportSize.y / 2 + margin,
            margin
        };
    }

    /**
     * Setup LOD configuration
     */
    private setupLODConfig(): void {
        this.lodConfig = {
            distances: [500, 1000, 2000], // Close, medium, far, very far
            particleReduction: [1.0, 0.7, 0.4, 0.1], // 100%, 70%, 40%, 10%
            qualityReduction: [1.0, 0.8, 0.5, 0.2]   // 100%, 80%, 50%, 20%
        };
    }

    /**
     * Adjust settings based on device tier
     */
    private adjustSettingsForDevice(deviceTier: DeviceTier): void {
        switch (deviceTier) {
            case DeviceTier.Low:
                this.qualitySettings.maxParticles = 500;
                this.qualitySettings.emissionRate = 0.6;
                this.qualitySettings.maxTrailLength = 10;
                this.qualitySettings.enableComplexEffects = false;
                this.qualitySettings.enableParticleCollision = false;
                this.qualitySettings.updateFrequency = 30;
                break;
                
            case DeviceTier.Medium:
                this.qualitySettings.maxParticles = 1000;
                this.qualitySettings.emissionRate = 0.8;
                this.qualitySettings.maxTrailLength = 15;
                this.qualitySettings.enableComplexEffects = true;
                this.qualitySettings.enableParticleCollision = true;
                this.qualitySettings.updateFrequency = 45;
                break;
                
            case DeviceTier.High:
                this.qualitySettings.maxParticles = 2000;
                this.qualitySettings.emissionRate = 1.0;
                this.qualitySettings.maxTrailLength = 25;
                this.qualitySettings.enableComplexEffects = true;
                this.qualitySettings.enableParticleCollision = true;
                this.qualitySettings.updateFrequency = 60;
                break;
        }
    }

    /**
     * Start adaptive quality monitoring
     */
    private startAdaptiveQuality(): void {
        // This would be called periodically to adjust quality based on performance
        log('MobileOptimizer: Started adaptive quality monitoring');
    }

    /**
     * Update adaptive quality based on performance
     */
    private updateAdaptiveQuality(): void {
        const now = Date.now();
        if (now - this.lastQualityAdjustment < this.qualityAdjustmentCooldown) {
            return;
        }

        const monitor = getPerformanceMonitor();
        const metrics = monitor.getCurrentMetrics();
        
        if (!metrics) {
            return;
        }

        // Add current FPS to history
        this.performanceHistory.push(metrics.fps);
        if (this.performanceHistory.length > this.maxHistorySize) {
            this.performanceHistory.shift();
        }

        // Calculate average FPS
        const avgFPS = this.performanceHistory.reduce((a, b) => a + b, 0) / this.performanceHistory.length;
        const thresholds = monitor.getThresholds();

        // Adjust quality based on performance
        if (avgFPS < thresholds.minFPS * 0.8) {
            this.adjustQuality('down');
            this.lastQualityAdjustment = now;
        } else if (avgFPS > thresholds.minFPS * 1.2 && this.qualitySettings.emissionRate < 1.0) {
            this.adjustQuality('up');
            this.lastQualityAdjustment = now;
        }
    }

    /**
     * Update culling bounds based on camera position
     */
    private updateCullingBounds(): void {
        this.setupCullingBounds();
    }

    /**
     * Get distance from camera to position
     */
    private getDistanceFromCamera(position: Vec2): number {
        const dx = position.x - this.cameraPosition.x;
        const dy = position.y - this.cameraPosition.y;
        return Math.sqrt(dx * dx + dy * dy);
    }
}

/**
 * Convenience function to get the global mobile optimizer
 */
export function getMobileOptimizer(): MobileOptimizer {
    return MobileOptimizer.getInstance();
}
