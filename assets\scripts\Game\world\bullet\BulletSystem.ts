import { _decorator, Vec3, Node } from "cc";
import { System } from "../base/System";
import { RegisterTypeID } from "../base/TypeID";
const { ccclass } = _decorator;

/**
 * Bullet data structure
 */
export interface BulletData {
    id: string;
    position: Vec3;
    velocity: Vec3;
    damage: number;
    lifetime: number;
    maxLifetime: number;
    bulletType: string;
    ownerId: number;
    node?: Node;
}

/**
 * Bullet configuration
 */
export interface BulletConfig {
    speed: number;
    damage: number;
    lifetime: number;
    bulletType: string;
    size: number;
}

/**
 * BulletSystem - manages all bullets in the game world
 * Handles bullet creation, movement, collision, and cleanup
 */
@ccclass("BulletSystem")
@RegisterTypeID
export class BulletSystem extends System {
    
    private _bullets: Map<string, BulletData> = new Map();
    private _bulletIdCounter: number = 0;
    private _maxBullets: number = 1000;
    private _bulletPool: BulletData[] = [];
    
    /**
     * Get the system name
     */
    public getSystemName(): string {
        return "BulletSystem";
    }
    
    /**
     * Initialize the bullet system
     */
    protected onInit(): void {
        console.log("BulletSystem: Initializing bullet system");
        
        // Pre-allocate bullet pool for performance
        this._preallocateBulletPool();
        
        console.log(`BulletSystem: Initialized with max bullets: ${this._maxBullets}`);
    }
    
    /**
     * Cleanup the bullet system
     */
    protected onUnInit(): void {
        console.log("BulletSystem: Cleaning up bullet system");
        
        // Destroy all bullets
        this._bullets.forEach(bullet => {
            this._destroyBullet(bullet);
        });
        
        this._bullets.clear();
        this._bulletPool.length = 0;
        this._bulletIdCounter = 0;
        
        console.log("BulletSystem: Cleanup complete");
    }
    
    /**
     * Update all bullets
     */
    protected onUpdate(deltaTime: number): void {
        // Update bullet positions and lifetimes
        const bulletsToRemove: string[] = [];
        
        this._bullets.forEach((bullet, id) => {
            // Update position
            bullet.position.add(Vec3.multiplyScalar(new Vec3(), bullet.velocity, deltaTime));
            
            // Update lifetime
            bullet.lifetime += deltaTime;
            
            // Check if bullet should be removed
            if (bullet.lifetime >= bullet.maxLifetime) {
                bulletsToRemove.push(id);
            }
            
            // Update visual node if exists
            if (bullet.node) {
                bullet.node.setPosition(bullet.position);
            }
        });
        
        // Remove expired bullets
        bulletsToRemove.forEach(id => {
            this.removeBullet(id);
        });
    }
    
    /**
     * Late update - handle any post-update logic
     */
    protected onLateUpdate(_deltaTime: number): void {
        // Could be used for collision detection, visual effects, etc.
    }
    
    /**
     * Create a new bullet
     * @param position Starting position
     * @param direction Direction vector (will be normalized)
     * @param config Bullet configuration
     * @param ownerId ID of the entity that created this bullet
     * @returns The bullet ID or null if creation failed
     */
    public createBullet(position: Vec3, direction: Vec3, config: BulletConfig, ownerId: number): string | null {
        if (this._bullets.size >= this._maxBullets) {
            console.warn("BulletSystem: Cannot create bullet - max bullets reached");
            return null;
        }
        
        // Get bullet from pool or create new one
        const bullet = this._getBulletFromPool();
        
        // Set bullet properties
        bullet.id = this._generateBulletId();
        bullet.position.set(position);
        bullet.velocity = Vec3.multiplyScalar(new Vec3(), direction.normalize(), config.speed);
        bullet.damage = config.damage;
        bullet.lifetime = 0;
        bullet.maxLifetime = config.lifetime;
        bullet.bulletType = config.bulletType;
        bullet.ownerId = ownerId;
        
        // Add to active bullets
        this._bullets.set(bullet.id, bullet);
        
        console.log(`BulletSystem: Created bullet ${bullet.id} for owner ${ownerId}`);
        return bullet.id;
    }
    
    /**
     * Remove a bullet by ID
     * @param bulletId The ID of the bullet to remove
     * @returns true if the bullet was removed
     */
    public removeBullet(bulletId: string): boolean {
        const bullet = this._bullets.get(bulletId);
        if (!bullet) {
            return false;
        }
        
        this._destroyBullet(bullet);
        this._bullets.delete(bulletId);
        this._returnBulletToPool(bullet);
        
        return true;
    }
    
    /**
     * Get a bullet by ID
     * @param bulletId The ID of the bullet to get
     * @returns The bullet data or null if not found
     */
    public getBullet(bulletId: string): BulletData | null {
        return this._bullets.get(bulletId) || null;
    }
    
    /**
     * Get all bullets
     * @returns Array of all active bullets
     */
    public getAllBullets(): BulletData[] {
        return Array.from(this._bullets.values());
    }
    
    /**
     * Get bullets by owner ID
     * @param ownerId The owner ID to filter by
     * @returns Array of bullets owned by the specified entity
     */
    public getBulletsByOwner(ownerId: number): BulletData[] {
        return Array.from(this._bullets.values()).filter(bullet => bullet.ownerId === ownerId);
    }
    
    /**
     * Get the number of active bullets
     * @returns The number of active bullets
     */
    public getBulletCount(): number {
        return this._bullets.size;
    }
    
    /**
     * Set the maximum number of bullets
     * @param maxBullets The new maximum number of bullets
     */
    public setMaxBullets(maxBullets: number): void {
        this._maxBullets = Math.max(1, maxBullets);
        console.log(`BulletSystem: Max bullets set to ${this._maxBullets}`);
    }
    
    /**
     * Clear all bullets
     */
    public clearAllBullets(): void {
        this._bullets.forEach(bullet => {
            this._destroyBullet(bullet);
            this._returnBulletToPool(bullet);
        });
        
        this._bullets.clear();
        console.log("BulletSystem: All bullets cleared");
    }
    
    /**
     * Pre-allocate bullet pool for performance
     */
    private _preallocateBulletPool(): void {
        const poolSize = Math.min(100, this._maxBullets);
        
        for (let i = 0; i < poolSize; i++) {
            this._bulletPool.push(this._createEmptyBullet());
        }
        
        console.log(`BulletSystem: Pre-allocated ${poolSize} bullets in pool`);
    }
    
    /**
     * Get a bullet from the pool or create a new one
     */
    private _getBulletFromPool(): BulletData {
        return this._bulletPool.pop() || this._createEmptyBullet();
    }
    
    /**
     * Return a bullet to the pool
     */
    private _returnBulletToPool(bullet: BulletData): void {
        // Reset bullet data
        bullet.id = "";
        bullet.position.set(0, 0, 0);
        bullet.velocity.set(0, 0, 0);
        bullet.damage = 0;
        bullet.lifetime = 0;
        bullet.maxLifetime = 0;
        bullet.bulletType = "";
        bullet.ownerId = 0;
        bullet.node = undefined;
        
        // Return to pool if not full
        if (this._bulletPool.length < 100) {
            this._bulletPool.push(bullet);
        }
    }
    
    /**
     * Create an empty bullet data structure
     */
    private _createEmptyBullet(): BulletData {
        return {
            id: "",
            position: new Vec3(),
            velocity: new Vec3(),
            damage: 0,
            lifetime: 0,
            maxLifetime: 0,
            bulletType: "",
            ownerId: 0,
            node: undefined
        };
    }
    
    /**
     * Generate a unique bullet ID
     */
    private _generateBulletId(): string {
        return `bullet_${++this._bulletIdCounter}`;
    }
    
    /**
     * Destroy a bullet's visual representation
     */
    private _destroyBullet(bullet: BulletData): void {
        if (bullet.node && bullet.node.isValid) {
            bullet.node.destroy();
            bullet.node = undefined;
        }
    }
}
