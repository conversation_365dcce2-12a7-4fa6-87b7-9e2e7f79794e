System.register(["__unresolved_0", "cc", "__unresolved_1"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, SystemContainer, World, _crd, WorldState;

  function _reportPossibleCrUseOfSystemContainer(extras) {
    _reporterNs.report("SystemContainer", "./SystemContainer", _context.meta, extras);
  }

  function _reportPossibleCrUseOfSystem(extras) {
    _reporterNs.report("System", "./System", _context.meta, extras);
  }

  function _reportPossibleCrUseOfIWorldInitializeData(extras) {
    _reporterNs.report("IWorldInitializeData", "../WorldInitializeData", _context.meta, extras);
  }

  _export("World", void 0);

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
    }, function (_unresolved_2) {
      SystemContainer = _unresolved_2.SystemContainer;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "73f5bfgtsRL0oB7oJDSC3wc", "World", undefined);

      /**
       * World state enumeration
       */
      _export("WorldState", WorldState = /*#__PURE__*/function (WorldState) {
        WorldState["UNINITIALIZED"] = "uninitialized";
        WorldState["INITIALIZING"] = "initializing";
        WorldState["RUNNING"] = "running";
        WorldState["PAUSED"] = "paused";
        WorldState["STOPPING"] = "stopping";
        WorldState["STOPPED"] = "stopped";
        WorldState["ERROR"] = "error";
        return WorldState;
      }({}));
      /**
       * World class - the main gameplay driver
       * Contains a SystemContainer that manages all game systems
       * Handles world initialization, update loops, and lifecycle management
       */


      _export("World", World = class World {
        /**
         * Create a new World instance
         */
        constructor() {
          this._systemContainer = void 0;
          this._initializeData = null;
          this._state = WorldState.UNINITIALIZED;
          this._timeScale = 1.0;
          this._totalTime = 0;
          this._frameCount = 0;
          // Event callbacks
          this._onStateChanged = null;
          this._onError = null;
          this._systemContainer = new (_crd && SystemContainer === void 0 ? (_reportPossibleCrUseOfSystemContainer({
            error: Error()
          }), SystemContainer) : SystemContainer)();
          console.log("World: Created new world instance");
        }
        /**
         * Initialize the world with the provided data
         * @param initData World initialization data
         * @returns Promise that resolves when initialization is complete
         */


        async initialize(initData) {
          if (this._state !== WorldState.UNINITIALIZED) {
            console.warn("World: Cannot initialize - world is not in uninitialized state");
            return false;
          } // Validate initialization data


          const validationErrors = initData.validate();

          if (validationErrors.length > 0) {
            const error = new Error(`World initialization failed: ${validationErrors.join(", ")}`);

            this._handleError(error);

            return false;
          }

          this._setState(WorldState.INITIALIZING);

          try {
            // Store initialization data
            this._initializeData = initData.clone(); // Apply world configuration

            this._timeScale = initData.physicsConfig.timeScale; // Initialize system container

            this._systemContainer.init();

            console.log(`World: Initialized successfully with mode: ${initData.modeId}, level: ${initData.levelId}`);

            this._setState(WorldState.RUNNING);

            return true;
          } catch (error) {
            this._handleError(error);

            return false;
          }
        }
        /**
         * Start the world (resume from paused state)
         */


        start() {
          if (this._state === WorldState.PAUSED) {
            this._setState(WorldState.RUNNING);

            console.log("World: Resumed from pause");
          } else if (this._state === WorldState.STOPPED) {
            this._setState(WorldState.RUNNING);

            console.log("World: Started from stopped state");
          } else {
            console.warn(`World: Cannot start from state: ${this._state}`);
          }
        }
        /**
         * Pause the world
         */


        pause() {
          if (this._state === WorldState.RUNNING) {
            this._setState(WorldState.PAUSED);

            console.log("World: Paused");
          } else {
            console.warn(`World: Cannot pause from state: ${this._state}`);
          }
        }
        /**
         * Stop the world
         */


        stop() {
          if (this._state === WorldState.RUNNING || this._state === WorldState.PAUSED) {
            this._setState(WorldState.STOPPING);

            try {
              // Cleanup systems
              this._systemContainer.unInit();

              this._setState(WorldState.STOPPED);

              console.log("World: Stopped");
            } catch (error) {
              this._handleError(error);
            }
          } else {
            console.warn(`World: Cannot stop from state: ${this._state}`);
          }
        }
        /**
         * Destroy the world and cleanup all resources
         */


        destroy() {
          this.stop(); // Clear references

          this._initializeData = null;
          this._onStateChanged = null;
          this._onError = null;

          this._setState(WorldState.UNINITIALIZED);

          console.log("World: Destroyed");
        }
        /**
         * Update the world
         * @param deltaTime Time elapsed since last frame in seconds
         */


        update(deltaTime) {
          if (this._state !== WorldState.RUNNING) {
            return;
          }

          try {
            // Apply time scale
            const scaledDeltaTime = deltaTime * this._timeScale; // Update total time and frame count

            this._totalTime += scaledDeltaTime;
            this._frameCount++; // Update all systems

            this._systemContainer.update(scaledDeltaTime);
          } catch (error) {
            this._handleError(error);
          }
        }
        /**
         * Late update the world
         * @param deltaTime Time elapsed since last frame in seconds
         */


        lateUpdate(deltaTime) {
          if (this._state !== WorldState.RUNNING) {
            return;
          }

          try {
            // Apply time scale
            const scaledDeltaTime = deltaTime * this._timeScale; // Late update all systems

            this._systemContainer.lateUpdate(scaledDeltaTime);
          } catch (error) {
            this._handleError(error);
          }
        }
        /**
         * Register a system to the world
         * @param system The system to register
         * @returns true if registration was successful
         */


        registerSystem(system) {
          return this._systemContainer.registerSystem(system);
        }
        /**
         * Unregister a system from the world
         * @param systemConstructor The constructor of the system to unregister
         * @returns true if unregistration was successful
         */


        unregisterSystem(systemConstructor) {
          return this._systemContainer.unregisterSystem(systemConstructor);
        }
        /**
         * Get a system by type
         * @param systemConstructor The constructor of the system to get
         * @returns The system instance or null if not found
         */


        getSystem(systemConstructor) {
          return this._systemContainer.getSystem(systemConstructor);
        }
        /**
         * Check if a system is registered
         * @param systemConstructor The constructor of the system to check
         * @returns true if the system is registered
         */


        hasSystem(systemConstructor) {
          return this._systemContainer.hasSystem(systemConstructor);
        }
        /**
         * Enable or disable a specific system
         * @param systemConstructor The constructor of the system to enable/disable
         * @param enabled Whether to enable or disable the system
         * @returns true if the operation was successful
         */


        setSystemEnabled(systemConstructor, enabled) {
          return this._systemContainer.setSystemEnabled(systemConstructor, enabled);
        }
        /**
         * Get the current world state
         */


        getState() {
          return this._state;
        }
        /**
         * Get the world initialization data
         */


        getInitializeData() {
          return this._initializeData ? this._initializeData.clone() : null;
        }
        /**
         * Get the current time scale
         */


        getTimeScale() {
          return this._timeScale;
        }
        /**
         * Set the time scale for the world
         * @param timeScale The new time scale (1.0 = normal speed)
         */


        setTimeScale(timeScale) {
          if (timeScale < 0) {
            console.warn("World: Time scale cannot be negative");
            return;
          }

          this._timeScale = timeScale;
          console.log(`World: Time scale set to ${timeScale}`);
        }
        /**
         * Get the total time the world has been running
         */


        getTotalTime() {
          return this._totalTime;
        }
        /**
         * Get the current frame count
         */


        getFrameCount() {
          return this._frameCount;
        }
        /**
         * Set the state changed callback
         * @param callback Function to call when state changes
         */


        setOnStateChanged(callback) {
          this._onStateChanged = callback;
        }
        /**
         * Set the error callback
         * @param callback Function to call when an error occurs
         */


        setOnError(callback) {
          this._onError = callback;
        }
        /**
         * Internal method to change the world state
         * @param newState The new state to set
         */


        _setState(newState) {
          if (this._state === newState) {
            return;
          }

          const oldState = this._state;
          this._state = newState;
          console.log(`World: State changed from ${oldState} to ${newState}`);

          if (this._onStateChanged) {
            try {
              this._onStateChanged(oldState, newState);
            } catch (error) {
              console.error("World: Error in state change callback:", error);
            }
          }
        }
        /**
         * Internal method to handle errors
         * @param error The error that occurred
         */


        _handleError(error) {
          console.error("World: Error occurred:", error);

          this._setState(WorldState.ERROR);

          if (this._onError) {
            try {
              this._onError(error);
            } catch (callbackError) {
              console.error("World: Error in error callback:", callbackError);
            }
          }
        }

      });

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=442921681ed59bd4dd2c9878b5e921e3006a229d.js.map