{"version": 3, "sources": ["file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/level/LevelSystem.ts"], "names": ["_decorator", "System", "RegisterTypeID", "ccclass", "LevelEventType", "LevelSystem", "_currentLevel", "_levelStartTime", "_levelElapsedTime", "_isLevelActive", "_isLevelCompleted", "_isLevelFailed", "_eventHistory", "_eventCallbacks", "Map", "getSystemName", "onInit", "console", "log", "Object", "values", "for<PERSON>ach", "eventType", "set", "onUnInit", "length", "clear", "onUpdate", "deltaTime", "timeLimit", "failLevel", "_areAllObjectivesCompleted", "completeLevel", "onLateUpdate", "loadLevel", "levelConfig", "warn", "levelId", "_cloneLevelConfig", "Date", "now", "checkpoints", "checkpoint", "isReached", "timestamp", "undefined", "objectives", "objective", "currentValue", "isCompleted", "_emitEvent", "LEVEL_START", "startTime", "LEVEL_COMPLETE", "completionTime", "objectivesCompleted", "filter", "obj", "totalObjectives", "reason", "LEVEL_FAILED", "elapsedTime", "reachCheckpoint", "checkpointId", "find", "cp", "id", "CHECKPOINT_REACHED", "position", "updateObjective", "objectiveId", "value", "oldValue", "Math", "max", "targetValue", "addEventListener", "callback", "callbacks", "get", "push", "removeEventListener", "index", "indexOf", "splice", "getCurrentLevel", "getLevelElapsedTime", "isLevelActive", "isLevelCompleted", "isLevelFailed", "getEventHistory", "every", "type", "data", "event", "error", "config", "JSON", "parse", "stringify"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;;AACAC,MAAAA,M,iBAAAA,M;;AACAC,MAAAA,c,iBAAAA,c;;;;;;;;;OACH;AAAEC,QAAAA;AAAF,O,GAAcH,U;AAEpB;AACA;AACA;;gCACYI,c,0BAAAA,c;AAAAA,QAAAA,c;AAAAA,QAAAA,c;AAAAA,QAAAA,c;AAAAA,QAAAA,c;AAAAA,QAAAA,c;AAAAA,QAAAA,c;eAAAA,c;;AASZ;AACA;AACA;;AAOA;AACA;AACA;;AAQA;AACA;AACA;;AAUA;AACA;AACA;;;AAiBA;AACA;AACA;6BAGaC,W,WAFZF,OAAO,CAAC,aAAD,C;;yEAAR,MAEaE,WAFb;AAAA;AAAA,4BAEwC;AAAA;AAAA;AAAA,eAE5BC,aAF4B,GAEQ,IAFR;AAAA,eAG5BC,eAH4B,GAGF,CAHE;AAAA,eAI5BC,iBAJ4B,GAIA,CAJA;AAAA,eAK5BC,cAL4B,GAKF,KALE;AAAA,eAM5BC,iBAN4B,GAMC,KAND;AAAA,eAO5BC,cAP4B,GAOF,KAPE;AASpC;AAToC,eAU5BC,aAV4B,GAUE,EAVF;AAAA,eAW5BC,eAX4B,GAW4C,IAAIC,GAAJ,EAX5C;AAAA;;AAapC;AACJ;AACA;AACWC,QAAAA,aAAa,GAAW;AAC3B,iBAAO,aAAP;AACH;AAED;AACJ;AACA;;;AACcC,QAAAA,MAAM,GAAS;AACrBC,UAAAA,OAAO,CAACC,GAAR,CAAY,wCAAZ,EADqB,CAGrB;;AACAC,UAAAA,MAAM,CAACC,MAAP,CAAchB,cAAd,EAA8BiB,OAA9B,CAAsCC,SAAS,IAAI;AAC/C,iBAAKT,eAAL,CAAqBU,GAArB,CAAyBD,SAAzB,EAAsD,EAAtD;AACH,WAFD;AAIAL,UAAAA,OAAO,CAACC,GAAR,CAAY,0BAAZ;AACH;AAED;AACJ;AACA;;;AACcM,QAAAA,QAAQ,GAAS;AACvBP,UAAAA,OAAO,CAACC,GAAR,CAAY,uCAAZ;AAEA,eAAKZ,aAAL,GAAqB,IAArB;AACA,eAAKC,eAAL,GAAuB,CAAvB;AACA,eAAKC,iBAAL,GAAyB,CAAzB;AACA,eAAKC,cAAL,GAAsB,KAAtB;AACA,eAAKC,iBAAL,GAAyB,KAAzB;AACA,eAAKC,cAAL,GAAsB,KAAtB;AACA,eAAKC,aAAL,CAAmBa,MAAnB,GAA4B,CAA5B;;AACA,eAAKZ,eAAL,CAAqBa,KAArB;;AAEAT,UAAAA,OAAO,CAACC,GAAR,CAAY,+BAAZ;AACH;AAED;AACJ;AACA;;;AACcS,QAAAA,QAAQ,CAACC,SAAD,EAA0B;AACxC,cAAI,CAAC,KAAKnB,cAAN,IAAwB,CAAC,KAAKH,aAAlC,EAAiD;AAC7C;AACH,WAHuC,CAKxC;;;AACA,eAAKE,iBAAL,IAA0BoB,SAA1B,CANwC,CAQxC;;AACA,cAAI,KAAKtB,aAAL,CAAmBuB,SAAnB,GAA+B,CAA/B,IAAoC,KAAKrB,iBAAL,IAA0B,KAAKF,aAAL,CAAmBuB,SAArF,EAAgG;AAC5F,iBAAKC,SAAL,CAAe,qBAAf;AACA;AACH,WAZuC,CAcxC;;;AACA,cAAI,KAAKC,0BAAL,EAAJ,EAAuC;AACnC,iBAAKC,aAAL;AACH;AACJ;AAED;AACJ;AACA;;;AACcC,QAAAA,YAAY,CAACL,SAAD,EAA0B,CAC5C;AACH;AAED;AACJ;AACA;AACA;AACA;;;AACWM,QAAAA,SAAS,CAACC,WAAD,EAAoC;AAChD,cAAI,KAAK1B,cAAT,EAAyB;AACrBQ,YAAAA,OAAO,CAACmB,IAAR,CAAa,kEAAb;AACA,mBAAO,KAAP;AACH;;AAEDnB,UAAAA,OAAO,CAACC,GAAR,CAAa,8BAA6BiB,WAAW,CAACE,OAAQ,EAA9D,EANgD,CAQhD;;AACA,eAAK/B,aAAL,GAAqB,KAAKgC,iBAAL,CAAuBH,WAAvB,CAArB,CATgD,CAWhD;;AACA,eAAK5B,eAAL,GAAuBgC,IAAI,CAACC,GAAL,EAAvB;AACA,eAAKhC,iBAAL,GAAyB,CAAzB;AACA,eAAKC,cAAL,GAAsB,IAAtB;AACA,eAAKC,iBAAL,GAAyB,KAAzB;AACA,eAAKC,cAAL,GAAsB,KAAtB;AACA,eAAKC,aAAL,CAAmBa,MAAnB,GAA4B,CAA5B,CAjBgD,CAmBhD;;AACA,eAAKnB,aAAL,CAAmBmC,WAAnB,CAA+BpB,OAA/B,CAAuCqB,UAAU,IAAI;AACjDA,YAAAA,UAAU,CAACC,SAAX,GAAuB,KAAvB;AACAD,YAAAA,UAAU,CAACE,SAAX,GAAuBC,SAAvB;AACH,WAHD,EApBgD,CAyBhD;;;AACA,eAAKvC,aAAL,CAAmBwC,UAAnB,CAA8BzB,OAA9B,CAAsC0B,SAAS,IAAI;AAC/CA,YAAAA,SAAS,CAACC,YAAV,GAAyB,CAAzB;AACAD,YAAAA,SAAS,CAACE,WAAV,GAAwB,KAAxB;AACH,WAHD,EA1BgD,CA+BhD;;;AACA,eAAKC,UAAL,CAAgB9C,cAAc,CAAC+C,WAA/B,EAA4C;AACxCd,YAAAA,OAAO,EAAEF,WAAW,CAACE,OADmB;AAExCe,YAAAA,SAAS,EAAE,KAAK7C;AAFwB,WAA5C;;AAKAU,UAAAA,OAAO,CAACC,GAAR,CAAa,sBAAqBiB,WAAW,CAACE,OAAQ,qBAAtD;AACA,iBAAO,IAAP;AACH;AAED;AACJ;AACA;;;AACWL,QAAAA,aAAa,GAAS;AAAA;;AACzB,cAAI,CAAC,KAAKvB,cAAN,IAAwB,KAAKC,iBAA7B,IAAkD,KAAKC,cAA3D,EAA2E;AACvE;AACH;;AAED,eAAKD,iBAAL,GAAyB,IAAzB;AACA,eAAKD,cAAL,GAAsB,KAAtB;AAEAQ,UAAAA,OAAO,CAACC,GAAR,CAAa,sBAAD,uBAAsB,KAAKZ,aAA3B,qBAAsB,oBAAoB+B,OAAQ,YAA9D;;AAEA,eAAKa,UAAL,CAAgB9C,cAAc,CAACiD,cAA/B,EAA+C;AAC3ChB,YAAAA,OAAO,0BAAE,KAAK/B,aAAP,qBAAE,qBAAoB+B,OADc;AAE3CiB,YAAAA,cAAc,EAAE,KAAK9C,iBAFsB;AAG3C+C,YAAAA,mBAAmB,0BAAE,KAAKjD,aAAP,qBAAE,qBAAoBwC,UAApB,CAA+BU,MAA/B,CAAsCC,GAAG,IAAIA,GAAG,CAACR,WAAjD,EAA8DxB,MAHxC;AAI3CiC,YAAAA,eAAe,0BAAE,KAAKpD,aAAP,qBAAE,qBAAoBwC,UAApB,CAA+BrB;AAJL,WAA/C;AAMH;AAED;AACJ;AACA;AACA;;;AACWK,QAAAA,SAAS,CAAC6B,MAAD,EAAuB;AAAA;;AACnC,cAAI,CAAC,KAAKlD,cAAN,IAAwB,KAAKC,iBAA7B,IAAkD,KAAKC,cAA3D,EAA2E;AACvE;AACH;;AAED,eAAKA,cAAL,GAAsB,IAAtB;AACA,eAAKF,cAAL,GAAsB,KAAtB;AAEAQ,UAAAA,OAAO,CAACC,GAAR,CAAa,sBAAD,wBAAsB,KAAKZ,aAA3B,qBAAsB,qBAAoB+B,OAAQ,YAAWsB,MAAO,EAAhF;;AAEA,eAAKT,UAAL,CAAgB9C,cAAc,CAACwD,YAA/B,EAA6C;AACzCvB,YAAAA,OAAO,0BAAE,KAAK/B,aAAP,qBAAE,qBAAoB+B,OADY;AAEzCsB,YAAAA,MAAM,EAAEA,MAFiC;AAGzCE,YAAAA,WAAW,EAAE,KAAKrD;AAHuB,WAA7C;AAKH;AAED;AACJ;AACA;AACA;AACA;;;AACWsD,QAAAA,eAAe,CAACC,YAAD,EAAgC;AAClD,cAAI,CAAC,KAAKzD,aAAV,EAAyB;AACrB,mBAAO,KAAP;AACH;;AAED,gBAAMoC,UAAU,GAAG,KAAKpC,aAAL,CAAmBmC,WAAnB,CAA+BuB,IAA/B,CAAoCC,EAAE,IAAIA,EAAE,CAACC,EAAH,KAAUH,YAApD,CAAnB;;AACA,cAAI,CAACrB,UAAD,IAAeA,UAAU,CAACC,SAA9B,EAAyC;AACrC,mBAAO,KAAP;AACH;;AAEDD,UAAAA,UAAU,CAACC,SAAX,GAAuB,IAAvB;AACAD,UAAAA,UAAU,CAACE,SAAX,GAAuB,KAAKpC,iBAA5B;AAEAS,UAAAA,OAAO,CAACC,GAAR,CAAa,2BAA0B6C,YAAa,UAApD;;AAEA,eAAKb,UAAL,CAAgB9C,cAAc,CAAC+D,kBAA/B,EAAmD;AAC/CJ,YAAAA,YAAY,EAAEA,YADiC;AAE/CK,YAAAA,QAAQ,EAAE1B,UAAU,CAAC0B,QAF0B;AAG/CxB,YAAAA,SAAS,EAAEF,UAAU,CAACE;AAHyB,WAAnD;;AAMA,iBAAO,IAAP;AACH;AAED;AACJ;AACA;AACA;AACA;AACA;;;AACWyB,QAAAA,eAAe,CAACC,WAAD,EAAsBC,KAAtB,EAA8C;AAChE,cAAI,CAAC,KAAKjE,aAAV,EAAyB;AACrB,mBAAO,KAAP;AACH;;AAED,gBAAMyC,SAAS,GAAG,KAAKzC,aAAL,CAAmBwC,UAAnB,CAA8BkB,IAA9B,CAAmCP,GAAG,IAAIA,GAAG,CAACS,EAAJ,KAAWI,WAArD,CAAlB;;AACA,cAAI,CAACvB,SAAL,EAAgB;AACZ,mBAAO,KAAP;AACH;;AAED,gBAAMyB,QAAQ,GAAGzB,SAAS,CAACC,YAA3B;AACAD,UAAAA,SAAS,CAACC,YAAV,GAAyByB,IAAI,CAACC,GAAL,CAAS,CAAT,EAAYH,KAAZ,CAAzB,CAXgE,CAahE;;AACA,cAAI,CAACxB,SAAS,CAACE,WAAX,IAA0BF,SAAS,CAACC,YAAV,IAA0BD,SAAS,CAAC4B,WAAlE,EAA+E;AAC3E5B,YAAAA,SAAS,CAACE,WAAV,GAAwB,IAAxB;AACAhC,YAAAA,OAAO,CAACC,GAAR,CAAa,0BAAyBoD,WAAY,YAAlD;AACH;;AAED,iBAAOE,QAAQ,KAAKzB,SAAS,CAACC,YAA9B;AACH;AAED;AACJ;AACA;AACA;AACA;;;AACW4B,QAAAA,gBAAgB,CAACtD,SAAD,EAA4BuD,QAA5B,EAAyE;AAC5F,gBAAMC,SAAS,GAAG,KAAKjE,eAAL,CAAqBkE,GAArB,CAAyBzD,SAAzB,CAAlB;;AACA,cAAIwD,SAAJ,EAAe;AACXA,YAAAA,SAAS,CAACE,IAAV,CAAeH,QAAf;AACH;AACJ;AAED;AACJ;AACA;AACA;AACA;;;AACWI,QAAAA,mBAAmB,CAAC3D,SAAD,EAA4BuD,QAA5B,EAAyE;AAC/F,gBAAMC,SAAS,GAAG,KAAKjE,eAAL,CAAqBkE,GAArB,CAAyBzD,SAAzB,CAAlB;;AACA,cAAIwD,SAAJ,EAAe;AACX,kBAAMI,KAAK,GAAGJ,SAAS,CAACK,OAAV,CAAkBN,QAAlB,CAAd;;AACA,gBAAIK,KAAK,IAAI,CAAb,EAAgB;AACZJ,cAAAA,SAAS,CAACM,MAAV,CAAiBF,KAAjB,EAAwB,CAAxB;AACH;AACJ;AACJ;AAED;AACJ;AACA;;;AACWG,QAAAA,eAAe,GAAuB;AACzC,iBAAO,KAAK/E,aAAL,GAAqB,KAAKgC,iBAAL,CAAuB,KAAKhC,aAA5B,CAArB,GAAkE,IAAzE;AACH;AAED;AACJ;AACA;;;AACWgF,QAAAA,mBAAmB,GAAW;AACjC,iBAAO,KAAK9E,iBAAZ;AACH;AAED;AACJ;AACA;;;AACW+E,QAAAA,aAAa,GAAY;AAC5B,iBAAO,KAAK9E,cAAZ;AACH;AAED;AACJ;AACA;;;AACW+E,QAAAA,gBAAgB,GAAY;AAC/B,iBAAO,KAAK9E,iBAAZ;AACH;AAED;AACJ;AACA;;;AACW+E,QAAAA,aAAa,GAAY;AAC5B,iBAAO,KAAK9E,cAAZ;AACH;AAED;AACJ;AACA;;;AACW+E,QAAAA,eAAe,GAAiB;AACnC,iBAAO,CAAC,GAAG,KAAK9E,aAAT,CAAP;AACH;AAED;AACJ;AACA;;;AACYmB,QAAAA,0BAA0B,GAAY;AAC1C,cAAI,CAAC,KAAKzB,aAAV,EAAyB;AACrB,mBAAO,KAAP;AACH;;AAED,iBAAO,KAAKA,aAAL,CAAmBwC,UAAnB,CAA8B6C,KAA9B,CAAoC5C,SAAS,IAAIA,SAAS,CAACE,WAA3D,CAAP;AACH;AAED;AACJ;AACA;;;AACYC,QAAAA,UAAU,CAAC0C,IAAD,EAAuBC,IAAvB,EAAwC;AACtD,gBAAMC,KAAiB,GAAG;AACtBF,YAAAA,IAAI,EAAEA,IADgB;AAEtBhD,YAAAA,SAAS,EAAE,KAAKpC,iBAFM;AAGtBqF,YAAAA,IAAI,EAAEA;AAHgB,WAA1B;;AAMA,eAAKjF,aAAL,CAAmBoE,IAAnB,CAAwBc,KAAxB,EAPsD,CAStD;;;AACA,gBAAMhB,SAAS,GAAG,KAAKjE,eAAL,CAAqBkE,GAArB,CAAyBa,IAAzB,CAAlB;;AACA,cAAId,SAAJ,EAAe;AACXA,YAAAA,SAAS,CAACzD,OAAV,CAAkBwD,QAAQ,IAAI;AAC1B,kBAAI;AACAA,gBAAAA,QAAQ,CAACiB,KAAD,CAAR;AACH,eAFD,CAEE,OAAOC,KAAP,EAAc;AACZ9E,gBAAAA,OAAO,CAAC8E,KAAR,CAAe,4CAA2CH,IAAK,GAA/D,EAAmEG,KAAnE;AACH;AACJ,aAND;AAOH;AACJ;AAED;AACJ;AACA;;;AACYzD,QAAAA,iBAAiB,CAAC0D,MAAD,EAAmC;AACxD,iBAAOC,IAAI,CAACC,KAAL,CAAWD,IAAI,CAACE,SAAL,CAAeH,MAAf,CAAX,CAAP;AACH;;AAjVmC,O", "sourcesContent": ["import { _decorator, Node, Vec3 } from \"cc\";\nimport { System } from \"../base/System\";\nimport { RegisterTypeID } from \"../base/TypeID\";\nconst { ccclass } = _decorator;\n\n/**\n * Level event types\n */\nexport enum LevelEventType {\n    LEVEL_START = \"level_start\",\n    LEVEL_COMPLETE = \"level_complete\",\n    LEVEL_FAILED = \"level_failed\",\n    CHECKPOINT_REACHED = \"checkpoint_reached\",\n    ENEMY_SPAWNED = \"enemy_spawned\",\n    ITEM_COLLECTED = \"item_collected\"\n}\n\n/**\n * Level event data\n */\nexport interface LevelEvent {\n    type: LevelEventType;\n    timestamp: number;\n    data: any;\n}\n\n/**\n * Checkpoint data\n */\nexport interface Checkpoint {\n    id: string;\n    position: Vec3;\n    isReached: boolean;\n    timestamp?: number;\n}\n\n/**\n * Level objective\n */\nexport interface LevelObjective {\n    id: string;\n    description: string;\n    type: string;\n    targetValue: number;\n    currentValue: number;\n    isCompleted: boolean;\n}\n\n/**\n * Level configuration\n */\nexport interface LevelConfig {\n    levelId: string;\n    name: string;\n    description: string;\n    timeLimit: number; // in seconds, 0 = no limit\n    objectives: LevelObjective[];\n    checkpoints: Checkpoint[];\n    spawnPoints: Vec3[];\n    boundaries: {\n        minX: number;\n        maxX: number;\n        minY: number;\n        maxY: number;\n    };\n}\n\n/**\n * LevelSystem - manages level state, objectives, checkpoints, and events\n */\n@ccclass(\"LevelSystem\")\n@RegisterTypeID\nexport class LevelSystem extends System {\n    \n    private _currentLevel: LevelConfig | null = null;\n    private _levelStartTime: number = 0;\n    private _levelElapsedTime: number = 0;\n    private _isLevelActive: boolean = false;\n    private _isLevelCompleted: boolean = false;\n    private _isLevelFailed: boolean = false;\n    \n    // Event system\n    private _eventHistory: LevelEvent[] = [];\n    private _eventCallbacks: Map<LevelEventType, ((event: LevelEvent) => void)[]> = new Map();\n    \n    /**\n     * Get the system name\n     */\n    public getSystemName(): string {\n        return \"LevelSystem\";\n    }\n    \n    /**\n     * Initialize the level system\n     */\n    protected onInit(): void {\n        console.log(\"LevelSystem: Initializing level system\");\n        \n        // Initialize event callback maps\n        Object.values(LevelEventType).forEach(eventType => {\n            this._eventCallbacks.set(eventType as LevelEventType, []);\n        });\n        \n        console.log(\"LevelSystem: Initialized\");\n    }\n    \n    /**\n     * Cleanup the level system\n     */\n    protected onUnInit(): void {\n        console.log(\"LevelSystem: Cleaning up level system\");\n        \n        this._currentLevel = null;\n        this._levelStartTime = 0;\n        this._levelElapsedTime = 0;\n        this._isLevelActive = false;\n        this._isLevelCompleted = false;\n        this._isLevelFailed = false;\n        this._eventHistory.length = 0;\n        this._eventCallbacks.clear();\n        \n        console.log(\"LevelSystem: Cleanup complete\");\n    }\n    \n    /**\n     * Update the level system\n     */\n    protected onUpdate(deltaTime: number): void {\n        if (!this._isLevelActive || !this._currentLevel) {\n            return;\n        }\n        \n        // Update elapsed time\n        this._levelElapsedTime += deltaTime;\n        \n        // Check time limit\n        if (this._currentLevel.timeLimit > 0 && this._levelElapsedTime >= this._currentLevel.timeLimit) {\n            this.failLevel(\"Time limit exceeded\");\n            return;\n        }\n        \n        // Check if all objectives are completed\n        if (this._areAllObjectivesCompleted()) {\n            this.completeLevel();\n        }\n    }\n    \n    /**\n     * Late update - handle any post-update logic\n     */\n    protected onLateUpdate(deltaTime: number): void {\n        // Could be used for UI updates, statistics, etc.\n    }\n    \n    /**\n     * Load and start a level\n     * @param levelConfig The level configuration to load\n     * @returns true if the level was loaded successfully\n     */\n    public loadLevel(levelConfig: LevelConfig): boolean {\n        if (this._isLevelActive) {\n            console.warn(\"LevelSystem: Cannot load level - another level is already active\");\n            return false;\n        }\n        \n        console.log(`LevelSystem: Loading level ${levelConfig.levelId}`);\n        \n        // Set current level\n        this._currentLevel = this._cloneLevelConfig(levelConfig);\n        \n        // Reset state\n        this._levelStartTime = Date.now();\n        this._levelElapsedTime = 0;\n        this._isLevelActive = true;\n        this._isLevelCompleted = false;\n        this._isLevelFailed = false;\n        this._eventHistory.length = 0;\n        \n        // Reset checkpoints\n        this._currentLevel.checkpoints.forEach(checkpoint => {\n            checkpoint.isReached = false;\n            checkpoint.timestamp = undefined;\n        });\n        \n        // Reset objectives\n        this._currentLevel.objectives.forEach(objective => {\n            objective.currentValue = 0;\n            objective.isCompleted = false;\n        });\n        \n        // Emit level start event\n        this._emitEvent(LevelEventType.LEVEL_START, {\n            levelId: levelConfig.levelId,\n            startTime: this._levelStartTime\n        });\n        \n        console.log(`LevelSystem: Level ${levelConfig.levelId} loaded and started`);\n        return true;\n    }\n    \n    /**\n     * Complete the current level\n     */\n    public completeLevel(): void {\n        if (!this._isLevelActive || this._isLevelCompleted || this._isLevelFailed) {\n            return;\n        }\n        \n        this._isLevelCompleted = true;\n        this._isLevelActive = false;\n        \n        console.log(`LevelSystem: Level ${this._currentLevel?.levelId} completed`);\n        \n        this._emitEvent(LevelEventType.LEVEL_COMPLETE, {\n            levelId: this._currentLevel?.levelId,\n            completionTime: this._levelElapsedTime,\n            objectivesCompleted: this._currentLevel?.objectives.filter(obj => obj.isCompleted).length,\n            totalObjectives: this._currentLevel?.objectives.length\n        });\n    }\n    \n    /**\n     * Fail the current level\n     * @param reason The reason for failure\n     */\n    public failLevel(reason: string): void {\n        if (!this._isLevelActive || this._isLevelCompleted || this._isLevelFailed) {\n            return;\n        }\n        \n        this._isLevelFailed = true;\n        this._isLevelActive = false;\n        \n        console.log(`LevelSystem: Level ${this._currentLevel?.levelId} failed: ${reason}`);\n        \n        this._emitEvent(LevelEventType.LEVEL_FAILED, {\n            levelId: this._currentLevel?.levelId,\n            reason: reason,\n            elapsedTime: this._levelElapsedTime\n        });\n    }\n    \n    /**\n     * Reach a checkpoint\n     * @param checkpointId The ID of the checkpoint to reach\n     * @returns true if the checkpoint was reached successfully\n     */\n    public reachCheckpoint(checkpointId: string): boolean {\n        if (!this._currentLevel) {\n            return false;\n        }\n        \n        const checkpoint = this._currentLevel.checkpoints.find(cp => cp.id === checkpointId);\n        if (!checkpoint || checkpoint.isReached) {\n            return false;\n        }\n        \n        checkpoint.isReached = true;\n        checkpoint.timestamp = this._levelElapsedTime;\n        \n        console.log(`LevelSystem: Checkpoint ${checkpointId} reached`);\n        \n        this._emitEvent(LevelEventType.CHECKPOINT_REACHED, {\n            checkpointId: checkpointId,\n            position: checkpoint.position,\n            timestamp: checkpoint.timestamp\n        });\n        \n        return true;\n    }\n    \n    /**\n     * Update an objective's progress\n     * @param objectiveId The ID of the objective to update\n     * @param value The new value for the objective\n     * @returns true if the objective was updated successfully\n     */\n    public updateObjective(objectiveId: string, value: number): boolean {\n        if (!this._currentLevel) {\n            return false;\n        }\n        \n        const objective = this._currentLevel.objectives.find(obj => obj.id === objectiveId);\n        if (!objective) {\n            return false;\n        }\n        \n        const oldValue = objective.currentValue;\n        objective.currentValue = Math.max(0, value);\n        \n        // Check if objective is now completed\n        if (!objective.isCompleted && objective.currentValue >= objective.targetValue) {\n            objective.isCompleted = true;\n            console.log(`LevelSystem: Objective ${objectiveId} completed`);\n        }\n        \n        return oldValue !== objective.currentValue;\n    }\n    \n    /**\n     * Add event listener for level events\n     * @param eventType The type of event to listen for\n     * @param callback The callback function to call when the event occurs\n     */\n    public addEventListener(eventType: LevelEventType, callback: (event: LevelEvent) => void): void {\n        const callbacks = this._eventCallbacks.get(eventType);\n        if (callbacks) {\n            callbacks.push(callback);\n        }\n    }\n    \n    /**\n     * Remove event listener\n     * @param eventType The type of event to stop listening for\n     * @param callback The callback function to remove\n     */\n    public removeEventListener(eventType: LevelEventType, callback: (event: LevelEvent) => void): void {\n        const callbacks = this._eventCallbacks.get(eventType);\n        if (callbacks) {\n            const index = callbacks.indexOf(callback);\n            if (index >= 0) {\n                callbacks.splice(index, 1);\n            }\n        }\n    }\n    \n    /**\n     * Get the current level configuration\n     */\n    public getCurrentLevel(): LevelConfig | null {\n        return this._currentLevel ? this._cloneLevelConfig(this._currentLevel) : null;\n    }\n    \n    /**\n     * Get level elapsed time\n     */\n    public getLevelElapsedTime(): number {\n        return this._levelElapsedTime;\n    }\n    \n    /**\n     * Check if level is active\n     */\n    public isLevelActive(): boolean {\n        return this._isLevelActive;\n    }\n    \n    /**\n     * Check if level is completed\n     */\n    public isLevelCompleted(): boolean {\n        return this._isLevelCompleted;\n    }\n    \n    /**\n     * Check if level is failed\n     */\n    public isLevelFailed(): boolean {\n        return this._isLevelFailed;\n    }\n    \n    /**\n     * Get event history\n     */\n    public getEventHistory(): LevelEvent[] {\n        return [...this._eventHistory];\n    }\n    \n    /**\n     * Check if all objectives are completed\n     */\n    private _areAllObjectivesCompleted(): boolean {\n        if (!this._currentLevel) {\n            return false;\n        }\n        \n        return this._currentLevel.objectives.every(objective => objective.isCompleted);\n    }\n    \n    /**\n     * Emit a level event\n     */\n    private _emitEvent(type: LevelEventType, data: any): void {\n        const event: LevelEvent = {\n            type: type,\n            timestamp: this._levelElapsedTime,\n            data: data\n        };\n        \n        this._eventHistory.push(event);\n        \n        // Call event callbacks\n        const callbacks = this._eventCallbacks.get(type);\n        if (callbacks) {\n            callbacks.forEach(callback => {\n                try {\n                    callback(event);\n                } catch (error) {\n                    console.error(`LevelSystem: Error in event callback for ${type}:`, error);\n                }\n            });\n        }\n    }\n    \n    /**\n     * Clone level configuration to prevent external modifications\n     */\n    private _cloneLevelConfig(config: LevelConfig): LevelConfig {\n        return JSON.parse(JSON.stringify(config));\n    }\n}\n"]}