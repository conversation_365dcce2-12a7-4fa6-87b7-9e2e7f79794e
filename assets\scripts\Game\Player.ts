import { _decorator, Component, Node, EventTouch, Vec2, Vec3, v3, UITransform, find, AudioSource } from 'cc';
import { GameFactory } from './factroy/GameFactory';
import { Global } from './Global';
import { PersistNode } from './PersistNode';
const { ccclass, property } = _decorator;

@ccclass('Player')
export class Player extends Component {

    curPos: Vec3 = null;  //当前player位置

    normalBulletTimer: number = 0;  //普通子弹定时器

    lightBulletTimer: number = 0;   //激光子弹定时器

    missileBulletTimer: number = 0;   //导弹子弹定时器

    normalBulletSpeed: number = 0;   //普通子弹发射时间间隔

    lightBulletSpeed: number = 0;   //激光子弹发射时间间隔

    missileBulletSpeed: number = 0;   //导弹子弹发射时间间隔

    planeBlood: number = 0;      //飞机当前血量

    planeTotalBlood: number = 0;  //飞机总血量

    persistNode: Node = null;   //得到常驻节点

    isShootLight: boolean = false;   //是否发射激光

    isShootMissile: boolean = false;    //是否发射导弹

    playerBulletFactory: GameFactory = null;

    

    onLoad() {
        this.node.on(Node.EventType.TOUCH_MOVE, this.onTouchCallback, this);  //开启节点触摸移动监听
        this.persistNode = find("PersistNode");
        this.playerBulletFactory = this.persistNode.getComponent(PersistNode).playerBulletFactory;  //得到工厂对象

        //得到面板上的速度
        this.normalBulletSpeed = this.persistNode.getComponent(PersistNode).normalBulletSpeed;
        this.lightBulletSpeed = this.persistNode.getComponent(PersistNode).lightBulletSpeed;
        this.missileBulletSpeed = this.persistNode.getComponent(PersistNode).missileBulletSpeed;

        this.planeTotalBlood = this.persistNode.getComponent(PersistNode).planeTotalBlood;
        this.planeBlood = this.planeTotalBlood;
    }

    update(deltaTime: number) {

        //发射普通子弹
        this.normalBulletTimer += deltaTime;
        if (this.normalBulletTimer > this.normalBulletSpeed) {
            this.shootNormalBullet();
            this.normalBulletTimer = 0;
        }

        if (this.isShootLight) {
            //发射激光子弹
            this.lightBulletTimer += deltaTime;
            if (this.lightBulletTimer > this.lightBulletSpeed) {
                this.shootLightBullet();
                this.lightBulletTimer = 0;
            }
        }
        
        if (this.isShootMissile) {
            //发射导弹子弹
            this.missileBulletTimer += deltaTime;
            if (this.missileBulletTimer > this.missileBulletSpeed) {
                this.shootMissileBullet();
                this.missileBulletTimer = 0;
            }
        }
    }

    /**
     * 发射普通子弹
     */
    shootNormalBullet() {
        let posBegin: Vec3 = new Vec3();    //定义子弹开始的位置
        let normalBullet: Node = null;

        normalBullet = this.playerBulletFactory.createProduct(Global.NORMAL_BULLET);  //制作子弹
        this.node.parent.addChild(normalBullet);        //添加节点到画布

        this.curPos = this.node.getPosition();      //得到飞机当前位置
        posBegin.x = this.curPos.x; 
        posBegin.y = this.curPos.y + 50;        //设置到机头位置
        normalBullet.setPosition(posBegin);
        this.persistNode.getComponent(AudioSource).playOneShot(this.persistNode.getComponent(PersistNode).bulletAudioClip);
    }

    /**
     * 发射激光子弹
     */
     shootLightBullet() {
        let posBegin: Vec3 = new Vec3();    //定义子弹开始的位置
        let normalBullet: Node = null;

        //左边激光
        normalBullet = this.playerBulletFactory.createProduct(Global.LIGHT_BULLET);  //制作激光子弹
        this.node.parent.addChild(normalBullet);        //添加节点到画布

        this.curPos = this.node.getPosition();      //得到飞机当前位置
        posBegin.x = this.curPos.x - 50; 
        posBegin.y = this.curPos.y + 50;        //设置到机头位置
        normalBullet.setPosition(posBegin);

        //右边激光
        normalBullet = this.playerBulletFactory.createProduct(Global.LIGHT_BULLET);  //制作激光子弹
        this.node.parent.addChild(normalBullet);        //添加节点到画布

        this.curPos = this.node.getPosition();      //得到飞机当前位置
        posBegin.x = this.curPos.x + 50; 
        posBegin.y = this.curPos.y + 50;        //设置到机头位置
        normalBullet.setPosition(posBegin);

        this.persistNode.getComponent(AudioSource).playOneShot(this.persistNode.getComponent(PersistNode).lightAudioClip);
    }

    /**
     * 发射导弹子弹
     */
     shootMissileBullet() {
        let posBegin: Vec3 = new Vec3();    //定义子弹开始的位置
        let normalBullet: Node = null;

        normalBullet = this.playerBulletFactory.createProduct(Global.MISSILE_BULLET);  //制作导弹子弹
        this.node.parent.addChild(normalBullet);        //添加节点到画布

        this.curPos = this.node.getPosition();      //得到飞机当前位置
        posBegin.x = this.curPos.x; 
        posBegin.y = this.curPos.y + 50;        //设置到机头位置
        normalBullet.setPosition(posBegin);

        this.persistNode.getComponent(AudioSource).playOneShot(this.persistNode.getComponent(PersistNode).missileAudioClip);
    }

    /**
     * 鼠标或者手指移动时候的回调
     * @param event
     */
    onTouchCallback(event: EventTouch) {
        let location: Vec2 = event.getLocation();   //得到手指鼠标位置,得到的是世界坐标
        this.curPos = this.node.parent.getComponent(UITransform).convertToNodeSpaceAR(v3(location.x, location.y, 0));   //player当前位置设置为手指或者鼠标，并转化为局部坐标
        this.node.setPosition(this.curPos);
    }

    onDisable() {
        this.node.off(Node.EventType.TOUCH_MOVE, this.onTouchCallback, this);  //取消监听
    }
}

