/**
 * CrazyStorm 2.0 Integration for Cocos Creator
 * Main Export Index
 *
 * This file exports all the public APIs for the CrazyStorm integration.
 * Import from this file to access all CrazyStorm functionality.
 */

// Core types and interfaces
export * from './core/CrazyStormTypes';

// Parser functionality
export { CrazyStormParser } from './core/CrazyStormParser';
export { CrazyStormBinaryParser } from './core/BinaryParser';

// Loader functionality
export { CrazyStormLoader, getCrazyStormLoader } from './core/CrazyStormLoader';
export type { LoadOptions } from './core/CrazyStormLoader';

// Component system
export { CrazyStormComponent } from './components/CrazyStormComponent';
export { EmitterComponent } from './components/EmitterComponent';
export { CurveEmitterComponent } from './components/CurveEmitterComponent';
export { MultiEmitterComponent } from './components/MultiEmitterComponent';
export { ForceFieldComponent } from './components/ForceFieldComponent';
export { EventFieldComponent } from './components/EventFieldComponent';
export { RebounderComponent } from './components/RebounderComponent';
export { ComponentFactory, ComponentRegistry } from './components/ComponentFactory';

// Physics system
export { Particle, CurveParticle, ParticlePhysicsUtils } from './physics/ParticlePhysics';

// Runtime system
export { CrazyStormManager } from './runtime/CrazyStormManager';
export { ParticleSystemManager } from './runtime/ParticleSystemManager';
export { LayerManager } from './runtime/LayerManager';
export { ResourceManager } from './runtime/ResourceManager';
export { GlobalVariableManager } from './runtime/GlobalVariableManager';

// Performance system
export * from './performance';

// Expression system
export * from './expression';

// Event system
export * from './events';

// Particle management
export * from './particle';

// Property system
export * from './property';

// Game integration
export * from './game';

/**
 * Version information
 */
export const CRAZY_STORM_INTEGRATION_VERSION = '1.0.0';

/**
 * Quick start function to initialize CrazyStorm integration
 */
export function initializeCrazyStorm(): void {
    console.log(`CrazyStorm 2.0 Integration v${CRAZY_STORM_INTEGRATION_VERSION} initialized`);

    // Initialize performance systems
    const { initializePerformanceSystems } = require('./performance');
    initializePerformanceSystems();

    // Set up default cache configuration
    const loader = getCrazyStormLoader();
    loader.setCacheConfig(50, 300000); // 50 entries, 5 minutes timeout

    // Clean up cache periodically
    setInterval(() => {
        loader.cleanupCache();
    }, 60000); // Every minute

    console.log('CrazyStorm 2.0 Integration: All systems ready!');
}
