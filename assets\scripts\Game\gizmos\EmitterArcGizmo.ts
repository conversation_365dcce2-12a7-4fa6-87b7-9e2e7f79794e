import { _decorator, Graphics, Color, Node } from 'cc';
import { GizmoDrawer } from './GizmoDrawer';
import { EmitterArc } from '../world/bullet/EmitterArc';

/**
 * Gizmo drawer for EmitterArc components
 * Draws visual debugging information for arc-based bullet emitters
 */
export class EmitterArcGizmo extends GizmoDrawer<EmitterArc> {
    
    public readonly componentType = EmitterArc;
    public readonly drawerName = "EmitterArcGizmo";
    
    // Gizmo display options
    public showRadius: boolean = true;
    public showDirections: boolean = true;
    public showCenter: boolean = true;
    public showArc: boolean = true;
    
    // Colors
    public radiusColor: Color = Color.GRAY;
    public directionColor: Color = Color.RED;
    public centerColor: Color = Color.WHITE;
    public arcColor: Color = Color.YELLOW;
    
    // Display settings
    public speedScale: number = 1.0;
    public arrowSize: number = 8;
    public centerSize: number = 8;
    
    public drawGizmos(emitter: EmitterArc, graphics: Graphics, node: Node): void {
        // Set transform to match the emitter's node
        const worldPos = node.worldPosition;
        const worldRot = node.worldRotation;
        const worldScale = node.worldScale;
        
        // Save current transform
        graphics.save();
        
        // Apply node transform
        graphics.translate(worldPos.x, worldPos.y);
        graphics.rotate(worldRot.z);
        graphics.scale(worldScale.x, worldScale.y);
        
        // Draw center point
        if (this.showCenter) {
            this.drawCenter(graphics);
        }
        
        // Draw radius circle
        if (this.showRadius && emitter.radius > 0) {
            this.drawRadius(graphics, emitter.radius);
        }
        
        // Draw arc indicator
        if (this.showArc && emitter.arc > 0) {
            this.drawArcIndicator(graphics, emitter);
        }
        
        // Draw direction arrows
        if (this.showDirections && emitter.count > 0) {
            this.drawDirections(graphics, emitter);
        }
        
        // Restore transform
        graphics.restore();
    }
    
    private drawCenter(graphics: Graphics): void {
        this.drawCross(graphics, 0, 0, this.centerSize, this.centerColor);
    }
    
    private drawRadius(graphics: Graphics, radius: number): void {
        this.drawCircle(graphics, 0, 0, radius, this.radiusColor, false);
    }
    
    private drawArcIndicator(graphics: Graphics, emitter: EmitterArc): void {
        if (emitter.arc <= 0) return;
        
        graphics.strokeColor = this.arcColor;
        graphics.lineWidth = 2;
        
        // Convert angle and arc to radians
        const baseAngleRad = (emitter.angle + 90) * Math.PI / 180; // +90 because 0 degrees is up in Cocos
        const arcRad = emitter.arc * Math.PI / 180;
        
        const startAngle = baseAngleRad - arcRad / 2;
        const endAngle = baseAngleRad + arcRad / 2;
        
        // Draw arc at radius distance
        const arcRadius = Math.max(emitter.radius, 30); // Minimum radius for visibility
        
        // Draw arc
        const segments = Math.max(8, Math.floor(emitter.arc / 5)); // More segments for larger arcs
        for (let i = 0; i <= segments; i++) {
            const angle = startAngle + (endAngle - startAngle) * (i / segments);
            const x = Math.cos(angle) * arcRadius;
            const y = Math.sin(angle) * arcRadius;
            
            if (i === 0) {
                graphics.moveTo(x, y);
            } else {
                graphics.lineTo(x, y);
            }
        }
        
        // Draw arc end indicators
        const startX = Math.cos(startAngle) * arcRadius;
        const startY = Math.sin(startAngle) * arcRadius;
        const endX = Math.cos(endAngle) * arcRadius;
        const endY = Math.sin(endAngle) * arcRadius;
        
        // Lines from center to arc ends
        graphics.moveTo(0, 0);
        graphics.lineTo(startX, startY);
        graphics.moveTo(0, 0);
        graphics.lineTo(endX, endY);
        
        graphics.stroke();
    }
    
    private drawDirections(graphics: Graphics, emitter: EmitterArc): void {
        const baseLength = 30;
        const speedFactor = emitter.speedMultiplier || 1;
        const arrowLength = Math.max(baseLength, baseLength * speedFactor * this.speedScale);
        
        for (let i = 0; i < emitter.count; i++) {
            const direction = emitter.getDirection(i);
            const spawnPos = emitter.getSpawnPosition(i);
            
            // Start position (at spawn position)
            const startX = spawnPos.x;
            const startY = spawnPos.y;
            
            // End position (direction from spawn position)
            const endX = startX + direction.x * arrowLength;
            const endY = startY + direction.y * arrowLength;
            
            // Draw arrow
            this.drawArrow(graphics, startX, startY, endX, endY, this.directionColor, this.arrowSize);
        }
    }
    
    public getPriority(): number {
        return 10; // Draw emitter gizmos with medium priority
    }
    
    /**
     * Configure display options
     */
    public configure(options: {
        showRadius?: boolean;
        showDirections?: boolean;
        showCenter?: boolean;
        showArc?: boolean;
        radiusColor?: Color;
        directionColor?: Color;
        centerColor?: Color;
        arcColor?: Color;
        speedScale?: number;
        arrowSize?: number;
        centerSize?: number;
    }): void {
        if (options.showRadius !== undefined) this.showRadius = options.showRadius;
        if (options.showDirections !== undefined) this.showDirections = options.showDirections;
        if (options.showCenter !== undefined) this.showCenter = options.showCenter;
        if (options.showArc !== undefined) this.showArc = options.showArc;
        if (options.radiusColor !== undefined) this.radiusColor = options.radiusColor;
        if (options.directionColor !== undefined) this.directionColor = options.directionColor;
        if (options.centerColor !== undefined) this.centerColor = options.centerColor;
        if (options.arcColor !== undefined) this.arcColor = options.arcColor;
        if (options.speedScale !== undefined) this.speedScale = options.speedScale;
        if (options.arrowSize !== undefined) this.arrowSize = options.arrowSize;
        if (options.centerSize !== undefined) this.centerSize = options.centerSize;
    }
}
