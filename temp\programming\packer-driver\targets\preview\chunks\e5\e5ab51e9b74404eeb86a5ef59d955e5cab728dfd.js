System.register(["__unresolved_0", "cc", "cc/env", "__unresolved_1"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, EDITOR, G<PERSON><PERSON><PERSON><PERSON><PERSON>, GizmoDrawer, _crd;

  function _reportPossibleCrUseOfGizmoUtils(extras) {
    _reporterNs.report("GizmoUtils", "./GizmoUtils", _context.meta, extras);
  }

  _export("GizmoDrawer", void 0);

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
    }, function (_ccEnv) {
      EDITOR = _ccEnv.EDITOR;
    }, function (_unresolved_2) {
      GizmoUtils = _unresolved_2.GizmoUtils;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "0088dL1x5NJPINMIFyaUJEf", "GizmoDrawer", undefined);

      __checkObsolete__(['_decorator', 'Component', 'Graphics', 'Color', 'Node']);

      /**
       * Abstract base class for drawing gizmos for specific component types
       * This is not a component itself, but a drawer that can be registered to GizmoManager
       */
      _export("GizmoDrawer", GizmoDrawer = class GizmoDrawer {
        constructor() {
          /**
           * The component type this drawer handles
           */

          /**
           * Name of this gizmo drawer for debugging
           */

          /**
           * Whether this drawer is enabled
           */
          this.enabled = true;
        }
        /**
         * Draw gizmos for the given component
         * @param component The component to draw gizmos for
         * @param graphics The graphics component to draw with
         * @param node The node that contains the component
         */


        /**
         * Check if this drawer can handle the given component
         * @param component The component to check
         * @returns true if this drawer can handle the component
         */
        canHandle(component) {
          return component instanceof this.componentType;
        }
        /**
         * Called when the drawer is registered to the manager
         * Override this to perform any initialization
         */


        onRegister() {
          if (EDITOR) {
            console.log("GizmoDrawer: Registered " + this.drawerName);
          }
        }
        /**
         * Called when the drawer is unregistered from the manager
         * Override this to perform any cleanup
         */


        onUnregister() {
          if (EDITOR) {
            console.log("GizmoDrawer: Unregistered " + this.drawerName);
          }
        }
        /**
         * Get the priority of this drawer (higher priority draws last/on top)
         * Override this to change drawing order
         */


        getPriority() {
          return 0;
        }
        /**
         * Helper method to draw a cross at the given position
         */


        drawCross(graphics, x, y, size, color) {
          (_crd && GizmoUtils === void 0 ? (_reportPossibleCrUseOfGizmoUtils({
            error: Error()
          }), GizmoUtils) : GizmoUtils).drawCross(graphics, x, y, size, color);
        }
        /**
         * Helper method to draw a circle
         */


        drawCircle(graphics, x, y, radius, color, filled) {
          if (filled === void 0) {
            filled = false;
          }

          (_crd && GizmoUtils === void 0 ? (_reportPossibleCrUseOfGizmoUtils({
            error: Error()
          }), GizmoUtils) : GizmoUtils).drawCircle(graphics, x, y, radius, color, filled);
        }
        /**
         * Helper method to draw an arrow
         */


        drawArrow(graphics, startX, startY, endX, endY, color, arrowSize) {
          if (arrowSize === void 0) {
            arrowSize = 8;
          }

          (_crd && GizmoUtils === void 0 ? (_reportPossibleCrUseOfGizmoUtils({
            error: Error()
          }), GizmoUtils) : GizmoUtils).drawArrow(graphics, startX, startY, endX, endY, color, arrowSize);
        }
        /**
         * Helper method to draw a line
         */


        drawLine(graphics, startX, startY, endX, endY, color, lineWidth) {
          if (lineWidth === void 0) {
            lineWidth = 1;
          }

          (_crd && GizmoUtils === void 0 ? (_reportPossibleCrUseOfGizmoUtils({
            error: Error()
          }), GizmoUtils) : GizmoUtils).drawLine(graphics, startX, startY, endX, endY, color, lineWidth);
        }
        /**
         * Helper method to draw a rectangle
         */


        drawRect(graphics, x, y, width, height, color, filled) {
          if (filled === void 0) {
            filled = false;
          }

          (_crd && GizmoUtils === void 0 ? (_reportPossibleCrUseOfGizmoUtils({
            error: Error()
          }), GizmoUtils) : GizmoUtils).drawRect(graphics, x, y, width, height, color, filled);
        }
        /**
         * Helper method to draw text (simple implementation)
         * Note: For more complex text rendering, consider using Label components
         */


        drawText(_graphics, text, x, y, _color) {
          // This is a placeholder - in a real implementation you might want to use Label components
          // or a more sophisticated text rendering system
          if (EDITOR) {
            console.log("Gizmo Text at (" + x + ", " + y + "): " + text);
          }
        }

      });

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=e5ab51e9b74404eeb86a5ef59d955e5cab728dfd.js.map