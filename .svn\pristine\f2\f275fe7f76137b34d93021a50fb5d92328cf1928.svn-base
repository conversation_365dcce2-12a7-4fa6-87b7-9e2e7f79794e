# Requirements for Air fighting game with Cocos Creator

## Level Editor

### Wave Config

```cs
enum eSmoothType 
{
}

class SpawnCurve
{
    eSmoothType SmoothType;
    List<Vec2> Points;
}

class SpawnConfig
{
    int UnitId;
    int Num;
    float Interval;
    /// Spawn curve or spawn flow ?
    SpawnCurve Curve;
}

interface IMapNode
{}

enum eLayer {
    BG_VeryFar,
    BG_Far,
    BG_Mid,
    BG_Close,
    BG_VeryClose,
    
    Player,
    
    FG_Close,
    FG_VeryClose,
}

class Background : IMapNode
{
    eLayer Layer;
    string TexturePath;
}

class Spawner : IMapNode
{
    List<SpawnConfig> SpawnConfigs;
}

class StaticSpawner : Spawner
{

}

class ConditionSpawner : Spawner
{

}

class Map 
{
    string Name;
    List<IMapNode> Nodes;
}

class Level
{
    List<Map> Maps;
}


class WaveConfig
{
    List<SpawnConfig> SpawnConfigs;
}

class WaveGroup : ILevelNode
{
    List<WaveConfig> WaveConfigs;
}

class RogueGroup : ILevelNode
{

}


```