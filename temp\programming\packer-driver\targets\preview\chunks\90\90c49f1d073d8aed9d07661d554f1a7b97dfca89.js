System.register(["__unresolved_0", "cc", "__unresolved_1"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Component, find, PersistNode, _dec, _class, _crd, ccclass, property, Anim;

  function _reportPossibleCrUseOfAnimFactory(extras) {
    _reporterNs.report("AnimFactory", "./factroy/AnimFactory", _context.meta, extras);
  }

  function _reportPossibleCrUseOfPersistNode(extras) {
    _reporterNs.report("PersistNode", "./PersistNode", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Component = _cc.Component;
      find = _cc.find;
    }, function (_unresolved_2) {
      PersistNode = _unresolved_2.PersistNode;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "5348aRrJnxAI4uEM4LrSPPI", "Anim", undefined);

      __checkObsolete__(['_decorator', 'Component', 'Node', 'find']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("Anim", Anim = (_dec = ccclass('Anim'), _dec(_class = class Anim extends Component {
        constructor() {
          super(...arguments);
          this.animFactory = null;
        }

        onLoad() {
          this.animFactory = find("PersistNode").getComponent(_crd && PersistNode === void 0 ? (_reportPossibleCrUseOfPersistNode({
            error: Error()
          }), PersistNode) : PersistNode).animFactory;
        }

        recycle() {
          this.animFactory.recycleProduct(this.node);
        }

      }) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=90c49f1d073d8aed9d07661d554f1a7b97dfca89.js.map