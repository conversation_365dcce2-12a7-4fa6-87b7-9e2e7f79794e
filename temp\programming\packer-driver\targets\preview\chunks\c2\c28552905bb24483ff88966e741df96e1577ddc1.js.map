{"version": 3, "sources": ["file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/gizmos/GizmoSetup.ts"], "names": ["_decorator", "Component", "EDITOR", "quickSetupGizmos", "GizmoManager", "ccclass", "property", "GizmoSetup", "onLoad", "setupInPlayMode", "autoSetup", "setupGizmos", "existingManager", "getInstance", "console", "log", "gizmoNode", "error", "setupGizmosForScene", "setup"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;;AACZC,MAAAA,M,UAAAA,M;;AACAC,MAAAA,gB,iBAAAA,gB;AAAkBC,MAAAA,Y,iBAAAA,Y;;;;;;;;;OAErB;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBN,U;AAE9B;AACA;AACA;AACA;;4BAEaO,U,WADZF,OAAO,CAAC,YAAD,C,2BAAR,MACaE,UADb,SACgCN,SADhC,CAC0C;AAAA;AAAA;;AAAA;;AAAA;AAAA;;AAQ5BO,QAAAA,MAAM,GAAS;AACrB,cAAI,CAACN,MAAD,IAAW,CAAC,KAAKO,eAArB,EAAsC;;AAEtC,cAAI,KAAKC,SAAT,EAAoB;AAChB,iBAAKC,WAAL;AACH;AACJ;AAED;AACJ;AACA;;;AACWA,QAAAA,WAAW,GAAS;AACvB;AACA,cAAMC,eAAe,GAAG;AAAA;AAAA,4CAAaC,WAAb,EAAxB;;AACA,cAAID,eAAJ,EAAqB;AACjBE,YAAAA,OAAO,CAACC,GAAR,CAAY,0DAAZ;AACA;AACH,WANsB,CAQvB;;;AACA,cAAMC,SAAS,GAAG;AAAA;AAAA,qDAAlB;;AACA,cAAIA,SAAJ,EAAe;AACXF,YAAAA,OAAO,CAACC,GAAR,CAAY,mDAAZ;AACH,WAFD,MAEO;AACHD,YAAAA,OAAO,CAACG,KAAR,CAAc,+CAAd;AACH;AACJ;AAED;AACJ;AACA;;;AACqC,eAAnBC,mBAAmB,GAAS;AACtC,cAAMC,KAAK,GAAG,IAAIZ,UAAJ,EAAd;AACAY,UAAAA,KAAK,CAACR,WAAN;AACH;;AA1CqC,O,4EAErCL,Q;;;;;iBAC2B,I;;0FAE3BA,Q;;;;;iBACiC,K;;;AAuCtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "sourcesContent": ["import { _decorator, Component, Node, director } from 'cc';\nimport { EDITOR } from 'cc/env';\nimport { quickSetupGizmos, GizmoManager } from './index';\n\nconst { ccclass, property } = _decorator;\n\n/**\n * Helper component to automatically setup gizmos in a scene\n * Add this to any node in your scene to automatically initialize the gizmo system\n */\n@ccclass('GizmoSetup')\nexport class GizmoSetup extends Component {\n    \n    @property\n    public autoSetup: boolean = true;\n    \n    @property\n    public setupInPlayMode: boolean = false;\n    \n    protected onLoad(): void {\n        if (!EDITOR && !this.setupInPlayMode) return;\n        \n        if (this.autoSetup) {\n            this.setupGizmos();\n        }\n    }\n    \n    /**\n     * Setup the gizmo system\n     */\n    public setupGizmos(): void {\n        // Check if gizmo manager already exists\n        const existingManager = GizmoManager.getInstance();\n        if (existingManager) {\n            console.log('GizmoSetup: Gizmo manager already exists, skipping setup');\n            return;\n        }\n        \n        // Setup gizmos\n        const gizmoNode = quickSetupGizmos();\n        if (gizmoNode) {\n            console.log('GizmoSetup: Gizmo system initialized successfully');\n        } else {\n            console.error('GizmoSetup: Failed to initialize gizmo system');\n        }\n    }\n    \n    /**\n     * Manual setup method that can be called from editor or code\n     */\n    public static setupGizmosForScene(): void {\n        const setup = new GizmoSetup();\n        setup.setupGizmos();\n    }\n}\n\n/**\n * Example usage:\n * \n * 1. Add GizmoSetup component to any node in your scene\n * 2. Set autoSetup to true (default)\n * 3. The gizmo system will be automatically initialized when the scene loads\n * \n * Or call manually:\n * ```typescript\n * import { GizmoSetup } from \"./Game/gizmos/GizmoSetup\";\n * \n * // In your game initialization code\n * GizmoSetup.setupGizmosForScene();\n * ```\n */\n"]}