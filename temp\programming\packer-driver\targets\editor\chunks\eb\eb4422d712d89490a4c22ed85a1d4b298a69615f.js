System.register(["cc"], function (_export, _context) {
  "use strict";

  var _cclegacy, TypeID, TypedBase, _crd, _typeIdCounter, _typeIdToName, _constructorToTypeId, TypeIDUtils;

  /**
   * Decorator to automatically register a class with TypeID system
   * Usage: @RegisterTypeID class MyClass { ... }
   */
  function RegisterTypeID(constructor) {
    // Register the type immediately
    TypeID.get(constructor);
    return constructor;
  }
  /**
   * Interface for objects that have a type ID
   */


  _export({
    TypeID: void 0,
    RegisterTypeID: RegisterTypeID,
    TypedBase: void 0,
    TypeIDUtils: void 0
  });

  return {
    setters: [function (_cc) {
      _cclegacy = _cc.cclegacy;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "0eea3z1HsBAVZnV7nTExThQ", "TypeID", undefined);

      /**
       * TypeID system for efficient type identification
       * Similar to C#'s TypeID<T> pattern but adapted for TypeScript
       */

      /**
       * Global counter for generating unique type IDs
       */
      _typeIdCounter = 0;
      /**
       * Map to store type names to IDs for debugging purposes
       */

      _typeIdToName = new Map();
      /**
       * Map to store constructor functions to their type IDs
       */

      _constructorToTypeId = new Map();
      /**
       * TypeID class that generates unique integer IDs for types
       * Usage: TypeID.get<MyClass>() or TypeID.get(MyClass)
       */

      _export("TypeID", TypeID = class TypeID {
        /**
         * Get the type ID for a given type using constructor function
         * @param constructor The constructor function of the type
         * @returns Unique integer ID for the type
         */
        static get(constructor) {
          // Check if we already have an ID for this constructor
          let typeId = _constructorToTypeId.get(constructor);

          if (typeId === undefined) {
            // Generate new ID
            typeId = ++_typeIdCounter; // Store the mapping

            _constructorToTypeId.set(constructor, typeId); // Store name for debugging (use constructor name)


            const typeName = constructor.name || `Anonymous_${typeId}`;

            _typeIdToName.set(typeId, typeName);

            console.log(`TypeID: Registered type '${typeName}' with ID ${typeId}`);
          }

          return typeId;
        }
        /**
         * Get the type ID for a given instance
         * @param instance The instance to get the type ID for
         * @returns Unique integer ID for the type
         */


        static getFromInstance(instance) {
          if (!instance || typeof instance !== 'object') {
            throw new Error('TypeID.getFromInstance: Invalid instance provided');
          }

          return TypeID.get(instance.constructor);
        }
        /**
         * Get the type name for a given type ID (for debugging)
         * @param typeId The type ID to get the name for
         * @returns The type name or 'Unknown' if not found
         */


        static getTypeName(typeId) {
          return _typeIdToName.get(typeId) || `Unknown_${typeId}`;
        }
        /**
         * Check if a type ID is registered
         * @param typeId The type ID to check
         * @returns true if the type ID is registered
         */


        static isRegistered(typeId) {
          return _typeIdToName.has(typeId);
        }
        /**
         * Get all registered type IDs and their names (for debugging)
         * @returns Array of [typeId, typeName] pairs
         */


        static getAllRegisteredTypes() {
          return Array.from(_typeIdToName.entries());
        }
        /**
         * Clear all registered types (mainly for testing)
         */


        static clear() {
          _typeIdCounter = 0;

          _typeIdToName.clear();

          _constructorToTypeId.clear();
        }

      });

      /**
       * Base class that implements ITyped interface
       * Classes can extend this to automatically get type ID functionality
       */
      _export("TypedBase", TypedBase = class TypedBase {
        /**
         * Get the type ID for this instance
         */
        getTypeId() {
          return TypeID.getFromInstance(this);
        }
        /**
         * Get the type name for this instance
         */


        getTypeName() {
          return TypeID.getTypeName(this.getTypeId());
        }
        /**
         * Check if this instance is of a specific type
         * @param constructor The constructor to check against
         * @returns true if this instance is of the specified type
         */


        isOfType(constructor) {
          return this.getTypeId() === TypeID.get(constructor);
        }

      });
      /**
       * Utility functions for working with TypeIDs
       */


      (function (_TypeIDUtils) {
        class TypedRegistry {
          constructor() {
            this._items = new Map();
          }

          /**
           * Register an item with its type ID
           */
          register(item) {
            const typeId = TypeID.getFromInstance(item);

            this._items.set(typeId, item);
          }
          /**
           * Get an item by its type
           */


          get(constructor) {
            const typeId = TypeID.get(constructor);
            return this._items.get(typeId) || null;
          }
          /**
           * Check if a type is registered
           */


          has(constructor) {
            const typeId = TypeID.get(constructor);
            return this._items.has(typeId);
          }
          /**
           * Remove an item by its type
           */


          remove(constructor) {
            const typeId = TypeID.get(constructor);
            return this._items.delete(typeId);
          }
          /**
           * Get all registered items
           */


          getAll() {
            return Array.from(this._items.values());
          }
          /**
           * Get the number of registered items
           */


          size() {
            return this._items.size;
          }
          /**
           * Clear all registered items
           */


          clear() {
            this._items.clear();
          }
          /**
           * Iterate over all registered items
           */


          forEach(callback) {
            this._items.forEach(callback);
          }

        }

        _TypeIDUtils.TypedRegistry = TypedRegistry;
      })(TypeIDUtils || _export("TypeIDUtils", TypeIDUtils = {}));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=eb4422d712d89490a4c22ed85a1d4b298a69615f.js.map