/**
 * CrazyStorm 2.0 Integration for Cocos Creator
 * Base Component System
 * 
 * This file contains the base classes for all CrazyStorm components
 * that integrate with Cocos Creator's component system.
 */

import { _decorator, Component, Node, Vec2, Vec3, log, warn } from 'cc';
import { 
    Component as CrazyStormComponentData, 
    ComponentData, 
    PropertyValue,
    VariableResource,
    EventGroup 
} from '../core/CrazyStormTypes';

const { ccclass, property } = _decorator;

/**
 * Base class for all CrazyStorm components in Cocos Creator
 */
@ccclass('CrazyStormComponent')
export abstract class CrazyStormComponent extends Component {
    @property({ displayName: "Component ID" })
    protected componentId: number = 0;

    @property({ displayName: "Component Name" })
    protected componentName: string = '';

    @property({ displayName: "Begin Frame" })
    protected beginFrame: number = 0;

    @property({ displayName: "Total Frame" })
    protected totalFrame: number = 200;

    @property({ displayName: "Visible" })
    protected visible: boolean = true;

    // Runtime state
    protected currentFrame: number = 0;
    protected layerFrame: number = 0;
    protected isActive: boolean = false;
    protected initialPosition: Vec3 = new Vec3();
    protected velocity: Vec2 = new Vec2();
    protected acceleration: Vec2 = new Vec2();

    // CrazyStorm data
    protected crazyStormData: CrazyStormComponentData | null = null;
    protected properties: Map<string, PropertyValue> = new Map();
    protected variables: VariableResource[] = [];
    protected eventGroups: EventGroup[] = [];
    protected children: CrazyStormComponent[] = [];
    protected parent: CrazyStormComponent | null = null;
    protected bindingTarget: CrazyStormComponent | null = null;

    // Global variables reference
    protected globals: Map<string, number> = new Map();

    onLoad() {
        this.initialPosition = this.node.position.clone();
        this.initializeComponent();
    }

    start() {
        this.reset();
    }

    update(deltaTime: number) {
        if (!this.isActive || !this.visible) {
            return;
        }

        this.updateComponent(deltaTime);
    }

    /**
     * Initialize component with CrazyStorm data
     */
    public initializeWithData(data: CrazyStormComponentData, globals: Map<string, number>): void {
        this.crazyStormData = data;
        this.globals = globals;

        // Set basic properties
        this.componentId = data.id;
        this.componentName = data.name;
        this.beginFrame = data.componentData.beginFrame;
        this.totalFrame = data.componentData.totalFrame;
        this.visible = data.componentData.visibility;

        // Set position
        const pos = data.componentData.position;
        this.node.setPosition(pos.x, pos.y, 0);
        this.initialPosition = this.node.position.clone();

        // Set velocity and acceleration
        this.setVelocityFromSpeedAngle(data.componentData.speed, data.componentData.speedAngle);
        this.setAccelerationFromSpeedAngle(data.componentData.acspeed, data.componentData.acspeedAngle);

        // Store properties
        this.properties.clear();
        Object.keys(data.properties).forEach(key => {
            this.properties.set(key, data.properties[key]);
        });

        // Store variables and events
        this.variables = [...data.variables];
        this.eventGroups = [...data.componentEventGroups];

        this.onDataInitialized();
    }

    /**
     * Reset component to initial state
     */
    public reset(): void {
        this.currentFrame = 0;
        this.layerFrame = 0;
        this.isActive = false;
        this.node.position = this.initialPosition.clone();
        
        if (this.crazyStormData) {
            this.setVelocityFromSpeedAngle(
                this.crazyStormData.componentData.speed, 
                this.crazyStormData.componentData.speedAngle
            );
            this.setAccelerationFromSpeedAngle(
                this.crazyStormData.componentData.acspeed, 
                this.crazyStormData.componentData.acspeedAngle
            );
        }

        this.onReset();
    }

    /**
     * Update component state
     */
    public updateComponent(deltaTime: number): boolean {
        if (!this.shouldUpdate()) {
            return false;
        }

        // Update frame counters
        this.currentFrame++;
        this.layerFrame++;

        // Check if component should be active
        if (this.currentFrame < 0 || this.currentFrame >= this.totalFrame) {
            this.isActive = false;
            return false;
        }

        // Execute expressions
        this.executeExpressions();

        // Update physics
        this.updatePhysics(deltaTime);

        // Execute events
        this.executeEvents();

        // Update specific component behavior
        this.onUpdate(deltaTime);

        return true;
    }

    /**
     * Set component active for specific layer frame
     */
    public setLayerFrame(frame: number): void {
        this.layerFrame = frame;
        this.currentFrame = frame - this.beginFrame;
        this.isActive = this.currentFrame >= 0 && this.currentFrame < this.totalFrame && this.visible;
    }

    /**
     * Get property value, evaluating expressions if needed
     */
    protected getPropertyValue(key: string, defaultValue: any = 0): any {
        const property = this.properties.get(key);
        if (!property) {
            return defaultValue;
        }

        if (property.expression && typeof property.value === 'string') {
            return this.evaluateExpression(property.value);
        }

        return property.value;
    }

    /**
     * Set global variable value
     */
    public setGlobal(label: string, value: number): void {
        this.globals.set(label, value);
    }

    /**
     * Get global variable value
     */
    protected getGlobal(label: string): number {
        return this.globals.get(label) || 0;
    }

    /**
     * Add child component
     */
    public addChild(child: CrazyStormComponent): void {
        this.children.push(child);
        child.parent = this;
    }

    /**
     * Remove child component
     */
    public removeChild(child: CrazyStormComponent): void {
        const index = this.children.indexOf(child);
        if (index >= 0) {
            this.children.splice(index, 1);
            child.parent = null;
        }
    }

    /**
     * Set binding target for this component
     */
    public setBindingTarget(target: CrazyStormComponent | null): void {
        this.bindingTarget = target;
    }

    /**
     * Get absolute position including parent transforms
     */
    public getAbsolutePosition(): Vec3 {
        if (this.parent) {
            const parentPos = this.parent.getAbsolutePosition();
            return this.node.position.clone().add(parentPos);
        }
        return this.node.position.clone();
    }

    // Protected methods for subclasses to override

    /**
     * Initialize component-specific data
     */
    protected abstract initializeComponent(): void;

    /**
     * Called when CrazyStorm data is initialized
     */
    protected abstract onDataInitialized(): void;

    /**
     * Called when component is reset
     */
    protected abstract onReset(): void;

    /**
     * Called every frame for component-specific updates
     */
    protected abstract onUpdate(deltaTime: number): void;

    /**
     * Check if component should update this frame
     */
    protected shouldUpdate(): boolean {
        return this.isActive && this.visible;
    }

    // Private helper methods

    /**
     * Set velocity from speed and angle
     */
    private setVelocityFromSpeedAngle(speed: number, angle: number): void {
        const radians = angle * Math.PI / 180;
        this.velocity.set(
            speed * Math.cos(radians),
            speed * Math.sin(radians)
        );
    }

    /**
     * Set acceleration from speed and angle
     */
    private setAccelerationFromSpeedAngle(speed: number, angle: number): void {
        const radians = angle * Math.PI / 180;
        this.acceleration.set(
            speed * Math.cos(radians),
            speed * Math.sin(radians)
        );
    }

    /**
     * Update physics (position, velocity, acceleration)
     */
    private updatePhysics(deltaTime: number): void {
        // Apply acceleration to velocity
        this.velocity.x += this.acceleration.x * deltaTime;
        this.velocity.y += this.acceleration.y * deltaTime;

        // Apply velocity to position
        const currentPos = this.node.position;
        this.node.setPosition(
            currentPos.x + this.velocity.x * deltaTime,
            currentPos.y + this.velocity.y * deltaTime,
            currentPos.z
        );
    }

    /**
     * Execute property expressions
     */
    private executeExpressions(): void {
        this.properties.forEach((property, key) => {
            if (property.expression && typeof property.value === 'string') {
                // Expression evaluation would be implemented here
                // For now, we'll just log that an expression needs evaluation
                if (this.currentFrame === 0) {
                    log(`CrazyStormComponent: Expression for ${key}: ${property.value}`);
                }
            }
        });
    }

    /**
     * Execute component events
     */
    private executeEvents(): void {
        this.eventGroups.forEach(group => {
            group.events.forEach(event => {
                // Event execution would be implemented here
                // This is a placeholder for the event system
            });
        });
    }

    /**
     * Evaluate expression string
     */
    private evaluateExpression(expression: string): number {
        // Simple expression evaluator
        // In a full implementation, this would parse and evaluate CrazyStorm expressions
        
        // Handle common cases
        if (expression === 'Position') {
            return 0; // Placeholder
        }
        
        // Handle global variables
        if (expression === 'cx') {
            return this.getGlobal('cx');
        }
        
        if (expression === 'cy') {
            return this.getGlobal('cy');
        }

        // Try to parse as number
        const numValue = parseFloat(expression);
        if (!isNaN(numValue)) {
            return numValue;
        }

        warn(`CrazyStormComponent: Unknown expression: ${expression}`);
        return 0;
    }
}
