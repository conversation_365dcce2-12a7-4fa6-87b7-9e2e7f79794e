/**
 * TypeID system for efficient type identification
 * Similar to C#'s TypeID<T> pattern but adapted for TypeScript
 */

/**
 * Global counter for generating unique type IDs
 */
let _typeIdCounter = 0;

/**
 * Map to store type names to IDs for debugging purposes
 */
const _typeIdToName: Map<number, string> = new Map();

/**
 * Map to store constructor functions to their type IDs
 */
const _constructorToTypeId: Map<Function, number> = new Map();

/**
 * TypeID class that generates unique integer IDs for types
 * Usage: TypeID.get<MyClass>() or TypeID.get(MyClass)
 */
export class TypeID {
    
    /**
     * Get the type ID for a given type using constructor function
     * @param constructor The constructor function of the type
     * @returns Unique integer ID for the type
     */
    public static get<T>(constructor: new (...args: any[]) => T): number {
        // Check if we already have an ID for this constructor
        let typeId = _constructorToTypeId.get(constructor);
        
        if (typeId === undefined) {
            // Generate new ID
            typeId = ++_typeIdCounter;
            
            // Store the mapping
            _constructorToTypeId.set(constructor, typeId);
            
            // Store name for debugging (use constructor name)
            const typeName = constructor.name || `Anonymous_${typeId}`;
            _typeIdToName.set(typeId, typeName);
            
            console.log(`TypeID: Registered type '${typeName}' with ID ${typeId}`);
        }
        
        return typeId;
    }
    
    /**
     * Get the type ID for a given instance
     * @param instance The instance to get the type ID for
     * @returns Unique integer ID for the type
     */
    public static getFromInstance<T>(instance: T): number {
        if (!instance || typeof instance !== 'object') {
            throw new Error('TypeID.getFromInstance: Invalid instance provided');
        }
        
        return TypeID.get(instance.constructor as new (...args: any[]) => T);
    }
    
    /**
     * Get the type name for a given type ID (for debugging)
     * @param typeId The type ID to get the name for
     * @returns The type name or 'Unknown' if not found
     */
    public static getTypeName(typeId: number): string {
        return _typeIdToName.get(typeId) || `Unknown_${typeId}`;
    }
    
    /**
     * Check if a type ID is registered
     * @param typeId The type ID to check
     * @returns true if the type ID is registered
     */
    public static isRegistered(typeId: number): boolean {
        return _typeIdToName.has(typeId);
    }
    
    /**
     * Get all registered type IDs and their names (for debugging)
     * @returns Array of [typeId, typeName] pairs
     */
    public static getAllRegisteredTypes(): [number, string][] {
        return Array.from(_typeIdToName.entries());
    }
    
    /**
     * Clear all registered types (mainly for testing)
     */
    public static clear(): void {
        _typeIdCounter = 0;
        _typeIdToName.clear();
        _constructorToTypeId.clear();
    }
}

/**
 * Decorator to automatically register a class with TypeID system
 * Usage: @RegisterTypeID class MyClass { ... }
 */
export function RegisterTypeID<T extends new (...args: any[]) => any>(constructor: T): T {
    // Register the type immediately
    TypeID.get(constructor);
    return constructor;
}

/**
 * Interface for objects that have a type ID
 */
export interface ITyped {
    getTypeId(): number;
}

/**
 * Base class that implements ITyped interface
 * Classes can extend this to automatically get type ID functionality
 */
export abstract class TypedBase implements ITyped {
    
    /**
     * Get the type ID for this instance
     */
    public getTypeId(): number {
        return TypeID.getFromInstance(this);
    }
    
    /**
     * Get the type name for this instance
     */
    public getTypeName(): string {
        return TypeID.getTypeName(this.getTypeId());
    }
    
    /**
     * Check if this instance is of a specific type
     * @param constructor The constructor to check against
     * @returns true if this instance is of the specified type
     */
    public isOfType<T>(constructor: new (...args: any[]) => T): boolean {
        return this.getTypeId() === TypeID.get(constructor);
    }
}

/**
 * Utility functions for working with TypeIDs
 */
export namespace TypeIDUtils {
    
    /**
     * Create a type-safe system registry using TypeIDs
     */
    export class TypedRegistry<TBase> {
        private _items: Map<number, TBase> = new Map();
        
        /**
         * Register an item with its type ID
         */
        public register<T extends TBase>(item: T): void {
            const typeId = TypeID.getFromInstance(item);
            this._items.set(typeId, item);
        }
        
        /**
         * Get an item by its type
         */
        public get<T extends TBase>(constructor: new (...args: any[]) => T): T | null {
            const typeId = TypeID.get(constructor);
            return (this._items.get(typeId) as T) || null;
        }
        
        /**
         * Check if a type is registered
         */
        public has<T extends TBase>(constructor: new (...args: any[]) => T): boolean {
            const typeId = TypeID.get(constructor);
            return this._items.has(typeId);
        }
        
        /**
         * Remove an item by its type
         */
        public remove<T extends TBase>(constructor: new (...args: any[]) => T): boolean {
            const typeId = TypeID.get(constructor);
            return this._items.delete(typeId);
        }
        
        /**
         * Get all registered items
         */
        public getAll(): TBase[] {
            return Array.from(this._items.values());
        }
        
        /**
         * Get the number of registered items
         */
        public size(): number {
            return this._items.size;
        }
        
        /**
         * Clear all registered items
         */
        public clear(): void {
            this._items.clear();
        }
        
        /**
         * Iterate over all registered items
         */
        public forEach(callback: (item: TBase, typeId: number) => void): void {
            this._items.forEach(callback);
        }
    }
}
