/**
 * CrazyStorm 2.0 Integration for Cocos Creator
 * Game Integration System
 * 
 * This system provides the main interface for integrating CrazyStorm
 * with your air-fighter game, including enemy patterns, collision detection,
 * and game state management.
 */

import { _decorator, Component, Node, Vec2, Vec3, log, warn, error } from 'cc';
import { CrazyStormManager } from '../runtime/CrazyStormManager';
import { CollisionManager } from './CollisionManager';
import { EnemyPatternController } from './EnemyPatternController';
import { GameStateManager } from './GameStateManager';
import { initializePerformanceSystems, updatePerformanceSystems } from '../performance';

const { ccclass, property } = _decorator;

/**
 * Game integration configuration
 */
export interface GameConfig {
    enableCollision: boolean;
    enablePerformanceMonitoring: boolean;
    enableDebugDraw: boolean;
    playerCollisionRadius: number;
    bulletCollisionRadius: number;
    maxConcurrentPatterns: number;
    autoLoadPatterns: boolean;
    patternDirectory: string;
}

/**
 * Player state for collision detection and global variables
 */
export interface PlayerState {
    position: Vec2;
    velocity: Vec2;
    health: number;
    maxHealth: number;
    isInvulnerable: boolean;
    invulnerabilityTime: number;
}

/**
 * Game events
 */
export interface GameEvents {
    onPlayerHit?: (damage: number, position: Vec2) => void;
    onEnemyDestroyed?: (enemyId: string, position: Vec2) => void;
    onPatternComplete?: (patternName: string) => void;
    onPerformanceIssue?: (recommendations: any[]) => void;
}

/**
 * Main game integration component
 */
@ccclass('GameIntegration')
export class GameIntegration extends Component {
    @property({ displayName: "Enable Collision Detection" })
    enableCollision: boolean = true;

    @property({ displayName: "Enable Performance Monitoring" })
    enablePerformanceMonitoring: boolean = true;

    @property({ displayName: "Enable Debug Draw" })
    enableDebugDraw: boolean = false;

    @property({ displayName: "Player Collision Radius" })
    playerCollisionRadius: number = 15;

    @property({ displayName: "Bullet Collision Radius" })
    bulletCollisionRadius: number = 5;

    @property({ displayName: "Max Concurrent Patterns" })
    maxConcurrentPatterns: number = 5;

    @property({ displayName: "Pattern Directory" })
    patternDirectory: string = 'crazy-storm/patterns';

    // Core systems
    private crazyStormManager: CrazyStormManager;
    private collisionManager: CollisionManager;
    private enemyPatternController: EnemyPatternController;
    private gameStateManager: GameStateManager;

    // Game state
    private playerState: PlayerState;
    private gameConfig: GameConfig;
    private gameEvents: GameEvents = {};
    private isInitialized: boolean = false;

    onLoad() {
        this.initializeConfig();
        this.initializeSystems();
        this.initializePlayerState();
    }

    start() {
        this.initializeIntegration();
    }

    update(deltaTime: number) {
        if (!this.isInitialized) {
            return;
        }

        // Update performance systems
        if (this.gameConfig.enablePerformanceMonitoring) {
            const particleCount = this.getActiveParticleCount();
            updatePerformanceSystems(deltaTime, particleCount, 0);
        }

        // Update collision detection
        if (this.gameConfig.enableCollision) {
            this.collisionManager.update(deltaTime);
        }

        // Update enemy patterns
        this.enemyPatternController.update(deltaTime);

        // Update game state
        this.gameStateManager.update(deltaTime);

        // Update player invulnerability
        this.updatePlayerInvulnerability(deltaTime);
    }

    /**
     * Set player position (updates global variables cx, cy)
     */
    public setPlayerPosition(position: Vec2): void {
        this.playerState.position.set(position.x, position.y);
        
        if (this.crazyStormManager) {
            this.crazyStormManager.setPlayerPosition(position.x, position.y);
        }
    }

    /**
     * Set player velocity
     */
    public setPlayerVelocity(velocity: Vec2): void {
        this.playerState.velocity.set(velocity.x, velocity.y);
    }

    /**
     * Get player state
     */
    public getPlayerState(): PlayerState {
        return { ...this.playerState };
    }

    /**
     * Set player health
     */
    public setPlayerHealth(health: number): void {
        this.playerState.health = Math.max(0, Math.min(this.playerState.maxHealth, health));
    }

    /**
     * Damage player
     */
    public damagePlayer(damage: number, position: Vec2): boolean {
        if (this.playerState.isInvulnerable || this.playerState.health <= 0) {
            return false;
        }

        this.playerState.health -= damage;
        this.playerState.isInvulnerable = true;
        this.playerState.invulnerabilityTime = 2.0; // 2 seconds of invulnerability

        // Trigger event
        if (this.gameEvents.onPlayerHit) {
            this.gameEvents.onPlayerHit(damage, position);
        }

        log(`GameIntegration: Player hit for ${damage} damage at [${position.x}, ${position.y}]`);
        return true;
    }

    /**
     * Spawn enemy pattern
     */
    public async spawnEnemyPattern(patternName: string, position: Vec2, enemyId?: string): Promise<boolean> {
        return this.enemyPatternController.spawnPattern(patternName, position, enemyId);
    }

    /**
     * Stop enemy pattern
     */
    public stopEnemyPattern(enemyId: string): boolean {
        return this.enemyPatternController.stopPattern(enemyId);
    }

    /**
     * Clear all enemy patterns
     */
    public clearAllEnemyPatterns(): void {
        this.enemyPatternController.clearAllPatterns();
    }

    /**
     * Set game event callbacks
     */
    public setGameEvents(events: GameEvents): void {
        this.gameEvents = { ...this.gameEvents, ...events };
    }

    /**
     * Get collision manager for custom collision handling
     */
    public getCollisionManager(): CollisionManager {
        return this.collisionManager;
    }

    /**
     * Get enemy pattern controller
     */
    public getEnemyPatternController(): EnemyPatternController {
        return this.enemyPatternController;
    }

    /**
     * Get game state manager
     */
    public getGameStateManager(): GameStateManager {
        return this.gameStateManager;
    }

    /**
     * Pause all CrazyStorm systems
     */
    public pauseGame(): void {
        if (this.crazyStormManager) {
            this.crazyStormManager.pause();
        }
        this.enemyPatternController.pauseAll();
        log('GameIntegration: Game paused');
    }

    /**
     * Resume all CrazyStorm systems
     */
    public resumeGame(): void {
        if (this.crazyStormManager) {
            this.crazyStormManager.resume();
        }
        this.enemyPatternController.resumeAll();
        log('GameIntegration: Game resumed');
    }

    /**
     * Reset game state
     */
    public resetGame(): void {
        if (this.crazyStormManager) {
            this.crazyStormManager.reset();
        }
        this.enemyPatternController.clearAllPatterns();
        this.collisionManager.clearAll();
        this.initializePlayerState();
        log('GameIntegration: Game reset');
    }

    /**
     * Get game statistics
     */
    public getGameStats(): {
        playerHealth: number;
        activePatterns: number;
        activeParticles: number;
        collisionChecks: number;
        performance: any;
    } {
        return {
            playerHealth: this.playerState.health,
            activePatterns: this.enemyPatternController.getActivePatternCount(),
            activeParticles: this.getActiveParticleCount(),
            collisionChecks: this.collisionManager.getCollisionCheckCount(),
            performance: this.gameConfig.enablePerformanceMonitoring ? 
                this.getPerformanceStats() : null
        };
    }

    // Private methods

    /**
     * Initialize configuration
     */
    private initializeConfig(): void {
        this.gameConfig = {
            enableCollision: this.enableCollision,
            enablePerformanceMonitoring: this.enablePerformanceMonitoring,
            enableDebugDraw: this.enableDebugDraw,
            playerCollisionRadius: this.playerCollisionRadius,
            bulletCollisionRadius: this.bulletCollisionRadius,
            maxConcurrentPatterns: this.maxConcurrentPatterns,
            autoLoadPatterns: true,
            patternDirectory: this.patternDirectory
        };
    }

    /**
     * Initialize all systems
     */
    private initializeSystems(): void {
        // Initialize performance systems
        if (this.gameConfig.enablePerformanceMonitoring) {
            initializePerformanceSystems();
        }

        // Create CrazyStorm manager
        this.crazyStormManager = this.node.addComponent(CrazyStormManager);

        // Create collision manager
        this.collisionManager = new CollisionManager(this.gameConfig);
        this.collisionManager.setPlayerCollisionCallback((position) => {
            this.damagePlayer(1, position);
        });

        // Create enemy pattern controller
        this.enemyPatternController = new EnemyPatternController(
            this.node,
            this.crazyStormManager,
            this.gameConfig
        );

        // Create game state manager
        this.gameStateManager = new GameStateManager();
    }

    /**
     * Initialize player state
     */
    private initializePlayerState(): void {
        this.playerState = {
            position: new Vec2(0, 0),
            velocity: new Vec2(0, 0),
            health: 100,
            maxHealth: 100,
            isInvulnerable: false,
            invulnerabilityTime: 0
        };
    }

    /**
     * Initialize integration
     */
    private async initializeIntegration(): Promise<void> {
        try {
            // Set up collision detection
            if (this.gameConfig.enableCollision) {
                this.setupCollisionDetection();
            }

            // Set up pattern callbacks
            this.setupPatternCallbacks();

            // Set up performance monitoring
            if (this.gameConfig.enablePerformanceMonitoring) {
                this.setupPerformanceMonitoring();
            }

            this.isInitialized = true;
            log('GameIntegration: Successfully initialized');

        } catch (err) {
            error('GameIntegration: Failed to initialize:', err);
        }
    }

    /**
     * Setup collision detection
     */
    private setupCollisionDetection(): void {
        // Register player for collision detection
        this.collisionManager.registerPlayer(
            () => this.playerState.position,
            this.gameConfig.playerCollisionRadius
        );

        // Register bullet collision callback
        this.collisionManager.setBulletCollisionCallback((bulletPosition) => {
            // Handle bullet collision (e.g., destroy bullet, create effect)
            return true; // Return true to destroy the bullet
        });
    }

    /**
     * Setup pattern callbacks
     */
    private setupPatternCallbacks(): void {
        this.enemyPatternController.setPatternCompleteCallback((patternName, enemyId) => {
            if (this.gameEvents.onPatternComplete) {
                this.gameEvents.onPatternComplete(patternName);
            }
        });

        this.enemyPatternController.setEnemyDestroyedCallback((enemyId, position) => {
            if (this.gameEvents.onEnemyDestroyed) {
                this.gameEvents.onEnemyDestroyed(enemyId, position);
            }
        });
    }

    /**
     * Setup performance monitoring
     */
    private setupPerformanceMonitoring(): void {
        // This would set up performance issue callbacks
        if (this.gameEvents.onPerformanceIssue) {
            // Set up performance monitoring callbacks
        }
    }

    /**
     * Update player invulnerability
     */
    private updatePlayerInvulnerability(deltaTime: number): void {
        if (this.playerState.isInvulnerable) {
            this.playerState.invulnerabilityTime -= deltaTime;
            if (this.playerState.invulnerabilityTime <= 0) {
                this.playerState.isInvulnerable = false;
                this.playerState.invulnerabilityTime = 0;
            }
        }
    }

    /**
     * Get active particle count
     */
    private getActiveParticleCount(): number {
        if (this.crazyStormManager) {
            const stats = this.crazyStormManager.getStats();
            return stats.activeParticles;
        }
        return 0;
    }

    /**
     * Get performance statistics
     */
    private getPerformanceStats(): any {
        // This would return performance statistics
        return {};
    }

    onDestroy() {
        if (this.collisionManager) {
            this.collisionManager.destroy();
        }
        if (this.enemyPatternController) {
            this.enemyPatternController.destroy();
        }
        if (this.gameStateManager) {
            this.gameStateManager.destroy();
        }
    }
}
