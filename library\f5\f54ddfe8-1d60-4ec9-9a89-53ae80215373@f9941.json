{"__type__": "cc.SpriteFrame", "content": {"name": "loadingBG", "atlas": "", "rect": {"x": 0, "y": 0, "width": 960, "height": 1600}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 960, "height": 1600}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-480, -800, 0, 480, -800, 0, -480, 800, 0, 480, 800, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 1600, 960, 1600, 0, 0, 960, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -480, "y": -800, "z": 0}, "maxPos": {"x": 480, "y": 800, "z": 0}}, "texture": "f54ddfe8-1d60-4ec9-9a89-53ae80215373@6c48a", "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}}