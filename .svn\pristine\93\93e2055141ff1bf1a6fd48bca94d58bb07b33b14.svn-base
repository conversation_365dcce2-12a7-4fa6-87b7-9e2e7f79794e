import { _decorator, Component, Node, NodePool, find } from 'cc';
const { ccclass, property } = _decorator;


export class GameFactory {
    productPool: NodePool = null;
    persistNode: Node = null;

    public constructor() {
        this.productPool = new NodePool();     //建立节点池
        this.persistNode = find("PersistNode"); //获取常驻节点，常驻节点上会有我们制造产品的原料
    }

    public createProduct(productType: string): Node {
        //具体实现交给子类
        return null;
    }

    public recycleProduct(product: Node) {
        this.productPool.put(product);   //回收产品，即放回节点池
    }
}

