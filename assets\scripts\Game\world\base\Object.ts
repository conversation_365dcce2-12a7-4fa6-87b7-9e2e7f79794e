import { _decorator, Component } from 'cc';
const { ccclass } = _decorator;

/**
 * Abstract base class for all world objects
 * Inherits from Cocos Creator Component and provides common functionality
 */
@ccclass('CObject')
export abstract class CObject extends Component {

    /**
     * Called when the object is initialized
     * Override this method to implement object-specific initialization logic
     */
    protected abstract onObjectInit(): void;

    /**
     * Called when the object is destroyed
     * Override this method to implement object-specific cleanup logic
     */
    protected abstract onObjectDestroy(): void;

    /**
     * Initialize the object
     * This is called automatically by the framework
     */
    protected onLoad(): void {
        this.onObjectInit();
    }

    /**
     * Cleanup the object
     * This is called automatically by the framework
     */
    protected onDestroy(): void {
        this.onObjectDestroy();
    }
}