{"version": 3, "sources": ["file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/base/TypeID.ts"], "names": ["TypeID", "TypedBase", "RegisterTypeID", "constructor", "get", "_typeIdCounter", "_typeIdToName", "Map", "_constructorToTypeId", "typeId", "undefined", "set", "typeName", "name", "console", "log", "getFromInstance", "instance", "Error", "getTypeName", "isRegistered", "has", "getAllRegisteredTypes", "Array", "from", "entries", "clear", "getTypeId", "isOfType", "TypedRegistry", "_items", "register", "item", "remove", "delete", "getAll", "values", "size", "for<PERSON>ach", "callback", "TypeIDUtils"], "mappings": ";;;iBAwBaA,M,EAkGSC,S;;AArBtB;AACA;AACA;AACA;AACO,WAASC,cAAT,CAA+DC,WAA/D,EAAkF;AACrF;AACAH,IAAAA,MAAM,CAACI,GAAP,CAAWD,WAAX;AACA,WAAOA,WAAP;AACH;AAED;AACA;AACA;;;;;oBARgBD,c;;;;;;;;;;;;;;AAzGhB;AACA;AACA;AACA;;AAEA;AACA;AACA;AACIG,MAAAA,c,GAAiB,C;AAErB;AACA;AACA;;AACMC,MAAAA,a,GAAqC,IAAIC,GAAJ,E;AAE3C;AACA;AACA;;AACMC,MAAAA,oB,GAA8C,IAAID,GAAJ,E;AAEpD;AACA;AACA;AACA;;wBACaP,M,GAAN,MAAMA,MAAN,CAAa;AAEhB;AACJ;AACA;AACA;AACA;AACqB,eAAHI,GAAG,CAAID,WAAJ,EAAoD;AACjE;AACA,cAAIM,MAAM,GAAGD,oBAAoB,CAACJ,GAArB,CAAyBD,WAAzB,CAAb;;AAEA,cAAIM,MAAM,KAAKC,SAAf,EAA0B;AACtB;AACAD,YAAAA,MAAM,GAAG,EAAEJ,cAAX,CAFsB,CAItB;;AACAG,YAAAA,oBAAoB,CAACG,GAArB,CAAyBR,WAAzB,EAAsCM,MAAtC,EALsB,CAOtB;;;AACA,gBAAMG,QAAQ,GAAGT,WAAW,CAACU,IAAZ,mBAAiCJ,MAAlD;;AACAH,YAAAA,aAAa,CAACK,GAAd,CAAkBF,MAAlB,EAA0BG,QAA1B;;AAEAE,YAAAA,OAAO,CAACC,GAAR,+BAAwCH,QAAxC,kBAA6DH,MAA7D;AACH;;AAED,iBAAOA,MAAP;AACH;AAED;AACJ;AACA;AACA;AACA;;;AACiC,eAAfO,eAAe,CAAIC,QAAJ,EAAyB;AAClD,cAAI,CAACA,QAAD,IAAa,OAAOA,QAAP,KAAoB,QAArC,EAA+C;AAC3C,kBAAM,IAAIC,KAAJ,CAAU,mDAAV,CAAN;AACH;;AAED,iBAAOlB,MAAM,CAACI,GAAP,CAAWa,QAAQ,CAACd,WAApB,CAAP;AACH;AAED;AACJ;AACA;AACA;AACA;;;AAC6B,eAAXgB,WAAW,CAACV,MAAD,EAAyB;AAC9C,iBAAOH,aAAa,CAACF,GAAd,CAAkBK,MAAlB,kBAAwCA,MAA/C;AACH;AAED;AACJ;AACA;AACA;AACA;;;AAC8B,eAAZW,YAAY,CAACX,MAAD,EAA0B;AAChD,iBAAOH,aAAa,CAACe,GAAd,CAAkBZ,MAAlB,CAAP;AACH;AAED;AACJ;AACA;AACA;;;AACuC,eAArBa,qBAAqB,GAAuB;AACtD,iBAAOC,KAAK,CAACC,IAAN,CAAWlB,aAAa,CAACmB,OAAd,EAAX,CAAP;AACH;AAED;AACJ;AACA;;;AACuB,eAALC,KAAK,GAAS;AACxBrB,UAAAA,cAAc,GAAG,CAAjB;;AACAC,UAAAA,aAAa,CAACoB,KAAd;;AACAlB,UAAAA,oBAAoB,CAACkB,KAArB;AACH;;AA1Ee,O;;AA8FpB;AACA;AACA;AACA;2BACsBzB,S,GAAf,MAAeA,SAAf,CAA2C;AAE9C;AACJ;AACA;AACW0B,QAAAA,SAAS,GAAW;AACvB,iBAAO3B,MAAM,CAACgB,eAAP,CAAuB,IAAvB,CAAP;AACH;AAED;AACJ;AACA;;;AACWG,QAAAA,WAAW,GAAW;AACzB,iBAAOnB,MAAM,CAACmB,WAAP,CAAmB,KAAKQ,SAAL,EAAnB,CAAP;AACH;AAED;AACJ;AACA;AACA;AACA;;;AACWC,QAAAA,QAAQ,CAAIzB,WAAJ,EAAqD;AAChE,iBAAO,KAAKwB,SAAL,OAAqB3B,MAAM,CAACI,GAAP,CAAWD,WAAX,CAA5B;AACH;;AAvB6C,O;AA0BlD;AACA;AACA;;;;AAMW,cAAM0B,aAAN,CAA2B;AAAA;AAAA,iBACtBC,MADsB,GACO,IAAIvB,GAAJ,EADP;AAAA;;AAG9B;AACR;AACA;AACewB,UAAAA,QAAQ,CAAkBC,IAAlB,EAAiC;AAC5C,gBAAMvB,MAAM,GAAGT,MAAM,CAACgB,eAAP,CAAuBgB,IAAvB,CAAf;;AACA,iBAAKF,MAAL,CAAYnB,GAAZ,CAAgBF,MAAhB,EAAwBuB,IAAxB;AACH;AAED;AACR;AACA;;;AACe5B,UAAAA,GAAG,CAAkBD,WAAlB,EAAoE;AAC1E,gBAAMM,MAAM,GAAGT,MAAM,CAACI,GAAP,CAAWD,WAAX,CAAf;AACA,mBAAQ,KAAK2B,MAAL,CAAY1B,GAAZ,CAAgBK,MAAhB,CAAD,IAAkC,IAAzC;AACH;AAED;AACR;AACA;;;AACeY,UAAAA,GAAG,CAAkBlB,WAAlB,EAAmE;AACzE,gBAAMM,MAAM,GAAGT,MAAM,CAACI,GAAP,CAAWD,WAAX,CAAf;AACA,mBAAO,KAAK2B,MAAL,CAAYT,GAAZ,CAAgBZ,MAAhB,CAAP;AACH;AAED;AACR;AACA;;;AACewB,UAAAA,MAAM,CAAkB9B,WAAlB,EAAmE;AAC5E,gBAAMM,MAAM,GAAGT,MAAM,CAACI,GAAP,CAAWD,WAAX,CAAf;AACA,mBAAO,KAAK2B,MAAL,CAAYI,MAAZ,CAAmBzB,MAAnB,CAAP;AACH;AAED;AACR;AACA;;;AACe0B,UAAAA,MAAM,GAAY;AACrB,mBAAOZ,KAAK,CAACC,IAAN,CAAW,KAAKM,MAAL,CAAYM,MAAZ,EAAX,CAAP;AACH;AAED;AACR;AACA;;;AACeC,UAAAA,IAAI,GAAW;AAClB,mBAAO,KAAKP,MAAL,CAAYO,IAAnB;AACH;AAED;AACR;AACA;;;AACeX,UAAAA,KAAK,GAAS;AACjB,iBAAKI,MAAL,CAAYJ,KAAZ;AACH;AAED;AACR;AACA;;;AACeY,UAAAA,OAAO,CAACC,QAAD,EAAwD;AAClE,iBAAKT,MAAL,CAAYQ,OAAZ,CAAoBC,QAApB;AACH;;AA7D6B;;;SALrBC,W,2BAAAA,W", "sourcesContent": ["/**\n * TypeID system for efficient type identification\n * Similar to C#'s TypeID<T> pattern but adapted for TypeScript\n */\n\n/**\n * Global counter for generating unique type IDs\n */\nlet _typeIdCounter = 0;\n\n/**\n * Map to store type names to IDs for debugging purposes\n */\nconst _typeIdToName: Map<number, string> = new Map();\n\n/**\n * Map to store constructor functions to their type IDs\n */\nconst _constructorToTypeId: Map<Function, number> = new Map();\n\n/**\n * TypeID class that generates unique integer IDs for types\n * Usage: TypeID.get<MyClass>() or TypeID.get(MyClass)\n */\nexport class TypeID {\n    \n    /**\n     * Get the type ID for a given type using constructor function\n     * @param constructor The constructor function of the type\n     * @returns Unique integer ID for the type\n     */\n    public static get<T>(constructor: new (...args: any[]) => T): number {\n        // Check if we already have an ID for this constructor\n        let typeId = _constructorToTypeId.get(constructor);\n        \n        if (typeId === undefined) {\n            // Generate new ID\n            typeId = ++_typeIdCounter;\n            \n            // Store the mapping\n            _constructorToTypeId.set(constructor, typeId);\n            \n            // Store name for debugging (use constructor name)\n            const typeName = constructor.name || `Anonymous_${typeId}`;\n            _typeIdToName.set(typeId, typeName);\n            \n            console.log(`TypeID: Registered type '${typeName}' with ID ${typeId}`);\n        }\n        \n        return typeId;\n    }\n    \n    /**\n     * Get the type ID for a given instance\n     * @param instance The instance to get the type ID for\n     * @returns Unique integer ID for the type\n     */\n    public static getFromInstance<T>(instance: T): number {\n        if (!instance || typeof instance !== 'object') {\n            throw new Error('TypeID.getFromInstance: Invalid instance provided');\n        }\n        \n        return TypeID.get(instance.constructor as new (...args: any[]) => T);\n    }\n    \n    /**\n     * Get the type name for a given type ID (for debugging)\n     * @param typeId The type ID to get the name for\n     * @returns The type name or 'Unknown' if not found\n     */\n    public static getTypeName(typeId: number): string {\n        return _typeIdToName.get(typeId) || `Unknown_${typeId}`;\n    }\n    \n    /**\n     * Check if a type ID is registered\n     * @param typeId The type ID to check\n     * @returns true if the type ID is registered\n     */\n    public static isRegistered(typeId: number): boolean {\n        return _typeIdToName.has(typeId);\n    }\n    \n    /**\n     * Get all registered type IDs and their names (for debugging)\n     * @returns Array of [typeId, typeName] pairs\n     */\n    public static getAllRegisteredTypes(): [number, string][] {\n        return Array.from(_typeIdToName.entries());\n    }\n    \n    /**\n     * Clear all registered types (mainly for testing)\n     */\n    public static clear(): void {\n        _typeIdCounter = 0;\n        _typeIdToName.clear();\n        _constructorToTypeId.clear();\n    }\n}\n\n/**\n * Decorator to automatically register a class with TypeID system\n * Usage: @RegisterTypeID class MyClass { ... }\n */\nexport function RegisterTypeID<T extends new (...args: any[]) => any>(constructor: T): T {\n    // Register the type immediately\n    TypeID.get(constructor);\n    return constructor;\n}\n\n/**\n * Interface for objects that have a type ID\n */\nexport interface ITyped {\n    getTypeId(): number;\n}\n\n/**\n * Base class that implements ITyped interface\n * Classes can extend this to automatically get type ID functionality\n */\nexport abstract class TypedBase implements ITyped {\n    \n    /**\n     * Get the type ID for this instance\n     */\n    public getTypeId(): number {\n        return TypeID.getFromInstance(this);\n    }\n    \n    /**\n     * Get the type name for this instance\n     */\n    public getTypeName(): string {\n        return TypeID.getTypeName(this.getTypeId());\n    }\n    \n    /**\n     * Check if this instance is of a specific type\n     * @param constructor The constructor to check against\n     * @returns true if this instance is of the specified type\n     */\n    public isOfType<T>(constructor: new (...args: any[]) => T): boolean {\n        return this.getTypeId() === TypeID.get(constructor);\n    }\n}\n\n/**\n * Utility functions for working with TypeIDs\n */\nexport namespace TypeIDUtils {\n    \n    /**\n     * Create a type-safe system registry using TypeIDs\n     */\n    export class TypedRegistry<TBase> {\n        private _items: Map<number, TBase> = new Map();\n        \n        /**\n         * Register an item with its type ID\n         */\n        public register<T extends TBase>(item: T): void {\n            const typeId = TypeID.getFromInstance(item);\n            this._items.set(typeId, item);\n        }\n        \n        /**\n         * Get an item by its type\n         */\n        public get<T extends TBase>(constructor: new (...args: any[]) => T): T | null {\n            const typeId = TypeID.get(constructor);\n            return (this._items.get(typeId) as T) || null;\n        }\n        \n        /**\n         * Check if a type is registered\n         */\n        public has<T extends TBase>(constructor: new (...args: any[]) => T): boolean {\n            const typeId = TypeID.get(constructor);\n            return this._items.has(typeId);\n        }\n        \n        /**\n         * Remove an item by its type\n         */\n        public remove<T extends TBase>(constructor: new (...args: any[]) => T): boolean {\n            const typeId = TypeID.get(constructor);\n            return this._items.delete(typeId);\n        }\n        \n        /**\n         * Get all registered items\n         */\n        public getAll(): TBase[] {\n            return Array.from(this._items.values());\n        }\n        \n        /**\n         * Get the number of registered items\n         */\n        public size(): number {\n            return this._items.size;\n        }\n        \n        /**\n         * Clear all registered items\n         */\n        public clear(): void {\n            this._items.clear();\n        }\n        \n        /**\n         * Iterate over all registered items\n         */\n        public forEach(callback: (item: TBase, typeId: number) => void): void {\n            this._items.forEach(callback);\n        }\n    }\n}\n"]}