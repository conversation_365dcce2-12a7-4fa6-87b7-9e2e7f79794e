System.register(["__unresolved_0", "cc", "cc/env", "__unresolved_1", "__unresolved_2"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Component, Color, Graphics, EDITOR, Emitter, EmitterArc, _dec, _dec2, _class, _class2, _descriptor, _descriptor2, _descriptor3, _descriptor4, _descriptor5, _descriptor6, _descriptor7, _crd, ccclass, property, executeInEditMode, EmitterGizmo;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfEmitter(extras) {
    _reporterNs.report("Emitter", "../assets/scripts/Game/world/bullet/Emitter", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEmitterArc(extras) {
    _reporterNs.report("EmitterArc", "../assets/scripts/Game/world/bullet/EmitterArc", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Component = _cc.Component;
      Color = _cc.Color;
      Graphics = _cc.Graphics;
    }, function (_ccEnv) {
      EDITOR = _ccEnv.EDITOR;
    }, function (_unresolved_2) {
      Emitter = _unresolved_2.Emitter;
    }, function (_unresolved_3) {
      EmitterArc = _unresolved_3.EmitterArc;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "92b82K+6DhHdIKGhR3HTOWv", "EmitterGizmo", undefined);
      /**
       * Emitter Gizmo Component
       * This component provides visual debugging for Emitter components in the scene view
       * It should be added to the same node as the Emitter component
       */


      __checkObsolete__(['_decorator', 'Component', 'Color', 'Graphics']);

      ({
        ccclass,
        property,
        executeInEditMode
      } = _decorator);

      _export("EmitterGizmo", EmitterGizmo = (_dec = ccclass('EmitterGizmo'), _dec2 = executeInEditMode(true), _dec(_class = _dec2(_class = (_class2 = class EmitterGizmo extends Component {
        constructor(...args) {
          super(...args);

          _initializerDefineProperty(this, "showRadius", _descriptor, this);

          _initializerDefineProperty(this, "showDirections", _descriptor2, this);

          _initializerDefineProperty(this, "showCenter", _descriptor3, this);

          _initializerDefineProperty(this, "radiusColor", _descriptor4, this);

          _initializerDefineProperty(this, "directionColor", _descriptor5, this);

          _initializerDefineProperty(this, "centerColor", _descriptor6, this);

          _initializerDefineProperty(this, "speedScale", _descriptor7, this);

          this.graphics = null;
          this.emitter = null;
        }

        onLoad() {
          if (!EDITOR) return; // Get or create Graphics component

          this.graphics = this.getComponent(Graphics) || this.addComponent(Graphics); // Get Emitter component (try EmitterArc first, then base Emitter)

          this.emitter = this.getComponent(_crd && EmitterArc === void 0 ? (_reportPossibleCrUseOfEmitterArc({
            error: Error()
          }), EmitterArc) : EmitterArc) || this.getComponent(_crd && Emitter === void 0 ? (_reportPossibleCrUseOfEmitter({
            error: Error()
          }), Emitter) : Emitter);

          if (!this.emitter) {
            console.warn('EmitterGizmo: No Emitter component found on this node');
          }
        }

        update() {
          if (!EDITOR || !this.graphics || !this.emitter) return;
          this.drawGizmos();
        }

        drawGizmos() {
          if (!this.graphics) return; // Clear previous drawings

          this.graphics.clear(); // Draw center point

          if (this.showCenter) {
            this.drawCenter();
          } // Draw radius circle (only for EmitterArc)


          if (this.showRadius && this.isEmitterArc() && this.emitter.radius > 0) {
            this.drawRadius();
          } // Draw direction arrows


          if (this.showDirections) {
            this.drawDirections();
          }
        }

        drawCenter() {
          if (!this.graphics) return;
          this.graphics.strokeColor = this.centerColor;
          this.graphics.lineWidth = 2;
          const centerSize = 8; // Draw cross at center

          this.graphics.moveTo(-centerSize, 0);
          this.graphics.lineTo(centerSize, 0);
          this.graphics.moveTo(0, -centerSize);
          this.graphics.lineTo(0, centerSize);
          this.graphics.stroke();
        }

        drawRadius() {
          if (!this.graphics || !this.isEmitterArc()) return;
          const emitterArc = this.emitter;
          this.graphics.strokeColor = this.radiusColor;
          this.graphics.lineWidth = 1; // Draw radius circle

          this.graphics.circle(0, 0, emitterArc.radius);
          this.graphics.stroke();
        }

        drawDirections() {
          if (!this.graphics || !this.emitter || this.emitter.count <= 0) return;
          this.graphics.strokeColor = this.directionColor;
          this.graphics.lineWidth = 2; // Check if this is an EmitterArc to access arc and angle properties

          if (this.isEmitterArc()) {
            this.drawArcDirections();
          } else {
            this.drawBasicDirections();
          }

          this.graphics.stroke();
        }

        drawArcDirections() {
          if (!this.graphics || !this.isEmitterArc()) return;
          const emitterArc = this.emitter; // Use arc property for spread calculation, angle for direction

          const baseDirection = emitterArc.angle || 0; // Base direction from angle property

          const totalArc = emitterArc.arc || 0; // Total arc to spread bullets across
          // Calculate angle per bullet based on arc and count

          const anglePerBullet = emitterArc.count > 1 ? totalArc / (emitterArc.count - 1) : 0;
          const startAngle = baseDirection - totalArc / 2; // Start from base direction minus half arc

          for (let i = 0; i < emitterArc.count; i++) {
            let bulletAngle;

            if (emitterArc.count === 1) {
              bulletAngle = baseDirection; // Single bullet goes in base direction
            } else {
              bulletAngle = startAngle + anglePerBullet * i;
            } // Convert angle to radians (0 degrees = up in Cocos Creator)


            const angleRad = (bulletAngle + 90) * Math.PI / 180; // Calculate direction vector

            const dirX = Math.cos(angleRad);
            const dirY = Math.sin(angleRad); // Start position (at radius distance from center)

            const startX = dirX * emitterArc.radius;
            const startY = dirY * emitterArc.radius; // Calculate arrow length based on speed multiplier
            // Base length of 30 pixels, scaled by speed multiplier and speedScale property

            const baseLength = 30;
            const speedFactor = emitterArc.speedMultiplier || 1; // Default to 1 if speedMultiplier is 0 or undefined

            const arrowLength = Math.max(baseLength, baseLength * speedFactor * this.speedScale);
            const endX = startX + dirX * arrowLength;
            const endY = startY + dirY * arrowLength; // Draw arrow line

            this.graphics.moveTo(startX, startY);
            this.graphics.lineTo(endX, endY); // Draw arrow head

            this.drawArrowHead(endX, endY, dirX, dirY);
          }
        }

        drawBasicDirections() {
          if (!this.graphics || !this.emitter) return; // For basic emitters, just draw a simple forward direction

          const baseLength = 30;
          const speedFactor = this.emitter.speedMultiplier || 1;
          const arrowLength = Math.max(baseLength, baseLength * speedFactor * this.speedScale);

          for (let i = 0; i < this.emitter.count; i++) {
            // Simple forward direction (up)
            const dirX = 0;
            const dirY = 1;
            const startX = 0;
            const startY = 0;
            const endX = dirX * arrowLength;
            const endY = dirY * arrowLength; // Draw arrow line

            this.graphics.moveTo(startX, startY);
            this.graphics.lineTo(endX, endY); // Draw arrow head

            this.drawArrowHead(endX, endY, dirX, dirY);
          }
        }
        /**
         * Check if the current emitter is an EmitterArc
         */


        isEmitterArc() {
          return this.emitter instanceof (_crd && EmitterArc === void 0 ? (_reportPossibleCrUseOfEmitterArc({
            error: Error()
          }), EmitterArc) : EmitterArc);
        }

        drawArrowHead(endX, endY, dirX, dirY) {
          if (!this.graphics) return;
          const arrowSize = 8; // Calculate arrow head points

          const arrowAngle = Math.PI / 6; // 30 degrees
          // Left arrow point

          const leftX = endX - arrowSize * (dirX * Math.cos(arrowAngle) - dirY * Math.sin(arrowAngle));
          const leftY = endY - arrowSize * (dirY * Math.cos(arrowAngle) + dirX * Math.sin(arrowAngle)); // Right arrow point

          const rightX = endX - arrowSize * (dirX * Math.cos(-arrowAngle) - dirY * Math.sin(-arrowAngle));
          const rightY = endY - arrowSize * (dirY * Math.cos(-arrowAngle) + dirX * Math.sin(-arrowAngle)); // Draw arrow head lines

          this.graphics.moveTo(endX, endY);
          this.graphics.lineTo(leftX, leftY);
          this.graphics.moveTo(endX, endY);
          this.graphics.lineTo(rightX, rightY);
        }

      }, (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "showRadius", [property], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return true;
        }
      }), _descriptor2 = _applyDecoratedDescriptor(_class2.prototype, "showDirections", [property], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return true;
        }
      }), _descriptor3 = _applyDecoratedDescriptor(_class2.prototype, "showCenter", [property], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return true;
        }
      }), _descriptor4 = _applyDecoratedDescriptor(_class2.prototype, "radiusColor", [property], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return Color.GRAY;
        }
      }), _descriptor5 = _applyDecoratedDescriptor(_class2.prototype, "directionColor", [property], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return Color.RED;
        }
      }), _descriptor6 = _applyDecoratedDescriptor(_class2.prototype, "centerColor", [property], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return Color.WHITE;
        }
      }), _descriptor7 = _applyDecoratedDescriptor(_class2.prototype, "speedScale", [property], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return 1.0;
        }
      })), _class2)) || _class) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=ef083c913ee08613bada8a2375e41d84691975a2.js.map