System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, instantiate, EnemyBullet, Global, PersistNode, GameFactory, EnemyBulletFactory, _crd, ccclass, property;

  function _reportPossibleCrUseOfEnemyBullet(extras) {
    _reporterNs.report("EnemyBullet", "../EnemyBullet", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGlobal(extras) {
    _reporterNs.report("Global", "../Global", _context.meta, extras);
  }

  function _reportPossibleCrUseOfPersistNode(extras) {
    _reporterNs.report("PersistNode", "../PersistNode", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameFactory(extras) {
    _reporterNs.report("GameFactory", "./GameFactory", _context.meta, extras);
  }

  _export("EnemyBulletFactory", void 0);

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      instantiate = _cc.instantiate;
    }, function (_unresolved_2) {
      EnemyBullet = _unresolved_2.EnemyBullet;
    }, function (_unresolved_3) {
      Global = _unresolved_3.Global;
    }, function (_unresolved_4) {
      PersistNode = _unresolved_4.PersistNode;
    }, function (_unresolved_5) {
      GameFactory = _unresolved_5.GameFactory;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "82bde69wYdIaYL4injqOKiI", "EnemyBulletFactory", undefined);

      __checkObsolete__(['_decorator', 'Component', 'Node', 'instantiate']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("EnemyBulletFactory", EnemyBulletFactory = class EnemyBulletFactory extends (_crd && GameFactory === void 0 ? (_reportPossibleCrUseOfGameFactory({
        error: Error()
      }), GameFactory) : GameFactory) {
        createProduct(productType) {
          let enemyBulletTemp = null;

          if (this.productPool.size() > 0) {
            enemyBulletTemp = this.productPool.get(); //如果池里有敌机子弹，就直接拿来用
          } else {
            enemyBulletTemp = instantiate(this.persistNode.getComponent(_crd && PersistNode === void 0 ? (_reportPossibleCrUseOfPersistNode({
              error: Error()
            }), PersistNode) : PersistNode).enemyBulletPreb); //从常驻节点拿到预制体原料
          }

          switch (productType) {
            case (_crd && Global === void 0 ? (_reportPossibleCrUseOfGlobal({
              error: Error()
            }), Global) : Global).ENEMY_BULLET_1:
              enemyBulletTemp.getComponent(_crd && EnemyBullet === void 0 ? (_reportPossibleCrUseOfEnemyBullet({
                error: Error()
              }), EnemyBullet) : EnemyBullet).init(productType, this.persistNode.getComponent(_crd && PersistNode === void 0 ? (_reportPossibleCrUseOfPersistNode({
                error: Error()
              }), PersistNode) : PersistNode).enemybullet1); //通过调用EnemyBullet的init方法，来创建子弹

              break;

            case (_crd && Global === void 0 ? (_reportPossibleCrUseOfGlobal({
              error: Error()
            }), Global) : Global).ENEMY_BULLET_2:
              enemyBulletTemp.getComponent(_crd && EnemyBullet === void 0 ? (_reportPossibleCrUseOfEnemyBullet({
                error: Error()
              }), EnemyBullet) : EnemyBullet).init(productType, this.persistNode.getComponent(_crd && PersistNode === void 0 ? (_reportPossibleCrUseOfPersistNode({
                error: Error()
              }), PersistNode) : PersistNode).enemybullet2);
              break;
          }

          return enemyBulletTemp;
        }

      });

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=b81146aaf158ea2edec76e5af0d70f6b9b37cda0.js.map