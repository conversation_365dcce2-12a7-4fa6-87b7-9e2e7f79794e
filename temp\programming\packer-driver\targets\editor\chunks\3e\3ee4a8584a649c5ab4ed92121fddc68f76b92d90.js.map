{"version": 3, "sources": ["file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/PlayerBulletFactory.ts"], "names": ["PlayerBulletFactory", "_decorator", "instantiate", "Global", "PersistNode", "<PERSON><PERSON><PERSON><PERSON>", "GameFactory", "ccclass", "property", "createProduct", "productType", "playBulletTemp", "productPool", "size", "get", "persistNode", "getComponent", "playerBulletPreb", "NORMAL_BULLET", "init", "normalBullet", "LIGHT_BULLET", "lightBullet", "MISSILE_BULLET", "missileBullet"], "mappings": ";;;wJAOaA,mB;;;;;;;;;;;;;;;;;;;;;;;;;;;AAPJC,MAAAA,U,OAAAA,U;AAA6BC,MAAAA,W,OAAAA,W;;AAC7BC,MAAAA,M,iBAAAA,M;;AACAC,MAAAA,W,iBAAAA,W;;AACAC,MAAAA,Y,iBAAAA,Y;;AACAC,MAAAA,W,iBAAAA,W;;;;;;;;;OACH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBP,U;;qCAEjBD,mB,GAAN,MAAMA,mBAAN;AAAA;AAAA,sCAA8C;AAE1CS,QAAAA,aAAa,CAACC,WAAD,EAA4B;AAC5C,cAAIC,cAAoB,GAAG,IAA3B;;AAEA,cAAI,KAAKC,WAAL,CAAiBC,IAAjB,KAA0B,CAA9B,EAAiC;AAC7BF,YAAAA,cAAc,GAAG,KAAKC,WAAL,CAAiBE,GAAjB,EAAjB,CAD6B,CACa;AAC7C,WAFD,MAEO;AACHH,YAAAA,cAAc,GAAGT,WAAW,CAAC,KAAKa,WAAL,CAAiBC,YAAjB;AAAA;AAAA,4CAA2CC,gBAA5C,CAA5B,CADG,CACyF;AAC/F;;AAED,kBAAOP,WAAP;AACI,iBAAK;AAAA;AAAA,kCAAOQ,aAAZ;AACIP,cAAAA,cAAc,CAACK,YAAf;AAAA;AAAA,gDAA0CG,IAA1C,CAA+CT,WAA/C,EAA4D,KAAKK,WAAL,CAAiBC,YAAjB;AAAA;AAAA,8CAA2CI,YAAvG,EADJ,CAC2H;;AACvH;;AACJ,iBAAK;AAAA;AAAA,kCAAOC,YAAZ;AACIV,cAAAA,cAAc,CAACK,YAAf;AAAA;AAAA,gDAA0CG,IAA1C,CAA+CT,WAA/C,EAA4D,KAAKK,WAAL,CAAiBC,YAAjB;AAAA;AAAA,8CAA2CM,WAAvG;AACA;;AACJ,iBAAK;AAAA;AAAA,kCAAOC,cAAZ;AACIZ,cAAAA,cAAc,CAACK,YAAf;AAAA;AAAA,gDAA0CG,IAA1C,CAA+CT,WAA/C,EAA4D,KAAKK,WAAL,CAAiBC,YAAjB;AAAA;AAAA,8CAA2CQ,aAAvG;AACA;AATR;;AAYA,iBAAOb,cAAP;AACH;;AAxBgD,O", "sourcesContent": ["import { _decorator, Component, Node, instantiate } from 'cc';\nimport { Global } from '../Global';\nimport { PersistNode } from '../PersistNode';\nimport { PlayerBullet } from '../PlayerBullet';\nimport { GameFactory } from './GameFactory';\nconst { ccclass, property } = _decorator;\n\nexport class PlayerBulletFactory extends GameFactory {\n\n    public createProduct(productType: string): Node {\n        let playBulletTemp: Node = null;\n\n        if (this.productPool.size() > 0) {\n            playBulletTemp = this.productPool.get();  //如果池里有子弹，就直接拿来用\n        } else {\n            playBulletTemp = instantiate(this.persistNode.getComponent(PersistNode).playerBulletPreb);  //从常驻节点拿到预制体原料\n        }\n        \n        switch(productType) {\n            case Global.NORMAL_BULLET:\n                playBulletTemp.getComponent(PlayerBullet).init(productType, this.persistNode.getComponent(PersistNode).normalBullet);  //通过调用PlayerBullet的init方法，来创建子弹\n                break;\n            case Global.LIGHT_BULLET:\n                playBulletTemp.getComponent(PlayerBullet).init(productType, this.persistNode.getComponent(PersistNode).lightBullet);\n                break;\n            case Global.MISSILE_BULLET:\n                playBulletTemp.getComponent(PlayerBullet).init(productType, this.persistNode.getComponent(PersistNode).missileBullet);\n                break;\n        }\n\n        return playBulletTemp;\n    }\n    \n}\n\n"]}