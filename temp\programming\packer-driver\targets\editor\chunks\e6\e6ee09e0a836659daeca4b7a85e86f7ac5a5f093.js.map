{"version": 3, "sources": ["file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Network/NetMgr.ts"], "names": ["_decorator", "IMgr", "ccclass", "property", "NetMgr", "init"], "mappings": ";;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;;AAEAC,MAAAA,I,iBAAAA,I;;;;;;;;;OADH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBH,U;;wBAGjBI,M,WADZF,OAAO,CAAC,QAAD,C,gBAAR,MACaE,MADb;AAAA;AAAA,wBACiC;AAC7BC,QAAAA,IAAI,GAAS;AACT,gBAAMA,IAAN;AACH;;AAH4B,O", "sourcesContent": ["import { _decorator, Component, Node, JsonAsset, AssetManager, resources } from \"cc\";\nconst { ccclass, property } = _decorator;\nimport { IMgr } from '../IMgr';\n@ccclass(\"NetMgr\")\nexport class NetMgr extends IMgr {\n    init(): void {\n        super.init();\n    }\n}"]}