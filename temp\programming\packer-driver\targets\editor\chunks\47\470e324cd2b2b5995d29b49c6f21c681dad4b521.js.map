{"version": 3, "sources": ["file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/Menu.ts"], "names": ["_decorator", "Component", "director", "ccclass", "property", "<PERSON><PERSON>", "onClicked", "event", "cutomData", "loadScene"], "mappings": ";;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAiBC,MAAAA,Q,OAAAA,Q;;;;;;;;;OAChC;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBJ,U;;sBAGjBK,I,WADZF,OAAO,CAAC,MAAD,C,gBAAR,MACaE,IADb,SAC0BJ,SAD1B,CACoC;AAEhCK,QAAAA,SAAS,CAACC,KAAD,EAAeC,SAAf,EAAkC;AACvCN,UAAAA,QAAQ,CAACO,SAAT,CAAmB,MAAnB;AACH;;AAJ+B,O", "sourcesContent": ["import { _decorator, Component, Node, director } from 'cc';\nconst { ccclass, property } = _decorator;\n\n@ccclass('Menu')\nexport class Menu extends Component {\n    \n    onClicked(event: Event, cutomData: string) {\n        director.loadScene(\"game\");\n    }\n}\n\n"]}