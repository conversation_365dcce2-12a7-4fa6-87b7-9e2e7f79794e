{"version": 3, "sources": ["file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/gizmos/EmitterGizmo.ts"], "names": ["_decorator", "Component", "Color", "Graphics", "EDITOR", "Emitter", "EmitterArc", "ccclass", "property", "executeInEditMode", "EmitterGizmo", "graphics", "emitter", "onLoad", "getComponent", "addComponent", "console", "warn", "update", "drawGizmos", "clear", "showCenter", "drawCenter", "showRadius", "isEmitterArc", "radius", "drawRadius", "showDirections", "drawDirections", "strokeColor", "centerColor", "lineWidth", "centerSize", "moveTo", "lineTo", "stroke", "emitterArc", "radiusColor", "circle", "count", "directionColor", "drawArcDirections", "drawBasicDirections", "baseDirection", "angle", "totalArc", "arc", "anglePerBullet", "startAngle", "i", "bulletAngle", "angleRad", "Math", "PI", "dirX", "cos", "dirY", "sin", "startX", "startY", "baseLength", "speedFactor", "speedMultiplier", "<PERSON><PERSON><PERSON><PERSON>", "max", "speedScale", "endX", "endY", "drawArrowHead", "arrowSize", "arrowAngle", "leftX", "leftY", "rightX", "rightY", "GRAY", "RED", "WHITE"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAMSA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,Q,OAAAA,Q;;AAC9BC,MAAAA,M,UAAAA,M;;AACAC,MAAAA,O,iBAAAA,O;;AACAC,MAAAA,U,iBAAAA,U;;;;;;AATT;AACA;AACA;AACA;AACA;;;;;OAMM;AAAEC,QAAAA,OAAF;AAAWC,QAAAA,QAAX;AAAqBC,QAAAA;AAArB,O,GAA2CT,U;;8BAIpCU,Y,WAFZH,OAAO,CAAC,cAAD,C,UACPE,iBAAiB,CAAC,IAAD,C,0CADlB,MAEaC,YAFb,SAEkCT,SAFlC,CAE4C;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA,eAuBhCU,QAvBgC,GAuBJ,IAvBI;AAAA,eAwBhCC,OAxBgC,GAwBN,IAxBM;AAAA;;AA0B9BC,QAAAA,MAAM,GAAS;AACrB,cAAI,CAACT,MAAL,EAAa,OADQ,CAGrB;;AACA,eAAKO,QAAL,GAAgB,KAAKG,YAAL,CAAkBX,QAAlB,KAA+B,KAAKY,YAAL,CAAkBZ,QAAlB,CAA/C,CAJqB,CAMrB;;AACA,eAAKS,OAAL,GAAe,KAAKE,YAAL;AAAA;AAAA,2CAAiC,KAAKA,YAAL;AAAA;AAAA,iCAAhD;;AAEA,cAAI,CAAC,KAAKF,OAAV,EAAmB;AACfI,YAAAA,OAAO,CAACC,IAAR,CAAa,uDAAb;AACH;AACJ;;AAESC,QAAAA,MAAM,GAAS;AACrB,cAAI,CAACd,MAAD,IAAW,CAAC,KAAKO,QAAjB,IAA6B,CAAC,KAAKC,OAAvC,EAAgD;AAEhD,eAAKO,UAAL;AACH;;AAEOA,QAAAA,UAAU,GAAS;AACvB,cAAI,CAAC,KAAKR,QAAV,EAAoB,OADG,CAGvB;;AACA,eAAKA,QAAL,CAAcS,KAAd,GAJuB,CAMvB;;AACA,cAAI,KAAKC,UAAT,EAAqB;AACjB,iBAAKC,UAAL;AACH,WATsB,CAWvB;;;AACA,cAAI,KAAKC,UAAL,IAAmB,KAAKC,YAAL,EAAnB,IAA2C,KAAKZ,OAAN,CAA6Ba,MAA7B,GAAsC,CAApF,EAAuF;AACnF,iBAAKC,UAAL;AACH,WAdsB,CAgBvB;;;AACA,cAAI,KAAKC,cAAT,EAAyB;AACrB,iBAAKC,cAAL;AACH;AACJ;;AAEON,QAAAA,UAAU,GAAS;AACvB,cAAI,CAAC,KAAKX,QAAV,EAAoB;AAEpB,eAAKA,QAAL,CAAckB,WAAd,GAA4B,KAAKC,WAAjC;AACA,eAAKnB,QAAL,CAAcoB,SAAd,GAA0B,CAA1B;AAEA,cAAMC,UAAU,GAAG,CAAnB,CANuB,CAQvB;;AACA,eAAKrB,QAAL,CAAcsB,MAAd,CAAqB,CAACD,UAAtB,EAAkC,CAAlC;AACA,eAAKrB,QAAL,CAAcuB,MAAd,CAAqBF,UAArB,EAAiC,CAAjC;AACA,eAAKrB,QAAL,CAAcsB,MAAd,CAAqB,CAArB,EAAwB,CAACD,UAAzB;AACA,eAAKrB,QAAL,CAAcuB,MAAd,CAAqB,CAArB,EAAwBF,UAAxB;AACA,eAAKrB,QAAL,CAAcwB,MAAd;AACH;;AAEOT,QAAAA,UAAU,GAAS;AACvB,cAAI,CAAC,KAAKf,QAAN,IAAkB,CAAC,KAAKa,YAAL,EAAvB,EAA4C;AAE5C,cAAMY,UAAU,GAAG,KAAKxB,OAAxB;AACA,eAAKD,QAAL,CAAckB,WAAd,GAA4B,KAAKQ,WAAjC;AACA,eAAK1B,QAAL,CAAcoB,SAAd,GAA0B,CAA1B,CALuB,CAOvB;;AACA,eAAKpB,QAAL,CAAc2B,MAAd,CAAqB,CAArB,EAAwB,CAAxB,EAA2BF,UAAU,CAACX,MAAtC;AACA,eAAKd,QAAL,CAAcwB,MAAd;AACH;;AAEOP,QAAAA,cAAc,GAAS;AAC3B,cAAI,CAAC,KAAKjB,QAAN,IAAkB,CAAC,KAAKC,OAAxB,IAAmC,KAAKA,OAAL,CAAa2B,KAAb,IAAsB,CAA7D,EAAgE;AAEhE,eAAK5B,QAAL,CAAckB,WAAd,GAA4B,KAAKW,cAAjC;AACA,eAAK7B,QAAL,CAAcoB,SAAd,GAA0B,CAA1B,CAJ2B,CAM3B;;AACA,cAAI,KAAKP,YAAL,EAAJ,EAAyB;AACrB,iBAAKiB,iBAAL;AACH,WAFD,MAEO;AACH,iBAAKC,mBAAL;AACH;;AAED,eAAK/B,QAAL,CAAcwB,MAAd;AACH;;AAEOM,QAAAA,iBAAiB,GAAS;AAC9B,cAAI,CAAC,KAAK9B,QAAN,IAAkB,CAAC,KAAKa,YAAL,EAAvB,EAA4C;AAE5C,cAAMY,UAAU,GAAG,KAAKxB,OAAxB,CAH8B,CAK9B;;AACA,cAAM+B,aAAa,GAAGP,UAAU,CAACQ,KAAX,IAAoB,CAA1C,CAN8B,CAMe;;AAC7C,cAAMC,QAAQ,GAAGT,UAAU,CAACU,GAAX,IAAkB,CAAnC,CAP8B,CAOQ;AAEtC;;AACA,cAAMC,cAAc,GAAGX,UAAU,CAACG,KAAX,GAAmB,CAAnB,GAAuBM,QAAQ,IAAIT,UAAU,CAACG,KAAX,GAAmB,CAAvB,CAA/B,GAA2D,CAAlF;AACA,cAAMS,UAAU,GAAGL,aAAa,GAAGE,QAAQ,GAAG,CAA9C,CAX8B,CAWmB;;AAEjD,eAAK,IAAII,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGb,UAAU,CAACG,KAA/B,EAAsCU,CAAC,EAAvC,EAA2C;AACvC,gBAAIC,WAAmB,SAAvB;;AAEA,gBAAId,UAAU,CAACG,KAAX,KAAqB,CAAzB,EAA4B;AACxBW,cAAAA,WAAW,GAAGP,aAAd,CADwB,CACK;AAChC,aAFD,MAEO;AACHO,cAAAA,WAAW,GAAGF,UAAU,GAAID,cAAc,GAAGE,CAA7C;AACH,aAPsC,CASvC;;;AACA,gBAAME,QAAQ,GAAG,CAACD,WAAW,GAAG,EAAf,IAAqBE,IAAI,CAACC,EAA1B,GAA+B,GAAhD,CAVuC,CAYvC;;AACA,gBAAMC,IAAI,GAAGF,IAAI,CAACG,GAAL,CAASJ,QAAT,CAAb;AACA,gBAAMK,IAAI,GAAGJ,IAAI,CAACK,GAAL,CAASN,QAAT,CAAb,CAduC,CAgBvC;;AACA,gBAAMO,MAAM,GAAGJ,IAAI,GAAGlB,UAAU,CAACX,MAAjC;AACA,gBAAMkC,MAAM,GAAGH,IAAI,GAAGpB,UAAU,CAACX,MAAjC,CAlBuC,CAoBvC;AACA;;AACA,gBAAMmC,UAAU,GAAG,EAAnB;AACA,gBAAMC,WAAW,GAAGzB,UAAU,CAAC0B,eAAX,IAA8B,CAAlD,CAvBuC,CAuBc;;AACrD,gBAAMC,WAAW,GAAGX,IAAI,CAACY,GAAL,CAASJ,UAAT,EAAqBA,UAAU,GAAGC,WAAb,GAA2B,KAAKI,UAArD,CAApB;AAEA,gBAAMC,IAAI,GAAGR,MAAM,GAAGJ,IAAI,GAAGS,WAA7B;AACA,gBAAMI,IAAI,GAAGR,MAAM,GAAGH,IAAI,GAAGO,WAA7B,CA3BuC,CA6BvC;;AACA,iBAAKpD,QAAL,CAAcsB,MAAd,CAAqByB,MAArB,EAA6BC,MAA7B;AACA,iBAAKhD,QAAL,CAAcuB,MAAd,CAAqBgC,IAArB,EAA2BC,IAA3B,EA/BuC,CAiCvC;;AACA,iBAAKC,aAAL,CAAmBF,IAAnB,EAAyBC,IAAzB,EAA+Bb,IAA/B,EAAqCE,IAArC;AACH;AACJ;;AAEOd,QAAAA,mBAAmB,GAAS;AAChC,cAAI,CAAC,KAAK/B,QAAN,IAAkB,CAAC,KAAKC,OAA5B,EAAqC,OADL,CAGhC;;AACA,cAAMgD,UAAU,GAAG,EAAnB;AACA,cAAMC,WAAW,GAAG,KAAKjD,OAAL,CAAakD,eAAb,IAAgC,CAApD;AACA,cAAMC,WAAW,GAAGX,IAAI,CAACY,GAAL,CAASJ,UAAT,EAAqBA,UAAU,GAAGC,WAAb,GAA2B,KAAKI,UAArD,CAApB;;AAEA,eAAK,IAAIhB,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKrC,OAAL,CAAa2B,KAAjC,EAAwCU,CAAC,EAAzC,EAA6C;AACzC;AACA,gBAAMK,IAAI,GAAG,CAAb;AACA,gBAAME,IAAI,GAAG,CAAb;AAEA,gBAAME,MAAM,GAAG,CAAf;AACA,gBAAMC,MAAM,GAAG,CAAf;AACA,gBAAMO,IAAI,GAAGZ,IAAI,GAAGS,WAApB;AACA,gBAAMI,IAAI,GAAGX,IAAI,GAAGO,WAApB,CARyC,CAUzC;;AACA,iBAAKpD,QAAL,CAAcsB,MAAd,CAAqByB,MAArB,EAA6BC,MAA7B;AACA,iBAAKhD,QAAL,CAAcuB,MAAd,CAAqBgC,IAArB,EAA2BC,IAA3B,EAZyC,CAczC;;AACA,iBAAKC,aAAL,CAAmBF,IAAnB,EAAyBC,IAAzB,EAA+Bb,IAA/B,EAAqCE,IAArC;AACH;AACJ;AAED;AACJ;AACA;;;AACYhC,QAAAA,YAAY,GAAY;AAC5B,iBAAO,KAAKZ,OAAL;AAAA;AAAA,uCAAP;AACH;;AAEOwD,QAAAA,aAAa,CAACF,IAAD,EAAeC,IAAf,EAA6Bb,IAA7B,EAA2CE,IAA3C,EAA+D;AAChF,cAAI,CAAC,KAAK7C,QAAV,EAAoB;AAEpB,cAAM0D,SAAS,GAAG,CAAlB,CAHgF,CAKhF;;AACA,cAAMC,UAAU,GAAGlB,IAAI,CAACC,EAAL,GAAU,CAA7B,CANgF,CAMhD;AAEhC;;AACA,cAAMkB,KAAK,GAAGL,IAAI,GAAGG,SAAS,IAAIf,IAAI,GAAGF,IAAI,CAACG,GAAL,CAASe,UAAT,CAAP,GAA8Bd,IAAI,GAAGJ,IAAI,CAACK,GAAL,CAASa,UAAT,CAAzC,CAA9B;AACA,cAAME,KAAK,GAAGL,IAAI,GAAGE,SAAS,IAAIb,IAAI,GAAGJ,IAAI,CAACG,GAAL,CAASe,UAAT,CAAP,GAA8BhB,IAAI,GAAGF,IAAI,CAACK,GAAL,CAASa,UAAT,CAAzC,CAA9B,CAVgF,CAYhF;;AACA,cAAMG,MAAM,GAAGP,IAAI,GAAGG,SAAS,IAAIf,IAAI,GAAGF,IAAI,CAACG,GAAL,CAAS,CAACe,UAAV,CAAP,GAA+Bd,IAAI,GAAGJ,IAAI,CAACK,GAAL,CAAS,CAACa,UAAV,CAA1C,CAA/B;AACA,cAAMI,MAAM,GAAGP,IAAI,GAAGE,SAAS,IAAIb,IAAI,GAAGJ,IAAI,CAACG,GAAL,CAAS,CAACe,UAAV,CAAP,GAA+BhB,IAAI,GAAGF,IAAI,CAACK,GAAL,CAAS,CAACa,UAAV,CAA1C,CAA/B,CAdgF,CAgBhF;;AACA,eAAK3D,QAAL,CAAcsB,MAAd,CAAqBiC,IAArB,EAA2BC,IAA3B;AACA,eAAKxD,QAAL,CAAcuB,MAAd,CAAqBqC,KAArB,EAA4BC,KAA5B;AACA,eAAK7D,QAAL,CAAcsB,MAAd,CAAqBiC,IAArB,EAA2BC,IAA3B;AACA,eAAKxD,QAAL,CAAcuB,MAAd,CAAqBuC,MAArB,EAA6BC,MAA7B;AACH;;AA1NuC,O,6EAEvClE,Q;;;;;iBAC4B,I;;yFAE5BA,Q;;;;;iBACgC,I;;qFAEhCA,Q;;;;;iBAC4B,I;;sFAE5BA,Q;;;;;iBAC2BN,KAAK,CAACyE,I;;yFAEjCnE,Q;;;;;iBAC8BN,KAAK,CAAC0E,G;;sFAEpCpE,Q;;;;;iBAC2BN,KAAK,CAAC2E,K;;qFAEjCrE,Q;;;;;iBAC2B,G", "sourcesContent": ["/**\n * Emitter Gizmo Component\n * This component provides visual debugging for Emitter components in the scene view\n * It should be added to the same node as the Emitter component\n */\n\nimport { _decorator, Component, Color, Graphics } from 'cc';\nimport { EDITOR } from 'cc/env';\nimport { Emitter } from '../assets/scripts/Game/world/bullet/Emitter';\nimport { EmitterArc } from '../assets/scripts/Game/world/bullet/EmitterArc';\nconst { ccclass, property, executeInEditMode } = _decorator;\n\n@ccclass('EmitterGizmo')\n@executeInEditMode(true)\nexport class EmitterGizmo extends Component {\n\n    @property\n    public showRadius: boolean = true;\n\n    @property\n    public showDirections: boolean = true;\n\n    @property\n    public showCenter: boolean = true;\n\n    @property\n    public radiusColor: Color = Color.GRAY;\n\n    @property\n    public directionColor: Color = Color.RED;\n\n    @property\n    public centerColor: Color = Color.WHITE;\n\n    @property\n    public speedScale: number = 1.0;\n\n    private graphics: Graphics | null = null;\n    private emitter: Emitter | null = null;\n\n    protected onLoad(): void {\n        if (!EDITOR) return;\n\n        // Get or create Graphics component\n        this.graphics = this.getComponent(Graphics) || this.addComponent(Graphics);\n\n        // Get Emitter component (try EmitterArc first, then base Emitter)\n        this.emitter = this.getComponent(EmitterArc) || this.getComponent(Emitter);\n\n        if (!this.emitter) {\n            console.warn('EmitterGizmo: No Emitter component found on this node');\n        }\n    }\n\n    protected update(): void {\n        if (!EDITOR || !this.graphics || !this.emitter) return;\n\n        this.drawGizmos();\n    }\n\n    private drawGizmos(): void {\n        if (!this.graphics) return;\n\n        // Clear previous drawings\n        this.graphics.clear();\n\n        // Draw center point\n        if (this.showCenter) {\n            this.drawCenter();\n        }\n\n        // Draw radius circle (only for EmitterArc)\n        if (this.showRadius && this.isEmitterArc() && (this.emitter as EmitterArc).radius > 0) {\n            this.drawRadius();\n        }\n\n        // Draw direction arrows\n        if (this.showDirections) {\n            this.drawDirections();\n        }\n    }\n\n    private drawCenter(): void {\n        if (!this.graphics) return;\n\n        this.graphics.strokeColor = this.centerColor;\n        this.graphics.lineWidth = 2;\n\n        const centerSize = 8;\n\n        // Draw cross at center\n        this.graphics.moveTo(-centerSize, 0);\n        this.graphics.lineTo(centerSize, 0);\n        this.graphics.moveTo(0, -centerSize);\n        this.graphics.lineTo(0, centerSize);\n        this.graphics.stroke();\n    }\n\n    private drawRadius(): void {\n        if (!this.graphics || !this.isEmitterArc()) return;\n\n        const emitterArc = this.emitter as EmitterArc;\n        this.graphics.strokeColor = this.radiusColor;\n        this.graphics.lineWidth = 1;\n\n        // Draw radius circle\n        this.graphics.circle(0, 0, emitterArc.radius);\n        this.graphics.stroke();\n    }\n\n    private drawDirections(): void {\n        if (!this.graphics || !this.emitter || this.emitter.count <= 0) return;\n\n        this.graphics.strokeColor = this.directionColor;\n        this.graphics.lineWidth = 2;\n\n        // Check if this is an EmitterArc to access arc and angle properties\n        if (this.isEmitterArc()) {\n            this.drawArcDirections();\n        } else {\n            this.drawBasicDirections();\n        }\n\n        this.graphics.stroke();\n    }\n\n    private drawArcDirections(): void {\n        if (!this.graphics || !this.isEmitterArc()) return;\n\n        const emitterArc = this.emitter as EmitterArc;\n\n        // Use arc property for spread calculation, angle for direction\n        const baseDirection = emitterArc.angle || 0; // Base direction from angle property\n        const totalArc = emitterArc.arc || 0; // Total arc to spread bullets across\n\n        // Calculate angle per bullet based on arc and count\n        const anglePerBullet = emitterArc.count > 1 ? totalArc / (emitterArc.count - 1) : 0;\n        const startAngle = baseDirection - totalArc / 2; // Start from base direction minus half arc\n\n        for (let i = 0; i < emitterArc.count; i++) {\n            let bulletAngle: number;\n\n            if (emitterArc.count === 1) {\n                bulletAngle = baseDirection; // Single bullet goes in base direction\n            } else {\n                bulletAngle = startAngle + (anglePerBullet * i);\n            }\n\n            // Convert angle to radians (0 degrees = up in Cocos Creator)\n            const angleRad = (bulletAngle + 90) * Math.PI / 180;\n\n            // Calculate direction vector\n            const dirX = Math.cos(angleRad);\n            const dirY = Math.sin(angleRad);\n\n            // Start position (at radius distance from center)\n            const startX = dirX * emitterArc.radius;\n            const startY = dirY * emitterArc.radius;\n\n            // Calculate arrow length based on speed multiplier\n            // Base length of 30 pixels, scaled by speed multiplier and speedScale property\n            const baseLength = 30;\n            const speedFactor = emitterArc.speedMultiplier || 1; // Default to 1 if speedMultiplier is 0 or undefined\n            const arrowLength = Math.max(baseLength, baseLength * speedFactor * this.speedScale);\n\n            const endX = startX + dirX * arrowLength;\n            const endY = startY + dirY * arrowLength;\n\n            // Draw arrow line\n            this.graphics.moveTo(startX, startY);\n            this.graphics.lineTo(endX, endY);\n\n            // Draw arrow head\n            this.drawArrowHead(endX, endY, dirX, dirY);\n        }\n    }\n\n    private drawBasicDirections(): void {\n        if (!this.graphics || !this.emitter) return;\n\n        // For basic emitters, just draw a simple forward direction\n        const baseLength = 30;\n        const speedFactor = this.emitter.speedMultiplier || 1;\n        const arrowLength = Math.max(baseLength, baseLength * speedFactor * this.speedScale);\n\n        for (let i = 0; i < this.emitter.count; i++) {\n            // Simple forward direction (up)\n            const dirX = 0;\n            const dirY = 1;\n\n            const startX = 0;\n            const startY = 0;\n            const endX = dirX * arrowLength;\n            const endY = dirY * arrowLength;\n\n            // Draw arrow line\n            this.graphics.moveTo(startX, startY);\n            this.graphics.lineTo(endX, endY);\n\n            // Draw arrow head\n            this.drawArrowHead(endX, endY, dirX, dirY);\n        }\n    }\n\n    /**\n     * Check if the current emitter is an EmitterArc\n     */\n    private isEmitterArc(): boolean {\n        return this.emitter instanceof EmitterArc;\n    }\n\n    private drawArrowHead(endX: number, endY: number, dirX: number, dirY: number): void {\n        if (!this.graphics) return;\n\n        const arrowSize = 8;\n\n        // Calculate arrow head points\n        const arrowAngle = Math.PI / 6; // 30 degrees\n\n        // Left arrow point\n        const leftX = endX - arrowSize * (dirX * Math.cos(arrowAngle) - dirY * Math.sin(arrowAngle));\n        const leftY = endY - arrowSize * (dirY * Math.cos(arrowAngle) + dirX * Math.sin(arrowAngle));\n\n        // Right arrow point\n        const rightX = endX - arrowSize * (dirX * Math.cos(-arrowAngle) - dirY * Math.sin(-arrowAngle));\n        const rightY = endY - arrowSize * (dirY * Math.cos(-arrowAngle) + dirX * Math.sin(-arrowAngle));\n\n        // Draw arrow head lines\n        this.graphics.moveTo(endX, endY);\n        this.graphics.lineTo(leftX, leftY);\n        this.graphics.moveTo(endX, endY);\n        this.graphics.lineTo(rightX, rightY);\n    }\n}\n"]}