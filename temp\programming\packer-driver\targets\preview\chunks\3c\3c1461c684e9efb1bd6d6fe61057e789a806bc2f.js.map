{"version": 3, "sources": ["file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/world/base/System.ts"], "names": ["_decorator", "TypedBase", "ccclass", "System", "_isInitialized", "_isEnabled", "init", "console", "warn", "getTypeName", "onInit", "unInit", "onUnInit", "update", "deltaTime", "onUpdate", "lateUpdate", "onLateUpdate", "setEnabled", "enabled", "isEnabled", "isInitialized"], "mappings": ";;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;;AACAC,MAAAA,S,iBAAAA,S;;;;;;;;;OACH;AAAEC,QAAAA;AAAF,O,GAAcF,U;AAEpB;AACA;AACA;AACA;;wBAEsBG,M,WADrBD,OAAO,CAAC,QAAD,C,gBAAR,MACsBC,MADtB;AAAA;AAAA,kCAC+C;AAAA;AAAA;AAAA,eAEjCC,cAFiC,GAEP,KAFO;AAAA,eAGjCC,UAHiC,GAGX,IAHW;AAAA;;AAK3C;AACJ;AACA;AACA;AACWC,QAAAA,IAAI,GAAS;AAChB,cAAI,KAAKF,cAAT,EAAyB;AACrBG,YAAAA,OAAO,CAACC,IAAR,aAAuB,KAAKC,WAAL,EAAvB;AACA;AACH;;AAED,eAAKC,MAAL;AACA,eAAKN,cAAL,GAAsB,IAAtB;AACH;AAED;AACJ;AACA;AACA;;;AACWO,QAAAA,MAAM,GAAS;AAClB,cAAI,CAAC,KAAKP,cAAV,EAA0B;AACtB;AACH;;AAED,eAAKQ,QAAL;AACA,eAAKR,cAAL,GAAsB,KAAtB;AACH;AAED;AACJ;AACA;AACA;AACA;;;AACWS,QAAAA,MAAM,CAACC,SAAD,EAA0B;AACnC,cAAI,CAAC,KAAKV,cAAN,IAAwB,CAAC,KAAKC,UAAlC,EAA8C;AAC1C;AACH;;AAED,eAAKU,QAAL,CAAcD,SAAd;AACH;AAED;AACJ;AACA;AACA;AACA;;;AACWE,QAAAA,UAAU,CAACF,SAAD,EAA0B;AACvC,cAAI,CAAC,KAAKV,cAAN,IAAwB,CAAC,KAAKC,UAAlC,EAA8C;AAC1C;AACH;;AAED,eAAKY,YAAL,CAAkBH,SAAlB;AACH;AAED;AACJ;AACA;AACA;;;AACWI,QAAAA,UAAU,CAACC,OAAD,EAAyB;AACtC,eAAKd,UAAL,GAAkBc,OAAlB;AACH;AAED;AACJ;AACA;;;AACWC,QAAAA,SAAS,GAAY;AACxB,iBAAO,KAAKf,UAAZ;AACH;AAED;AACJ;AACA;;;AACWgB,QAAAA,aAAa,GAAY;AAC5B,iBAAO,KAAKjB,cAAZ;AACH;AAED;AACJ;AACA;;AAGI;AACJ;AACA;;AAGI;AACJ;AACA;AACA;;AAGI;AACJ;AACA;AACA;;;AAnG+C,O", "sourcesContent": ["import { _decorator } from \"cc\";\r\nimport { TypedBase, TypeID } from \"./TypeID\";\r\nconst { ccclass } = _decorator;\r\n\r\n/**\r\n * Base System class for all game systems\r\n * Follows the same pattern as IMgr but specifically designed for game world systems\r\n */\r\n@ccclass(\"System\")\r\nexport abstract class System extends TypedBase {\r\n\r\n    protected _isInitialized: boolean = false;\r\n    protected _isEnabled: boolean = true;\r\n\r\n    /**\r\n     * Initialize the system\r\n     * Called once when the system is registered to the world\r\n     */\r\n    public init(): void {\r\n        if (this._isInitialized) {\r\n            console.warn(`System ${this.getTypeName()} is already initialized`);\r\n            return;\r\n        }\r\n\r\n        this.onInit();\r\n        this._isInitialized = true;\r\n    }\r\n\r\n    /**\r\n     * Cleanup the system\r\n     * Called when the system is removed or world is destroyed\r\n     */\r\n    public unInit(): void {\r\n        if (!this._isInitialized) {\r\n            return;\r\n        }\r\n\r\n        this.onUnInit();\r\n        this._isInitialized = false;\r\n    }\r\n\r\n    /**\r\n     * Update the system\r\n     * Called every frame when the system is enabled\r\n     * @param deltaTime Time elapsed since last frame in seconds\r\n     */\r\n    public update(deltaTime: number): void {\r\n        if (!this._isInitialized || !this._isEnabled) {\r\n            return;\r\n        }\r\n\r\n        this.onUpdate(deltaTime);\r\n    }\r\n\r\n    /**\r\n     * Late update the system\r\n     * Called after all systems have been updated\r\n     * @param deltaTime Time elapsed since last frame in seconds\r\n     */\r\n    public lateUpdate(deltaTime: number): void {\r\n        if (!this._isInitialized || !this._isEnabled) {\r\n            return;\r\n        }\r\n\r\n        this.onLateUpdate(deltaTime);\r\n    }\r\n\r\n    /**\r\n     * Enable or disable the system\r\n     * Disabled systems will not receive update calls\r\n     */\r\n    public setEnabled(enabled: boolean): void {\r\n        this._isEnabled = enabled;\r\n    }\r\n\r\n    /**\r\n     * Check if the system is enabled\r\n     */\r\n    public isEnabled(): boolean {\r\n        return this._isEnabled;\r\n    }\r\n\r\n    /**\r\n     * Check if the system is initialized\r\n     */\r\n    public isInitialized(): boolean {\r\n        return this._isInitialized;\r\n    }\r\n\r\n    /**\r\n     * Override this method to implement system initialization logic\r\n     */\r\n    protected abstract onInit(): void;\r\n\r\n    /**\r\n     * Override this method to implement system cleanup logic\r\n     */\r\n    protected abstract onUnInit(): void;\r\n\r\n    /**\r\n     * Override this method to implement system update logic\r\n     * @param deltaTime Time elapsed since last frame in seconds\r\n     */\r\n    protected abstract onUpdate(deltaTime: number): void;\r\n\r\n    /**\r\n     * Override this method to implement system late update logic\r\n     * @param deltaTime Time elapsed since last frame in seconds\r\n     */\r\n    protected abstract onLateUpdate(deltaTime: number): void;\r\n}"]}