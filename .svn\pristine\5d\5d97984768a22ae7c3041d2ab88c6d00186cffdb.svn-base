/**
 * CrazyStorm 2.0 Integration for Cocos Creator
 * Resource Manager
 * 
 * This manager handles loading and caching of images, sounds, and other
 * resources referenced by CrazyStorm files.
 */

import { resources, Texture2D, AudioClip, SpriteFrame, log, warn, error } from 'cc';
import { CrazyStormFile, FileResource } from '../core/CrazyStormTypes';

/**
 * Resource cache entry
 */
interface ResourceCacheEntry {
    id: number;
    path: string;
    asset: any;
    loadTime: number;
    accessCount: number;
    lastAccess: number;
}

/**
 * Resource loading options
 */
export interface ResourceLoadOptions {
    preload: boolean;
    cache: boolean;
    timeout: number;
}

/**
 * Manager for CrazyStorm resources
 */
export class ResourceManager {
    private imageCache: Map<number, ResourceCacheEntry> = new Map();
    private soundCache: Map<number, ResourceCacheEntry> = new Map();
    private spriteFrameCache: Map<number, SpriteFrame> = new Map();
    
    private loadingPromises: Map<string, Promise<any>> = new Map();
    private maxCacheSize: number = 100;
    private cacheTimeout: number = 300000; // 5 minutes

    /**
     * Initialize from CrazyStorm file
     */
    public async initializeFromFile(file: CrazyStormFile): Promise<void> {
        log(`ResourceManager: Initializing with ${file.images.length} images and ${file.sounds.length} sounds`);

        // Load images
        const imagePromises = file.images.map(image => this.loadImage(image));
        
        // Load sounds
        const soundPromises = file.sounds.map(sound => this.loadSound(sound));

        try {
            await Promise.all([...imagePromises, ...soundPromises]);
            log('ResourceManager: All resources loaded successfully');
        } catch (err) {
            error('ResourceManager: Failed to load some resources:', err);
        }
    }

    /**
     * Load an image resource
     */
    public async loadImage(imageResource: FileResource): Promise<Texture2D | null> {
        const cacheEntry = this.imageCache.get(imageResource.id);
        if (cacheEntry) {
            cacheEntry.accessCount++;
            cacheEntry.lastAccess = Date.now();
            return cacheEntry.asset;
        }

        // Check if already loading
        const loadingKey = `image_${imageResource.id}`;
        if (this.loadingPromises.has(loadingKey)) {
            return this.loadingPromises.get(loadingKey);
        }

        // Start loading
        const loadPromise = this.loadImageAsset(imageResource);
        this.loadingPromises.set(loadingKey, loadPromise);

        try {
            const texture = await loadPromise;
            this.loadingPromises.delete(loadingKey);

            if (texture) {
                // Cache the loaded texture
                this.cacheImage(imageResource, texture);
                
                // Create sprite frame
                this.createSpriteFrame(imageResource.id, texture);
            }

            return texture;
        } catch (err) {
            this.loadingPromises.delete(loadingKey);
            error(`ResourceManager: Failed to load image ${imageResource.relativePath}:`, err);
            return null;
        }
    }

    /**
     * Load a sound resource
     */
    public async loadSound(soundResource: FileResource): Promise<AudioClip | null> {
        const cacheEntry = this.soundCache.get(soundResource.id);
        if (cacheEntry) {
            cacheEntry.accessCount++;
            cacheEntry.lastAccess = Date.now();
            return cacheEntry.asset;
        }

        // Check if already loading
        const loadingKey = `sound_${soundResource.id}`;
        if (this.loadingPromises.has(loadingKey)) {
            return this.loadingPromises.get(loadingKey);
        }

        // Start loading
        const loadPromise = this.loadSoundAsset(soundResource);
        this.loadingPromises.set(loadingKey, loadPromise);

        try {
            const audioClip = await loadPromise;
            this.loadingPromises.delete(loadingKey);

            if (audioClip) {
                // Cache the loaded audio clip
                this.cacheSound(soundResource, audioClip);
            }

            return audioClip;
        } catch (err) {
            this.loadingPromises.delete(loadingKey);
            error(`ResourceManager: Failed to load sound ${soundResource.relativePath}:`, err);
            return null;
        }
    }

    /**
     * Get cached image by ID
     */
    public getImage(id: number): Texture2D | null {
        const cacheEntry = this.imageCache.get(id);
        if (cacheEntry) {
            cacheEntry.accessCount++;
            cacheEntry.lastAccess = Date.now();
            return cacheEntry.asset;
        }
        return null;
    }

    /**
     * Get cached sound by ID
     */
    public getSound(id: number): AudioClip | null {
        const cacheEntry = this.soundCache.get(id);
        if (cacheEntry) {
            cacheEntry.accessCount++;
            cacheEntry.lastAccess = Date.now();
            return cacheEntry.asset;
        }
        return null;
    }

    /**
     * Get sprite frame by ID
     */
    public getSpriteFrame(id: number): SpriteFrame | null {
        return this.spriteFrameCache.get(id) || null;
    }

    /**
     * Preload resources
     */
    public async preloadResources(images: FileResource[], sounds: FileResource[]): Promise<void> {
        const promises: Promise<any>[] = [];

        images.forEach(image => {
            promises.push(this.loadImage(image));
        });

        sounds.forEach(sound => {
            promises.push(this.loadSound(sound));
        });

        try {
            await Promise.all(promises);
            log('ResourceManager: Preloading completed');
        } catch (err) {
            warn('ResourceManager: Some resources failed to preload:', err);
        }
    }

    /**
     * Clear cache
     */
    public clearCache(): void {
        this.imageCache.clear();
        this.soundCache.clear();
        this.spriteFrameCache.clear();
        log('ResourceManager: Cache cleared');
    }

    /**
     * Clean up expired cache entries
     */
    public cleanupCache(): void {
        const now = Date.now();
        let removedCount = 0;

        // Clean image cache
        this.imageCache.forEach((entry, id) => {
            if (now - entry.lastAccess > this.cacheTimeout) {
                this.imageCache.delete(id);
                this.spriteFrameCache.delete(id);
                removedCount++;
            }
        });

        // Clean sound cache
        this.soundCache.forEach((entry, id) => {
            if (now - entry.lastAccess > this.cacheTimeout) {
                this.soundCache.delete(id);
                removedCount++;
            }
        });

        if (removedCount > 0) {
            log(`ResourceManager: Cleaned up ${removedCount} expired cache entries`);
        }
    }

    /**
     * Get cache statistics
     */
    public getCacheStats(): {
        images: number;
        sounds: number;
        spriteFrames: number;
        totalMemory: number;
    } {
        return {
            images: this.imageCache.size,
            sounds: this.soundCache.size,
            spriteFrames: this.spriteFrameCache.size,
            totalMemory: this.estimateMemoryUsage()
        };
    }

    /**
     * Set cache configuration
     */
    public setCacheConfig(maxSize: number, timeoutMs: number): void {
        this.maxCacheSize = maxSize;
        this.cacheTimeout = timeoutMs;
    }

    /**
     * Destroy and clean up
     */
    public destroy(): void {
        this.clearCache();
        this.loadingPromises.clear();
        log('ResourceManager: Destroyed');
    }

    // Private helper methods

    /**
     * Load image asset from resources
     */
    private loadImageAsset(imageResource: FileResource): Promise<Texture2D | null> {
        return new Promise((resolve) => {
            resources.load(imageResource.relativePath, Texture2D, (err, texture) => {
                if (err) {
                    warn(`ResourceManager: Failed to load image ${imageResource.relativePath}:`, err);
                    resolve(null);
                } else {
                    resolve(texture);
                }
            });
        });
    }

    /**
     * Load sound asset from resources
     */
    private loadSoundAsset(soundResource: FileResource): Promise<AudioClip | null> {
        return new Promise((resolve) => {
            resources.load(soundResource.relativePath, AudioClip, (err, audioClip) => {
                if (err) {
                    warn(`ResourceManager: Failed to load sound ${soundResource.relativePath}:`, err);
                    resolve(null);
                } else {
                    resolve(audioClip);
                }
            });
        });
    }

    /**
     * Cache loaded image
     */
    private cacheImage(imageResource: FileResource, texture: Texture2D): void {
        // Remove oldest entry if cache is full
        if (this.imageCache.size >= this.maxCacheSize) {
            this.removeOldestImageEntry();
        }

        const entry: ResourceCacheEntry = {
            id: imageResource.id,
            path: imageResource.relativePath,
            asset: texture,
            loadTime: Date.now(),
            accessCount: 1,
            lastAccess: Date.now()
        };

        this.imageCache.set(imageResource.id, entry);
    }

    /**
     * Cache loaded sound
     */
    private cacheSound(soundResource: FileResource, audioClip: AudioClip): void {
        // Remove oldest entry if cache is full
        if (this.soundCache.size >= this.maxCacheSize) {
            this.removeOldestSoundEntry();
        }

        const entry: ResourceCacheEntry = {
            id: soundResource.id,
            path: soundResource.relativePath,
            asset: audioClip,
            loadTime: Date.now(),
            accessCount: 1,
            lastAccess: Date.now()
        };

        this.soundCache.set(soundResource.id, entry);
    }

    /**
     * Create sprite frame from texture
     */
    private createSpriteFrame(id: number, texture: Texture2D): void {
        const spriteFrame = new SpriteFrame();
        spriteFrame.texture = texture;
        this.spriteFrameCache.set(id, spriteFrame);
    }

    /**
     * Remove oldest image cache entry
     */
    private removeOldestImageEntry(): void {
        let oldestId: number | null = null;
        let oldestTime = Date.now();

        this.imageCache.forEach((entry, id) => {
            if (entry.lastAccess < oldestTime) {
                oldestTime = entry.lastAccess;
                oldestId = id;
            }
        });

        if (oldestId !== null) {
            this.imageCache.delete(oldestId);
            this.spriteFrameCache.delete(oldestId);
        }
    }

    /**
     * Remove oldest sound cache entry
     */
    private removeOldestSoundEntry(): void {
        let oldestId: number | null = null;
        let oldestTime = Date.now();

        this.soundCache.forEach((entry, id) => {
            if (entry.lastAccess < oldestTime) {
                oldestTime = entry.lastAccess;
                oldestId = id;
            }
        });

        if (oldestId !== null) {
            this.soundCache.delete(oldestId);
        }
    }

    /**
     * Estimate memory usage
     */
    private estimateMemoryUsage(): number {
        let totalMemory = 0;

        this.imageCache.forEach(entry => {
            const texture = entry.asset as Texture2D;
            if (texture) {
                // Rough estimate: width * height * 4 bytes (RGBA)
                totalMemory += texture.width * texture.height * 4;
            }
        });

        // Sound memory is harder to estimate, so we'll use a rough approximation
        totalMemory += this.soundCache.size * 1024 * 1024; // 1MB per sound

        return totalMemory;
    }
}
