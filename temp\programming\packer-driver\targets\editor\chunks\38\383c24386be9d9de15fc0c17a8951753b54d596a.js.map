{"version": 3, "sources": ["file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts"], "names": ["makeMSAA", "enabled", "sampleCount", "SampleCount", "X4", "fillRequiredMSAA", "value", "undefined", "makeHBAO", "radiusScale", "angleBiasDegree", "blurSharpness", "aoSaturation", "need<PERSON><PERSON><PERSON>", "fillRequiredHBAO", "makeBloom", "material", "enableAlphaMask", "iterations", "threshold", "intensity", "fillRequiredBloom", "makeColorGrading", "contribute", "colorGradingMap", "fillRequiredColorGrading", "makeFSR", "sharpness", "fillRequiredFSR", "makeFXAA", "fillRequiredFXAA", "makeToneMapping", "fillRequiredToneMapping", "makePipelineSettings", "msaa", "enableShadingScale", "shadingScale", "bloom", "toneMapping", "colorGrading", "fsr", "fxaa", "fillRequiredPipelineSettings", "gfx"], "mappings": ";;;;;AAwCO,WAASA,QAAT,GAA0B;AAC7B,WAAO;AACHC,MAAAA,OAAO,EAAE,KADN;AAEHC,MAAAA,WAAW,EAAEC,WAAW,CAACC;AAFtB,KAAP;AAIH;;AAEM,WAASC,gBAAT,CAA0BC,KAA1B,EAA6C;AAChD,QAAIA,KAAK,CAACL,OAAN,KAAkBM,SAAtB,EAAiC;AAC7BD,MAAAA,KAAK,CAACL,OAAN,GAAgB,KAAhB;AACH;;AACD,QAAIK,KAAK,CAACJ,WAAN,KAAsBK,SAA1B,EAAqC;AACjCD,MAAAA,KAAK,CAACJ,WAAN,GAAoBC,WAAW,CAACC,EAAhC;AACH;AACJ;;AAYM,WAASI,QAAT,GAA0B;AAC7B,WAAO;AACHP,MAAAA,OAAO,EAAE,KADN;AAEHQ,MAAAA,WAAW,EAAE,CAFV;AAGHC,MAAAA,eAAe,EAAE,EAHd;AAIHC,MAAAA,aAAa,EAAE,CAJZ;AAKHC,MAAAA,YAAY,EAAE,CALX;AAMHC,MAAAA,QAAQ,EAAE;AANP,KAAP;AAQH;;AAEM,WAASC,gBAAT,CAA0BR,KAA1B,EAA6C;AAChD,QAAIA,KAAK,CAACL,OAAN,KAAkBM,SAAtB,EAAiC;AAC7BD,MAAAA,KAAK,CAACL,OAAN,GAAgB,KAAhB;AACH;;AACD,QAAIK,KAAK,CAACG,WAAN,KAAsBF,SAA1B,EAAqC;AACjCD,MAAAA,KAAK,CAACG,WAAN,GAAoB,CAApB;AACH;;AACD,QAAIH,KAAK,CAACI,eAAN,KAA0BH,SAA9B,EAAyC;AACrCD,MAAAA,KAAK,CAACI,eAAN,GAAwB,EAAxB;AACH;;AACD,QAAIJ,KAAK,CAACK,aAAN,KAAwBJ,SAA5B,EAAuC;AACnCD,MAAAA,KAAK,CAACK,aAAN,GAAsB,CAAtB;AACH;;AACD,QAAIL,KAAK,CAACM,YAAN,KAAuBL,SAA3B,EAAsC;AAClCD,MAAAA,KAAK,CAACM,YAAN,GAAqB,CAArB;AACH;;AACD,QAAIN,KAAK,CAACO,QAAN,KAAmBN,SAAvB,EAAkC;AAC9BD,MAAAA,KAAK,CAACO,QAAN,GAAiB,KAAjB;AACH;AACJ;;AAYM,WAASE,SAAT,GAA4B;AAC/B,WAAO;AACHd,MAAAA,OAAO,EAAE,KADN;AAEHe,MAAAA,QAAQ,EAAE,IAFP;AAGHC,MAAAA,eAAe,EAAE,KAHd;AAIHC,MAAAA,UAAU,EAAE,CAJT;AAKHC,MAAAA,SAAS,EAAE,GALR;AAMHC,MAAAA,SAAS,EAAE;AANR,KAAP;AAQH;;AAEM,WAASC,iBAAT,CAA2Bf,KAA3B,EAA+C;AAClD,QAAIA,KAAK,CAACL,OAAN,KAAkBM,SAAtB,EAAiC;AAC7BD,MAAAA,KAAK,CAACL,OAAN,GAAgB,KAAhB;AACH;;AACD,QAAIK,KAAK,CAACU,QAAN,KAAmBT,SAAvB,EAAkC;AAC9BD,MAAAA,KAAK,CAACU,QAAN,GAAiB,IAAjB;AACH;;AACD,QAAIV,KAAK,CAACW,eAAN,KAA0BV,SAA9B,EAAyC;AACrCD,MAAAA,KAAK,CAACW,eAAN,GAAwB,KAAxB;AACH;;AACD,QAAIX,KAAK,CAACY,UAAN,KAAqBX,SAAzB,EAAoC;AAChCD,MAAAA,KAAK,CAACY,UAAN,GAAmB,CAAnB;AACH;;AACD,QAAIZ,KAAK,CAACa,SAAN,KAAoBZ,SAAxB,EAAmC;AAC/BD,MAAAA,KAAK,CAACa,SAAN,GAAkB,GAAlB;AACH;;AACD,QAAIb,KAAK,CAACc,SAAN,KAAoBb,SAAxB,EAAmC;AAC/BD,MAAAA,KAAK,CAACc,SAAN,GAAkB,GAAlB;AACH;AACJ;;AAUM,WAASE,gBAAT,GAA0C;AAC7C,WAAO;AACHrB,MAAAA,OAAO,EAAE,KADN;AAEHe,MAAAA,QAAQ,EAAE,IAFP;AAGHO,MAAAA,UAAU,EAAE,CAHT;AAIHC,MAAAA,eAAe,EAAE;AAJd,KAAP;AAMH;;AAEM,WAASC,wBAAT,CAAkCnB,KAAlC,EAA6D;AAChE,QAAIA,KAAK,CAACL,OAAN,KAAkBM,SAAtB,EAAiC;AAC7BD,MAAAA,KAAK,CAACL,OAAN,GAAgB,KAAhB;AACH;;AACD,QAAIK,KAAK,CAACU,QAAN,KAAmBT,SAAvB,EAAkC;AAC9BD,MAAAA,KAAK,CAACU,QAAN,GAAiB,IAAjB;AACH;;AACD,QAAIV,KAAK,CAACiB,UAAN,KAAqBhB,SAAzB,EAAoC;AAChCD,MAAAA,KAAK,CAACiB,UAAN,GAAmB,CAAnB;AACH;;AACD,QAAIjB,KAAK,CAACkB,eAAN,KAA0BjB,SAA9B,EAAyC;AACrCD,MAAAA,KAAK,CAACkB,eAAN,GAAwB,IAAxB;AACH;AACJ;;AASM,WAASE,OAAT,GAAwB;AAC3B,WAAO;AACHzB,MAAAA,OAAO,EAAE,KADN;AAEHe,MAAAA,QAAQ,EAAE,IAFP;AAGHW,MAAAA,SAAS,EAAE;AAHR,KAAP;AAKH;;AAEM,WAASC,eAAT,CAAyBtB,KAAzB,EAA2C;AAC9C,QAAIA,KAAK,CAACL,OAAN,KAAkBM,SAAtB,EAAiC;AAC7BD,MAAAA,KAAK,CAACL,OAAN,GAAgB,KAAhB;AACH;;AACD,QAAIK,KAAK,CAACU,QAAN,KAAmBT,SAAvB,EAAkC;AAC9BD,MAAAA,KAAK,CAACU,QAAN,GAAiB,IAAjB;AACH;;AACD,QAAIV,KAAK,CAACqB,SAAN,KAAoBpB,SAAxB,EAAmC;AAC/BD,MAAAA,KAAK,CAACqB,SAAN,GAAkB,GAAlB;AACH;AACJ;;AAQM,WAASE,QAAT,GAA0B;AAC7B,WAAO;AACH5B,MAAAA,OAAO,EAAE,KADN;AAEHe,MAAAA,QAAQ,EAAE;AAFP,KAAP;AAIH;;AAEM,WAASc,gBAAT,CAA0BxB,KAA1B,EAA6C;AAChD,QAAIA,KAAK,CAACL,OAAN,KAAkBM,SAAtB,EAAiC;AAC7BD,MAAAA,KAAK,CAACL,OAAN,GAAgB,KAAhB;AACH;;AACD,QAAIK,KAAK,CAACU,QAAN,KAAmBT,SAAvB,EAAkC;AAC9BD,MAAAA,KAAK,CAACU,QAAN,GAAiB,IAAjB;AACH;AACJ;;AAOM,WAASe,eAAT,GAAwC;AAC3C,WAAO;AACHf,MAAAA,QAAQ,EAAE;AADP,KAAP;AAGH;;AAEM,WAASgB,uBAAT,CAAiC1B,KAAjC,EAA2D;AAC9D,QAAIA,KAAK,CAACU,QAAN,KAAmBT,SAAvB,EAAkC;AAC9BD,MAAAA,KAAK,CAACU,QAAN,GAAiB,IAAjB;AACH;AACJ;;AAcM,WAASiB,oBAAT,GAAkD;AACrD,WAAO;AACHC,MAAAA,IAAI,EAAElC,QAAQ,EADX;AAEHmC,MAAAA,kBAAkB,EAAE,KAFjB;AAGHC,MAAAA,YAAY,EAAE,GAHX;AAIHC,MAAAA,KAAK,EAAEtB,SAAS,EAJb;AAKHuB,MAAAA,WAAW,EAAEP,eAAe,EALzB;AAMHQ,MAAAA,YAAY,EAAEjB,gBAAgB,EAN3B;AAOHkB,MAAAA,GAAG,EAAEd,OAAO,EAPT;AAQHe,MAAAA,IAAI,EAAEZ,QAAQ;AARX,KAAP;AAUH;;AAEM,WAASa,4BAAT,CAAsCpC,KAAtC,EAAqE;AACxE,QAAI,CAACA,KAAK,CAAC4B,IAAX,EAAiB;AACZ5B,MAAAA,KAAK,CAAC4B,IAAP,GAAuBlC,QAAQ,EAA/B;AACH,KAFD,MAEO;AACHK,MAAAA,gBAAgB,CAACC,KAAK,CAAC4B,IAAP,CAAhB;AACH;;AACD,QAAI5B,KAAK,CAAC6B,kBAAN,KAA6B5B,SAAjC,EAA4C;AACxCD,MAAAA,KAAK,CAAC6B,kBAAN,GAA2B,KAA3B;AACH;;AACD,QAAI7B,KAAK,CAAC8B,YAAN,KAAuB7B,SAA3B,EAAsC;AAClCD,MAAAA,KAAK,CAAC8B,YAAN,GAAqB,GAArB;AACH;;AACD,QAAI,CAAC9B,KAAK,CAAC+B,KAAX,EAAkB;AACb/B,MAAAA,KAAK,CAAC+B,KAAP,GAAyBtB,SAAS,EAAlC;AACH,KAFD,MAEO;AACHM,MAAAA,iBAAiB,CAACf,KAAK,CAAC+B,KAAP,CAAjB;AACH;;AACD,QAAI,CAAC/B,KAAK,CAACgC,WAAX,EAAwB;AACnBhC,MAAAA,KAAK,CAACgC,WAAP,GAAqCP,eAAe,EAApD;AACH,KAFD,MAEO;AACHC,MAAAA,uBAAuB,CAAC1B,KAAK,CAACgC,WAAP,CAAvB;AACH;;AACD,QAAI,CAAChC,KAAK,CAACiC,YAAX,EAAyB;AACpBjC,MAAAA,KAAK,CAACiC,YAAP,GAAuCjB,gBAAgB,EAAvD;AACH,KAFD,MAEO;AACHG,MAAAA,wBAAwB,CAACnB,KAAK,CAACiC,YAAP,CAAxB;AACH;;AACD,QAAI,CAACjC,KAAK,CAACkC,GAAX,EAAgB;AACXlC,MAAAA,KAAK,CAACkC,GAAP,GAAqBd,OAAO,EAA5B;AACH,KAFD,MAEO;AACHE,MAAAA,eAAe,CAACtB,KAAK,CAACkC,GAAP,CAAf;AACH;;AACD,QAAI,CAAClC,KAAK,CAACmC,IAAX,EAAiB;AACZnC,MAAAA,KAAK,CAACmC,IAAP,GAAuBZ,QAAQ,EAA/B;AACH,KAFD,MAEO;AACHC,MAAAA,gBAAgB,CAACxB,KAAK,CAACmC,IAAP,CAAhB;AACH;AACJ;;;cApQezC,Q;sBAOAK,gB;cAmBAG,Q;sBAWAM,gB;eA+BAC,S;uBAWAM,iB;sBA6BAC,gB;8BASAG,wB;aAsBAC,O;qBAQAE,e;cAkBAC,Q;sBAOAC,gB;qBAcAC,e;6BAMAC,uB;0BAkBAC,oB;kCAaAS;;;;;;;;AAzOcC,MAAAA,G,OAAAA,G;;;;;;AA9B9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AACA;;;;;OAGM;AAAExC,QAAAA;AAAF,O,GAAkBwC,G", "sourcesContent": ["/*\r\n Copyright (c) 2021-2024 Xiamen Yaji Software Co., Ltd.\r\n\r\n https://www.cocos.com\r\n\r\n Permission is hereby granted, free of charge, to any person obtaining a copy\r\n of this software and associated documentation files (the \"Software\"), to deal\r\n in the Software without restriction, including without limitation the rights to\r\n use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies\r\n of the Software, and to permit persons to whom the Software is furnished to do so,\r\n subject to the following conditions:\r\n\r\n The above copyright notice and this permission notice shall be included in\r\n all copies or substantial portions of the Software.\r\n\r\n THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\r\n IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\r\n FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\r\n AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\r\n LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\r\n OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\r\n THE SOFTWARE.\r\n*/\r\n\r\n/**\r\n * ========================= !DO NOT CHANGE THE FOLLOWING SECTION MANUALLY! =========================\r\n * The following section is auto-generated.\r\n * ========================= !DO NOT CHANGE THE FOLLOWING SECTION MANUALLY! =========================\r\n */\r\n/* eslint-disable max-len */\r\nimport { Material, Texture2D, gfx } from 'cc';\r\n\r\nconst { SampleCount } = gfx;\r\n\r\nexport interface MSAA {\r\n    enabled: boolean; /* false */\r\n    sampleCount: gfx.SampleCount; /* SampleCount.X4 */\r\n    [name: string]: unknown;\r\n}\r\n\r\nexport function makeMSAA(): MSAA {\r\n    return {\r\n        enabled: false,\r\n        sampleCount: SampleCount.X4,\r\n    };\r\n}\r\n\r\nexport function fillRequiredMSAA(value: MSAA): void {\r\n    if (value.enabled === undefined) {\r\n        value.enabled = false;\r\n    }\r\n    if (value.sampleCount === undefined) {\r\n        value.sampleCount = SampleCount.X4;\r\n    }\r\n}\r\n\r\nexport interface HBAO {\r\n    enabled: boolean; /* false */\r\n    radiusScale: number; /* 1 */\r\n    angleBiasDegree: number; /* 10 */\r\n    blurSharpness: number; /* 3 */\r\n    aoSaturation: number; /* 1 */\r\n    needBlur: boolean; /* false */\r\n    [name: string]: unknown;\r\n}\r\n\r\nexport function makeHBAO(): HBAO {\r\n    return {\r\n        enabled: false,\r\n        radiusScale: 1,\r\n        angleBiasDegree: 10,\r\n        blurSharpness: 3,\r\n        aoSaturation: 1,\r\n        needBlur: false,\r\n    };\r\n}\r\n\r\nexport function fillRequiredHBAO(value: HBAO): void {\r\n    if (value.enabled === undefined) {\r\n        value.enabled = false;\r\n    }\r\n    if (value.radiusScale === undefined) {\r\n        value.radiusScale = 1;\r\n    }\r\n    if (value.angleBiasDegree === undefined) {\r\n        value.angleBiasDegree = 10;\r\n    }\r\n    if (value.blurSharpness === undefined) {\r\n        value.blurSharpness = 3;\r\n    }\r\n    if (value.aoSaturation === undefined) {\r\n        value.aoSaturation = 1;\r\n    }\r\n    if (value.needBlur === undefined) {\r\n        value.needBlur = false;\r\n    }\r\n}\r\n\r\nexport interface Bloom {\r\n    enabled: boolean; /* false */\r\n    /* refcount */ material: Material | null;\r\n    enableAlphaMask: boolean; /* false */\r\n    iterations: number; /* 3 */\r\n    threshold: number; /* 0.8 */\r\n    intensity: number; /* 2.3 */\r\n    [name: string]: unknown;\r\n}\r\n\r\nexport function makeBloom(): Bloom {\r\n    return {\r\n        enabled: false,\r\n        material: null,\r\n        enableAlphaMask: false,\r\n        iterations: 3,\r\n        threshold: 0.8,\r\n        intensity: 2.3,\r\n    };\r\n}\r\n\r\nexport function fillRequiredBloom(value: Bloom): void {\r\n    if (value.enabled === undefined) {\r\n        value.enabled = false;\r\n    }\r\n    if (value.material === undefined) {\r\n        value.material = null;\r\n    }\r\n    if (value.enableAlphaMask === undefined) {\r\n        value.enableAlphaMask = false;\r\n    }\r\n    if (value.iterations === undefined) {\r\n        value.iterations = 3;\r\n    }\r\n    if (value.threshold === undefined) {\r\n        value.threshold = 0.8;\r\n    }\r\n    if (value.intensity === undefined) {\r\n        value.intensity = 2.3;\r\n    }\r\n}\r\n\r\nexport interface ColorGrading {\r\n    enabled: boolean; /* false */\r\n    /* refcount */ material: Material | null;\r\n    contribute: number; /* 1 */\r\n    /* refcount */ colorGradingMap: Texture2D | null;\r\n    [name: string]: unknown;\r\n}\r\n\r\nexport function makeColorGrading(): ColorGrading {\r\n    return {\r\n        enabled: false,\r\n        material: null,\r\n        contribute: 1,\r\n        colorGradingMap: null,\r\n    };\r\n}\r\n\r\nexport function fillRequiredColorGrading(value: ColorGrading): void {\r\n    if (value.enabled === undefined) {\r\n        value.enabled = false;\r\n    }\r\n    if (value.material === undefined) {\r\n        value.material = null;\r\n    }\r\n    if (value.contribute === undefined) {\r\n        value.contribute = 1;\r\n    }\r\n    if (value.colorGradingMap === undefined) {\r\n        value.colorGradingMap = null;\r\n    }\r\n}\r\n\r\nexport interface FSR {\r\n    enabled: boolean; /* false */\r\n    /* refcount */ material: Material | null;\r\n    sharpness: number; /* 0.8 */\r\n    [name: string]: unknown;\r\n}\r\n\r\nexport function makeFSR(): FSR {\r\n    return {\r\n        enabled: false,\r\n        material: null,\r\n        sharpness: 0.8,\r\n    };\r\n}\r\n\r\nexport function fillRequiredFSR(value: FSR): void {\r\n    if (value.enabled === undefined) {\r\n        value.enabled = false;\r\n    }\r\n    if (value.material === undefined) {\r\n        value.material = null;\r\n    }\r\n    if (value.sharpness === undefined) {\r\n        value.sharpness = 0.8;\r\n    }\r\n}\r\n\r\nexport interface FXAA {\r\n    enabled: boolean; /* false */\r\n    /* refcount */ material: Material | null;\r\n    [name: string]: unknown;\r\n}\r\n\r\nexport function makeFXAA(): FXAA {\r\n    return {\r\n        enabled: false,\r\n        material: null,\r\n    };\r\n}\r\n\r\nexport function fillRequiredFXAA(value: FXAA): void {\r\n    if (value.enabled === undefined) {\r\n        value.enabled = false;\r\n    }\r\n    if (value.material === undefined) {\r\n        value.material = null;\r\n    }\r\n}\r\n\r\nexport interface ToneMapping {\r\n    /* refcount */ material: Material | null;\r\n    [name: string]: unknown;\r\n}\r\n\r\nexport function makeToneMapping(): ToneMapping {\r\n    return {\r\n        material: null,\r\n    };\r\n}\r\n\r\nexport function fillRequiredToneMapping(value: ToneMapping): void {\r\n    if (value.material === undefined) {\r\n        value.material = null;\r\n    }\r\n}\r\n\r\nexport interface PipelineSettings {\r\n    readonly msaa: MSAA;\r\n    enableShadingScale: boolean; /* false */\r\n    shadingScale: number; /* 0.5 */\r\n    readonly bloom: Bloom;\r\n    readonly toneMapping: ToneMapping;\r\n    readonly colorGrading: ColorGrading;\r\n    readonly fsr: FSR;\r\n    readonly fxaa: FXAA;\r\n    [name: string]: unknown;\r\n}\r\n\r\nexport function makePipelineSettings(): PipelineSettings {\r\n    return {\r\n        msaa: makeMSAA(),\r\n        enableShadingScale: false,\r\n        shadingScale: 0.5,\r\n        bloom: makeBloom(),\r\n        toneMapping: makeToneMapping(),\r\n        colorGrading: makeColorGrading(),\r\n        fsr: makeFSR(),\r\n        fxaa: makeFXAA(),\r\n    };\r\n}\r\n\r\nexport function fillRequiredPipelineSettings(value: PipelineSettings): void {\r\n    if (!value.msaa) {\r\n        (value.msaa as MSAA) = makeMSAA();\r\n    } else {\r\n        fillRequiredMSAA(value.msaa);\r\n    }\r\n    if (value.enableShadingScale === undefined) {\r\n        value.enableShadingScale = false;\r\n    }\r\n    if (value.shadingScale === undefined) {\r\n        value.shadingScale = 0.5;\r\n    }\r\n    if (!value.bloom) {\r\n        (value.bloom as Bloom) = makeBloom();\r\n    } else {\r\n        fillRequiredBloom(value.bloom);\r\n    }\r\n    if (!value.toneMapping) {\r\n        (value.toneMapping as ToneMapping) = makeToneMapping();\r\n    } else {\r\n        fillRequiredToneMapping(value.toneMapping);\r\n    }\r\n    if (!value.colorGrading) {\r\n        (value.colorGrading as ColorGrading) = makeColorGrading();\r\n    } else {\r\n        fillRequiredColorGrading(value.colorGrading);\r\n    }\r\n    if (!value.fsr) {\r\n        (value.fsr as FSR) = makeFSR();\r\n    } else {\r\n        fillRequiredFSR(value.fsr);\r\n    }\r\n    if (!value.fxaa) {\r\n        (value.fxaa as FXAA) = makeFXAA();\r\n    } else {\r\n        fillRequiredFXAA(value.fxaa);\r\n    }\r\n}\r\n"]}