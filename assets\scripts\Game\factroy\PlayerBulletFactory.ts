import { _decorator, Component, Node, instantiate } from 'cc';
import { Global } from '../Global';
import { PersistNode } from '../PersistNode';
import { PlayerBullet } from '../PlayerBullet';
import { GameFactory } from './GameFactory';
const { ccclass, property } = _decorator;

export class PlayerBulletFactory extends GameFactory {

    public createProduct(productType: string): Node {
        let playBulletTemp: Node = null;

        if (this.productPool.size() > 0) {
            playBulletTemp = this.productPool.get();  //如果池里有子弹，就直接拿来用
        } else {
            playBulletTemp = instantiate(this.persistNode.getComponent(PersistNode).playerBulletPreb);  //从常驻节点拿到预制体原料
        }
        
        switch(productType) {
            case Global.NORMAL_BULLET:
                playBulletTemp.getComponent(PlayerBullet).init(productType, this.persistNode.getComponent(PersistNode).normalBullet);  //通过调用PlayerBullet的init方法，来创建子弹
                break;
            case Global.LIGHT_BULLET:
                playBulletTemp.getComponent(PlayerBullet).init(productType, this.persistNode.getComponent(PersistNode).lightBullet);
                break;
            case Global.MISSILE_BULLET:
                playBulletTemp.getComponent(PlayerBullet).init(productType, this.persistNode.getComponent(PersistNode).missileBullet);
                break;
        }

        return playBulletTemp;
    }
    
}

