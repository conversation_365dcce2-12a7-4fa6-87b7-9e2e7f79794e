import { _decorator } from "cc";
import { TypedBase, TypeID } from "./TypeID";
const { ccclass } = _decorator;

/**
 * Base System class for all game systems
 * Follows the same pattern as IMgr but specifically designed for game world systems
 */
@ccclass("System")
export abstract class System extends TypedBase {

    protected _isInitialized: boolean = false;
    protected _isEnabled: boolean = true;

    /**
     * Initialize the system
     * Called once when the system is registered to the world
     */
    public init(): void {
        if (this._isInitialized) {
            console.warn(`System ${this.getTypeName()} is already initialized`);
            return;
        }

        this.onInit();
        this._isInitialized = true;
    }

    /**
     * Cleanup the system
     * Called when the system is removed or world is destroyed
     */
    public unInit(): void {
        if (!this._isInitialized) {
            return;
        }

        this.onUnInit();
        this._isInitialized = false;
    }

    /**
     * Update the system
     * Called every frame when the system is enabled
     * @param deltaTime Time elapsed since last frame in seconds
     */
    public update(deltaTime: number): void {
        if (!this._isInitialized || !this._isEnabled) {
            return;
        }

        this.onUpdate(deltaTime);
    }

    /**
     * Late update the system
     * Called after all systems have been updated
     * @param deltaTime Time elapsed since last frame in seconds
     */
    public lateUpdate(deltaTime: number): void {
        if (!this._isInitialized || !this._isEnabled) {
            return;
        }

        this.onLateUpdate(deltaTime);
    }

    /**
     * Enable or disable the system
     * Disabled systems will not receive update calls
     */
    public setEnabled(enabled: boolean): void {
        this._isEnabled = enabled;
    }

    /**
     * Check if the system is enabled
     */
    public isEnabled(): boolean {
        return this._isEnabled;
    }

    /**
     * Check if the system is initialized
     */
    public isInitialized(): boolean {
        return this._isInitialized;
    }

    /**
     * Override this method to implement system initialization logic
     */
    protected abstract onInit(): void;

    /**
     * Override this method to implement system cleanup logic
     */
    protected abstract onUnInit(): void;

    /**
     * Override this method to implement system update logic
     * @param deltaTime Time elapsed since last frame in seconds
     */
    protected abstract onUpdate(deltaTime: number): void;

    /**
     * Override this method to implement system late update logic
     * @param deltaTime Time elapsed since last frame in seconds
     */
    protected abstract onLateUpdate(deltaTime: number): void;
}