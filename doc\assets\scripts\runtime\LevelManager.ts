import { _decorator, Component, Node, instantiate, Prefab, resources } from 'cc';
import { ILevelData, SubLevel } from '../core/LevelData';
import { SubLevelState } from '../core/Types';
import { SubLevelComponent } from './SubLevelComponent';

const { ccclass, property } = _decorator;

/**
 * Level Manager handles SubLevel lifecycle and coordination
 */
@ccclass('LevelManager')
export class LevelManager extends Component {
    @property
    public preloadDistance: number = 1000; // Distance to start preloading
    
    @property
    public unloadDelay: number = 5.0; // Delay before unloading out-of-view sublevels
    
    @property
    public maxActiveSubLevels: number = 3; // Maximum number of active sublevels
    
    // Current level data
    private currentLevel: ILevelData | null = null;
    
    // SubLevel management
    private subLevelComponents: Map<string, SubLevelComponent> = new Map();
    private activeSubLevels: Set<string> = new Set();
    private loadedSubLevels: Set<string> = new Set();
    private unloadTimers: Map<string, number> = new Map();
    
    // Events
    public onLevelLoaded?: (level: ILevelData) => void;
    public onLevelUnloaded?: () => void;
    public onSubLevelActivated?: (subLevel: SubLevel) => void;
    public onSubLevelDeactivated?: (subLevel: SubLevel) => void;
    
    protected onLoad(): void {
        console.log('LevelManager initialized');
    }
    
    /**
     * Load a level and setup all sublevels
     */
    public async loadLevel(levelData: ILevelData): Promise<void> {
        try {
            // Unload current level if exists
            if (this.currentLevel) {
                await this.unloadLevel();
            }
            
            this.currentLevel = levelData;
            console.log(`Loading level: ${levelData.metadata.name}`);
            
            // Setup all sublevels
            await this.setupSubLevels();
            
            // Trigger level loaded event
            this.onLevelLoaded?.(levelData);
            
            console.log(`Level loaded successfully: ${levelData.metadata.name}`);
        } catch (error) {
            console.error('Failed to load level:', error);
            throw error;
        }
    }
    
    /**
     * Unload current level and cleanup
     */
    public async unloadLevel(): Promise<void> {
        if (!this.currentLevel) return;
        
        console.log(`Unloading level: ${this.currentLevel.metadata.name}`);
        
        // Cleanup all sublevels
        await this.cleanupSubLevels();
        
        // Clear state
        this.currentLevel = null;
        this.subLevelComponents.clear();
        this.activeSubLevels.clear();
        this.loadedSubLevels.clear();
        this.unloadTimers.clear();
        
        // Trigger level unloaded event
        this.onLevelUnloaded?.();
        
        console.log('Level unloaded successfully');
    }
    
    /**
     * Setup all sublevel components
     */
    private async setupSubLevels(): Promise<void> {
        if (!this.currentLevel) return;
        
        for (const subLevel of this.currentLevel.subLevels) {
            await this.createSubLevelComponent(subLevel);
        }
    }
    
    /**
     * Create a sublevel component
     */
    private async createSubLevelComponent(subLevel: SubLevel): Promise<void> {
        const subLevelNode = new Node(`SubLevel_${subLevel.id}`);
        const component = subLevelNode.addComponent(SubLevelComponent);
        component.subLevelData = subLevel;
        
        // Setup callbacks
        component.onWillEnterView = this.onSubLevelWillEnterView.bind(this);
        component.onWillExitView = this.onSubLevelWillExitView.bind(this);
        component.onFullyInView = this.onSubLevelFullyInView.bind(this);
        component.onFullyOutOfView = this.onSubLevelFullyOutOfView.bind(this);
        component.onStateChanged = this.onSubLevelStateChanged.bind(this);
        
        // Add to scene
        this.node.addChild(subLevelNode);
        this.subLevelComponents.set(subLevel.id, component);
        
        console.log(`SubLevel component created: ${subLevel.name}`);
    }
    
    /**
     * Cleanup all sublevel components
     */
    private async cleanupSubLevels(): Promise<void> {
        for (const [id, component] of this.subLevelComponents) {
            if (component && component.node) {
                component.node.destroy();
            }
        }
    }
    
    /**
     * Callback: SubLevel will enter camera view
     */
    private onSubLevelWillEnterView(subLevel: SubLevel): void {
        console.log(`SubLevel ${subLevel.name} will enter view`);
        
        // Cancel any pending unload
        this.cancelUnloadTimer(subLevel.id);
        
        // Preload sublevel if not already loaded
        this.preloadSubLevel(subLevel.id);
        
        // Preload connected sublevels
        this.preloadConnectedSubLevels(subLevel);
    }
    
    /**
     * Callback: SubLevel will exit camera view
     */
    private onSubLevelWillExitView(subLevel: SubLevel): void {
        console.log(`SubLevel ${subLevel.name} will exit view`);
        
        // Schedule unload with delay
        this.scheduleUnloadSubLevel(subLevel.id);
    }
    
    /**
     * Callback: SubLevel fully in camera view
     */
    private onSubLevelFullyInView(subLevel: SubLevel): void {
        console.log(`SubLevel ${subLevel.name} fully in view`);
        
        // Activate sublevel
        this.activateSubLevel(subLevel.id);
        
        // Trigger entry events
        this.triggerEntryEvents(subLevel);
    }
    
    /**
     * Callback: SubLevel fully out of camera view
     */
    private onSubLevelFullyOutOfView(subLevel: SubLevel): void {
        console.log(`SubLevel ${subLevel.name} fully out of view`);
        
        // Deactivate sublevel
        this.deactivateSubLevel(subLevel.id);
        
        // Trigger exit events
        this.triggerExitEvents(subLevel);
    }
    
    /**
     * Callback: SubLevel state changed
     */
    private onSubLevelStateChanged(subLevel: SubLevel, oldState: SubLevelState, newState: SubLevelState): void {
        console.log(`SubLevel ${subLevel.name} state: ${oldState} -> ${newState}`);
        
        // Update tracking sets
        if (newState === SubLevelState.Active) {
            this.activeSubLevels.add(subLevel.id);
            this.onSubLevelActivated?.(subLevel);
        } else if (oldState === SubLevelState.Active) {
            this.activeSubLevels.delete(subLevel.id);
            this.onSubLevelDeactivated?.(subLevel);
        }
        
        if (newState === SubLevelState.Loaded || newState === SubLevelState.Active) {
            this.loadedSubLevels.add(subLevel.id);
        } else if (newState === SubLevelState.Unloaded) {
            this.loadedSubLevels.delete(subLevel.id);
        }
    }
    
    /**
     * Preload a sublevel's assets
     */
    private async preloadSubLevel(subLevelId: string): Promise<void> {
        const component = this.subLevelComponents.get(subLevelId);
        if (!component || !component.subLevelData) return;
        
        const subLevel = component.subLevelData;
        
        if (component.getState() === SubLevelState.Unloaded) {
            console.log(`Preloading SubLevel: ${subLevel.name}`);
            
            component.setState(SubLevelState.Loading);
            
            try {
                // Load map assets
                await this.loadMapAssets(subLevel);
                
                // Load spawner prefabs
                await this.loadSpawnerAssets(subLevel);
                
                // Mark as loaded
                component.setState(SubLevelState.Loaded);
                
                console.log(`SubLevel preloaded: ${subLevel.name}`);
            } catch (error) {
                console.error(`Failed to preload SubLevel ${subLevel.name}:`, error);
                component.setState(SubLevelState.Unloaded);
            }
        }
    }
    
    /**
     * Activate a sublevel (start its systems)
     */
    private activateSubLevel(subLevelId: string): void {
        const component = this.subLevelComponents.get(subLevelId);
        if (!component || !component.subLevelData) return;
        
        const subLevel = component.subLevelData;
        
        if (component.getState() === SubLevelState.Loaded) {
            // Check if we need to deactivate other sublevels first
            if (this.activeSubLevels.size >= this.maxActiveSubLevels) {
                this.deactivateOldestSubLevel();
            }
            
            console.log(`Activating SubLevel: ${subLevel.name}`);
            component.setState(SubLevelState.Active);
            
            // Start sublevel systems
            this.startSubLevelSystems(subLevel);
        }
    }
    
    /**
     * Deactivate a sublevel (stop its systems)
     */
    private deactivateSubLevel(subLevelId: string): void {
        const component = this.subLevelComponents.get(subLevelId);
        if (!component || !component.subLevelData) return;
        
        const subLevel = component.subLevelData;
        
        if (component.getState() === SubLevelState.Active) {
            console.log(`Deactivating SubLevel: ${subLevel.name}`);
            component.setState(SubLevelState.Loaded);
            
            // Stop sublevel systems
            this.stopSubLevelSystems(subLevel);
        }
    }
    
    /**
     * Schedule sublevel unload with delay
     */
    private scheduleUnloadSubLevel(subLevelId: string): void {
        // Cancel existing timer
        this.cancelUnloadTimer(subLevelId);
        
        // Schedule new timer
        const timerId = setTimeout(() => {
            this.unloadSubLevel(subLevelId);
            this.unloadTimers.delete(subLevelId);
        }, this.unloadDelay * 1000);
        
        this.unloadTimers.set(subLevelId, timerId as any);
    }
    
    /**
     * Cancel unload timer for a sublevel
     */
    private cancelUnloadTimer(subLevelId: string): void {
        const timerId = this.unloadTimers.get(subLevelId);
        if (timerId) {
            clearTimeout(timerId);
            this.unloadTimers.delete(subLevelId);
        }
    }
    
    /**
     * Unload a sublevel
     */
    private unloadSubLevel(subLevelId: string): void {
        const component = this.subLevelComponents.get(subLevelId);
        if (!component || !component.subLevelData) return;
        
        const subLevel = component.subLevelData;
        
        // Don't unload if still near camera
        if (component.isNearCamera(this.preloadDistance)) {
            console.log(`SubLevel ${subLevel.name} still near camera, skipping unload`);
            return;
        }
        
        console.log(`Unloading SubLevel: ${subLevel.name}`);
        
        // Deactivate first if active
        if (component.getState() === SubLevelState.Active) {
            this.deactivateSubLevel(subLevelId);
        }
        
        // Unload assets
        this.unloadSubLevelAssets(subLevel);
        
        // Mark as unloaded
        component.setState(SubLevelState.Unloaded);
    }
    
    // Placeholder methods for asset loading/unloading and system management
    private async loadMapAssets(subLevel: SubLevel): Promise<void> {
        // TODO: Implement map asset loading
    }
    
    private async loadSpawnerAssets(subLevel: SubLevel): Promise<void> {
        // TODO: Implement spawner asset loading
    }
    
    private unloadSubLevelAssets(subLevel: SubLevel): void {
        // TODO: Implement asset unloading
    }
    
    private startSubLevelSystems(subLevel: SubLevel): void {
        // TODO: Start spawners, events, etc.
    }
    
    private stopSubLevelSystems(subLevel: SubLevel): void {
        // TODO: Stop spawners, events, etc.
    }
    
    private preloadConnectedSubLevels(subLevel: SubLevel): void {
        // TODO: Preload connected sublevels
    }
    
    private triggerEntryEvents(subLevel: SubLevel): void {
        // TODO: Trigger entry events
    }
    
    private triggerExitEvents(subLevel: SubLevel): void {
        // TODO: Trigger exit events
    }
    
    private deactivateOldestSubLevel(): void {
        // TODO: Implement LRU deactivation
    }
    
    // Public API methods
    public getCurrentLevel(): ILevelData | null {
        return this.currentLevel;
    }
    
    public getActiveSubLevels(): SubLevel[] {
        return Array.from(this.activeSubLevels).map(id => {
            const component = this.subLevelComponents.get(id);
            return component?.subLevelData;
        }).filter(Boolean) as SubLevel[];
    }
    
    public getSubLevelComponent(subLevelId: string): SubLevelComponent | undefined {
        return this.subLevelComponents.get(subLevelId);
    }
}
