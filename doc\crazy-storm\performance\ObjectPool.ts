/**
 * CrazyStorm 2.0 Integration for Cocos Creator
 * Object Pool System
 * 
 * This system provides efficient object pooling for particles and other
 * frequently created/destroyed objects to reduce garbage collection pressure.
 */

import { log, warn, error } from 'cc';

/**
 * Interface for poolable objects
 */
export interface IPoolable {
    reset(): void;
    destroy?(): void;
}

/**
 * Pool statistics
 */
export interface PoolStats {
    name: string;
    capacity: number;
    available: number;
    inUse: number;
    totalCreated: number;
    totalReused: number;
    hitRate: number;
}

/**
 * Generic object pool
 */
export class ObjectPool<T extends IPoolable> {
    private name: string;
    private factory: () => T;
    private pool: T[] = [];
    private capacity: number;
    private totalCreated: number = 0;
    private totalReused: number = 0;
    private inUseCount: number = 0;

    constructor(name: string, factory: () => T, initialCapacity: number = 50, maxCapacity: number = 1000) {
        this.name = name;
        this.factory = factory;
        this.capacity = maxCapacity;
        
        // Pre-populate pool
        for (let i = 0; i < initialCapacity; i++) {
            const obj = this.factory();
            this.pool.push(obj);
            this.totalCreated++;
        }

        log(`ObjectPool: Created '${name}' pool with ${initialCapacity} objects`);
    }

    /**
     * Get object from pool
     */
    public get(): T {
        let obj: T;

        if (this.pool.length > 0) {
            obj = this.pool.pop()!;
            this.totalReused++;
        } else {
            obj = this.factory();
            this.totalCreated++;
        }

        this.inUseCount++;
        return obj;
    }

    /**
     * Return object to pool
     */
    public release(obj: T): void {
        if (!obj) {
            warn(`ObjectPool: Attempted to release null object to pool '${this.name}'`);
            return;
        }

        // Reset object state
        obj.reset();

        // Add back to pool if under capacity
        if (this.pool.length < this.capacity) {
            this.pool.push(obj);
        } else {
            // Pool is full, destroy the object
            if (obj.destroy) {
                obj.destroy();
            }
        }

        this.inUseCount = Math.max(0, this.inUseCount - 1);
    }

    /**
     * Clear all objects from pool
     */
    public clear(): void {
        this.pool.forEach(obj => {
            if (obj.destroy) {
                obj.destroy();
            }
        });
        this.pool = [];
        this.inUseCount = 0;
        log(`ObjectPool: Cleared pool '${this.name}'`);
    }

    /**
     * Resize pool capacity
     */
    public resize(newCapacity: number): void {
        this.capacity = newCapacity;
        
        // Remove excess objects if new capacity is smaller
        while (this.pool.length > newCapacity) {
            const obj = this.pool.pop();
            if (obj && obj.destroy) {
                obj.destroy();
            }
        }

        log(`ObjectPool: Resized pool '${this.name}' to capacity ${newCapacity}`);
    }

    /**
     * Get pool statistics
     */
    public getStats(): PoolStats {
        const totalRequests = this.totalCreated + this.totalReused;
        return {
            name: this.name,
            capacity: this.capacity,
            available: this.pool.length,
            inUse: this.inUseCount,
            totalCreated: this.totalCreated,
            totalReused: this.totalReused,
            hitRate: totalRequests > 0 ? this.totalReused / totalRequests : 0
        };
    }

    /**
     * Warm up pool by pre-creating objects
     */
    public warmUp(count: number): void {
        const toCreate = Math.min(count, this.capacity - this.pool.length);
        
        for (let i = 0; i < toCreate; i++) {
            const obj = this.factory();
            this.pool.push(obj);
            this.totalCreated++;
        }

        log(`ObjectPool: Warmed up pool '${this.name}' with ${toCreate} objects`);
    }
}

/**
 * Pool manager for managing multiple object pools
 */
export class PoolManager {
    private static instance: PoolManager;
    private pools: Map<string, ObjectPool<any>> = new Map();

    private constructor() {}

    /**
     * Get singleton instance
     */
    public static getInstance(): PoolManager {
        if (!PoolManager.instance) {
            PoolManager.instance = new PoolManager();
        }
        return PoolManager.instance;
    }

    /**
     * Create or get a pool
     */
    public getPool<T extends IPoolable>(
        name: string, 
        factory: () => T, 
        initialCapacity: number = 50, 
        maxCapacity: number = 1000
    ): ObjectPool<T> {
        if (!this.pools.has(name)) {
            const pool = new ObjectPool(name, factory, initialCapacity, maxCapacity);
            this.pools.set(name, pool);
        }
        return this.pools.get(name)!;
    }

    /**
     * Remove a pool
     */
    public removePool(name: string): boolean {
        const pool = this.pools.get(name);
        if (pool) {
            pool.clear();
            this.pools.delete(name);
            log(`PoolManager: Removed pool '${name}'`);
            return true;
        }
        return false;
    }

    /**
     * Clear all pools
     */
    public clearAll(): void {
        this.pools.forEach(pool => pool.clear());
        this.pools.clear();
        log('PoolManager: Cleared all pools');
    }

    /**
     * Get statistics for all pools
     */
    public getAllStats(): PoolStats[] {
        const stats: PoolStats[] = [];
        this.pools.forEach(pool => {
            stats.push(pool.getStats());
        });
        return stats;
    }

    /**
     * Get total memory usage estimate
     */
    public getMemoryUsage(): {
        totalPools: number;
        totalObjects: number;
        estimatedMemoryMB: number;
    } {
        let totalObjects = 0;
        this.pools.forEach(pool => {
            const stats = pool.getStats();
            totalObjects += stats.available + stats.inUse;
        });

        // Rough estimate: 1KB per object
        const estimatedMemoryMB = (totalObjects * 1024) / (1024 * 1024);

        return {
            totalPools: this.pools.size,
            totalObjects,
            estimatedMemoryMB
        };
    }

    /**
     * Optimize all pools
     */
    public optimize(): void {
        this.pools.forEach((pool, name) => {
            const stats = pool.getStats();
            
            // If hit rate is low, reduce capacity
            if (stats.hitRate < 0.5 && stats.capacity > 10) {
                pool.resize(Math.max(10, Math.floor(stats.capacity * 0.8)));
            }
            
            // If pool is always full, increase capacity
            if (stats.available === 0 && stats.inUse > stats.capacity * 0.9) {
                pool.resize(Math.min(2000, Math.floor(stats.capacity * 1.2)));
            }
        });

        log('PoolManager: Optimized all pools');
    }
}

/**
 * Specialized particle pool
 */
export class ParticlePool<T extends IPoolable> extends ObjectPool<T> {
    private maxLifetime: number;
    private cleanupInterval: number;
    private lastCleanup: number = 0;

    constructor(
        name: string, 
        factory: () => T, 
        initialCapacity: number = 100, 
        maxCapacity: number = 2000,
        maxLifetime: number = 30000, // 30 seconds
        cleanupInterval: number = 5000 // 5 seconds
    ) {
        super(name, factory, initialCapacity, maxCapacity);
        this.maxLifetime = maxLifetime;
        this.cleanupInterval = cleanupInterval;
    }

    /**
     * Update pool (call this regularly to clean up old objects)
     */
    public update(deltaTime: number): void {
        this.lastCleanup += deltaTime * 1000; // Convert to milliseconds
        
        if (this.lastCleanup >= this.cleanupInterval) {
            this.cleanup();
            this.lastCleanup = 0;
        }
    }

    /**
     * Clean up old objects
     */
    private cleanup(): void {
        // This is a simplified cleanup - in a real implementation,
        // you'd track object creation times and remove old ones
        const stats = this.getStats();
        
        // If pool is getting too full, remove some objects
        if (stats.available > stats.capacity * 0.8) {
            const toRemove = Math.floor(stats.available * 0.2);
            for (let i = 0; i < toRemove && this.pool.length > 0; i++) {
                const obj = this.pool.pop();
                if (obj && obj.destroy) {
                    obj.destroy();
                }
            }
        }
    }
}

/**
 * Convenience function to get the global pool manager
 */
export function getPoolManager(): PoolManager {
    return PoolManager.getInstance();
}
