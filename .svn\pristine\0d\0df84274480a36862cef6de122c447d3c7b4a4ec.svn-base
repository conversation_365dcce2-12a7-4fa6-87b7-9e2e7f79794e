/**
 * CrazyStorm 2.0 Integration for Cocos Creator
 * Performance Module Index
 * 
 * This file exports all performance-related functionality including
 * object pooling, performance monitoring, mobile optimization, and profiling.
 */

// Object pooling
export { ObjectPool, ParticlePool, PoolManager, getPoolManager } from './ObjectPool';
export type { IPoolable, PoolStats } from './ObjectPool';

// Performance monitoring
export { PerformanceMonitor, getPerformanceMonitor } from './PerformanceMonitor';
export type { 
    PerformanceMetrics, 
    PerformanceThresholds, 
    OptimizationRecommendation 
} from './PerformanceMonitor';
export { DeviceTier } from './PerformanceMonitor';

// Mobile optimization
export { MobileOptimizer, getMobileOptimizer } from './MobileOptimizer';
export type { 
    QualitySettings, 
    LODConfig, 
    CullingBounds 
} from './MobileOptimizer';

// Profiling
export { Profiler, getProfiler, profile, profileAsync } from './Profiler';

/**
 * Initialize all performance systems
 */
export function initializePerformanceSystems(): void {
    console.log('CrazyStorm Performance Systems: Initializing...');
    
    // Initialize performance monitor
    const monitor = getPerformanceMonitor();
    monitor.startMonitoring();
    
    // Initialize mobile optimizer
    const optimizer = getMobileOptimizer();
    optimizer.initialize();
    
    // Initialize profiler (disabled by default)
    const profiler = getProfiler();
    // profiler.enable(); // Uncomment for development builds
    
    console.log('CrazyStorm Performance Systems: Initialized');
}

/**
 * Update all performance systems (call this every frame)
 */
export function updatePerformanceSystems(deltaTime: number, particleCount: number = 0, drawCalls: number = 0): void {
    // Update performance monitor
    const monitor = getPerformanceMonitor();
    monitor.update(deltaTime, particleCount, drawCalls);
    
    // Update mobile optimizer
    const optimizer = getMobileOptimizer();
    optimizer.update(deltaTime);
}

/**
 * Get comprehensive performance report
 */
export function getPerformanceReport(): {
    monitor: any;
    optimizer: any;
    poolManager: any;
    profiler: any;
} {
    const monitor = getPerformanceMonitor();
    const optimizer = getMobileOptimizer();
    const poolManager = getPoolManager();
    const profiler = getProfiler();
    
    return {
        monitor: {
            deviceTier: monitor.getDeviceTier(),
            currentMetrics: monitor.getCurrentMetrics(),
            averageMetrics: monitor.getAverageMetrics(30),
            recommendations: monitor.getOptimizationRecommendations()
        },
        optimizer: optimizer.getStats(),
        poolManager: {
            stats: poolManager.getAllStats(),
            memoryUsage: poolManager.getMemoryUsage()
        },
        profiler: {
            currentSession: profiler.getCurrentSession(),
            bottlenecks: profiler.getBottlenecks(),
            memoryUsage: profiler.getMemoryUsage()
        }
    };
}
