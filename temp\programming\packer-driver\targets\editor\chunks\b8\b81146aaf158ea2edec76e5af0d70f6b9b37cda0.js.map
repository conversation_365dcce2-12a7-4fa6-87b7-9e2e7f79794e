{"version": 3, "sources": ["file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/factroy/EnemyBulletFactory.ts"], "names": ["EnemyBulletFactory", "_decorator", "instantiate", "EnemyBullet", "Global", "PersistNode", "GameFactory", "ccclass", "property", "createProduct", "productType", "enemyBulletTemp", "productPool", "size", "get", "persistNode", "getComponent", "enemyBulletPreb", "ENEMY_BULLET_1", "init", "enemybullet1", "ENEMY_BULLET_2", "enemybullet2"], "mappings": ";;;uJAQaA,kB;;;;;;;;;;;;;;;;;;;;;;;;;;;AARJC,MAAAA,U,OAAAA,U;AAA6BC,MAAAA,W,OAAAA,W;;AAC7BC,MAAAA,W,iBAAAA,W;;AACAC,MAAAA,M,iBAAAA,M;;AACAC,MAAAA,W,iBAAAA,W;;AACAC,MAAAA,W,iBAAAA,W;;;;;;;;;OACH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBP,U;;oCAGjBD,kB,GAAN,MAAMA,kBAAN;AAAA;AAAA,sCAA6C;AAEzCS,QAAAA,aAAa,CAACC,WAAD,EAA4B;AAC5C,cAAIC,eAAqB,GAAG,IAA5B;;AAEA,cAAI,KAAKC,WAAL,CAAiBC,IAAjB,KAA0B,CAA9B,EAAiC;AAC7BF,YAAAA,eAAe,GAAG,KAAKC,WAAL,CAAiBE,GAAjB,EAAlB,CAD6B,CACc;AAC9C,WAFD,MAEO;AACHH,YAAAA,eAAe,GAAGT,WAAW,CAAC,KAAKa,WAAL,CAAiBC,YAAjB;AAAA;AAAA,4CAA2CC,eAA5C,CAA7B,CADG,CACyF;AAC/F;;AAED,kBAAOP,WAAP;AACI,iBAAK;AAAA;AAAA,kCAAOQ,cAAZ;AACIP,cAAAA,eAAe,CAACK,YAAhB;AAAA;AAAA,8CAA0CG,IAA1C,CAA+CT,WAA/C,EAA4D,KAAKK,WAAL,CAAiBC,YAAjB;AAAA;AAAA,8CAA2CI,YAAvG,EADJ,CAC2H;;AACvH;;AACJ,iBAAK;AAAA;AAAA,kCAAOC,cAAZ;AACIV,cAAAA,eAAe,CAACK,YAAhB;AAAA;AAAA,8CAA0CG,IAA1C,CAA+CT,WAA/C,EAA4D,KAAKK,WAAL,CAAiBC,YAAjB;AAAA;AAAA,8CAA2CM,YAAvG;AACA;AANR;;AASA,iBAAOX,eAAP;AACH;;AArB+C,O", "sourcesContent": ["import { _decorator, Component, Node, instantiate } from 'cc';\nimport { EnemyBullet } from '../EnemyBullet';\nimport { Global } from '../Global';\nimport { PersistNode } from '../PersistNode';\nimport { GameFactory } from './GameFactory';\nconst { ccclass, property } = _decorator;\n\n\nexport class EnemyBulletFactory extends GameFactory {\n    \n    public createProduct(productType: string): Node {\n        let enemyBulletTemp: Node = null;\n\n        if (this.productPool.size() > 0) {\n            enemyBulletTemp = this.productPool.get();  //如果池里有敌机子弹，就直接拿来用\n        } else {\n            enemyBulletTemp = instantiate(this.persistNode.getComponent(PersistNode).enemyBulletPreb);  //从常驻节点拿到预制体原料\n        }\n        \n        switch(productType) {\n            case Global.ENEMY_BULLET_1:\n                enemyBulletTemp.getComponent(EnemyBullet).init(productType, this.persistNode.getComponent(PersistNode).enemybullet1);  //通过调用EnemyBullet的init方法，来创建子弹\n                break;\n            case Global.ENEMY_BULLET_2:\n                enemyBulletTemp.getComponent(EnemyBullet).init(productType, this.persistNode.getComponent(PersistNode).enemybullet2);\n                break;\n        }\n\n        return enemyBulletTemp;\n    }\n\n}\n\n"]}