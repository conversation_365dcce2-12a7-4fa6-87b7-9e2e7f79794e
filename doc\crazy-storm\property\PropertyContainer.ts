/**
 * CrazyStorm 2.0 Integration for Cocos Creator
 * Property Container System
 * 
 * Sophisticated property management with validation, constraints, and expression compilation.
 * Based on the C# PropertyContainer.cs implementation.
 */

import { Vec2, log, warn } from 'cc';
import { ExpressionVM, VMInstruction, VMContext } from '../expression';
import { PropertyType, TypeSet } from '../events';

/**
 * Property metadata for validation and constraints
 */
export interface PropertyMetadata {
    type: PropertyType;
    displayName?: string;
    description?: string;
    defaultValue?: any;
    minValue?: number;
    maxValue?: number;
    allowedValues?: any[];
    readonly?: boolean;
    hidden?: boolean;
    category?: string;
    validator?: (value: any) => boolean;
    formatter?: (value: any) => string;
}

/**
 * Property value with expression support
 */
export interface PropertyValue {
    value: any;
    expression: boolean;
    compiledExpression?: VMInstruction[];
    metadata?: PropertyMetadata;
    lastEvaluatedValue?: any;
    dirty?: boolean;
}

/**
 * Property change event
 */
export interface PropertyChangeEvent {
    propertyName: string;
    oldValue: any;
    newValue: any;
    source: 'user' | 'expression' | 'event' | 'binding';
}

/**
 * Property change listener
 */
export type PropertyChangeListener = (event: PropertyChangeEvent) => void;

/**
 * Enhanced property container with sophisticated property management
 */
export abstract class PropertyContainer {
    protected properties: Map<string, PropertyValue> = new Map();
    protected metadata: Map<string, PropertyMetadata> = new Map();
    protected changeListeners: PropertyChangeListener[] = [];
    protected bindingTargets: Map<string, PropertyContainer[]> = new Map();
    protected vmContext: VMContext = {};

    constructor() {
        this.initializeProperties();
    }

    /**
     * Initialize default properties (to be implemented by subclasses)
     */
    protected abstract initializeProperties(): void;

    /**
     * Register property metadata
     */
    protected registerProperty(
        name: string, 
        metadata: PropertyMetadata, 
        initialValue?: any
    ): void {
        this.metadata.set(name, metadata);
        
        const value = initialValue !== undefined ? initialValue : metadata.defaultValue;
        this.properties.set(name, {
            value: value,
            expression: false,
            metadata: metadata,
            dirty: false
        });
    }

    /**
     * Set property value with validation
     */
    public setProperty(propertyName: string, value: any, source: 'user' | 'expression' | 'event' | 'binding' = 'user'): boolean {
        const property = this.properties.get(propertyName);
        const metadata = this.metadata.get(propertyName);
        
        if (!property) {
            warn(`PropertyContainer: Unknown property: ${propertyName}`);
            return false;
        }

        if (metadata?.readonly && source === 'user') {
            warn(`PropertyContainer: Property ${propertyName} is readonly`);
            return false;
        }

        // Validate value
        if (!this.validateValue(propertyName, value, metadata)) {
            return false;
        }

        const oldValue = property.value;
        property.value = value;
        property.dirty = true;

        // Notify listeners
        this.notifyPropertyChange({
            propertyName,
            oldValue,
            newValue: value,
            source
        });

        // Update binding targets
        this.updateBindingTargets(propertyName, value);

        return true;
    }

    /**
     * Get property value
     */
    public getProperty(propertyName: string): any {
        const property = this.properties.get(propertyName);
        if (!property) {
            warn(`PropertyContainer: Unknown property: ${propertyName}`);
            return undefined;
        }

        // If property has expression, evaluate it
        if (property.expression && property.compiledExpression) {
            try {
                const result = ExpressionVM.execute(property.compiledExpression, this.vmContext);
                property.lastEvaluatedValue = result;
                return result;
            } catch (error) {
                warn(`PropertyContainer: Error evaluating expression for ${propertyName}:`, error);
                return property.value;
            }
        }

        return property.value;
    }

    /**
     * Set property expression
     */
    public setPropertyExpression(propertyName: string, expression: string): boolean {
        const property = this.properties.get(propertyName);
        if (!property) {
            warn(`PropertyContainer: Unknown property: ${propertyName}`);
            return false;
        }

        try {
            // Compile expression to bytecode
            const compiledExpression = ExpressionVM.compileExpression(expression);
            
            property.expression = true;
            property.compiledExpression = compiledExpression;
            property.value = expression; // Store original expression string
            property.dirty = true;

            log(`PropertyContainer: Set expression for ${propertyName}: ${expression}`);
            return true;
        } catch (error) {
            warn(`PropertyContainer: Error compiling expression for ${propertyName}: ${expression}`, error);
            return false;
        }
    }

    /**
     * Clear property expression
     */
    public clearPropertyExpression(propertyName: string): boolean {
        const property = this.properties.get(propertyName);
        if (!property) {
            return false;
        }

        property.expression = false;
        property.compiledExpression = undefined;
        property.lastEvaluatedValue = undefined;
        property.dirty = true;

        return true;
    }

    /**
     * Check if property has expression
     */
    public hasExpression(propertyName: string): boolean {
        const property = this.properties.get(propertyName);
        return property?.expression || false;
    }

    /**
     * Get property metadata
     */
    public getPropertyMetadata(propertyName: string): PropertyMetadata | undefined {
        return this.metadata.get(propertyName);
    }

    /**
     * Get all property names
     */
    public getPropertyNames(): string[] {
        return Array.from(this.properties.keys());
    }

    /**
     * Get properties by category
     */
    public getPropertiesByCategory(category: string): string[] {
        const result: string[] = [];
        
        for (const [name, metadata] of this.metadata) {
            if (metadata.category === category) {
                result.push(name);
            }
        }
        
        return result;
    }

    /**
     * Validate property value
     */
    protected validateValue(propertyName: string, value: any, metadata?: PropertyMetadata): boolean {
        if (!metadata) {
            return true;
        }

        // Type validation
        if (!this.validateType(value, metadata.type)) {
            warn(`PropertyContainer: Invalid type for ${propertyName}. Expected ${PropertyType[metadata.type]}`);
            return false;
        }

        // Range validation
        if (metadata.minValue !== undefined && value < metadata.minValue) {
            warn(`PropertyContainer: Value ${value} for ${propertyName} is below minimum ${metadata.minValue}`);
            return false;
        }

        if (metadata.maxValue !== undefined && value > metadata.maxValue) {
            warn(`PropertyContainer: Value ${value} for ${propertyName} is above maximum ${metadata.maxValue}`);
            return false;
        }

        // Allowed values validation
        if (metadata.allowedValues && !metadata.allowedValues.includes(value)) {
            warn(`PropertyContainer: Value ${value} for ${propertyName} is not in allowed values`);
            return false;
        }

        // Custom validator
        if (metadata.validator && !metadata.validator(value)) {
            warn(`PropertyContainer: Custom validation failed for ${propertyName}`);
            return false;
        }

        return true;
    }

    /**
     * Validate value type
     */
    protected validateType(value: any, expectedType: PropertyType): boolean {
        switch (expectedType) {
            case PropertyType.Boolean:
                return typeof value === 'boolean';
            case PropertyType.Int32:
                return typeof value === 'number' && Number.isInteger(value);
            case PropertyType.Single:
                return typeof value === 'number';
            case PropertyType.String:
                return typeof value === 'string';
            case PropertyType.Vector2:
                return value instanceof Vec2 || (typeof value === 'object' && value.x !== undefined && value.y !== undefined);
            case PropertyType.RGB:
                return typeof value === 'object' && value.r !== undefined && value.g !== undefined && value.b !== undefined;
            case PropertyType.Enum:
                return typeof value === 'number' && Number.isInteger(value);
            default:
                return true;
        }
    }

    /**
     * Add property change listener
     */
    public addPropertyChangeListener(listener: PropertyChangeListener): void {
        this.changeListeners.push(listener);
    }

    /**
     * Remove property change listener
     */
    public removePropertyChangeListener(listener: PropertyChangeListener): void {
        const index = this.changeListeners.indexOf(listener);
        if (index >= 0) {
            this.changeListeners.splice(index, 1);
        }
    }

    /**
     * Notify property change listeners
     */
    protected notifyPropertyChange(event: PropertyChangeEvent): void {
        this.changeListeners.forEach(listener => {
            try {
                listener(event);
            } catch (error) {
                warn('PropertyContainer: Error in property change listener:', error);
            }
        });
    }

    /**
     * Add binding target
     */
    public addBindingTarget(propertyName: string, target: PropertyContainer): void {
        if (!this.bindingTargets.has(propertyName)) {
            this.bindingTargets.set(propertyName, []);
        }
        this.bindingTargets.get(propertyName)!.push(target);
    }

    /**
     * Remove binding target
     */
    public removeBindingTarget(propertyName: string, target: PropertyContainer): void {
        const targets = this.bindingTargets.get(propertyName);
        if (targets) {
            const index = targets.indexOf(target);
            if (index >= 0) {
                targets.splice(index, 1);
            }
        }
    }

    /**
     * Update binding targets
     */
    protected updateBindingTargets(propertyName: string, value: any): void {
        const targets = this.bindingTargets.get(propertyName);
        if (targets) {
            targets.forEach(target => {
                target.setProperty(propertyName, value, 'binding');
            });
        }
    }

    /**
     * Update VM context
     */
    public updateVMContext(context: Partial<VMContext>): void {
        this.vmContext = { ...this.vmContext, ...context };
    }

    /**
     * Execute all property expressions
     */
    public executeExpressions(): void {
        for (const [propertyName, property] of this.properties) {
            if (property.expression && property.compiledExpression) {
                try {
                    const result = ExpressionVM.execute(property.compiledExpression, this.vmContext);
                    property.lastEvaluatedValue = result;
                    
                    // Update the actual property value if it changed
                    if (result !== property.lastEvaluatedValue) {
                        this.setProperty(propertyName, result, 'expression');
                    }
                } catch (error) {
                    warn(`PropertyContainer: Error executing expression for ${propertyName}:`, error);
                }
            }
        }
    }

    /**
     * Push property value onto VM stack (for VM integration)
     */
    public abstract pushProperty(propertyName: string): boolean;

    /**
     * Set property value from VM stack (for VM integration)
     */
    public abstract setPropertyFromStack(propertyName: string): boolean;

    /**
     * Get debug information
     */
    public getDebugInfo(): any {
        const info: any = {
            properties: {},
            metadata: {},
            expressions: {}
        };

        for (const [name, property] of this.properties) {
            info.properties[name] = property.value;
            if (property.expression) {
                info.expressions[name] = {
                    expression: property.value,
                    lastEvaluated: property.lastEvaluatedValue
                };
            }
        }

        for (const [name, metadata] of this.metadata) {
            info.metadata[name] = {
                type: PropertyType[metadata.type],
                category: metadata.category,
                readonly: metadata.readonly
            };
        }

        return info;
    }
}

/**
 * Property validation utilities
 */
export class PropertyValidator {
    /**
     * Create a range validator
     */
    public static range(min: number, max: number): (value: any) => boolean {
        return (value: any) => {
            const num = Number(value);
            return !isNaN(num) && num >= min && num <= max;
        };
    }

    /**
     * Create a positive number validator
     */
    public static positive(): (value: any) => boolean {
        return (value: any) => {
            const num = Number(value);
            return !isNaN(num) && num > 0;
        };
    }

    /**
     * Create a non-negative number validator
     */
    public static nonNegative(): (value: any) => boolean {
        return (value: any) => {
            const num = Number(value);
            return !isNaN(num) && num >= 0;
        };
    }

    /**
     * Create a string length validator
     */
    public static stringLength(minLength: number, maxLength?: number): (value: any) => boolean {
        return (value: any) => {
            const str = String(value);
            return str.length >= minLength && (maxLength === undefined || str.length <= maxLength);
        };
    }

    /**
     * Create a regex pattern validator
     */
    public static pattern(regex: RegExp): (value: any) => boolean {
        return (value: any) => {
            return regex.test(String(value));
        };
    }

    /**
     * Create a custom validator that combines multiple validators
     */
    public static combine(...validators: ((value: any) => boolean)[]): (value: any) => boolean {
        return (value: any) => {
            return validators.every(validator => validator(value));
        };
    }
}

/**
 * Property formatters for display
 */
export class PropertyFormatter {
    /**
     * Format number with specified decimal places
     */
    public static decimal(places: number): (value: any) => string {
        return (value: any) => {
            const num = Number(value);
            return isNaN(num) ? String(value) : num.toFixed(places);
        };
    }

    /**
     * Format as percentage
     */
    public static percentage(places: number = 1): (value: any) => string {
        return (value: any) => {
            const num = Number(value);
            return isNaN(num) ? String(value) : `${(num * 100).toFixed(places)}%`;
        };
    }

    /**
     * Format angle in degrees
     */
    public static degrees(): (value: any) => string {
        return (value: any) => {
            const num = Number(value);
            return isNaN(num) ? String(value) : `${num.toFixed(1)}°`;
        };
    }

    /**
     * Format vector2
     */
    public static vector2(places: number = 2): (value: any) => string {
        return (value: any) => {
            if (value instanceof Vec2) {
                return `(${value.x.toFixed(places)}, ${value.y.toFixed(places)})`;
            }
            if (typeof value === 'object' && value.x !== undefined && value.y !== undefined) {
                return `(${Number(value.x).toFixed(places)}, ${Number(value.y).toFixed(places)})`;
            }
            return String(value);
        };
    }

    /**
     * Format RGB color
     */
    public static rgb(): (value: any) => string {
        return (value: any) => {
            if (typeof value === 'object' && value.r !== undefined) {
                return `rgb(${Math.round(value.r)}, ${Math.round(value.g)}, ${Math.round(value.b)})`;
            }
            return String(value);
        };
    }
}
