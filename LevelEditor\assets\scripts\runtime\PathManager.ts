import { _decorator, Component, Node, Vec3, Color } from 'cc';
import { Path, BezierPoint } from '../core/LevelData';
import { PathUtils, IDGenerator } from '../core/Types';

const { ccclass, property } = _decorator;

/**
 * Path manager for handling multiple paths
 */
@ccclass('PathManager')
export class PathManager extends Component {
    private paths: Map<string, Path> = new Map();
    
    /**
     * Add a path to the manager
     */
    public addPath(path: Path): void {
        this.paths.set(path.id, path);
        console.log(`Path added: ${path.name}`);
    }
    
    /**
     * Remove a path from the manager
     */
    public removePath(pathId: string): void {
        if (this.paths.delete(pathId)) {
            console.log(`Path removed: ${pathId}`);
        }
    }
    
    /**
     * Get a path by ID
     */
    public getPath(pathId: string): Path | undefined {
        return this.paths.get(pathId);
    }
    
    /**
     * Get all paths
     */
    public getAllPaths(): Path[] {
        return Array.from(this.paths.values());
    }
    
    /**
     * Create a new path
     */
    public createPath(name: string, startPosition: Vec3): Path {
        const path: Path = {
            id: IDGenerator.generate('path'),
            name: name,
            points: [
                {
                    position: startPosition.clone(),
                    controlPoint1: Vec3.ZERO,
                    controlPoint2: Vec3.ZERO,
                    speed: 100,
                    rotation: 0
                }
            ],
            isLoop: false,
            totalLength: 0,
            editorData: {
                color: Color.CYAN,
                showDirection: true,
                showSpeed: false,
                notes: ''
            }
        };
        
        this.addPath(path);
        return path;
    }
    
    /**
     * Add a point to a path
     */
    public addPointToPath(pathId: string, position: Vec3, insertIndex?: number): boolean {
        const path = this.getPath(pathId);
        if (!path) return false;
        
        const newPoint: BezierPoint = {
            position: position.clone(),
            controlPoint1: Vec3.ZERO,
            controlPoint2: Vec3.ZERO,
            speed: 100,
            rotation: 0
        };
        
        if (insertIndex !== undefined && insertIndex >= 0 && insertIndex <= path.points.length) {
            path.points.splice(insertIndex, 0, newPoint);
        } else {
            path.points.push(newPoint);
        }
        
        this.recalculatePathLength(path);
        return true;
    }
    
    /**
     * Remove a point from a path
     */
    public removePointFromPath(pathId: string, pointIndex: number): boolean {
        const path = this.getPath(pathId);
        if (!path || pointIndex < 0 || pointIndex >= path.points.length) return false;
        
        // Don't allow removing if only one point left
        if (path.points.length <= 1) return false;
        
        path.points.splice(pointIndex, 1);
        this.recalculatePathLength(path);
        return true;
    }
    
    /**
     * Update a point in a path
     */
    public updatePathPoint(pathId: string, pointIndex: number, point: Partial<BezierPoint>): boolean {
        const path = this.getPath(pathId);
        if (!path || pointIndex < 0 || pointIndex >= path.points.length) return false;
        
        const existingPoint = path.points[pointIndex];
        Object.assign(existingPoint, point);
        
        this.recalculatePathLength(path);
        return true;
    }
    
    /**
     * Recalculate total path length
     */
    private recalculatePathLength(path: Path): void {
        let totalLength = 0;
        
        for (let i = 0; i < path.points.length - 1; i++) {
            const point1 = path.points[i];
            const point2 = path.points[i + 1];
            
            const p0 = point1.position;
            const p1 = point1.position.clone().add(point1.controlPoint2);
            const p2 = point2.position.clone().add(point2.controlPoint1);
            const p3 = point2.position;
            
            totalLength += PathUtils.calculateBezierLength(p0, p1, p2, p3);
        }
        
        path.totalLength = totalLength;
    }
    
    /**
     * Validate a path
     */
    public validatePath(path: Path): { isValid: boolean; errors: string[] } {
        const errors: string[] = [];
        
        if (!path.name || path.name.trim() === '') {
            errors.push('Path name is required');
        }
        
        if (path.points.length < 2) {
            errors.push('Path must have at least 2 points');
        }
        
        // Check for invalid positions
        path.points.forEach((point, index) => {
            if (!isFinite(point.position.x) || !isFinite(point.position.y) || !isFinite(point.position.z)) {
                errors.push(`Point ${index} has invalid position`);
            }
            
            if (point.speed < 0) {
                errors.push(`Point ${index} has negative speed`);
            }
        });
        
        return {
            isValid: errors.length === 0,
            errors
        };
    }
}
