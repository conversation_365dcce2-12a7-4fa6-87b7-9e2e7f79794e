/**
 * CrazyStorm 2.0 Integration for Cocos Creator
 * Particle Physics System
 * 
 * This file contains the physics simulation for CrazyStorm particles,
 * including movement, collision detection, and force application.
 */

import { Vec2, Vec3, Color, Node, log, warn } from 'cc';
import { 
    RuntimeParticle, 
    ParticleType, 
    BlendType,
    ParticleBaseData 
} from '../core/CrazyStormTypes';

/**
 * Particle instance for runtime simulation
 */
export class Particle implements RuntimeParticle {
    public id: number;
    public alive: boolean = false;
    public position: Vec2 = new Vec2();
    public velocity: Vec2 = new Vec2();
    public acceleration: Vec2 = new Vec2();
    public rotation: number = 0;
    public scale: number = 1;
    public opacity: number = 1;
    public color: Color = new Color(255, 255, 255, 255);
    public currentFrame: number = 0;
    public maxLife: number = 200;
    public particleType: ParticleType;
    public emitterId: number;
    public layerId: number;

    // Physics properties
    public mass: number = 1;
    public speedVector: Vec2 = new Vec2();
    public acspeedVector: Vec2 = new Vec2();
    public rotationSpeed: number = 0;

    // Behavior flags
    public killOutside: boolean = true;
    public collision: boolean = true;
    public ignoreMask: boolean = false;
    public ignoreRebound: boolean = false;
    public ignoreForce: boolean = false;
    public fogEffect: boolean = true;
    public fadeEffect: boolean = true;

    // Visual properties
    public blendType: BlendType = BlendType.AlphaBlend;
    public widthScale: number = 1;
    public fogFrame: number = 0;

    // Cocos Creator node reference
    public node: Node | null = null;

    constructor(id: number, particleType: ParticleType, emitterId: number, layerId: number) {
        this.id = id;
        this.particleType = particleType;
        this.emitterId = emitterId;
        this.layerId = layerId;
    }

    /**
     * Initialize particle with base data
     */
    public initializeWithData(data: ParticleBaseData): void {
        this.maxLife = data.maxLife;
        this.currentFrame = data.pcurrentFrame;
        this.position.set(data.pposition.x, data.pposition.y);
        this.widthScale = data.widthScale;
        this.color = data.rgb.clone();
        this.mass = data.mass;
        this.opacity = data.opacity / 100; // Convert from 0-100 to 0-1
        this.rotation = data.protation;
        this.blendType = data.blendType;
        this.killOutside = data.killOutside;
        this.collision = data.collision;
        this.ignoreMask = data.ignoreMask;
        this.ignoreRebound = data.ignoreRebound;
        this.ignoreForce = data.ignoreForce;
        this.fogEffect = data.fogEffect;
        this.fadeEffect = data.fadeEffect;

        // Set initial velocity
        this.setSpeedVector(data.pspeed, data.pspeedAngle);
        this.setAccelerationVector(data.pacspeed, data.pacspeedAngle);

        this.alive = true;
        this.currentFrame = 0;
        this.fogFrame = 0;
    }

    /**
     * Update particle physics
     */
    public update(deltaTime: number): boolean {
        if (!this.alive) {
            return false;
        }

        // Check if particle should die
        if (this.currentFrame >= this.maxLife) {
            this.alive = false;
            return false;
        }

        // Update physics
        this.updatePhysics(deltaTime);

        // Update visual properties
        this.updateVisuals();

        // Update frame counter
        this.currentFrame++;

        // Update fog frame for fade effects
        this.updateFogFrame();

        return true;
    }

    /**
     * Set speed vector from magnitude and angle
     */
    public setSpeedVector(speed: number, angle: number): void {
        const radians = angle * Math.PI / 180;
        this.speedVector.set(
            speed * Math.cos(radians),
            speed * Math.sin(radians)
        );
        this.velocity.set(this.speedVector.x, this.speedVector.y);
    }

    /**
     * Set acceleration vector from magnitude and angle
     */
    public setAccelerationVector(speed: number, angle: number): void {
        const radians = angle * Math.PI / 180;
        this.acspeedVector.set(
            speed * Math.cos(radians),
            speed * Math.sin(radians)
        );
        this.acceleration.set(this.acspeedVector.x, this.acspeedVector.y);
    }

    /**
     * Apply force to particle
     */
    public applyForce(force: Vec2): void {
        if (this.ignoreForce) {
            return;
        }

        // F = ma, so a = F/m
        this.acceleration.x += force.x / this.mass;
        this.acceleration.y += force.y / this.mass;
    }

    /**
     * Check if particle is outside bounds
     */
    public isOutsideBounds(bounds: { left: number; right: number; top: number; bottom: number }): boolean {
        return this.position.x < bounds.left || 
               this.position.x > bounds.right ||
               this.position.y < bounds.top || 
               this.position.y > bounds.bottom;
    }

    /**
     * Get distance to another particle
     */
    public getDistanceTo(other: Particle): number {
        const dx = this.position.x - other.position.x;
        const dy = this.position.y - other.position.y;
        return Math.sqrt(dx * dx + dy * dy);
    }

    /**
     * Check collision with another particle
     */
    public checkCollisionWith(other: Particle): boolean {
        if (!this.collision || !other.collision) {
            return false;
        }

        const distance = this.getDistanceTo(other);
        const combinedRadius = this.particleType.radius + other.particleType.radius;
        
        return distance <= combinedRadius;
    }

    /**
     * Reset particle to initial state
     */
    public reset(): void {
        this.alive = false;
        this.currentFrame = 0;
        this.fogFrame = 0;
        this.position.set(0, 0);
        this.velocity.set(0, 0);
        this.acceleration.set(0, 0);
        this.rotation = 0;
        this.scale = 1;
        this.opacity = 1;
        this.color.set(255, 255, 255, 255);
    }

    // Private helper methods

    /**
     * Update particle physics
     */
    private updatePhysics(deltaTime: number): void {
        // Apply acceleration to velocity
        this.velocity.x += this.acceleration.x * deltaTime;
        this.velocity.y += this.acceleration.y * deltaTime;

        // Apply velocity to position
        this.position.x += this.velocity.x * deltaTime;
        this.position.y += this.velocity.y * deltaTime;

        // Update rotation
        this.rotation += this.rotationSpeed * deltaTime;

        // Update node position if available
        if (this.node) {
            this.node.setPosition(this.position.x, this.position.y, 0);
            this.node.setRotationFromEuler(0, 0, this.rotation);
        }
    }

    /**
     * Update visual properties
     */
    private updateVisuals(): void {
        if (!this.node) {
            return;
        }

        // Update scale
        this.node.setScale(this.scale * this.widthScale, this.scale, this.scale);

        // Update opacity with fade effect
        let currentOpacity = this.opacity;
        
        if (this.fadeEffect) {
            const fadeRatio = this.getFadeRatio();
            currentOpacity *= fadeRatio;
        }

        // Apply fog effect
        if (this.fogEffect && this.fogFrame < 60) { // FOG_TIME = 60 frames
            const fogRatio = this.fogFrame / 60;
            currentOpacity *= fogRatio;
        }

        // Update node opacity
        this.node.getComponent('UIOpacity')?.setOpacity(currentOpacity * 255);
    }

    /**
     * Update fog frame for fade effects
     */
    private updateFogFrame(): void {
        const FOG_TIME = 60;
        
        if (this.maxLife <= FOG_TIME) {
            this.fogFrame = FOG_TIME;
        } else if (this.currentFrame < this.maxLife - FOG_TIME) {
            this.fogFrame++;
            if (!this.fogEffect || this.fogFrame >= FOG_TIME) {
                this.fogFrame = FOG_TIME;
            }
        }
    }

    /**
     * Get fade ratio for fade effect
     */
    private getFadeRatio(): number {
        if (!this.fadeEffect) {
            return 1;
        }

        const lifeRatio = this.currentFrame / this.maxLife;
        
        // Fade in during first 10% of life
        if (lifeRatio < 0.1) {
            return lifeRatio / 0.1;
        }
        
        // Fade out during last 20% of life
        if (lifeRatio > 0.8) {
            return (1 - lifeRatio) / 0.2;
        }
        
        // Full opacity in middle
        return 1;
    }
}

/**
 * Curve particle with trail rendering
 */
export class CurveParticle extends Particle {
    public length: number = 10;
    public trail: Vec2[] = [];

    constructor(id: number, particleType: ParticleType, emitterId: number, layerId: number, length: number = 10) {
        super(id, particleType, emitterId, layerId);
        this.length = length;
        this.trail = [];
    }

    /**
     * Update curve particle with trail
     */
    public update(deltaTime: number): boolean {
        const result = super.update(deltaTime);
        
        if (result) {
            this.updateTrail();
        }
        
        return result;
    }

    /**
     * Update particle trail
     */
    private updateTrail(): void {
        // Add current position to trail
        this.trail.push(new Vec2(this.position.x, this.position.y));
        
        // Limit trail length
        if (this.trail.length > this.length) {
            this.trail.shift();
        }
    }

    /**
     * Get trail points for rendering
     */
    public getTrailPoints(): Vec2[] {
        return [...this.trail];
    }

    /**
     * Reset curve particle
     */
    public reset(): void {
        super.reset();
        this.trail = [];
    }
}

/**
 * Utility functions for particle physics
 */
export class ParticlePhysicsUtils {
    /**
     * Calculate angle between two points
     */
    public static getAngleBetween(from: Vec2, to: Vec2): number {
        const dx = to.x - from.x;
        const dy = to.y - from.y;
        return Math.atan2(dy, dx) * 180 / Math.PI;
    }

    /**
     * Calculate distance between two points
     */
    public static getDistance(from: Vec2, to: Vec2): number {
        const dx = to.x - from.x;
        const dy = to.y - from.y;
        return Math.sqrt(dx * dx + dy * dy);
    }

    /**
     * Normalize angle to 0-360 range
     */
    public static normalizeAngle(angle: number): number {
        while (angle < 0) angle += 360;
        while (angle >= 360) angle -= 360;
        return angle;
    }

    /**
     * Linear interpolation
     */
    public static lerp(a: number, b: number, t: number): number {
        return a + (b - a) * t;
    }

    /**
     * Vector from magnitude and angle
     */
    public static vectorFromAngle(magnitude: number, angle: number): Vec2 {
        const radians = angle * Math.PI / 180;
        return new Vec2(
            magnitude * Math.cos(radians),
            magnitude * Math.sin(radians)
        );
    }
}
