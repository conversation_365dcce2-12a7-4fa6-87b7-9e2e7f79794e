import { _decorator, Component,Prefab, Node,loader, director, LabelComponent, ProgressBar, JsonAsset, math, Enum, CCString, ccenum, resources } from "cc";
import { WECHAT } from "cc/env";
const { ccclass, property } = _decorator;

class ResLoadingTips {
	 public Progress : number;
	 public Context : string;
}


if (WECHAT) {
    var warnCustom = console.warn;
    console.warn =function(res){
        if(typeof res =="string"&&res.indexOf("文件路径在真机上可能无法读取")>-1 ){
            return;
        }else{
            warnCustom(res)
        }
    
    
    }
    var groupCustom = console.group;
    console.group =function(res){
        if(typeof res =="string"&&res.indexOf("读取文件/文件夹警告")>-1 ){
            return;
        }else{
            groupCustom(res)
        }
    }
}

enum GameLogLevel {
    TRACE = 0,
    DEBUG = 1,
    LOG = 2,
    INFO = 3,
    WARN = 4,
    ERROR = 5,
}
ccenum(GameLogLevel)

@ccclass("ResUpdate") 
export class ResUpdate extends Component {
    /* class member could be defined like this */
    // dummy = '';

    @property(LabelComponent)
    private countLabel : LabelComponent = null;
    @property(LabelComponent)
    private perLabel : LabelComponent = null;
    @property(LabelComponent)
    private versionLabel : LabelComponent = null;
    @property(ProgressBar)
    private loadingBar : ProgressBar= null;
    //private isMainSceneLoaded = false;
    @property({type:GameLogLevel})
    private logLevel = GameLogLevel.TRACE;

    start() {
        this.SetLogLevel();
        // Your initialization goes here.
        let THIS = this;
        cc.loader.onProgress = this.OnLoadProgress.bind(this)
        console.log('ybgg start load main scene')
        director.loadScene("Main", () => {
            console.log("load main scene success")
        })
        //director.preloadScene("MainScene", this.OnLoadProgress.bind(this), () => {
            //this.isMainSceneLoaded = true;
            //console.log("ybgg load main scene success")
        //})
    }
    SetLogLevel()
    {
        switch(this.logLevel)
        {
            case GameLogLevel.ERROR :
                console.warn = ()=>{};
            case GameLogLevel.WARN :
                console.info = ()=>{};
            case GameLogLevel.INFO :
                console.log = ()=>{};
            case GameLogLevel.LOG :
                console.debug = ()=>{};
            case GameLogLevel.DEBUG :
                console.trace = ()=>{};
            case GameLogLevel.TRACE :
        }
    }

    OnLoadProgress(completedCount:number, totalCount:number, item)
    {
        let progress = (completedCount / totalCount)
        console.log('ybgg load main scene', completedCount, totalCount, (progress*100).toFixed(2), item.id)
        if (this.node == null) {
            return;
        }
        this.perLabel.string = (progress * 100).toFixed(2) + "%"
        this.countLabel.string = '加载中...(' + completedCount + '/' + totalCount + ')'
        this.loadingBar.progress = progress
    }

    onLoad() {
    }
    onDestroy()
    {
        cc.loader.onProgress = null;
    }

    update(deltaTime: number) {
        //if (this.isMainSceneLoaded) //&& window.GameInstance && window.GameInstance.getLogin().isLogined)
        //{
        //    director.loadScene("MainScene")
        //}
        // Your update function goes here.
    }
}
