import { ILevelData } from './LevelData';

/**
 * Base interface for level serializers
 */
export interface ILevelSerializer {
    serialize(levelData: ILevelData): string | ArrayBuffer;
    deserialize(data: string | ArrayBuffer): ILevelData;
    getFormat(): 'json' | 'binary';
    getVersion(): string;
}

/**
 * JSON serializer for level data
 */
export class JsonLevelSerializer implements ILevelSerializer {
    private version: string = '1.0.0';
    
    serialize(levelData: ILevelData): string {
        try {
            // Clean the data before serialization (remove editor-only properties)
            const cleanData = this.cleanForSerialization(levelData);
            
            const serializedData = {
                version: this.version,
                format: 'json',
                timestamp: new Date().toISOString(),
                data: cleanData
            };
            
            return JSON.stringify(serializedData, null, 2);
        } catch (error) {
            throw new Error(`Failed to serialize level data: ${error.message}`);
        }
    }
    
    deserialize(data: string): ILevelData {
        try {
            const parsed = JSON.parse(data);
            
            // Validate format and version
            if (parsed.format !== 'json') {
                throw new Error('Invalid format: expected JSON');
            }
            
            if (!this.isVersionCompatible(parsed.version)) {
                throw new Error(`Incompatible version: ${parsed.version}`);
            }
            
            // Restore runtime properties
            const levelData = this.restoreRuntimeProperties(parsed.data);
            
            return levelData;
        } catch (error) {
            throw new Error(`Failed to deserialize level data: ${error.message}`);
        }
    }
    
    getFormat(): 'json' {
        return 'json';
    }
    
    getVersion(): string {
        return this.version;
    }
    
    private cleanForSerialization(levelData: ILevelData): any {
        // Deep clone and remove editor-only properties
        const cleaned = JSON.parse(JSON.stringify(levelData));
        
        // Remove runtime state from sublevels
        if (cleaned.subLevels) {
            cleaned.subLevels.forEach((subLevel: any) => {
                delete subLevel.isActive;
                delete subLevel.isLoaded;
                delete subLevel.loadPriority;
                
                // Remove editor data from maps
                if (subLevel.maps) {
                    subLevel.maps.forEach((map: any) => {
                        delete map.editorData;
                    });
                }
                
                // Remove editor data from spawners
                if (subLevel.spawners) {
                    subLevel.spawners.forEach((spawner: any) => {
                        delete spawner.editorData;
                    });
                }
                
                // Remove editor data from paths
                if (subLevel.paths) {
                    subLevel.paths.forEach((path: any) => {
                        delete path.editorData;
                    });
                }
                
                // Remove editor data from event nodes
                if (subLevel.events && subLevel.events.nodes) {
                    subLevel.events.nodes.forEach((node: any) => {
                        delete node.editorData;
                    });
                }
            });
        }
        
        return cleaned;
    }
    
    private restoreRuntimeProperties(data: any): ILevelData {
        // Restore default runtime properties
        if (data.subLevels) {
            data.subLevels.forEach((subLevel: any) => {
                subLevel.isActive = false;
                subLevel.isLoaded = false;
                subLevel.loadPriority = 0;
            });
        }
        
        return data as ILevelData;
    }
    
    private isVersionCompatible(version: string): boolean {
        // Simple version compatibility check
        const [major] = version.split('.').map(Number);
        const [currentMajor] = this.version.split('.').map(Number);
        
        return major === currentMajor;
    }
}

/**
 * Binary serializer for level data (extensible for future use)
 */
export class BinaryLevelSerializer implements ILevelSerializer {
    private version: string = '1.0.0';
    
    serialize(levelData: ILevelData): ArrayBuffer {
        try {
            // For now, use JSON as intermediate format
            // In the future, this can be replaced with proper binary serialization
            const jsonSerializer = new JsonLevelSerializer();
            const jsonData = jsonSerializer.serialize(levelData);
            
            // Convert to binary format
            const encoder = new TextEncoder();
            const binaryData = encoder.encode(jsonData);
            
            // Add binary header
            const header = this.createBinaryHeader();
            const result = new ArrayBuffer(header.byteLength + binaryData.byteLength);
            const resultView = new Uint8Array(result);
            
            resultView.set(new Uint8Array(header), 0);
            resultView.set(binaryData, header.byteLength);
            
            return result;
        } catch (error) {
            throw new Error(`Failed to serialize level data to binary: ${error.message}`);
        }
    }
    
    deserialize(data: ArrayBuffer): ILevelData {
        try {
            // Read binary header
            const header = this.readBinaryHeader(data);
            
            // Extract JSON data
            const jsonData = data.slice(header.headerSize);
            const decoder = new TextDecoder();
            const jsonString = decoder.decode(jsonData);
            
            // Use JSON deserializer
            const jsonSerializer = new JsonLevelSerializer();
            return jsonSerializer.deserialize(jsonString);
        } catch (error) {
            throw new Error(`Failed to deserialize binary level data: ${error.message}`);
        }
    }
    
    getFormat(): 'binary' {
        return 'binary';
    }
    
    getVersion(): string {
        return this.version;
    }
    
    private createBinaryHeader(): ArrayBuffer {
        const header = new ArrayBuffer(32); // 32 bytes header
        const view = new DataView(header);
        
        // Magic number (4 bytes): "LVLD"
        view.setUint32(0, 0x4C564C44, false);
        
        // Version (4 bytes)
        const [major, minor, patch] = this.version.split('.').map(Number);
        view.setUint8(4, major);
        view.setUint8(5, minor);
        view.setUint16(6, patch, false);
        
        // Format type (4 bytes): 1 = binary
        view.setUint32(8, 1, false);
        
        // Header size (4 bytes)
        view.setUint32(12, 32, false);
        
        // Reserved for future use (16 bytes)
        // Can be used for compression flags, checksum, etc.
        
        return header;
    }
    
    private readBinaryHeader(data: ArrayBuffer): any {
        if (data.byteLength < 32) {
            throw new Error('Invalid binary data: header too small');
        }
        
        const view = new DataView(data, 0, 32);
        
        // Check magic number
        const magic = view.getUint32(0, false);
        if (magic !== 0x4C564C44) { // "LVLD"
            throw new Error('Invalid binary data: wrong magic number');
        }
        
        // Read version
        const major = view.getUint8(4);
        const minor = view.getUint8(5);
        const patch = view.getUint16(6, false);
        const version = `${major}.${minor}.${patch}`;
        
        // Read format type
        const formatType = view.getUint32(8, false);
        if (formatType !== 1) {
            throw new Error('Invalid binary data: unsupported format type');
        }
        
        // Read header size
        const headerSize = view.getUint32(12, false);
        
        return {
            version,
            formatType,
            headerSize
        };
    }
}

/**
 * Serializer factory for creating appropriate serializers
 */
export class SerializerFactory {
    private static serializers: Map<string, () => ILevelSerializer> = new Map([
        ['json', () => new JsonLevelSerializer()],
        ['binary', () => new BinaryLevelSerializer()]
    ]);
    
    public static createSerializer(format: 'json' | 'binary'): ILevelSerializer {
        const factory = this.serializers.get(format);
        if (!factory) {
            throw new Error(`Unsupported serialization format: ${format}`);
        }
        return factory();
    }
    
    public static registerSerializer(format: string, factory: () => ILevelSerializer): void {
        this.serializers.set(format, factory);
    }
    
    public static getSupportedFormats(): string[] {
        return Array.from(this.serializers.keys());
    }
}

/**
 * Level validation utilities
 */
export class LevelValidator {
    public static validate(levelData: ILevelData): ValidationResult {
        const errors: string[] = [];
        const warnings: string[] = [];
        
        // Validate metadata
        if (!levelData.metadata.name || levelData.metadata.name.trim() === '') {
            errors.push('Level name is required');
        }
        
        if (levelData.metadata.duration <= 0) {
            errors.push('Level duration must be positive');
        }
        
        // Validate sublevels
        if (!levelData.subLevels || levelData.subLevels.length === 0) {
            errors.push('Level must contain at least one sublevel');
        }
        
        // Validate sublevel IDs are unique
        const subLevelIds = new Set<string>();
        levelData.subLevels.forEach(subLevel => {
            if (subLevelIds.has(subLevel.id)) {
                errors.push(`Duplicate sublevel ID: ${subLevel.id}`);
            }
            subLevelIds.add(subLevel.id);
        });
        
        // Validate paths and spawner references
        levelData.subLevels.forEach(subLevel => {
            const pathIds = new Set(subLevel.paths.map(p => p.id));
            
            subLevel.spawners.forEach(spawner => {
                if (spawner.pathId && !pathIds.has(spawner.pathId)) {
                    errors.push(`Spawner ${spawner.id} references non-existent path ${spawner.pathId}`);
                }
            });
        });
        
        return {
            isValid: errors.length === 0,
            errors,
            warnings
        };
    }
}

export interface ValidationResult {
    isValid: boolean;
    errors: string[];
    warnings: string[];
}
