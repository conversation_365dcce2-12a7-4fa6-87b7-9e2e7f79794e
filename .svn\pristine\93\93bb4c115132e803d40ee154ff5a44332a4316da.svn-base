import { _decorator, Component, Node, director, find, Label } from 'cc';
import { Global } from './Global';
const { ccclass, property } = _decorator;

@ccclass('GameOver')
export class GameOver extends Component {

    onLoad() {
        find("Canvas/Score").getComponent(Label).string = "Score:" + Global.SCORE.toString();
    }
    
    onClicked(event: Event, cutom: string) {
        director.loadScene("menu");
        Global.SCORE = 0;
    }
}

