{"__type__": "cc.EffectAsset", "_name": "pipeline/post-process/hbao", "_objFlags": 0, "__editorExtras__": {}, "_native": "", "techniques": [{"passes": [{"pass": "hbao-pass", "rasterizerState": {"cullMode": 0}, "program": "pipeline/post-process/hbao|hbao-vs|hbao-fs", "depthStencilState": {"depthTest": false, "depthWrite": false}}, {"pass": "blurx-pass", "rasterizerState": {"cullMode": 0}, "program": "pipeline/post-process/hbao|hbao-vs|blurx-fs", "depthStencilState": {"depthTest": false, "depthWrite": false}}, {"pass": "blury-pass", "rasterizerState": {"cullMode": 0}, "program": "pipeline/post-process/hbao|hbao-vs|blury-fs", "depthStencilState": {"depthTest": false, "depthWrite": false}}, {"pass": "combine-pass", "rasterizerState": {"cullMode": 0}, "blendState": {"targets": [{"blend": true, "blendSrc": 0, "blendDst": 2, "blendSrcAlpha": 0, "blendDstAlpha": 1}]}, "program": "pipeline/post-process/hbao|hbao-vs|combine-fs", "depthStencilState": {"depthTest": false, "depthWrite": false}}]}], "shaders": [{"blocks": [{"name": "hbaoUBO", "members": [{"name": "uvDepthToEyePosParams", "type": 16, "count": 1}, {"name": "radiusParam", "type": 16, "count": 1}, {"name": "miscParam", "type": 16, "count": 1}, {"name": "randomTexSize", "type": 16, "count": 1}, {"name": "blurParam", "type": 16, "count": 1}], "defines": [], "stageFlags": 16, "binding": 0}], "samplerTextures": [{"name": "RandomTex", "type": 28, "count": 1, "defines": [], "stageFlags": 16, "sampleType": 0, "binding": 2}], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": [], "attributes": [{"name": "a_position", "defines": [], "format": 32, "location": 0}, {"name": "a_normal", "defines": [], "format": 32, "location": 1}, {"name": "a_texCoord", "defines": [], "format": 21, "location": 2}, {"name": "a_tangent", "defines": [], "format": 44, "location": 3}, {"name": "a_joints", "defines": ["CC_USE_SKINNING"], "location": 4}, {"name": "a_weights", "defines": ["CC_USE_SKINNING"], "format": 44, "location": 5}, {"name": "a_jointAnimInfo", "defines": ["USE_INSTANCING", "CC_USE_BAKED_ANIMATION"], "format": 44, "isInstanced": true, "location": 6}, {"name": "a_matWorld0", "defines": ["USE_INSTANCING"], "format": 44, "isInstanced": true, "location": 7}, {"name": "a_matWorld1", "defines": ["USE_INSTANCING"], "format": 44, "isInstanced": true, "location": 8}, {"name": "a_matWorld2", "defines": ["USE_INSTANCING"], "format": 44, "isInstanced": true, "location": 9}, {"name": "a_lightingMapUVParam", "defines": ["USE_INSTANCING", "CC_USE_LIGHTMAP"], "format": 44, "isInstanced": true, "location": 10}, {"name": "a_localShadowBiasAndProbeId", "defines": ["USE_INSTANCING"], "format": 44, "isInstanced": true, "location": 11}, {"name": "a_reflectionProbeData", "defines": ["USE_INSTANCING", "CC_USE_REFLECTION_PROBE"], "format": 44, "isInstanced": true, "location": 12}, {"name": "a_sh_linear_const_r", "defines": ["USE_INSTANCING", "CC_USE_LIGHT_PROBE"], "format": 44, "isInstanced": true, "location": 13}, {"name": "a_sh_linear_const_g", "defines": ["USE_INSTANCING", "CC_USE_LIGHT_PROBE"], "format": 44, "isInstanced": true, "location": 14}, {"name": "a_sh_linear_const_b", "defines": ["USE_INSTANCING", "CC_USE_LIGHT_PROBE"], "format": 44, "isInstanced": true, "location": 15}, {"name": "a_vertexId", "defines": ["CC_USE_MORPH"], "format": 11, "location": 16}], "varyings": [{"name": "v_uv", "type": 14, "count": 1, "defines": [], "stageFlags": 17, "location": 0}], "fragColors": [{"name": "fragColor", "typename": "vec4", "type": 16, "count": 1, "defines": [], "stageFlags": 16, "location": 0}], "descriptors": [{"rate": 0, "blocks": [], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}, {"rate": 1, "blocks": [{"name": "hbaoUBO", "members": [{"name": "uvDepthToEyePosParams", "type": 16, "count": 1}, {"name": "radiusParam", "type": 16, "count": 1}, {"name": "miscParam", "type": 16, "count": 1}, {"name": "randomTexSize", "type": 16, "count": 1}, {"name": "blurParam", "type": 16, "count": 1}], "defines": [], "stageFlags": 16, "binding": 0}], "samplerTextures": [{"name": "RandomTex", "type": 28, "count": 1, "defines": [], "stageFlags": 16, "sampleType": 0, "binding": 2}], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}, {"rate": 2, "blocks": [], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}, {"rate": 3, "blocks": [{"tags": {"builtin": "global"}, "name": "CCGlobal", "members": [{"name": "cc_time", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_screenSize", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_nativeSize", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_probeInfo", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_debug_view_mode", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}], "defines": [], "stageFlags": 17}, {"tags": {"builtin": "global"}, "name": "CCCamera", "members": [{"name": "cc_matView", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matViewInv", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matProj", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matProjInv", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matViewProj", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matViewProjInv", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_cameraPos", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_surfaceTransform", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_screenScale", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_exposure", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_mainLitDir", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_mainLitColor", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_ambientSky", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_ambientGround", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_fogColor", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_fogBase", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_fogAdd", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_nearFar", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_viewPort", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}], "defines": [], "stageFlags": 17}], "samplerTextures": [{"name": "DepthTex", "type": 28, "count": 1, "defines": [], "stageFlags": 16, "sampleType": 0, "binding": 1}, {"name": "AOTexNearest", "type": 28, "count": 1, "defines": [], "stageFlags": 16, "sampleType": 0, "binding": 3}], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}], "hash": 4212493832, "glsl4": {"vert": "\nprecision highp float;\n#define QUATER_PI         0.78539816340\n#define HALF_PI           1.57079632679\n#define PI                3.14159265359\n#define PI2               6.28318530718\n#define PI4               12.5663706144\n#define INV_QUATER_PI     1.27323954474\n#define INV_HALF_PI       0.63661977237\n#define INV_PI            0.31830988618\n#define INV_PI2           0.15915494309\n#define INV_PI4           0.07957747155\n#define EPSILON           1e-6\n#define EPSILON_LOWP      1e-4\n#define LOG2              1.442695\n#define EXP_VALUE         2.71828183\n#define FP_MAX            65504.0\n#define FP_SCALE          0.0009765625\n#define FP_SCALE_INV      1024.0\n#define GRAY_VECTOR       vec3(0.299, 0.587, 0.114)\n#define LIGHT_MAP_TYPE_DISABLED 0\n#define LIGHT_MAP_TYPE_ALL_IN_ONE 1\n#define LIGHT_MAP_TYPE_INDIRECT_OCCLUSION 2\n#define REFLECTION_PROBE_TYPE_NONE 0\n#define REFLECTION_PROBE_TYPE_CUBE 1\n#define REFLECTION_PROBE_TYPE_PLANAR 2\n#define REFLECTION_PROBE_TYPE_BLEND 3\n#define REFLECTION_PROBE_TYPE_BLEND_AND_SKYBOX 4\n#define LIGHT_TYPE_DIRECTIONAL 0.0\n#define LIGHT_TYPE_SPHERE 1.0\n#define LIGHT_TYPE_SPOT 2.0\n#define LIGHT_TYPE_POINT 3.0\n#define LIGHT_TYPE_RANGED_DIRECTIONAL 4.0\n#define IS_DIRECTIONAL_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_DIRECTIONAL)) < EPSILON_LOWP)\n#define IS_SPHERE_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_SPHERE)) < EPSILON_LOWP)\n#define IS_SPOT_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_SPOT)) < EPSILON_LOWP)\n#define IS_POINT_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_POINT)) < EPSILON_LOWP)\n#define IS_RANGED_DIRECTIONAL_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_RANGED_DIRECTIONAL)) < EPSILON_LOWP)\n#define TONE_MAPPING_ACES 0\n#define TONE_MAPPING_LINEAR 1\n#define SURFACES_MAX_TRANSMIT_DEPTH_VALUE 999999.0\n#ifndef CC_SURFACES_DEBUG_VIEW_SINGLE\n  #define CC_SURFACES_DEBUG_VIEW_SINGLE 1\n#endif\n#ifndef CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC\n  #define CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC 2\n#endif\nstruct StandardVertInput {\n  highp vec4 position;\n  vec3 normal;\n  vec4 tangent;\n};\nlayout(location = 0) in vec3 a_position;\nlayout(location = 1) in vec3 a_normal;\nlayout(location = 2) in vec2 a_texCoord;\nlayout(location = 3) in vec4 a_tangent;\n#if CC_USE_SKINNING\n    layout(location = 4) in u32vec4 a_joints;\n  layout(location = 5) in vec4 a_weights;\n#endif\n#if USE_INSTANCING\n  #if CC_USE_BAKED_ANIMATION\n    layout(location = 6) in highp vec4 a_jointAnimInfo;\n  #endif\n  layout(location = 7) in vec4 a_matWorld0;\n  layout(location = 8) in vec4 a_matWorld1;\n  layout(location = 9) in vec4 a_matWorld2;\n  #if CC_USE_LIGHTMAP\n    layout(location = 10) in vec4 a_lightingMapUVParam;\n  #endif\n  #if CC_USE_REFLECTION_PROBE || CC_RECEIVE_SHADOW\n    #if CC_RECEIVE_SHADOW\n    #endif\n    layout(location = 11) in vec4 a_localShadowBiasAndProbeId;\n  #endif\n  #if CC_USE_REFLECTION_PROBE\n    layout(location = 12) in vec4 a_reflectionProbeData;\n  #endif\n  #if CC_USE_LIGHT_PROBE\n    layout(location = 13) in vec4 a_sh_linear_const_r;\n    layout(location = 14) in vec4 a_sh_linear_const_g;\n    layout(location = 15) in vec4 a_sh_linear_const_b;\n  #endif\n#endif\n#if CC_USE_MORPH\n#endif\nlayout(set = 0, binding = 0) uniform CCGlobal {\n  highp   vec4 cc_time;\n  mediump vec4 cc_screenSize;\n  mediump vec4 cc_nativeSize;\n  mediump vec4 cc_probeInfo;\n  mediump vec4 cc_debug_view_mode;\n};\nlayout(set = 0, binding = 1) uniform CCCamera {\n  highp   mat4 cc_matView;\n  highp   mat4 cc_matViewInv;\n  highp   mat4 cc_matProj;\n  highp   mat4 cc_matProjInv;\n  highp   mat4 cc_matViewProj;\n  highp   mat4 cc_matViewProjInv;\n  highp   vec4 cc_cameraPos;\n  mediump vec4 cc_surfaceTransform;\n  mediump vec4 cc_screenScale;\n  mediump vec4 cc_exposure;\n  mediump vec4 cc_mainLitDir;\n  mediump vec4 cc_mainLitColor;\n  mediump vec4 cc_ambientSky;\n  mediump vec4 cc_ambientGround;\n  mediump vec4 cc_fogColor;\n  mediump vec4 cc_fogBase;\n  mediump vec4 cc_fogAdd;\n  mediump vec4 cc_nearFar;\n  mediump vec4 cc_viewPort;\n};\nlayout(location = 0) out vec2 v_uv;\nvoid main () {\n  StandardVertInput In;\n    In.position = vec4(a_position, 1.0);\n    In.normal = a_normal;\n    In.tangent = a_tangent;\n  In.position.xy = cc_cameraPos.w == 0.0 ? vec2(In.position.xy.x, -In.position.xy.y) : In.position.xy;\n  gl_Position = In.position;\n  v_uv = a_texCoord;\n}", "frag": "\nprecision highp float;\nprecision highp float;\nlayout(set = 0, binding = 0) uniform CCGlobal {\n  highp   vec4 cc_time;\n  mediump vec4 cc_screenSize;\n  mediump vec4 cc_nativeSize;\n  mediump vec4 cc_probeInfo;\n  mediump vec4 cc_debug_view_mode;\n};\nlayout(set = 0, binding = 1) uniform CCCamera {\n  highp   mat4 cc_matView;\n  highp   mat4 cc_matViewInv;\n  highp   mat4 cc_matProj;\n  highp   mat4 cc_matProjInv;\n  highp   mat4 cc_matViewProj;\n  highp   mat4 cc_matViewProjInv;\n  highp   vec4 cc_cameraPos;\n  mediump vec4 cc_surfaceTransform;\n  mediump vec4 cc_screenScale;\n  mediump vec4 cc_exposure;\n  mediump vec4 cc_mainLitDir;\n  mediump vec4 cc_mainLitColor;\n  mediump vec4 cc_ambientSky;\n  mediump vec4 cc_ambientGround;\n  mediump vec4 cc_fogColor;\n  mediump vec4 cc_fogBase;\n  mediump vec4 cc_fogAdd;\n  mediump vec4 cc_nearFar;\n  mediump vec4 cc_viewPort;\n};\n#define QUATER_PI         0.78539816340\n#define HALF_PI           1.57079632679\n#define PI                3.14159265359\n#define PI2               6.28318530718\n#define PI4               12.5663706144\n#define INV_QUATER_PI     1.27323954474\n#define INV_HALF_PI       0.63661977237\n#define INV_PI            0.31830988618\n#define INV_PI2           0.15915494309\n#define INV_PI4           0.07957747155\n#define EPSILON           1e-6\n#define EPSILON_LOWP      1e-4\n#define LOG2              1.442695\n#define EXP_VALUE         2.71828183\n#define FP_MAX            65504.0\n#define FP_SCALE          0.0009765625\n#define FP_SCALE_INV      1024.0\n#define GRAY_VECTOR       vec3(0.299, 0.587, 0.114)\nfloat saturate(float value) { return clamp(value, 0.0, 1.0); }\nvec2 saturate(vec2 value) { return clamp(value, vec2(0.0), vec2(1.0)); }\nvec3 saturate(vec3 value) { return clamp(value, vec3(0.0), vec3(1.0)); }\nvec4 saturate(vec4 value) { return clamp(value, vec4(0.0), vec4(1.0)); }\n#define LIGHT_MAP_TYPE_DISABLED 0\n#define LIGHT_MAP_TYPE_ALL_IN_ONE 1\n#define LIGHT_MAP_TYPE_INDIRECT_OCCLUSION 2\n#define REFLECTION_PROBE_TYPE_NONE 0\n#define REFLECTION_PROBE_TYPE_CUBE 1\n#define REFLECTION_PROBE_TYPE_PLANAR 2\n#define REFLECTION_PROBE_TYPE_BLEND 3\n#define REFLECTION_PROBE_TYPE_BLEND_AND_SKYBOX 4\n#define LIGHT_TYPE_DIRECTIONAL 0.0\n#define LIGHT_TYPE_SPHERE 1.0\n#define LIGHT_TYPE_SPOT 2.0\n#define LIGHT_TYPE_POINT 3.0\n#define LIGHT_TYPE_RANGED_DIRECTIONAL 4.0\n#define IS_DIRECTIONAL_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_DIRECTIONAL)) < EPSILON_LOWP)\n#define IS_SPHERE_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_SPHERE)) < EPSILON_LOWP)\n#define IS_SPOT_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_SPOT)) < EPSILON_LOWP)\n#define IS_POINT_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_POINT)) < EPSILON_LOWP)\n#define IS_RANGED_DIRECTIONAL_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_RANGED_DIRECTIONAL)) < EPSILON_LOWP)\n#define TONE_MAPPING_ACES 0\n#define TONE_MAPPING_LINEAR 1\n#define SURFACES_MAX_TRANSMIT_DEPTH_VALUE 999999.0\n#ifndef CC_SURFACES_DEBUG_VIEW_SINGLE\n  #define CC_SURFACES_DEBUG_VIEW_SINGLE 1\n#endif\n#ifndef CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC\n  #define CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC 2\n#endif\n#if defined(CC_USE_METAL) || defined(CC_USE_WGPU)\n#define CC_HANDLE_SAMPLE_NDC_FLIP_STATIC(y) y = -y\n#else\n#define CC_HANDLE_SAMPLE_NDC_FLIP_STATIC(y)\n#endif\nfloat GetCameraDepthRH(float depthHS, mat4 matProj)\n{\n    return -matProj[3][2] / (depthHS + matProj[2][2]);\n}\nfloat GetCameraDepthRH(float depthHS, float matProj32, float matProj22)\n{\n    return -matProj32 / (depthHS + matProj22);\n}\nvec4 GetWorldPosFromNDCPosRH(vec3 posHS, mat4 matProj, mat4 matViewProjInv)\n{\n    float w = -GetCameraDepthRH(posHS.z, matProj);\n    return matViewProjInv * vec4(posHS * w, w);\n}\nfloat rsqrt(float value) { return 1.0 / sqrt(value); }\nvec2 rsqrt(vec2 value) { return vec2(1.0) / sqrt(value); }\nvec3 rsqrt(vec3 value) { return vec3(1.0) / sqrt(value); }\nvec4 rsqrt(vec4 value) { return vec4(1.0) / sqrt(value); }\nfloat rand(vec2 seeds_zero_to_one) {\n    return fract(sin(dot(seeds_zero_to_one.xy, vec2(12.9898, 78.233))) * 43758.5453);\n}\n#if CC_USE_FOG != 4\n  float LinearFog(vec4 pos, vec3 cameraPos, float fogStart, float fogEnd) {\n      vec4 wPos = pos;\n      float cam_dis = distance(cameraPos, wPos.xyz);\n      return clamp((fogEnd - cam_dis) / (fogEnd - fogStart), 0., 1.);\n  }\n  float ExpFog(vec4 pos, vec3 cameraPos, float fogStart, float fogDensity, float fogAtten) {\n      vec4 wPos = pos;\n      float cam_dis = max(distance(cameraPos, wPos.xyz) - fogStart, 0.0) / fogAtten * 4.;\n      float f = exp(-cam_dis * fogDensity);\n      return f;\n  }\n  float ExpSquaredFog(vec4 pos, vec3 cameraPos, float fogStart, float fogDensity, float fogAtten) {\n      vec4 wPos = pos;\n      float cam_dis = max(distance(cameraPos, wPos.xyz) - fogStart, 0.0) / fogAtten * 4.;\n      float f = exp(-cam_dis * cam_dis * fogDensity * fogDensity);\n      return f;\n  }\n  float LayeredFog(vec4 pos, vec3 cameraPos, float fogTop, float fogRange, float fogAtten) {\n      vec4 wPos = pos;\n      vec3 camWorldProj = cameraPos.xyz;\n      camWorldProj.y = 0.;\n      vec3 worldPosProj = wPos.xyz;\n      worldPosProj.y = 0.;\n      float fDeltaD = distance(worldPosProj, camWorldProj) / fogAtten * 2.0;\n      float fDeltaY, fDensityIntegral;\n      if (cameraPos.y > fogTop) {\n          if (wPos.y < fogTop) {\n              fDeltaY = (fogTop - wPos.y) / fogRange * 2.0;\n              fDensityIntegral = fDeltaY * fDeltaY * 0.5;\n          }\n          else {\n              fDeltaY = 0.;\n              fDensityIntegral = 0.;\n          }\n      }\n      else {\n          if (wPos.y < fogTop) {\n              float fDeltaA = (fogTop - cameraPos.y) / fogRange * 2.;\n              float fDeltaB = (fogTop - wPos.y) / fogRange * 2.;\n              fDeltaY = abs(fDeltaA - fDeltaB);\n              fDensityIntegral = abs((fDeltaA * fDeltaA * 0.5) - (fDeltaB * fDeltaB * 0.5));\n          }\n          else {\n              fDeltaY = abs(fogTop - cameraPos.y) / fogRange * 2.;\n              fDensityIntegral = abs(fDeltaY * fDeltaY * 0.5);\n          }\n      }\n      float fDensity;\n      if (fDeltaY != 0.) {\n          fDensity = (sqrt(1.0 + ((fDeltaD / fDeltaY) * (fDeltaD / fDeltaY)))) * fDensityIntegral;\n      }\n      else {\n          fDensity = 0.;\n      }\n      float f = exp(-fDensity);\n      return f;\n  }\n#endif\nvoid CC_TRANSFER_FOG_BASE(vec4 pos, out float factor)\n{\n#if CC_USE_FOG == 0\n\tfactor = LinearFog(pos, cc_cameraPos.xyz, cc_fogBase.x, cc_fogBase.y);\n#elif CC_USE_FOG == 1\n\tfactor = ExpFog(pos, cc_cameraPos.xyz, cc_fogBase.x, cc_fogBase.z, cc_fogAdd.z);\n#elif CC_USE_FOG == 2\n\tfactor = ExpSquaredFog(pos, cc_cameraPos.xyz, cc_fogBase.x, cc_fogBase.z, cc_fogAdd.z);\n#elif CC_USE_FOG == 3\n\tfactor = LayeredFog(pos, cc_cameraPos.xyz, cc_fogAdd.x, cc_fogAdd.y, cc_fogAdd.z);\n#else\n\tfactor = 1.0;\n#endif\n}\nlayout(set = 1, binding = 0) uniform hbaoUBO {\n    vec4 uvDepthToEyePosParams;\n    vec4 radiusParam;\n    vec4 miscParam;\n    vec4 randomTexSize;\n    vec4 blurParam;\n};\n#define NUM_STEPS 8.0\n#define NUM_STEPS_LOOP NUM_STEPS*2.0\n#define INV_NUM_DIRECTIONS 0.125\n#define NUM_DIRECTIONS 8\n#define CONSTANT_2PI 6.2831852\n#define g_AOResolution cc_screenSize.xy\n#define g_InvAOResolution cc_screenSize.zw\n#define g_UVToViewA uvDepthToEyePosParams.xy\n#define g_UVToViewB uvDepthToEyePosParams.zw\n#define g_R radiusParam.x\n#define g_R2 radiusParam.y\n#define g_NegInvR2 radiusParam.z\n#define g_MaxRadiusPixels radiusParam.w\n#define g_FocalLen miscParam.xy\n#define g_TanAngleBias miscParam.z\n#define g_Strength miscParam.w\nlayout(set = 1, binding = 1) uniform sampler2D DepthTex;\nlayout(set = 1, binding = 2) uniform sampler2D RandomTex;\nvec2 fixUV(vec2 uv)\n{\n  return saturate(uv);\n}\nfloat InvLength(vec2 v)\n{\n\treturn rsqrt(dot(v,v));\n}\nfloat Tangent(vec3 P, vec3 S)\n{\n\treturn (P.z - S.z) * InvLength(S.xy - P.xy);\n}\nvec3 UVToEye(vec2 uv, float eye_z)\n{\n    uv = g_UVToViewA * uv + g_UVToViewB;\n    return vec3(uv * eye_z, eye_z);\n}\nfloat GetLinearDepth(vec2 uv) {\n  float depthHS = 0.0;\n  #if defined(CC_USE_WGPU)\n      depthHS = textureLod(DepthTex, fixUV(uv), 0.0).r * 2.0 - 1.0;\n  #else\n      depthHS = texture(DepthTex, fixUV(uv)).r * 2.0 - 1.0;\n  #endif\n  return -GetCameraDepthRH(depthHS, cc_matProj);\n}\nvec3 FetchEyePos(vec2 uv)\n{\n    float depth = GetLinearDepth(uv);\n    return UVToEye(uv, depth);\n}\nfloat Length2(vec3 v)\n{\n  \treturn dot(v, v);\n}\nvec3 MinDiff(vec3 P, vec3 Pr, vec3 Pl)\n{\n  \tvec3 V1 = Pr - P;\n  \tvec3 V2 = P - Pl;\n  \treturn (Length2(V1) < Length2(V2)) ? V1 : V2;\n}\nfloat Falloff(float d2)\n{\n    return d2 * g_NegInvR2 + 1.0;\n}\nvec2 SnapUVOffset(vec2 uv)\n{\n    return round(uv * g_AOResolution) * g_InvAOResolution;\n}\nfloat TanToSin(float x)\n{\n  \treturn x * rsqrt(x*x + 1.0);\n}\nvec3 TangentVector(vec2 deltaUV, vec3 dPdu, vec3 dPdv)\n{\n  \treturn deltaUV.x * dPdu + deltaUV.y * dPdv;\n}\nfloat Tangent(vec3 T)\n{\n  \treturn -T.z * InvLength(T.xy);\n}\nfloat BiasedTangent(vec3 T)\n{\n    return Tangent(T) + g_TanAngleBias;\n}\nvec2 RotateDirections(vec2 Dir, vec2 CosSin)\n{\n  \treturn vec2(Dir.x*CosSin.x - Dir.y*CosSin.y,\n  \t\tDir.x*CosSin.y + Dir.y*CosSin.x);\n}\nfloat IntegerateOcclusion(vec2 uv0,\n                            vec2 snapped_duv,\n                            vec3 P,\n                            vec3 dPdu,\n                            vec3 dPdv,\n                            inout float tanH)\n{\n    float ao = 0.0;\n    vec3 T1 = TangentVector(snapped_duv, dPdu, dPdv);\n    float tanT = BiasedTangent(T1);\n    float sinT = TanToSin(tanT);\n    vec3 S = FetchEyePos(uv0 + snapped_duv);\n    float tanS = Tangent(P, S);\n    float sinS = TanToSin(tanS);\n    float d2 = Length2(S - P);\n    if ((d2 < g_R2) && (tanS > tanT))\n    {\n      ao = Falloff(d2) * (sinS - sinT);\n      tanH = max(tanH, tanS);\n    }\n    return ao;\n}\nfloat HorizonOcclusion(vec2 deltaUV,\n                        vec2 texelDeltaUV,\n                        vec2 uv0,\n                        vec3 P,\n                        float numSteps,\n                        float randstep,\n                        vec3 dPdu,\n                        vec3 dPdv)\n{\n    float ao = 0.0;\n    vec2 uv = uv0 + SnapUVOffset( randstep * deltaUV );\n    deltaUV = SnapUVOffset( deltaUV );\n    vec3 T = deltaUV.x * dPdu + deltaUV.y * dPdv;\n    float tanH = BiasedTangent(T);\n#if SAMPLE_FIRST_STEP\n    vec2 snapped_duv = SnapUVOffset( randstep * deltaUV + texelDeltaUV );\n    ao = IntegerateOcclusion(uv0, snapped_duv, P, dPdu, dPdv, tanH);\n    --numSteps;\n#endif\n    float sinH = tanH / sqrt(1.0 + tanH*tanH);\n    for (float j = 1.0; j <= NUM_STEPS_LOOP; j += 1.0)\n    {\n      if (j <= numSteps)\n      {\n        uv += deltaUV;\n        vec3 S = FetchEyePos(uv);\n        float tanS = Tangent(P, S);\n        float d2 = Length2(S - P);\n        if ((d2 < g_R2) && (tanS > tanH))\n        {\n          float sinS = tanS / sqrt(1.0 + tanS*tanS);\n          ao += Falloff(d2) * (sinS - sinH);\n          tanH = tanS;\n          sinH = sinS;\n        }\n      }\n    }\n    return ao;\n}\nvoid ComputeSteps(inout vec2 step_size_uv, inout float numSteps, float ray_radius_pix, float rand)\n{\n    numSteps = min(NUM_STEPS, ray_radius_pix);\n    float step_size_pix = ray_radius_pix / (numSteps + 1.0);\n    float maxNumSteps = g_MaxRadiusPixels / step_size_pix;\n    if (maxNumSteps < numSteps)\n    {\n      numSteps = floor(maxNumSteps + rand);\n      numSteps = max(numSteps, 1.0);\n      step_size_pix = g_MaxRadiusPixels / numSteps;\n    }\n    step_size_uv = step_size_pix * g_InvAOResolution;\n}\nfloat CalculateAO(vec2 uv)\n{\n    vec3 P = FetchEyePos(uv);\n    vec3 rand = texture(RandomTex, uv * g_AOResolution.xy * randomTexSize.zw).xyz;\n    vec2 ray_radius_uv = 0.5 * g_R * g_FocalLen / P.z;\n    float ray_radius_pix = ray_radius_uv.x * g_AOResolution.x;\n    if (ray_radius_pix < 1.0)\n    {\n      return 1.0;\n    }\n    float numSteps;\n    vec2 step_size;\n    ComputeSteps(step_size, numSteps, ray_radius_pix, rand.z);\n    vec3 Pr = FetchEyePos(uv + vec2(g_InvAOResolution.x, 0.0));\n    vec3 Pl = FetchEyePos(uv + vec2(-g_InvAOResolution.x, 0.0));\n    vec3 Pt = FetchEyePos(uv + vec2(0.0, g_InvAOResolution.y));\n    vec3 Pb = FetchEyePos(uv + vec2(0.0, -g_InvAOResolution.y));\n    vec3 dPdu = MinDiff(P, Pr, Pl);\n    vec3 dPdv = MinDiff(P, Pt, Pb) * (g_AOResolution.y * g_InvAOResolution.x);\n    float ao = 0.0;\n    float alpha = CONSTANT_2PI * INV_NUM_DIRECTIONS;\n    for (int d = 0; d < NUM_DIRECTIONS; ++d)\n    {\n      float angle = alpha * float(d);\n      vec2 dir = RotateDirections(vec2(cos(angle), sin(angle)), rand.xy);\n      vec2 deltaUV = dir * step_size.xy;\n      vec2 texelDeltaUV = dir * g_InvAOResolution;\n      ao += HorizonOcclusion(deltaUV, texelDeltaUV, uv, P, numSteps, rand.z, dPdu, dPdv);\n    }\n  ao = 1.0 - ao * INV_NUM_DIRECTIONS * g_Strength;\n  #if CC_USE_FOG != 4 && !CC_USE_FLOAT_OUTPUT\n    float fogFactor = 1.0;\n    float depth = 0.0;\n    #if defined(CC_USE_WGPU)\n      depth = textureLod(DepthTex, uv, 0.0).r;\n    #else\n      depth = texture(DepthTex, uv).r;\n    #endif\n    vec3 posHS = vec3(uv, depth) * 2.0 - vec3(1.0);\n    posHS.xy = cc_cameraPos.w == 0.0 ? vec2(posHS.xy.x, -posHS.xy.y) : posHS.xy;\n    vec4 worldPos = GetWorldPosFromNDCPosRH(posHS, cc_matProj, cc_matViewProjInv);\n    CC_TRANSFER_FOG_BASE(vec4(worldPos.xyz, 1.0), fogFactor);\n    ao = mix(1.0, ao, pow(fogFactor * 0.9, 4.0));\n  #endif\n  return ao;\n}\n#define KERNEL_FALLOFF 3.0f\n#define KERNEL_RADIUS 8\n#define HALF_KERNEL_RADIUS 4\n#define g_InvFullResolution cc_screenSize.zw\n#define g_BlurFallOff blurParam.x\n#define g_BlurDepthThreshold blurParam.y\n#define g_BlurSimpleRadius blurParam.z\nlayout(set = 1, binding = 3) uniform sampler2D AOTexNearest;\n#define AOTexBilinear AOTexNearest\n#define GetLinearDepthBilinear GetLinearDepth\n#define g_AOSaturation blurParam.w\nlayout(location = 0) in vec2 v_uv;\nlayout(location = 0) out vec4 fragColor;\nvoid main () {\n  float ao = CalculateAO(v_uv);\n  fragColor = vec4(ao, ao, ao, 1.0);\n}"}, "glsl3": {"vert": "\nprecision highp float;\n#define QUATER_PI         0.78539816340\n#define HALF_PI           1.57079632679\n#define PI                3.14159265359\n#define PI2               6.28318530718\n#define PI4               12.5663706144\n#define INV_QUATER_PI     1.27323954474\n#define INV_HALF_PI       0.63661977237\n#define INV_PI            0.31830988618\n#define INV_PI2           0.15915494309\n#define INV_PI4           0.07957747155\n#define EPSILON           1e-6\n#define EPSILON_LOWP      1e-4\n#define LOG2              1.442695\n#define EXP_VALUE         2.71828183\n#define FP_MAX            65504.0\n#define FP_SCALE          0.0009765625\n#define FP_SCALE_INV      1024.0\n#define GRAY_VECTOR       vec3(0.299, 0.587, 0.114)\n#define LIGHT_MAP_TYPE_DISABLED 0\n#define LIGHT_MAP_TYPE_ALL_IN_ONE 1\n#define LIGHT_MAP_TYPE_INDIRECT_OCCLUSION 2\n#define REFLECTION_PROBE_TYPE_NONE 0\n#define REFLECTION_PROBE_TYPE_CUBE 1\n#define REFLECTION_PROBE_TYPE_PLANAR 2\n#define REFLECTION_PROBE_TYPE_BLEND 3\n#define REFLECTION_PROBE_TYPE_BLEND_AND_SKYBOX 4\n#define LIGHT_TYPE_DIRECTIONAL 0.0\n#define LIGHT_TYPE_SPHERE 1.0\n#define LIGHT_TYPE_SPOT 2.0\n#define LIGHT_TYPE_POINT 3.0\n#define LIGHT_TYPE_RANGED_DIRECTIONAL 4.0\n#define IS_DIRECTIONAL_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_DIRECTIONAL)) < EPSILON_LOWP)\n#define IS_SPHERE_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_SPHERE)) < EPSILON_LOWP)\n#define IS_SPOT_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_SPOT)) < EPSILON_LOWP)\n#define IS_POINT_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_POINT)) < EPSILON_LOWP)\n#define IS_RANGED_DIRECTIONAL_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_RANGED_DIRECTIONAL)) < EPSILON_LOWP)\n#define TONE_MAPPING_ACES 0\n#define TONE_MAPPING_LINEAR 1\n#define SURFACES_MAX_TRANSMIT_DEPTH_VALUE 999999.0\n#ifndef CC_SURFACES_DEBUG_VIEW_SINGLE\n  #define CC_SURFACES_DEBUG_VIEW_SINGLE 1\n#endif\n#ifndef CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC\n  #define CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC 2\n#endif\nstruct StandardVertInput {\n  highp vec4 position;\n  vec3 normal;\n  vec4 tangent;\n};\nin vec3 a_position;\nin vec3 a_normal;\nin vec2 a_texCoord;\nin vec4 a_tangent;\n#if CC_USE_SKINNING\n    in vec4 a_joints;\n  in vec4 a_weights;\n#endif\n#if USE_INSTANCING\n  #if CC_USE_BAKED_ANIMATION\n    in highp vec4 a_jointAnimInfo;\n  #endif\n  in vec4 a_matWorld0;\n  in vec4 a_matWorld1;\n  in vec4 a_matWorld2;\n  #if CC_USE_LIGHTMAP\n    in vec4 a_lightingMapUVParam;\n  #endif\n  #if CC_USE_REFLECTION_PROBE || CC_RECEIVE_SHADOW\n    #if CC_RECEIVE_SHADOW\n    #endif\n    in vec4 a_localShadowBiasAndProbeId;\n  #endif\n  #if CC_USE_REFLECTION_PROBE\n    in vec4 a_reflectionProbeData;\n  #endif\n  #if CC_USE_LIGHT_PROBE\n    in vec4 a_sh_linear_const_r;\n    in vec4 a_sh_linear_const_g;\n    in vec4 a_sh_linear_const_b;\n  #endif\n#endif\n#if CC_USE_MORPH\n    in float a_vertexId;\n#endif\nlayout(std140) uniform CCGlobal {\n  highp   vec4 cc_time;\n  mediump vec4 cc_screenSize;\n  mediump vec4 cc_nativeSize;\n  mediump vec4 cc_probeInfo;\n  mediump vec4 cc_debug_view_mode;\n};\nlayout(std140) uniform CCCamera {\n  highp   mat4 cc_matView;\n  highp   mat4 cc_matViewInv;\n  highp   mat4 cc_matProj;\n  highp   mat4 cc_matProjInv;\n  highp   mat4 cc_matViewProj;\n  highp   mat4 cc_matViewProjInv;\n  highp   vec4 cc_cameraPos;\n  mediump vec4 cc_surfaceTransform;\n  mediump vec4 cc_screenScale;\n  mediump vec4 cc_exposure;\n  mediump vec4 cc_mainLitDir;\n  mediump vec4 cc_mainLitColor;\n  mediump vec4 cc_ambientSky;\n  mediump vec4 cc_ambientGround;\n  mediump vec4 cc_fogColor;\n  mediump vec4 cc_fogBase;\n  mediump vec4 cc_fogAdd;\n  mediump vec4 cc_nearFar;\n  mediump vec4 cc_viewPort;\n};\nout vec2 v_uv;\nvoid main () {\n  StandardVertInput In;\n    In.position = vec4(a_position, 1.0);\n    In.normal = a_normal;\n    In.tangent = a_tangent;\n  In.position.xy = cc_cameraPos.w == 0.0 ? vec2(In.position.xy.x, -In.position.xy.y) : In.position.xy;\n  gl_Position = In.position;\n  v_uv = a_texCoord;\n}", "frag": "\nprecision highp float;\nprecision highp float;\nlayout(std140) uniform CCGlobal {\n  highp   vec4 cc_time;\n  mediump vec4 cc_screenSize;\n  mediump vec4 cc_nativeSize;\n  mediump vec4 cc_probeInfo;\n  mediump vec4 cc_debug_view_mode;\n};\nlayout(std140) uniform CCCamera {\n  highp   mat4 cc_matView;\n  highp   mat4 cc_matViewInv;\n  highp   mat4 cc_matProj;\n  highp   mat4 cc_matProjInv;\n  highp   mat4 cc_matViewProj;\n  highp   mat4 cc_matViewProjInv;\n  highp   vec4 cc_cameraPos;\n  mediump vec4 cc_surfaceTransform;\n  mediump vec4 cc_screenScale;\n  mediump vec4 cc_exposure;\n  mediump vec4 cc_mainLitDir;\n  mediump vec4 cc_mainLitColor;\n  mediump vec4 cc_ambientSky;\n  mediump vec4 cc_ambientGround;\n  mediump vec4 cc_fogColor;\n  mediump vec4 cc_fogBase;\n  mediump vec4 cc_fogAdd;\n  mediump vec4 cc_nearFar;\n  mediump vec4 cc_viewPort;\n};\n#define QUATER_PI         0.78539816340\n#define HALF_PI           1.57079632679\n#define PI                3.14159265359\n#define PI2               6.28318530718\n#define PI4               12.5663706144\n#define INV_QUATER_PI     1.27323954474\n#define INV_HALF_PI       0.63661977237\n#define INV_PI            0.31830988618\n#define INV_PI2           0.15915494309\n#define INV_PI4           0.07957747155\n#define EPSILON           1e-6\n#define EPSILON_LOWP      1e-4\n#define LOG2              1.442695\n#define EXP_VALUE         2.71828183\n#define FP_MAX            65504.0\n#define FP_SCALE          0.0009765625\n#define FP_SCALE_INV      1024.0\n#define GRAY_VECTOR       vec3(0.299, 0.587, 0.114)\nfloat saturate(float value) { return clamp(value, 0.0, 1.0); }\nvec2 saturate(vec2 value) { return clamp(value, vec2(0.0), vec2(1.0)); }\nvec3 saturate(vec3 value) { return clamp(value, vec3(0.0), vec3(1.0)); }\nvec4 saturate(vec4 value) { return clamp(value, vec4(0.0), vec4(1.0)); }\n#define LIGHT_MAP_TYPE_DISABLED 0\n#define LIGHT_MAP_TYPE_ALL_IN_ONE 1\n#define LIGHT_MAP_TYPE_INDIRECT_OCCLUSION 2\n#define REFLECTION_PROBE_TYPE_NONE 0\n#define REFLECTION_PROBE_TYPE_CUBE 1\n#define REFLECTION_PROBE_TYPE_PLANAR 2\n#define REFLECTION_PROBE_TYPE_BLEND 3\n#define REFLECTION_PROBE_TYPE_BLEND_AND_SKYBOX 4\n#define LIGHT_TYPE_DIRECTIONAL 0.0\n#define LIGHT_TYPE_SPHERE 1.0\n#define LIGHT_TYPE_SPOT 2.0\n#define LIGHT_TYPE_POINT 3.0\n#define LIGHT_TYPE_RANGED_DIRECTIONAL 4.0\n#define IS_DIRECTIONAL_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_DIRECTIONAL)) < EPSILON_LOWP)\n#define IS_SPHERE_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_SPHERE)) < EPSILON_LOWP)\n#define IS_SPOT_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_SPOT)) < EPSILON_LOWP)\n#define IS_POINT_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_POINT)) < EPSILON_LOWP)\n#define IS_RANGED_DIRECTIONAL_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_RANGED_DIRECTIONAL)) < EPSILON_LOWP)\n#define TONE_MAPPING_ACES 0\n#define TONE_MAPPING_LINEAR 1\n#define SURFACES_MAX_TRANSMIT_DEPTH_VALUE 999999.0\n#ifndef CC_SURFACES_DEBUG_VIEW_SINGLE\n  #define CC_SURFACES_DEBUG_VIEW_SINGLE 1\n#endif\n#ifndef CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC\n  #define CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC 2\n#endif\n#if defined(CC_USE_METAL) || defined(CC_USE_WGPU)\n#define CC_HANDLE_SAMPLE_NDC_FLIP_STATIC(y) y = -y\n#else\n#define CC_HANDLE_SAMPLE_NDC_FLIP_STATIC(y)\n#endif\nfloat GetCameraDepthRH(float depthHS, mat4 matProj)\n{\n    return -matProj[3][2] / (depthHS + matProj[2][2]);\n}\nfloat GetCameraDepthRH(float depthHS, float matProj32, float matProj22)\n{\n    return -matProj32 / (depthHS + matProj22);\n}\nvec4 GetWorldPosFromNDCPosRH(vec3 posHS, mat4 matProj, mat4 matViewProjInv)\n{\n    float w = -GetCameraDepthRH(posHS.z, matProj);\n    return matViewProjInv * vec4(posHS * w, w);\n}\nfloat rsqrt(float value) { return 1.0 / sqrt(value); }\nvec2 rsqrt(vec2 value) { return vec2(1.0) / sqrt(value); }\nvec3 rsqrt(vec3 value) { return vec3(1.0) / sqrt(value); }\nvec4 rsqrt(vec4 value) { return vec4(1.0) / sqrt(value); }\nfloat rand(vec2 seeds_zero_to_one) {\n    return fract(sin(dot(seeds_zero_to_one.xy, vec2(12.9898, 78.233))) * 43758.5453);\n}\n#if CC_USE_FOG != 4\n  float LinearFog(vec4 pos, vec3 cameraPos, float fogStart, float fogEnd) {\n      vec4 wPos = pos;\n      float cam_dis = distance(cameraPos, wPos.xyz);\n      return clamp((fogEnd - cam_dis) / (fogEnd - fogStart), 0., 1.);\n  }\n  float ExpFog(vec4 pos, vec3 cameraPos, float fogStart, float fogDensity, float fogAtten) {\n      vec4 wPos = pos;\n      float cam_dis = max(distance(cameraPos, wPos.xyz) - fogStart, 0.0) / fogAtten * 4.;\n      float f = exp(-cam_dis * fogDensity);\n      return f;\n  }\n  float ExpSquaredFog(vec4 pos, vec3 cameraPos, float fogStart, float fogDensity, float fogAtten) {\n      vec4 wPos = pos;\n      float cam_dis = max(distance(cameraPos, wPos.xyz) - fogStart, 0.0) / fogAtten * 4.;\n      float f = exp(-cam_dis * cam_dis * fogDensity * fogDensity);\n      return f;\n  }\n  float LayeredFog(vec4 pos, vec3 cameraPos, float fogTop, float fogRange, float fogAtten) {\n      vec4 wPos = pos;\n      vec3 camWorldProj = cameraPos.xyz;\n      camWorldProj.y = 0.;\n      vec3 worldPosProj = wPos.xyz;\n      worldPosProj.y = 0.;\n      float fDeltaD = distance(worldPosProj, camWorldProj) / fogAtten * 2.0;\n      float fDeltaY, fDensityIntegral;\n      if (cameraPos.y > fogTop) {\n          if (wPos.y < fogTop) {\n              fDeltaY = (fogTop - wPos.y) / fogRange * 2.0;\n              fDensityIntegral = fDeltaY * fDeltaY * 0.5;\n          }\n          else {\n              fDeltaY = 0.;\n              fDensityIntegral = 0.;\n          }\n      }\n      else {\n          if (wPos.y < fogTop) {\n              float fDeltaA = (fogTop - cameraPos.y) / fogRange * 2.;\n              float fDeltaB = (fogTop - wPos.y) / fogRange * 2.;\n              fDeltaY = abs(fDeltaA - fDeltaB);\n              fDensityIntegral = abs((fDeltaA * fDeltaA * 0.5) - (fDeltaB * fDeltaB * 0.5));\n          }\n          else {\n              fDeltaY = abs(fogTop - cameraPos.y) / fogRange * 2.;\n              fDensityIntegral = abs(fDeltaY * fDeltaY * 0.5);\n          }\n      }\n      float fDensity;\n      if (fDeltaY != 0.) {\n          fDensity = (sqrt(1.0 + ((fDeltaD / fDeltaY) * (fDeltaD / fDeltaY)))) * fDensityIntegral;\n      }\n      else {\n          fDensity = 0.;\n      }\n      float f = exp(-fDensity);\n      return f;\n  }\n#endif\nvoid CC_TRANSFER_FOG_BASE(vec4 pos, out float factor)\n{\n#if CC_USE_FOG == 0\n\tfactor = LinearFog(pos, cc_cameraPos.xyz, cc_fogBase.x, cc_fogBase.y);\n#elif CC_USE_FOG == 1\n\tfactor = ExpFog(pos, cc_cameraPos.xyz, cc_fogBase.x, cc_fogBase.z, cc_fogAdd.z);\n#elif CC_USE_FOG == 2\n\tfactor = ExpSquaredFog(pos, cc_cameraPos.xyz, cc_fogBase.x, cc_fogBase.z, cc_fogAdd.z);\n#elif CC_USE_FOG == 3\n\tfactor = LayeredFog(pos, cc_cameraPos.xyz, cc_fogAdd.x, cc_fogAdd.y, cc_fogAdd.z);\n#else\n\tfactor = 1.0;\n#endif\n}\nlayout(std140) uniform hbaoUBO {\n    vec4 uvDepthToEyePosParams;\n    vec4 radiusParam;\n    vec4 miscParam;\n    vec4 randomTexSize;\n    vec4 blurParam;\n};\n#define NUM_STEPS 8.0\n#define NUM_STEPS_LOOP NUM_STEPS*2.0\n#define INV_NUM_DIRECTIONS 0.125\n#define NUM_DIRECTIONS 8\n#define CONSTANT_2PI 6.2831852\n#define g_AOResolution cc_screenSize.xy\n#define g_InvAOResolution cc_screenSize.zw\n#define g_UVToViewA uvDepthToEyePosParams.xy\n#define g_UVToViewB uvDepthToEyePosParams.zw\n#define g_R radiusParam.x\n#define g_R2 radiusParam.y\n#define g_NegInvR2 radiusParam.z\n#define g_MaxRadiusPixels radiusParam.w\n#define g_FocalLen miscParam.xy\n#define g_TanAngleBias miscParam.z\n#define g_Strength miscParam.w\nuniform sampler2D DepthTex;\nuniform sampler2D RandomTex;\nvec2 fixUV(vec2 uv)\n{\n  return saturate(uv);\n}\nfloat InvLength(vec2 v)\n{\n\treturn rsqrt(dot(v,v));\n}\nfloat Tangent(vec3 P, vec3 S)\n{\n\treturn (P.z - S.z) * InvLength(S.xy - P.xy);\n}\nvec3 UVToEye(vec2 uv, float eye_z)\n{\n    uv = g_UVToViewA * uv + g_UVToViewB;\n    return vec3(uv * eye_z, eye_z);\n}\nfloat GetLinearDepth(vec2 uv) {\n  float depthHS = 0.0;\n  #if defined(CC_USE_WGPU)\n      depthHS = textureLod(DepthTex, fixUV(uv), 0.0).r * 2.0 - 1.0;\n  #else\n      depthHS = texture(DepthTex, fixUV(uv)).r * 2.0 - 1.0;\n  #endif\n  return -GetCameraDepthRH(depthHS, cc_matProj);\n}\nvec3 FetchEyePos(vec2 uv)\n{\n    float depth = GetLinearDepth(uv);\n    return UVToEye(uv, depth);\n}\nfloat Length2(vec3 v)\n{\n  \treturn dot(v, v);\n}\nvec3 MinDiff(vec3 P, vec3 Pr, vec3 Pl)\n{\n  \tvec3 V1 = Pr - P;\n  \tvec3 V2 = P - Pl;\n  \treturn (Length2(V1) < Length2(V2)) ? V1 : V2;\n}\nfloat Falloff(float d2)\n{\n    return d2 * g_NegInvR2 + 1.0;\n}\nvec2 SnapUVOffset(vec2 uv)\n{\n    return round(uv * g_AOResolution) * g_InvAOResolution;\n}\nfloat TanToSin(float x)\n{\n  \treturn x * rsqrt(x*x + 1.0);\n}\nvec3 TangentVector(vec2 deltaUV, vec3 dPdu, vec3 dPdv)\n{\n  \treturn deltaUV.x * dPdu + deltaUV.y * dPdv;\n}\nfloat Tangent(vec3 T)\n{\n  \treturn -T.z * InvLength(T.xy);\n}\nfloat BiasedTangent(vec3 T)\n{\n    return Tangent(T) + g_TanAngleBias;\n}\nvec2 RotateDirections(vec2 Dir, vec2 CosSin)\n{\n  \treturn vec2(Dir.x*CosSin.x - Dir.y*CosSin.y,\n  \t\tDir.x*CosSin.y + Dir.y*CosSin.x);\n}\nfloat IntegerateOcclusion(vec2 uv0,\n                            vec2 snapped_duv,\n                            vec3 P,\n                            vec3 dPdu,\n                            vec3 dPdv,\n                            inout float tanH)\n{\n    float ao = 0.0;\n    vec3 T1 = TangentVector(snapped_duv, dPdu, dPdv);\n    float tanT = BiasedTangent(T1);\n    float sinT = TanToSin(tanT);\n    vec3 S = FetchEyePos(uv0 + snapped_duv);\n    float tanS = Tangent(P, S);\n    float sinS = TanToSin(tanS);\n    float d2 = Length2(S - P);\n    if ((d2 < g_R2) && (tanS > tanT))\n    {\n      ao = Falloff(d2) * (sinS - sinT);\n      tanH = max(tanH, tanS);\n    }\n    return ao;\n}\nfloat HorizonOcclusion(vec2 deltaUV,\n                        vec2 texelDeltaUV,\n                        vec2 uv0,\n                        vec3 P,\n                        float numSteps,\n                        float randstep,\n                        vec3 dPdu,\n                        vec3 dPdv)\n{\n    float ao = 0.0;\n    vec2 uv = uv0 + SnapUVOffset( randstep * deltaUV );\n    deltaUV = SnapUVOffset( deltaUV );\n    vec3 T = deltaUV.x * dPdu + deltaUV.y * dPdv;\n    float tanH = BiasedTangent(T);\n#if SAMPLE_FIRST_STEP\n    vec2 snapped_duv = SnapUVOffset( randstep * deltaUV + texelDeltaUV );\n    ao = IntegerateOcclusion(uv0, snapped_duv, P, dPdu, dPdv, tanH);\n    --numSteps;\n#endif\n    float sinH = tanH / sqrt(1.0 + tanH*tanH);\n    for (float j = 1.0; j <= NUM_STEPS_LOOP; j += 1.0)\n    {\n      if (j <= numSteps)\n      {\n        uv += deltaUV;\n        vec3 S = FetchEyePos(uv);\n        float tanS = Tangent(P, S);\n        float d2 = Length2(S - P);\n        if ((d2 < g_R2) && (tanS > tanH))\n        {\n          float sinS = tanS / sqrt(1.0 + tanS*tanS);\n          ao += Falloff(d2) * (sinS - sinH);\n          tanH = tanS;\n          sinH = sinS;\n        }\n      }\n    }\n    return ao;\n}\nvoid ComputeSteps(inout vec2 step_size_uv, inout float numSteps, float ray_radius_pix, float rand)\n{\n    numSteps = min(NUM_STEPS, ray_radius_pix);\n    float step_size_pix = ray_radius_pix / (numSteps + 1.0);\n    float maxNumSteps = g_MaxRadiusPixels / step_size_pix;\n    if (maxNumSteps < numSteps)\n    {\n      numSteps = floor(maxNumSteps + rand);\n      numSteps = max(numSteps, 1.0);\n      step_size_pix = g_MaxRadiusPixels / numSteps;\n    }\n    step_size_uv = step_size_pix * g_InvAOResolution;\n}\nfloat CalculateAO(vec2 uv)\n{\n    vec3 P = FetchEyePos(uv);\n    vec3 rand = texture(RandomTex, uv * g_AOResolution.xy * randomTexSize.zw).xyz;\n    vec2 ray_radius_uv = 0.5 * g_R * g_FocalLen / P.z;\n    float ray_radius_pix = ray_radius_uv.x * g_AOResolution.x;\n    if (ray_radius_pix < 1.0)\n    {\n      return 1.0;\n    }\n    float numSteps;\n    vec2 step_size;\n    ComputeSteps(step_size, numSteps, ray_radius_pix, rand.z);\n    vec3 Pr = FetchEyePos(uv + vec2(g_InvAOResolution.x, 0.0));\n    vec3 Pl = FetchEyePos(uv + vec2(-g_InvAOResolution.x, 0.0));\n    vec3 Pt = FetchEyePos(uv + vec2(0.0, g_InvAOResolution.y));\n    vec3 Pb = FetchEyePos(uv + vec2(0.0, -g_InvAOResolution.y));\n    vec3 dPdu = MinDiff(P, Pr, Pl);\n    vec3 dPdv = MinDiff(P, Pt, Pb) * (g_AOResolution.y * g_InvAOResolution.x);\n    float ao = 0.0;\n    float alpha = CONSTANT_2PI * INV_NUM_DIRECTIONS;\n    for (int d = 0; d < NUM_DIRECTIONS; ++d)\n    {\n      float angle = alpha * float(d);\n      vec2 dir = RotateDirections(vec2(cos(angle), sin(angle)), rand.xy);\n      vec2 deltaUV = dir * step_size.xy;\n      vec2 texelDeltaUV = dir * g_InvAOResolution;\n      ao += HorizonOcclusion(deltaUV, texelDeltaUV, uv, P, numSteps, rand.z, dPdu, dPdv);\n    }\n  ao = 1.0 - ao * INV_NUM_DIRECTIONS * g_Strength;\n  #if CC_USE_FOG != 4 && !CC_USE_FLOAT_OUTPUT\n    float fogFactor = 1.0;\n    float depth = 0.0;\n    #if defined(CC_USE_WGPU)\n      depth = textureLod(DepthTex, uv, 0.0).r;\n    #else\n      depth = texture(DepthTex, uv).r;\n    #endif\n    vec3 posHS = vec3(uv, depth) * 2.0 - vec3(1.0);\n    posHS.xy = cc_cameraPos.w == 0.0 ? vec2(posHS.xy.x, -posHS.xy.y) : posHS.xy;\n    vec4 worldPos = GetWorldPosFromNDCPosRH(posHS, cc_matProj, cc_matViewProjInv);\n    CC_TRANSFER_FOG_BASE(vec4(worldPos.xyz, 1.0), fogFactor);\n    ao = mix(1.0, ao, pow(fogFactor * 0.9, 4.0));\n  #endif\n  return ao;\n}\n#define KERNEL_FALLOFF 3.0f\n#define KERNEL_RADIUS 8\n#define HALF_KERNEL_RADIUS 4\n#define g_InvFullResolution cc_screenSize.zw\n#define g_BlurFallOff blurParam.x\n#define g_BlurDepthThreshold blurParam.y\n#define g_BlurSimpleRadius blurParam.z\nuniform sampler2D AOTexNearest;\n#define AOTexBilinear AOTexNearest\n#define GetLinearDepthBilinear GetLinearDepth\n#define g_AOSaturation blurParam.w\nin vec2 v_uv;\nlayout(location = 0) out vec4 fragColor;\nvoid main () {\n  float ao = CalculateAO(v_uv);\n  fragColor = vec4(ao, ao, ao, 1.0);\n}"}, "glsl1": {"vert": "\nprecision highp float;\n#define QUATER_PI         0.78539816340\n#define HALF_PI           1.57079632679\n#define PI                3.14159265359\n#define PI2               6.28318530718\n#define PI4               12.5663706144\n#define INV_QUATER_PI     1.27323954474\n#define INV_HALF_PI       0.63661977237\n#define INV_PI            0.31830988618\n#define INV_PI2           0.15915494309\n#define INV_PI4           0.07957747155\n#define EPSILON           1e-6\n#define EPSILON_LOWP      1e-4\n#define LOG2              1.442695\n#define EXP_VALUE         2.71828183\n#define FP_MAX            65504.0\n#define FP_SCALE          0.0009765625\n#define FP_SCALE_INV      1024.0\n#define GRAY_VECTOR       vec3(0.299, 0.587, 0.114)\n#define LIGHT_MAP_TYPE_DISABLED 0\n#define LIGHT_MAP_TYPE_ALL_IN_ONE 1\n#define LIGHT_MAP_TYPE_INDIRECT_OCCLUSION 2\n#define REFLECTION_PROBE_TYPE_NONE 0\n#define REFLECTION_PROBE_TYPE_CUBE 1\n#define REFLECTION_PROBE_TYPE_PLANAR 2\n#define REFLECTION_PROBE_TYPE_BLEND 3\n#define REFLECTION_PROBE_TYPE_BLEND_AND_SKYBOX 4\n#define LIGHT_TYPE_DIRECTIONAL 0.0\n#define LIGHT_TYPE_SPHERE 1.0\n#define LIGHT_TYPE_SPOT 2.0\n#define LIGHT_TYPE_POINT 3.0\n#define LIGHT_TYPE_RANGED_DIRECTIONAL 4.0\n#define IS_DIRECTIONAL_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_DIRECTIONAL)) < EPSILON_LOWP)\n#define IS_SPHERE_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_SPHERE)) < EPSILON_LOWP)\n#define IS_SPOT_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_SPOT)) < EPSILON_LOWP)\n#define IS_POINT_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_POINT)) < EPSILON_LOWP)\n#define IS_RANGED_DIRECTIONAL_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_RANGED_DIRECTIONAL)) < EPSILON_LOWP)\n#define TONE_MAPPING_ACES 0\n#define TONE_MAPPING_LINEAR 1\n#define SURFACES_MAX_TRANSMIT_DEPTH_VALUE 999999.0\n#ifndef CC_SURFACES_DEBUG_VIEW_SINGLE\n  #define CC_SURFACES_DEBUG_VIEW_SINGLE 1\n#endif\n#ifndef CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC\n  #define CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC 2\n#endif\nstruct StandardVertInput {\n  highp vec4 position;\n  vec3 normal;\n  vec4 tangent;\n};\nattribute vec3 a_position;\nattribute vec3 a_normal;\nattribute vec2 a_texCoord;\nattribute vec4 a_tangent;\n#if CC_USE_SKINNING\n    attribute vec4 a_joints;\n  attribute vec4 a_weights;\n#endif\n#if USE_INSTANCING\n  #if CC_USE_BAKED_ANIMATION\n    attribute highp vec4 a_jointAnimInfo;\n  #endif\n  attribute vec4 a_matWorld0;\n  attribute vec4 a_matWorld1;\n  attribute vec4 a_matWorld2;\n  #if CC_USE_LIGHTMAP\n    attribute vec4 a_lightingMapUVParam;\n  #endif\n  #if CC_USE_REFLECTION_PROBE || CC_RECEIVE_SHADOW\n    #if CC_RECEIVE_SHADOW\n    #endif\n    attribute vec4 a_localShadowBiasAndProbeId;\n  #endif\n  #if CC_USE_REFLECTION_PROBE\n    attribute vec4 a_reflectionProbeData;\n  #endif\n  #if CC_USE_LIGHT_PROBE\n    attribute vec4 a_sh_linear_const_r;\n    attribute vec4 a_sh_linear_const_g;\n    attribute vec4 a_sh_linear_const_b;\n  #endif\n#endif\n#if CC_USE_MORPH\n    attribute float a_vertexId;\n#endif\nuniform highp vec4 cc_cameraPos;\nvarying vec2 v_uv;\nvoid main () {\n  StandardVertInput In;\n    In.position = vec4(a_position, 1.0);\n    In.normal = a_normal;\n    In.tangent = a_tangent;\n  In.position.xy = cc_cameraPos.w == 0.0 ? vec2(In.position.xy.x, -In.position.xy.y) : In.position.xy;\n  gl_Position = In.position;\n  v_uv = a_texCoord;\n}", "frag": "\nprecision highp float;\nprecision highp float;\nuniform mediump vec4 cc_screenSize;\nuniform highp mat4 cc_matProj;\n  uniform highp mat4 cc_matViewProjInv;\n  uniform highp vec4 cc_cameraPos;\n  uniform mediump vec4 cc_fogBase;\n  uniform mediump vec4 cc_fogAdd;\n#define QUATER_PI         0.78539816340\n#define HALF_PI           1.57079632679\n#define PI                3.14159265359\n#define PI2               6.28318530718\n#define PI4               12.5663706144\n#define INV_QUATER_PI     1.27323954474\n#define INV_HALF_PI       0.63661977237\n#define INV_PI            0.31830988618\n#define INV_PI2           0.15915494309\n#define INV_PI4           0.07957747155\n#define EPSILON           1e-6\n#define EPSILON_LOWP      1e-4\n#define LOG2              1.442695\n#define EXP_VALUE         2.71828183\n#define FP_MAX            65504.0\n#define FP_SCALE          0.0009765625\n#define FP_SCALE_INV      1024.0\n#define GRAY_VECTOR       vec3(0.299, 0.587, 0.114)\nfloat saturate(float value) { return clamp(value, 0.0, 1.0); }\nvec2 saturate(vec2 value) { return clamp(value, vec2(0.0), vec2(1.0)); }\nvec3 saturate(vec3 value) { return clamp(value, vec3(0.0), vec3(1.0)); }\nvec4 saturate(vec4 value) { return clamp(value, vec4(0.0), vec4(1.0)); }\n#define LIGHT_MAP_TYPE_DISABLED 0\n#define LIGHT_MAP_TYPE_ALL_IN_ONE 1\n#define LIGHT_MAP_TYPE_INDIRECT_OCCLUSION 2\n#define REFLECTION_PROBE_TYPE_NONE 0\n#define REFLECTION_PROBE_TYPE_CUBE 1\n#define REFLECTION_PROBE_TYPE_PLANAR 2\n#define REFLECTION_PROBE_TYPE_BLEND 3\n#define REFLECTION_PROBE_TYPE_BLEND_AND_SKYBOX 4\n#define LIGHT_TYPE_DIRECTIONAL 0.0\n#define LIGHT_TYPE_SPHERE 1.0\n#define LIGHT_TYPE_SPOT 2.0\n#define LIGHT_TYPE_POINT 3.0\n#define LIGHT_TYPE_RANGED_DIRECTIONAL 4.0\n#define IS_DIRECTIONAL_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_DIRECTIONAL)) < EPSILON_LOWP)\n#define IS_SPHERE_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_SPHERE)) < EPSILON_LOWP)\n#define IS_SPOT_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_SPOT)) < EPSILON_LOWP)\n#define IS_POINT_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_POINT)) < EPSILON_LOWP)\n#define IS_RANGED_DIRECTIONAL_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_RANGED_DIRECTIONAL)) < EPSILON_LOWP)\n#define TONE_MAPPING_ACES 0\n#define TONE_MAPPING_LINEAR 1\n#define SURFACES_MAX_TRANSMIT_DEPTH_VALUE 999999.0\n#ifndef CC_SURFACES_DEBUG_VIEW_SINGLE\n  #define CC_SURFACES_DEBUG_VIEW_SINGLE 1\n#endif\n#ifndef CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC\n  #define CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC 2\n#endif\n#if defined(CC_USE_METAL) || defined(CC_USE_WGPU)\n#define CC_HANDLE_SAMPLE_NDC_FLIP_STATIC(y) y = -y\n#else\n#define CC_HANDLE_SAMPLE_NDC_FLIP_STATIC(y)\n#endif\nfloat GetCameraDepthRH(float depthHS, mat4 matProj)\n{\n    return -matProj[3][2] / (depthHS + matProj[2][2]);\n}\nfloat GetCameraDepthRH(float depthHS, float matProj32, float matProj22)\n{\n    return -matProj32 / (depthHS + matProj22);\n}\nvec4 GetWorldPosFromNDCPosRH(vec3 posHS, mat4 matProj, mat4 matViewProjInv)\n{\n    float w = -GetCameraDepthRH(posHS.z, matProj);\n    return matViewProjInv * vec4(posHS * w, w);\n}\n  float round(float value)   {\n      float f = fract(value);\n      return value - f + (f < 0.5 ? 0.0 : 1.0);\n  }\n  vec2 round(vec2 value) { return vec2(round(value.x), round(value.y)); }\n  vec3 round(vec3 value) { return vec3(round(value.x), round(value.y), round(value.z)); }\n  vec4 round(vec4 value) { return vec4(round(value.x), round(value.y), round(value.z), round(value.w)); }\nfloat rsqrt(float value) { return 1.0 / sqrt(value); }\nvec2 rsqrt(vec2 value) { return vec2(1.0) / sqrt(value); }\nvec3 rsqrt(vec3 value) { return vec3(1.0) / sqrt(value); }\nvec4 rsqrt(vec4 value) { return vec4(1.0) / sqrt(value); }\nfloat rand(vec2 seeds_zero_to_one) {\n    return fract(sin(dot(seeds_zero_to_one.xy, vec2(12.9898, 78.233))) * 43758.5453);\n}\n#if CC_USE_FOG != 4\n  float LinearFog(vec4 pos, vec3 cameraPos, float fogStart, float fogEnd) {\n      vec4 wPos = pos;\n      float cam_dis = distance(cameraPos, wPos.xyz);\n      return clamp((fogEnd - cam_dis) / (fogEnd - fogStart), 0., 1.);\n  }\n  float ExpFog(vec4 pos, vec3 cameraPos, float fogStart, float fogDensity, float fogAtten) {\n      vec4 wPos = pos;\n      float cam_dis = max(distance(cameraPos, wPos.xyz) - fogStart, 0.0) / fogAtten * 4.;\n      float f = exp(-cam_dis * fogDensity);\n      return f;\n  }\n  float ExpSquaredFog(vec4 pos, vec3 cameraPos, float fogStart, float fogDensity, float fogAtten) {\n      vec4 wPos = pos;\n      float cam_dis = max(distance(cameraPos, wPos.xyz) - fogStart, 0.0) / fogAtten * 4.;\n      float f = exp(-cam_dis * cam_dis * fogDensity * fogDensity);\n      return f;\n  }\n  float LayeredFog(vec4 pos, vec3 cameraPos, float fogTop, float fogRange, float fogAtten) {\n      vec4 wPos = pos;\n      vec3 camWorldProj = cameraPos.xyz;\n      camWorldProj.y = 0.;\n      vec3 worldPosProj = wPos.xyz;\n      worldPosProj.y = 0.;\n      float fDeltaD = distance(worldPosProj, camWorldProj) / fogAtten * 2.0;\n      float fDeltaY, fDensityIntegral;\n      if (cameraPos.y > fogTop) {\n          if (wPos.y < fogTop) {\n              fDeltaY = (fogTop - wPos.y) / fogRange * 2.0;\n              fDensityIntegral = fDeltaY * fDeltaY * 0.5;\n          }\n          else {\n              fDeltaY = 0.;\n              fDensityIntegral = 0.;\n          }\n      }\n      else {\n          if (wPos.y < fogTop) {\n              float fDeltaA = (fogTop - cameraPos.y) / fogRange * 2.;\n              float fDeltaB = (fogTop - wPos.y) / fogRange * 2.;\n              fDeltaY = abs(fDeltaA - fDeltaB);\n              fDensityIntegral = abs((fDeltaA * fDeltaA * 0.5) - (fDeltaB * fDeltaB * 0.5));\n          }\n          else {\n              fDeltaY = abs(fogTop - cameraPos.y) / fogRange * 2.;\n              fDensityIntegral = abs(fDeltaY * fDeltaY * 0.5);\n          }\n      }\n      float fDensity;\n      if (fDeltaY != 0.) {\n          fDensity = (sqrt(1.0 + ((fDeltaD / fDeltaY) * (fDeltaD / fDeltaY)))) * fDensityIntegral;\n      }\n      else {\n          fDensity = 0.;\n      }\n      float f = exp(-fDensity);\n      return f;\n  }\n#endif\nvoid CC_TRANSFER_FOG_BASE(vec4 pos, out float factor)\n{\n#if CC_USE_FOG == 0\n\tfactor = LinearFog(pos, cc_cameraPos.xyz, cc_fogBase.x, cc_fogBase.y);\n#elif CC_USE_FOG == 1\n\tfactor = ExpFog(pos, cc_cameraPos.xyz, cc_fogBase.x, cc_fogBase.z, cc_fogAdd.z);\n#elif CC_USE_FOG == 2\n\tfactor = ExpSquaredFog(pos, cc_cameraPos.xyz, cc_fogBase.x, cc_fogBase.z, cc_fogAdd.z);\n#elif CC_USE_FOG == 3\n\tfactor = LayeredFog(pos, cc_cameraPos.xyz, cc_fogAdd.x, cc_fogAdd.y, cc_fogAdd.z);\n#else\n\tfactor = 1.0;\n#endif\n}\n      uniform vec4 uvDepthToEyePosParams;\n      uniform vec4 radiusParam;\n      uniform vec4 miscParam;\n      uniform vec4 randomTexSize;\n      uniform vec4 blurParam;\n#define NUM_STEPS 8.0\n#define NUM_STEPS_LOOP NUM_STEPS*2.0\n#define INV_NUM_DIRECTIONS 0.125\n#define NUM_DIRECTIONS 8\n#define CONSTANT_2PI 6.2831852\n#define g_AOResolution cc_screenSize.xy\n#define g_InvAOResolution cc_screenSize.zw\n#define g_UVToViewA uvDepthToEyePosParams.xy\n#define g_UVToViewB uvDepthToEyePosParams.zw\n#define g_R radiusParam.x\n#define g_R2 radiusParam.y\n#define g_NegInvR2 radiusParam.z\n#define g_MaxRadiusPixels radiusParam.w\n#define g_FocalLen miscParam.xy\n#define g_TanAngleBias miscParam.z\n#define g_Strength miscParam.w\nuniform sampler2D DepthTex;\nuniform sampler2D RandomTex;\nvec2 fixUV(vec2 uv)\n{\n  return saturate(uv);\n}\nfloat InvLength(vec2 v)\n{\n\treturn rsqrt(dot(v,v));\n}\nfloat Tangent(vec3 P, vec3 S)\n{\n\treturn (P.z - S.z) * InvLength(S.xy - P.xy);\n}\nvec3 UVToEye(vec2 uv, float eye_z)\n{\n    uv = g_UVToViewA * uv + g_UVToViewB;\n    return vec3(uv * eye_z, eye_z);\n}\nfloat GetLinearDepth(vec2 uv) {\n  float depthHS = 0.0;\n  #if defined(CC_USE_WGPU)\n      depthHS = texture2DLod(DepthTex, fixUV(uv), 0.0).r * 2.0 - 1.0;\n  #else\n      depthHS = texture2D(DepthTex, fixUV(uv)).r * 2.0 - 1.0;\n  #endif\n  return -GetCameraDepthRH(depthHS, cc_matProj);\n}\nvec3 FetchEyePos(vec2 uv)\n{\n    float depth = GetLinearDepth(uv);\n    return UVToEye(uv, depth);\n}\nfloat Length2(vec3 v)\n{\n  \treturn dot(v, v);\n}\nvec3 MinDiff(vec3 P, vec3 Pr, vec3 Pl)\n{\n  \tvec3 V1 = Pr - P;\n  \tvec3 V2 = P - Pl;\n  \treturn (Length2(V1) < Length2(V2)) ? V1 : V2;\n}\nfloat Falloff(float d2)\n{\n    return d2 * g_NegInvR2 + 1.0;\n}\nvec2 SnapUVOffset(vec2 uv)\n{\n    return round(uv * g_AOResolution) * g_InvAOResolution;\n}\nfloat TanToSin(float x)\n{\n  \treturn x * rsqrt(x*x + 1.0);\n}\nvec3 TangentVector(vec2 deltaUV, vec3 dPdu, vec3 dPdv)\n{\n  \treturn deltaUV.x * dPdu + deltaUV.y * dPdv;\n}\nfloat Tangent(vec3 T)\n{\n  \treturn -T.z * InvLength(T.xy);\n}\nfloat BiasedTangent(vec3 T)\n{\n    return Tangent(T) + g_TanAngleBias;\n}\nvec2 RotateDirections(vec2 Dir, vec2 CosSin)\n{\n  \treturn vec2(Dir.x*CosSin.x - Dir.y*CosSin.y,\n  \t\tDir.x*CosSin.y + Dir.y*CosSin.x);\n}\nfloat IntegerateOcclusion(vec2 uv0,\n                            vec2 snapped_duv,\n                            vec3 P,\n                            vec3 dPdu,\n                            vec3 dPdv,\n                            inout float tanH)\n{\n    float ao = 0.0;\n    vec3 T1 = TangentVector(snapped_duv, dPdu, dPdv);\n    float tanT = BiasedTangent(T1);\n    float sinT = TanToSin(tanT);\n    vec3 S = FetchEyePos(uv0 + snapped_duv);\n    float tanS = Tangent(P, S);\n    float sinS = TanToSin(tanS);\n    float d2 = Length2(S - P);\n    if ((d2 < g_R2) && (tanS > tanT))\n    {\n      ao = Falloff(d2) * (sinS - sinT);\n      tanH = max(tanH, tanS);\n    }\n    return ao;\n}\nfloat HorizonOcclusion(vec2 deltaUV,\n                        vec2 texelDeltaUV,\n                        vec2 uv0,\n                        vec3 P,\n                        float numSteps,\n                        float randstep,\n                        vec3 dPdu,\n                        vec3 dPdv)\n{\n    float ao = 0.0;\n    vec2 uv = uv0 + SnapUVOffset( randstep * deltaUV );\n    deltaUV = SnapUVOffset( deltaUV );\n    vec3 T = deltaUV.x * dPdu + deltaUV.y * dPdv;\n    float tanH = BiasedTangent(T);\n#if SAMPLE_FIRST_STEP\n    vec2 snapped_duv = SnapUVOffset( randstep * deltaUV + texelDeltaUV );\n    ao = IntegerateOcclusion(uv0, snapped_duv, P, dPdu, dPdv, tanH);\n    --numSteps;\n#endif\n    float sinH = tanH / sqrt(1.0 + tanH*tanH);\n    for (float j = 1.0; j <= NUM_STEPS_LOOP; j += 1.0)\n    {\n      if (j <= numSteps)\n      {\n        uv += deltaUV;\n        vec3 S = FetchEyePos(uv);\n        float tanS = Tangent(P, S);\n        float d2 = Length2(S - P);\n        if ((d2 < g_R2) && (tanS > tanH))\n        {\n          float sinS = tanS / sqrt(1.0 + tanS*tanS);\n          ao += Falloff(d2) * (sinS - sinH);\n          tanH = tanS;\n          sinH = sinS;\n        }\n      }\n    }\n    return ao;\n}\nvoid ComputeSteps(inout vec2 step_size_uv, inout float numSteps, float ray_radius_pix, float rand)\n{\n    numSteps = min(NUM_STEPS, ray_radius_pix);\n    float step_size_pix = ray_radius_pix / (numSteps + 1.0);\n    float maxNumSteps = g_MaxRadiusPixels / step_size_pix;\n    if (maxNumSteps < numSteps)\n    {\n      numSteps = floor(maxNumSteps + rand);\n      numSteps = max(numSteps, 1.0);\n      step_size_pix = g_MaxRadiusPixels / numSteps;\n    }\n    step_size_uv = step_size_pix * g_InvAOResolution;\n}\nfloat CalculateAO(vec2 uv)\n{\n    vec3 P = FetchEyePos(uv);\n    vec3 rand = texture2D(RandomTex, uv * g_AOResolution.xy * randomTexSize.zw).xyz;\n    vec2 ray_radius_uv = 0.5 * g_R * g_FocalLen / P.z;\n    float ray_radius_pix = ray_radius_uv.x * g_AOResolution.x;\n    if (ray_radius_pix < 1.0)\n    {\n      return 1.0;\n    }\n    float numSteps;\n    vec2 step_size;\n    ComputeSteps(step_size, numSteps, ray_radius_pix, rand.z);\n    vec3 Pr = FetchEyePos(uv + vec2(g_InvAOResolution.x, 0.0));\n    vec3 Pl = FetchEyePos(uv + vec2(-g_InvAOResolution.x, 0.0));\n    vec3 Pt = FetchEyePos(uv + vec2(0.0, g_InvAOResolution.y));\n    vec3 Pb = FetchEyePos(uv + vec2(0.0, -g_InvAOResolution.y));\n    vec3 dPdu = MinDiff(P, Pr, Pl);\n    vec3 dPdv = MinDiff(P, Pt, Pb) * (g_AOResolution.y * g_InvAOResolution.x);\n    float ao = 0.0;\n    float alpha = CONSTANT_2PI * INV_NUM_DIRECTIONS;\n    for (int d = 0; d < NUM_DIRECTIONS; ++d)\n    {\n      float angle = alpha * float(d);\n      vec2 dir = RotateDirections(vec2(cos(angle), sin(angle)), rand.xy);\n      vec2 deltaUV = dir * step_size.xy;\n      vec2 texelDeltaUV = dir * g_InvAOResolution;\n      ao += HorizonOcclusion(deltaUV, texelDeltaUV, uv, P, numSteps, rand.z, dPdu, dPdv);\n    }\n  ao = 1.0 - ao * INV_NUM_DIRECTIONS * g_Strength;\n  #if CC_USE_FOG != 4 && !CC_USE_FLOAT_OUTPUT\n    float fogFactor = 1.0;\n    float depth = 0.0;\n    #if defined(CC_USE_WGPU)\n      depth = texture2DLod(DepthTex, uv, 0.0).r;\n    #else\n      depth = texture2D(DepthTex, uv).r;\n    #endif\n    vec3 posHS = vec3(uv, depth) * 2.0 - vec3(1.0);\n    posHS.xy = cc_cameraPos.w == 0.0 ? vec2(posHS.xy.x, -posHS.xy.y) : posHS.xy;\n    vec4 worldPos = GetWorldPosFromNDCPosRH(posHS, cc_matProj, cc_matViewProjInv);\n    CC_TRANSFER_FOG_BASE(vec4(worldPos.xyz, 1.0), fogFactor);\n    ao = mix(1.0, ao, pow(fogFactor * 0.9, 4.0));\n  #endif\n  return ao;\n}\n#define KERNEL_FALLOFF 3.0f\n#define KERNEL_RADIUS 8\n#define HALF_KERNEL_RADIUS 4\n#define g_InvFullResolution cc_screenSize.zw\n#define g_BlurFallOff blurParam.x\n#define g_BlurDepthThreshold blurParam.y\n#define g_BlurSimpleRadius blurParam.z\nuniform sampler2D AOTexNearest;\n#define AOTexBilinear AOTexNearest\n#define GetLinearDepthBilinear GetLinearDepth\n#define g_AOSaturation blurParam.w\nvarying vec2 v_uv;\nvoid main () {\n  float ao = CalculateAO(v_uv);\n  gl_FragColor = vec4(ao, ao, ao, 1.0);\n}"}, "builtins": {"globals": {"blocks": [{"name": "CCGlobal", "defines": []}, {"name": "CCCamera", "defines": []}], "samplerTextures": [], "buffers": [], "images": []}, "locals": {"blocks": [], "samplerTextures": [], "buffers": [], "images": []}, "statistics": {"CC_EFFECT_USED_VERTEX_UNIFORM_VECTORS": 42, "CC_EFFECT_USED_FRAGMENT_UNIFORM_VECTORS": 47}}, "defines": [{"name": "USE_INSTANCING", "type": "boolean", "defines": [], "editor": {"elevated": true}}, {"name": "CC_USE_SKINNING", "type": "boolean", "defines": []}, {"name": "CC_USE_BAKED_ANIMATION", "type": "boolean", "defines": ["USE_INSTANCING"]}, {"name": "CC_USE_LIGHTMAP", "type": "boolean", "defines": ["USE_INSTANCING"]}, {"name": "CC_USE_REFLECTION_PROBE", "type": "boolean", "defines": ["USE_INSTANCING"]}, {"name": "CC_RECEIVE_SHADOW", "type": "boolean", "defines": ["USE_INSTANCING"]}, {"name": "CC_USE_LIGHT_PROBE", "type": "boolean", "defines": ["USE_INSTANCING"]}, {"name": "CC_USE_MORPH", "type": "boolean", "defines": []}, {"name": "CC_USE_FOG", "type": "number", "defines": [], "range": [0, 4]}, {"name": "SAMPLE_FIRST_STEP", "type": "boolean", "defines": []}, {"name": "CC_USE_FLOAT_OUTPUT", "type": "boolean", "defines": ["CC_USE_FOG"]}], "name": "pipeline/post-process/hbao|hbao-vs|hbao-fs"}, {"blocks": [{"name": "hbaoUBO", "members": [{"name": "uvDepthToEyePosParams", "type": 16, "count": 1}, {"name": "radiusParam", "type": 16, "count": 1}, {"name": "miscParam", "type": 16, "count": 1}, {"name": "randomTexSize", "type": 16, "count": 1}, {"name": "blurParam", "type": 16, "count": 1}], "defines": [], "stageFlags": 16, "binding": 0}], "samplerTextures": [{"name": "RandomTex", "type": 28, "count": 1, "defines": [], "stageFlags": 16, "sampleType": 0, "binding": 2}], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": [], "attributes": [{"name": "a_position", "defines": [], "format": 32, "location": 0}, {"name": "a_normal", "defines": [], "format": 32, "location": 1}, {"name": "a_texCoord", "defines": [], "format": 21, "location": 2}, {"name": "a_tangent", "defines": [], "format": 44, "location": 3}, {"name": "a_joints", "defines": ["CC_USE_SKINNING"], "location": 4}, {"name": "a_weights", "defines": ["CC_USE_SKINNING"], "format": 44, "location": 5}, {"name": "a_jointAnimInfo", "defines": ["USE_INSTANCING", "CC_USE_BAKED_ANIMATION"], "format": 44, "isInstanced": true, "location": 6}, {"name": "a_matWorld0", "defines": ["USE_INSTANCING"], "format": 44, "isInstanced": true, "location": 7}, {"name": "a_matWorld1", "defines": ["USE_INSTANCING"], "format": 44, "isInstanced": true, "location": 8}, {"name": "a_matWorld2", "defines": ["USE_INSTANCING"], "format": 44, "isInstanced": true, "location": 9}, {"name": "a_lightingMapUVParam", "defines": ["USE_INSTANCING", "CC_USE_LIGHTMAP"], "format": 44, "isInstanced": true, "location": 10}, {"name": "a_localShadowBiasAndProbeId", "defines": ["USE_INSTANCING"], "format": 44, "isInstanced": true, "location": 11}, {"name": "a_reflectionProbeData", "defines": ["USE_INSTANCING", "CC_USE_REFLECTION_PROBE"], "format": 44, "isInstanced": true, "location": 12}, {"name": "a_sh_linear_const_r", "defines": ["USE_INSTANCING", "CC_USE_LIGHT_PROBE"], "format": 44, "isInstanced": true, "location": 13}, {"name": "a_sh_linear_const_g", "defines": ["USE_INSTANCING", "CC_USE_LIGHT_PROBE"], "format": 44, "isInstanced": true, "location": 14}, {"name": "a_sh_linear_const_b", "defines": ["USE_INSTANCING", "CC_USE_LIGHT_PROBE"], "format": 44, "isInstanced": true, "location": 15}, {"name": "a_vertexId", "defines": ["CC_USE_MORPH"], "format": 11, "location": 16}], "varyings": [{"name": "v_uv", "type": 14, "count": 1, "defines": [], "stageFlags": 17, "location": 0}], "fragColors": [{"name": "fragColor", "typename": "vec4", "type": 16, "count": 1, "defines": [], "stageFlags": 16, "location": 0}], "descriptors": [{"rate": 0, "blocks": [], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}, {"rate": 1, "blocks": [{"name": "hbaoUBO", "members": [{"name": "uvDepthToEyePosParams", "type": 16, "count": 1}, {"name": "radiusParam", "type": 16, "count": 1}, {"name": "miscParam", "type": 16, "count": 1}, {"name": "randomTexSize", "type": 16, "count": 1}, {"name": "blurParam", "type": 16, "count": 1}], "defines": [], "stageFlags": 16, "binding": 0}], "samplerTextures": [{"name": "RandomTex", "type": 28, "count": 1, "defines": [], "stageFlags": 16, "sampleType": 0, "binding": 2}], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}, {"rate": 2, "blocks": [], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}, {"rate": 3, "blocks": [{"tags": {"builtin": "global"}, "name": "CCGlobal", "members": [{"name": "cc_time", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_screenSize", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_nativeSize", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_probeInfo", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_debug_view_mode", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}], "defines": [], "stageFlags": 17}, {"tags": {"builtin": "global"}, "name": "CCCamera", "members": [{"name": "cc_matView", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matViewInv", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matProj", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matProjInv", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matViewProj", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matViewProjInv", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_cameraPos", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_surfaceTransform", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_screenScale", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_exposure", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_mainLitDir", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_mainLitColor", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_ambientSky", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_ambientGround", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_fogColor", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_fogBase", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_fogAdd", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_nearFar", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_viewPort", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}], "defines": [], "stageFlags": 17}], "samplerTextures": [{"name": "DepthTex", "type": 28, "count": 1, "defines": [], "stageFlags": 16, "sampleType": 0, "binding": 1}, {"name": "AOTexNearest", "type": 28, "count": 1, "defines": [], "stageFlags": 16, "sampleType": 0, "binding": 3}], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}], "hash": 4208032054, "glsl4": {"vert": "\nprecision highp float;\n#define QUATER_PI         0.78539816340\n#define HALF_PI           1.57079632679\n#define PI                3.14159265359\n#define PI2               6.28318530718\n#define PI4               12.5663706144\n#define INV_QUATER_PI     1.27323954474\n#define INV_HALF_PI       0.63661977237\n#define INV_PI            0.31830988618\n#define INV_PI2           0.15915494309\n#define INV_PI4           0.07957747155\n#define EPSILON           1e-6\n#define EPSILON_LOWP      1e-4\n#define LOG2              1.442695\n#define EXP_VALUE         2.71828183\n#define FP_MAX            65504.0\n#define FP_SCALE          0.0009765625\n#define FP_SCALE_INV      1024.0\n#define GRAY_VECTOR       vec3(0.299, 0.587, 0.114)\n#define LIGHT_MAP_TYPE_DISABLED 0\n#define LIGHT_MAP_TYPE_ALL_IN_ONE 1\n#define LIGHT_MAP_TYPE_INDIRECT_OCCLUSION 2\n#define REFLECTION_PROBE_TYPE_NONE 0\n#define REFLECTION_PROBE_TYPE_CUBE 1\n#define REFLECTION_PROBE_TYPE_PLANAR 2\n#define REFLECTION_PROBE_TYPE_BLEND 3\n#define REFLECTION_PROBE_TYPE_BLEND_AND_SKYBOX 4\n#define LIGHT_TYPE_DIRECTIONAL 0.0\n#define LIGHT_TYPE_SPHERE 1.0\n#define LIGHT_TYPE_SPOT 2.0\n#define LIGHT_TYPE_POINT 3.0\n#define LIGHT_TYPE_RANGED_DIRECTIONAL 4.0\n#define IS_DIRECTIONAL_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_DIRECTIONAL)) < EPSILON_LOWP)\n#define IS_SPHERE_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_SPHERE)) < EPSILON_LOWP)\n#define IS_SPOT_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_SPOT)) < EPSILON_LOWP)\n#define IS_POINT_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_POINT)) < EPSILON_LOWP)\n#define IS_RANGED_DIRECTIONAL_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_RANGED_DIRECTIONAL)) < EPSILON_LOWP)\n#define TONE_MAPPING_ACES 0\n#define TONE_MAPPING_LINEAR 1\n#define SURFACES_MAX_TRANSMIT_DEPTH_VALUE 999999.0\n#ifndef CC_SURFACES_DEBUG_VIEW_SINGLE\n  #define CC_SURFACES_DEBUG_VIEW_SINGLE 1\n#endif\n#ifndef CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC\n  #define CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC 2\n#endif\nstruct StandardVertInput {\n  highp vec4 position;\n  vec3 normal;\n  vec4 tangent;\n};\nlayout(location = 0) in vec3 a_position;\nlayout(location = 1) in vec3 a_normal;\nlayout(location = 2) in vec2 a_texCoord;\nlayout(location = 3) in vec4 a_tangent;\n#if CC_USE_SKINNING\n    layout(location = 4) in u32vec4 a_joints;\n  layout(location = 5) in vec4 a_weights;\n#endif\n#if USE_INSTANCING\n  #if CC_USE_BAKED_ANIMATION\n    layout(location = 6) in highp vec4 a_jointAnimInfo;\n  #endif\n  layout(location = 7) in vec4 a_matWorld0;\n  layout(location = 8) in vec4 a_matWorld1;\n  layout(location = 9) in vec4 a_matWorld2;\n  #if CC_USE_LIGHTMAP\n    layout(location = 10) in vec4 a_lightingMapUVParam;\n  #endif\n  #if CC_USE_REFLECTION_PROBE || CC_RECEIVE_SHADOW\n    #if CC_RECEIVE_SHADOW\n    #endif\n    layout(location = 11) in vec4 a_localShadowBiasAndProbeId;\n  #endif\n  #if CC_USE_REFLECTION_PROBE\n    layout(location = 12) in vec4 a_reflectionProbeData;\n  #endif\n  #if CC_USE_LIGHT_PROBE\n    layout(location = 13) in vec4 a_sh_linear_const_r;\n    layout(location = 14) in vec4 a_sh_linear_const_g;\n    layout(location = 15) in vec4 a_sh_linear_const_b;\n  #endif\n#endif\n#if CC_USE_MORPH\n#endif\nlayout(set = 0, binding = 0) uniform CCGlobal {\n  highp   vec4 cc_time;\n  mediump vec4 cc_screenSize;\n  mediump vec4 cc_nativeSize;\n  mediump vec4 cc_probeInfo;\n  mediump vec4 cc_debug_view_mode;\n};\nlayout(set = 0, binding = 1) uniform CCCamera {\n  highp   mat4 cc_matView;\n  highp   mat4 cc_matViewInv;\n  highp   mat4 cc_matProj;\n  highp   mat4 cc_matProjInv;\n  highp   mat4 cc_matViewProj;\n  highp   mat4 cc_matViewProjInv;\n  highp   vec4 cc_cameraPos;\n  mediump vec4 cc_surfaceTransform;\n  mediump vec4 cc_screenScale;\n  mediump vec4 cc_exposure;\n  mediump vec4 cc_mainLitDir;\n  mediump vec4 cc_mainLitColor;\n  mediump vec4 cc_ambientSky;\n  mediump vec4 cc_ambientGround;\n  mediump vec4 cc_fogColor;\n  mediump vec4 cc_fogBase;\n  mediump vec4 cc_fogAdd;\n  mediump vec4 cc_nearFar;\n  mediump vec4 cc_viewPort;\n};\nlayout(location = 0) out vec2 v_uv;\nvoid main () {\n  StandardVertInput In;\n    In.position = vec4(a_position, 1.0);\n    In.normal = a_normal;\n    In.tangent = a_tangent;\n  In.position.xy = cc_cameraPos.w == 0.0 ? vec2(In.position.xy.x, -In.position.xy.y) : In.position.xy;\n  gl_Position = In.position;\n  v_uv = a_texCoord;\n}", "frag": "\nprecision highp float;\nprecision highp float;\nlayout(set = 0, binding = 0) uniform CCGlobal {\n  highp   vec4 cc_time;\n  mediump vec4 cc_screenSize;\n  mediump vec4 cc_nativeSize;\n  mediump vec4 cc_probeInfo;\n  mediump vec4 cc_debug_view_mode;\n};\nlayout(set = 0, binding = 1) uniform CCCamera {\n  highp   mat4 cc_matView;\n  highp   mat4 cc_matViewInv;\n  highp   mat4 cc_matProj;\n  highp   mat4 cc_matProjInv;\n  highp   mat4 cc_matViewProj;\n  highp   mat4 cc_matViewProjInv;\n  highp   vec4 cc_cameraPos;\n  mediump vec4 cc_surfaceTransform;\n  mediump vec4 cc_screenScale;\n  mediump vec4 cc_exposure;\n  mediump vec4 cc_mainLitDir;\n  mediump vec4 cc_mainLitColor;\n  mediump vec4 cc_ambientSky;\n  mediump vec4 cc_ambientGround;\n  mediump vec4 cc_fogColor;\n  mediump vec4 cc_fogBase;\n  mediump vec4 cc_fogAdd;\n  mediump vec4 cc_nearFar;\n  mediump vec4 cc_viewPort;\n};\n#define QUATER_PI         0.78539816340\n#define HALF_PI           1.57079632679\n#define PI                3.14159265359\n#define PI2               6.28318530718\n#define PI4               12.5663706144\n#define INV_QUATER_PI     1.27323954474\n#define INV_HALF_PI       0.63661977237\n#define INV_PI            0.31830988618\n#define INV_PI2           0.15915494309\n#define INV_PI4           0.07957747155\n#define EPSILON           1e-6\n#define EPSILON_LOWP      1e-4\n#define LOG2              1.442695\n#define EXP_VALUE         2.71828183\n#define FP_MAX            65504.0\n#define FP_SCALE          0.0009765625\n#define FP_SCALE_INV      1024.0\n#define GRAY_VECTOR       vec3(0.299, 0.587, 0.114)\nfloat saturate(float value) { return clamp(value, 0.0, 1.0); }\nvec2 saturate(vec2 value) { return clamp(value, vec2(0.0), vec2(1.0)); }\nvec3 saturate(vec3 value) { return clamp(value, vec3(0.0), vec3(1.0)); }\nvec4 saturate(vec4 value) { return clamp(value, vec4(0.0), vec4(1.0)); }\n#define LIGHT_MAP_TYPE_DISABLED 0\n#define LIGHT_MAP_TYPE_ALL_IN_ONE 1\n#define LIGHT_MAP_TYPE_INDIRECT_OCCLUSION 2\n#define REFLECTION_PROBE_TYPE_NONE 0\n#define REFLECTION_PROBE_TYPE_CUBE 1\n#define REFLECTION_PROBE_TYPE_PLANAR 2\n#define REFLECTION_PROBE_TYPE_BLEND 3\n#define REFLECTION_PROBE_TYPE_BLEND_AND_SKYBOX 4\n#define LIGHT_TYPE_DIRECTIONAL 0.0\n#define LIGHT_TYPE_SPHERE 1.0\n#define LIGHT_TYPE_SPOT 2.0\n#define LIGHT_TYPE_POINT 3.0\n#define LIGHT_TYPE_RANGED_DIRECTIONAL 4.0\n#define IS_DIRECTIONAL_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_DIRECTIONAL)) < EPSILON_LOWP)\n#define IS_SPHERE_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_SPHERE)) < EPSILON_LOWP)\n#define IS_SPOT_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_SPOT)) < EPSILON_LOWP)\n#define IS_POINT_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_POINT)) < EPSILON_LOWP)\n#define IS_RANGED_DIRECTIONAL_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_RANGED_DIRECTIONAL)) < EPSILON_LOWP)\n#define TONE_MAPPING_ACES 0\n#define TONE_MAPPING_LINEAR 1\n#define SURFACES_MAX_TRANSMIT_DEPTH_VALUE 999999.0\n#ifndef CC_SURFACES_DEBUG_VIEW_SINGLE\n  #define CC_SURFACES_DEBUG_VIEW_SINGLE 1\n#endif\n#ifndef CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC\n  #define CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC 2\n#endif\n#if defined(CC_USE_METAL) || defined(CC_USE_WGPU)\n#define CC_HANDLE_SAMPLE_NDC_FLIP_STATIC(y) y = -y\n#else\n#define CC_HANDLE_SAMPLE_NDC_FLIP_STATIC(y)\n#endif\nfloat GetCameraDepthRH(float depthHS, mat4 matProj)\n{\n    return -matProj[3][2] / (depthHS + matProj[2][2]);\n}\nfloat GetCameraDepthRH(float depthHS, float matProj32, float matProj22)\n{\n    return -matProj32 / (depthHS + matProj22);\n}\n#if CC_USE_FOG != 4\n#endif\nlayout(set = 1, binding = 0) uniform hbaoUBO {\n    vec4 uvDepthToEyePosParams;\n    vec4 radiusParam;\n    vec4 miscParam;\n    vec4 randomTexSize;\n    vec4 blurParam;\n};\n#define NUM_STEPS 8.0\n#define NUM_STEPS_LOOP NUM_STEPS*2.0\n#define INV_NUM_DIRECTIONS 0.125\n#define NUM_DIRECTIONS 8\n#define CONSTANT_2PI 6.2831852\n#define g_AOResolution cc_screenSize.xy\n#define g_InvAOResolution cc_screenSize.zw\n#define g_UVToViewA uvDepthToEyePosParams.xy\n#define g_UVToViewB uvDepthToEyePosParams.zw\n#define g_R radiusParam.x\n#define g_R2 radiusParam.y\n#define g_NegInvR2 radiusParam.z\n#define g_MaxRadiusPixels radiusParam.w\n#define g_FocalLen miscParam.xy\n#define g_TanAngleBias miscParam.z\n#define g_Strength miscParam.w\nlayout(set = 1, binding = 1) uniform sampler2D DepthTex;\nlayout(set = 1, binding = 2) uniform sampler2D RandomTex;\nvec2 fixUV(vec2 uv)\n{\n  return saturate(uv);\n}\nfloat GetLinearDepth(vec2 uv) {\n  float depthHS = 0.0;\n  #if defined(CC_USE_WGPU)\n      depthHS = textureLod(DepthTex, fixUV(uv), 0.0).r * 2.0 - 1.0;\n  #else\n      depthHS = texture(DepthTex, fixUV(uv)).r * 2.0 - 1.0;\n  #endif\n  return -GetCameraDepthRH(depthHS, cc_matProj);\n}\n#define KERNEL_FALLOFF 3.0f\n#define KERNEL_RADIUS 8\n#define HALF_KERNEL_RADIUS 4\n#define g_InvFullResolution cc_screenSize.zw\n#define g_BlurFallOff blurParam.x\n#define g_BlurDepthThreshold blurParam.y\n#define g_BlurSimpleRadius blurParam.z\nlayout(set = 1, binding = 3) uniform sampler2D AOTexNearest;\n#define AOTexBilinear AOTexNearest\n#define GetLinearDepthBilinear GetLinearDepth\nfloat CrossBilateralWeight(float r, float d, float d0, float blurFalloff, float blurDepthThreshold)\n{\n  return exp2(-r*r*blurFalloff) * (abs(d - d0) < blurDepthThreshold ? 1.0 : 0.0);\n}\nvec2 SumWeightedAO(int i, float centerCameraDepth, vec2 centerUV, vec2 sampleDir, float blurFalloff, float blurDepthThreshold)\n{\n  float r = 2.0 * float(i) + 0.5;\n  vec2 sampleUV = centerUV + r * sampleDir * g_InvFullResolution;\n  float d = GetLinearDepthBilinear(sampleUV);\n  float sampledAO = texture(AOTexNearest, fixUV(sampleUV)).x;\n  float w = CrossBilateralWeight(r, d, centerCameraDepth, blurFalloff, blurDepthThreshold);\n  return vec2(w * sampledAO, w);\n}\nfloat BlurCore(vec2 uv, vec2 sampleDir)\n{\n  float ao = texture(AOTexNearest, uv).x;\n  float weight = 1.0;\n  float centerCameraDepth = GetLinearDepth(uv);\n  for(int i = -HALF_KERNEL_RADIUS; i <= HALF_KERNEL_RADIUS; i++)\n  {\n    vec2 aoAndW = SumWeightedAO(i, centerCameraDepth, uv, sampleDir, g_BlurFallOff, g_BlurDepthThreshold);\n    ao += aoAndW.x;\n    weight += aoAndW.y;\n  }\n  return ao / weight;\n}\n#define g_AOSaturation blurParam.w\nlayout(location = 0) in vec2 v_uv;\nlayout(location = 0) out vec4 fragColor;\nvoid main () {\n  float ao = BlurCore(v_uv, vec2(1.0, 0.0));\n  fragColor = vec4(ao, ao, ao, 1.0);\n}"}, "glsl3": {"vert": "\nprecision highp float;\n#define QUATER_PI         0.78539816340\n#define HALF_PI           1.57079632679\n#define PI                3.14159265359\n#define PI2               6.28318530718\n#define PI4               12.5663706144\n#define INV_QUATER_PI     1.27323954474\n#define INV_HALF_PI       0.63661977237\n#define INV_PI            0.31830988618\n#define INV_PI2           0.15915494309\n#define INV_PI4           0.07957747155\n#define EPSILON           1e-6\n#define EPSILON_LOWP      1e-4\n#define LOG2              1.442695\n#define EXP_VALUE         2.71828183\n#define FP_MAX            65504.0\n#define FP_SCALE          0.0009765625\n#define FP_SCALE_INV      1024.0\n#define GRAY_VECTOR       vec3(0.299, 0.587, 0.114)\n#define LIGHT_MAP_TYPE_DISABLED 0\n#define LIGHT_MAP_TYPE_ALL_IN_ONE 1\n#define LIGHT_MAP_TYPE_INDIRECT_OCCLUSION 2\n#define REFLECTION_PROBE_TYPE_NONE 0\n#define REFLECTION_PROBE_TYPE_CUBE 1\n#define REFLECTION_PROBE_TYPE_PLANAR 2\n#define REFLECTION_PROBE_TYPE_BLEND 3\n#define REFLECTION_PROBE_TYPE_BLEND_AND_SKYBOX 4\n#define LIGHT_TYPE_DIRECTIONAL 0.0\n#define LIGHT_TYPE_SPHERE 1.0\n#define LIGHT_TYPE_SPOT 2.0\n#define LIGHT_TYPE_POINT 3.0\n#define LIGHT_TYPE_RANGED_DIRECTIONAL 4.0\n#define IS_DIRECTIONAL_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_DIRECTIONAL)) < EPSILON_LOWP)\n#define IS_SPHERE_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_SPHERE)) < EPSILON_LOWP)\n#define IS_SPOT_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_SPOT)) < EPSILON_LOWP)\n#define IS_POINT_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_POINT)) < EPSILON_LOWP)\n#define IS_RANGED_DIRECTIONAL_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_RANGED_DIRECTIONAL)) < EPSILON_LOWP)\n#define TONE_MAPPING_ACES 0\n#define TONE_MAPPING_LINEAR 1\n#define SURFACES_MAX_TRANSMIT_DEPTH_VALUE 999999.0\n#ifndef CC_SURFACES_DEBUG_VIEW_SINGLE\n  #define CC_SURFACES_DEBUG_VIEW_SINGLE 1\n#endif\n#ifndef CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC\n  #define CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC 2\n#endif\nstruct StandardVertInput {\n  highp vec4 position;\n  vec3 normal;\n  vec4 tangent;\n};\nin vec3 a_position;\nin vec3 a_normal;\nin vec2 a_texCoord;\nin vec4 a_tangent;\n#if CC_USE_SKINNING\n    in vec4 a_joints;\n  in vec4 a_weights;\n#endif\n#if USE_INSTANCING\n  #if CC_USE_BAKED_ANIMATION\n    in highp vec4 a_jointAnimInfo;\n  #endif\n  in vec4 a_matWorld0;\n  in vec4 a_matWorld1;\n  in vec4 a_matWorld2;\n  #if CC_USE_LIGHTMAP\n    in vec4 a_lightingMapUVParam;\n  #endif\n  #if CC_USE_REFLECTION_PROBE || CC_RECEIVE_SHADOW\n    #if CC_RECEIVE_SHADOW\n    #endif\n    in vec4 a_localShadowBiasAndProbeId;\n  #endif\n  #if CC_USE_REFLECTION_PROBE\n    in vec4 a_reflectionProbeData;\n  #endif\n  #if CC_USE_LIGHT_PROBE\n    in vec4 a_sh_linear_const_r;\n    in vec4 a_sh_linear_const_g;\n    in vec4 a_sh_linear_const_b;\n  #endif\n#endif\n#if CC_USE_MORPH\n    in float a_vertexId;\n#endif\nlayout(std140) uniform CCGlobal {\n  highp   vec4 cc_time;\n  mediump vec4 cc_screenSize;\n  mediump vec4 cc_nativeSize;\n  mediump vec4 cc_probeInfo;\n  mediump vec4 cc_debug_view_mode;\n};\nlayout(std140) uniform CCCamera {\n  highp   mat4 cc_matView;\n  highp   mat4 cc_matViewInv;\n  highp   mat4 cc_matProj;\n  highp   mat4 cc_matProjInv;\n  highp   mat4 cc_matViewProj;\n  highp   mat4 cc_matViewProjInv;\n  highp   vec4 cc_cameraPos;\n  mediump vec4 cc_surfaceTransform;\n  mediump vec4 cc_screenScale;\n  mediump vec4 cc_exposure;\n  mediump vec4 cc_mainLitDir;\n  mediump vec4 cc_mainLitColor;\n  mediump vec4 cc_ambientSky;\n  mediump vec4 cc_ambientGround;\n  mediump vec4 cc_fogColor;\n  mediump vec4 cc_fogBase;\n  mediump vec4 cc_fogAdd;\n  mediump vec4 cc_nearFar;\n  mediump vec4 cc_viewPort;\n};\nout vec2 v_uv;\nvoid main () {\n  StandardVertInput In;\n    In.position = vec4(a_position, 1.0);\n    In.normal = a_normal;\n    In.tangent = a_tangent;\n  In.position.xy = cc_cameraPos.w == 0.0 ? vec2(In.position.xy.x, -In.position.xy.y) : In.position.xy;\n  gl_Position = In.position;\n  v_uv = a_texCoord;\n}", "frag": "\nprecision highp float;\nprecision highp float;\nlayout(std140) uniform CCGlobal {\n  highp   vec4 cc_time;\n  mediump vec4 cc_screenSize;\n  mediump vec4 cc_nativeSize;\n  mediump vec4 cc_probeInfo;\n  mediump vec4 cc_debug_view_mode;\n};\nlayout(std140) uniform CCCamera {\n  highp   mat4 cc_matView;\n  highp   mat4 cc_matViewInv;\n  highp   mat4 cc_matProj;\n  highp   mat4 cc_matProjInv;\n  highp   mat4 cc_matViewProj;\n  highp   mat4 cc_matViewProjInv;\n  highp   vec4 cc_cameraPos;\n  mediump vec4 cc_surfaceTransform;\n  mediump vec4 cc_screenScale;\n  mediump vec4 cc_exposure;\n  mediump vec4 cc_mainLitDir;\n  mediump vec4 cc_mainLitColor;\n  mediump vec4 cc_ambientSky;\n  mediump vec4 cc_ambientGround;\n  mediump vec4 cc_fogColor;\n  mediump vec4 cc_fogBase;\n  mediump vec4 cc_fogAdd;\n  mediump vec4 cc_nearFar;\n  mediump vec4 cc_viewPort;\n};\n#define QUATER_PI         0.78539816340\n#define HALF_PI           1.57079632679\n#define PI                3.14159265359\n#define PI2               6.28318530718\n#define PI4               12.5663706144\n#define INV_QUATER_PI     1.27323954474\n#define INV_HALF_PI       0.63661977237\n#define INV_PI            0.31830988618\n#define INV_PI2           0.15915494309\n#define INV_PI4           0.07957747155\n#define EPSILON           1e-6\n#define EPSILON_LOWP      1e-4\n#define LOG2              1.442695\n#define EXP_VALUE         2.71828183\n#define FP_MAX            65504.0\n#define FP_SCALE          0.0009765625\n#define FP_SCALE_INV      1024.0\n#define GRAY_VECTOR       vec3(0.299, 0.587, 0.114)\nfloat saturate(float value) { return clamp(value, 0.0, 1.0); }\nvec2 saturate(vec2 value) { return clamp(value, vec2(0.0), vec2(1.0)); }\nvec3 saturate(vec3 value) { return clamp(value, vec3(0.0), vec3(1.0)); }\nvec4 saturate(vec4 value) { return clamp(value, vec4(0.0), vec4(1.0)); }\n#define LIGHT_MAP_TYPE_DISABLED 0\n#define LIGHT_MAP_TYPE_ALL_IN_ONE 1\n#define LIGHT_MAP_TYPE_INDIRECT_OCCLUSION 2\n#define REFLECTION_PROBE_TYPE_NONE 0\n#define REFLECTION_PROBE_TYPE_CUBE 1\n#define REFLECTION_PROBE_TYPE_PLANAR 2\n#define REFLECTION_PROBE_TYPE_BLEND 3\n#define REFLECTION_PROBE_TYPE_BLEND_AND_SKYBOX 4\n#define LIGHT_TYPE_DIRECTIONAL 0.0\n#define LIGHT_TYPE_SPHERE 1.0\n#define LIGHT_TYPE_SPOT 2.0\n#define LIGHT_TYPE_POINT 3.0\n#define LIGHT_TYPE_RANGED_DIRECTIONAL 4.0\n#define IS_DIRECTIONAL_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_DIRECTIONAL)) < EPSILON_LOWP)\n#define IS_SPHERE_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_SPHERE)) < EPSILON_LOWP)\n#define IS_SPOT_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_SPOT)) < EPSILON_LOWP)\n#define IS_POINT_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_POINT)) < EPSILON_LOWP)\n#define IS_RANGED_DIRECTIONAL_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_RANGED_DIRECTIONAL)) < EPSILON_LOWP)\n#define TONE_MAPPING_ACES 0\n#define TONE_MAPPING_LINEAR 1\n#define SURFACES_MAX_TRANSMIT_DEPTH_VALUE 999999.0\n#ifndef CC_SURFACES_DEBUG_VIEW_SINGLE\n  #define CC_SURFACES_DEBUG_VIEW_SINGLE 1\n#endif\n#ifndef CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC\n  #define CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC 2\n#endif\n#if defined(CC_USE_METAL) || defined(CC_USE_WGPU)\n#define CC_HANDLE_SAMPLE_NDC_FLIP_STATIC(y) y = -y\n#else\n#define CC_HANDLE_SAMPLE_NDC_FLIP_STATIC(y)\n#endif\nfloat GetCameraDepthRH(float depthHS, mat4 matProj)\n{\n    return -matProj[3][2] / (depthHS + matProj[2][2]);\n}\nfloat GetCameraDepthRH(float depthHS, float matProj32, float matProj22)\n{\n    return -matProj32 / (depthHS + matProj22);\n}\n#if CC_USE_FOG != 4\n#endif\nlayout(std140) uniform hbaoUBO {\n    vec4 uvDepthToEyePosParams;\n    vec4 radiusParam;\n    vec4 miscParam;\n    vec4 randomTexSize;\n    vec4 blurParam;\n};\n#define NUM_STEPS 8.0\n#define NUM_STEPS_LOOP NUM_STEPS*2.0\n#define INV_NUM_DIRECTIONS 0.125\n#define NUM_DIRECTIONS 8\n#define CONSTANT_2PI 6.2831852\n#define g_AOResolution cc_screenSize.xy\n#define g_InvAOResolution cc_screenSize.zw\n#define g_UVToViewA uvDepthToEyePosParams.xy\n#define g_UVToViewB uvDepthToEyePosParams.zw\n#define g_R radiusParam.x\n#define g_R2 radiusParam.y\n#define g_NegInvR2 radiusParam.z\n#define g_MaxRadiusPixels radiusParam.w\n#define g_FocalLen miscParam.xy\n#define g_TanAngleBias miscParam.z\n#define g_Strength miscParam.w\nuniform sampler2D DepthTex;\nuniform sampler2D RandomTex;\nvec2 fixUV(vec2 uv)\n{\n  return saturate(uv);\n}\nfloat GetLinearDepth(vec2 uv) {\n  float depthHS = 0.0;\n  #if defined(CC_USE_WGPU)\n      depthHS = textureLod(DepthTex, fixUV(uv), 0.0).r * 2.0 - 1.0;\n  #else\n      depthHS = texture(DepthTex, fixUV(uv)).r * 2.0 - 1.0;\n  #endif\n  return -GetCameraDepthRH(depthHS, cc_matProj);\n}\n#define KERNEL_FALLOFF 3.0f\n#define KERNEL_RADIUS 8\n#define HALF_KERNEL_RADIUS 4\n#define g_InvFullResolution cc_screenSize.zw\n#define g_BlurFallOff blurParam.x\n#define g_BlurDepthThreshold blurParam.y\n#define g_BlurSimpleRadius blurParam.z\nuniform sampler2D AOTexNearest;\n#define AOTexBilinear AOTexNearest\n#define GetLinearDepthBilinear GetLinearDepth\nfloat CrossBilateralWeight(float r, float d, float d0, float blurFalloff, float blurDepthThreshold)\n{\n  return exp2(-r*r*blurFalloff) * (abs(d - d0) < blurDepthThreshold ? 1.0 : 0.0);\n}\nvec2 SumWeightedAO(int i, float centerCameraDepth, vec2 centerUV, vec2 sampleDir, float blurFalloff, float blurDepthThreshold)\n{\n  float r = 2.0 * float(i) + 0.5;\n  vec2 sampleUV = centerUV + r * sampleDir * g_InvFullResolution;\n  float d = GetLinearDepthBilinear(sampleUV);\n  float sampledAO = texture(AOTexNearest, fixUV(sampleUV)).x;\n  float w = CrossBilateralWeight(r, d, centerCameraDepth, blurFalloff, blurDepthThreshold);\n  return vec2(w * sampledAO, w);\n}\nfloat BlurCore(vec2 uv, vec2 sampleDir)\n{\n  float ao = texture(AOTexNearest, uv).x;\n  float weight = 1.0;\n  float centerCameraDepth = GetLinearDepth(uv);\n  for(int i = -HALF_KERNEL_RADIUS; i <= HALF_KERNEL_RADIUS; i++)\n  {\n    vec2 aoAndW = SumWeightedAO(i, centerCameraDepth, uv, sampleDir, g_BlurFallOff, g_BlurDepthThreshold);\n    ao += aoAndW.x;\n    weight += aoAndW.y;\n  }\n  return ao / weight;\n}\n#define g_AOSaturation blurParam.w\nin vec2 v_uv;\nlayout(location = 0) out vec4 fragColor;\nvoid main () {\n  float ao = BlurCore(v_uv, vec2(1.0, 0.0));\n  fragColor = vec4(ao, ao, ao, 1.0);\n}"}, "glsl1": {"vert": "\nprecision highp float;\n#define QUATER_PI         0.78539816340\n#define HALF_PI           1.57079632679\n#define PI                3.14159265359\n#define PI2               6.28318530718\n#define PI4               12.5663706144\n#define INV_QUATER_PI     1.27323954474\n#define INV_HALF_PI       0.63661977237\n#define INV_PI            0.31830988618\n#define INV_PI2           0.15915494309\n#define INV_PI4           0.07957747155\n#define EPSILON           1e-6\n#define EPSILON_LOWP      1e-4\n#define LOG2              1.442695\n#define EXP_VALUE         2.71828183\n#define FP_MAX            65504.0\n#define FP_SCALE          0.0009765625\n#define FP_SCALE_INV      1024.0\n#define GRAY_VECTOR       vec3(0.299, 0.587, 0.114)\n#define LIGHT_MAP_TYPE_DISABLED 0\n#define LIGHT_MAP_TYPE_ALL_IN_ONE 1\n#define LIGHT_MAP_TYPE_INDIRECT_OCCLUSION 2\n#define REFLECTION_PROBE_TYPE_NONE 0\n#define REFLECTION_PROBE_TYPE_CUBE 1\n#define REFLECTION_PROBE_TYPE_PLANAR 2\n#define REFLECTION_PROBE_TYPE_BLEND 3\n#define REFLECTION_PROBE_TYPE_BLEND_AND_SKYBOX 4\n#define LIGHT_TYPE_DIRECTIONAL 0.0\n#define LIGHT_TYPE_SPHERE 1.0\n#define LIGHT_TYPE_SPOT 2.0\n#define LIGHT_TYPE_POINT 3.0\n#define LIGHT_TYPE_RANGED_DIRECTIONAL 4.0\n#define IS_DIRECTIONAL_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_DIRECTIONAL)) < EPSILON_LOWP)\n#define IS_SPHERE_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_SPHERE)) < EPSILON_LOWP)\n#define IS_SPOT_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_SPOT)) < EPSILON_LOWP)\n#define IS_POINT_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_POINT)) < EPSILON_LOWP)\n#define IS_RANGED_DIRECTIONAL_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_RANGED_DIRECTIONAL)) < EPSILON_LOWP)\n#define TONE_MAPPING_ACES 0\n#define TONE_MAPPING_LINEAR 1\n#define SURFACES_MAX_TRANSMIT_DEPTH_VALUE 999999.0\n#ifndef CC_SURFACES_DEBUG_VIEW_SINGLE\n  #define CC_SURFACES_DEBUG_VIEW_SINGLE 1\n#endif\n#ifndef CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC\n  #define CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC 2\n#endif\nstruct StandardVertInput {\n  highp vec4 position;\n  vec3 normal;\n  vec4 tangent;\n};\nattribute vec3 a_position;\nattribute vec3 a_normal;\nattribute vec2 a_texCoord;\nattribute vec4 a_tangent;\n#if CC_USE_SKINNING\n    attribute vec4 a_joints;\n  attribute vec4 a_weights;\n#endif\n#if USE_INSTANCING\n  #if CC_USE_BAKED_ANIMATION\n    attribute highp vec4 a_jointAnimInfo;\n  #endif\n  attribute vec4 a_matWorld0;\n  attribute vec4 a_matWorld1;\n  attribute vec4 a_matWorld2;\n  #if CC_USE_LIGHTMAP\n    attribute vec4 a_lightingMapUVParam;\n  #endif\n  #if CC_USE_REFLECTION_PROBE || CC_RECEIVE_SHADOW\n    #if CC_RECEIVE_SHADOW\n    #endif\n    attribute vec4 a_localShadowBiasAndProbeId;\n  #endif\n  #if CC_USE_REFLECTION_PROBE\n    attribute vec4 a_reflectionProbeData;\n  #endif\n  #if CC_USE_LIGHT_PROBE\n    attribute vec4 a_sh_linear_const_r;\n    attribute vec4 a_sh_linear_const_g;\n    attribute vec4 a_sh_linear_const_b;\n  #endif\n#endif\n#if CC_USE_MORPH\n    attribute float a_vertexId;\n#endif\nuniform highp vec4 cc_cameraPos;\nvarying vec2 v_uv;\nvoid main () {\n  StandardVertInput In;\n    In.position = vec4(a_position, 1.0);\n    In.normal = a_normal;\n    In.tangent = a_tangent;\n  In.position.xy = cc_cameraPos.w == 0.0 ? vec2(In.position.xy.x, -In.position.xy.y) : In.position.xy;\n  gl_Position = In.position;\n  v_uv = a_texCoord;\n}", "frag": "\nprecision highp float;\nprecision highp float;\nuniform mediump vec4 cc_screenSize;\nuniform highp mat4 cc_matProj;\n#define QUATER_PI         0.78539816340\n#define HALF_PI           1.57079632679\n#define PI                3.14159265359\n#define PI2               6.28318530718\n#define PI4               12.5663706144\n#define INV_QUATER_PI     1.27323954474\n#define INV_HALF_PI       0.63661977237\n#define INV_PI            0.31830988618\n#define INV_PI2           0.15915494309\n#define INV_PI4           0.07957747155\n#define EPSILON           1e-6\n#define EPSILON_LOWP      1e-4\n#define LOG2              1.442695\n#define EXP_VALUE         2.71828183\n#define FP_MAX            65504.0\n#define FP_SCALE          0.0009765625\n#define FP_SCALE_INV      1024.0\n#define GRAY_VECTOR       vec3(0.299, 0.587, 0.114)\nfloat saturate(float value) { return clamp(value, 0.0, 1.0); }\nvec2 saturate(vec2 value) { return clamp(value, vec2(0.0), vec2(1.0)); }\nvec3 saturate(vec3 value) { return clamp(value, vec3(0.0), vec3(1.0)); }\nvec4 saturate(vec4 value) { return clamp(value, vec4(0.0), vec4(1.0)); }\n#define LIGHT_MAP_TYPE_DISABLED 0\n#define LIGHT_MAP_TYPE_ALL_IN_ONE 1\n#define LIGHT_MAP_TYPE_INDIRECT_OCCLUSION 2\n#define REFLECTION_PROBE_TYPE_NONE 0\n#define REFLECTION_PROBE_TYPE_CUBE 1\n#define REFLECTION_PROBE_TYPE_PLANAR 2\n#define REFLECTION_PROBE_TYPE_BLEND 3\n#define REFLECTION_PROBE_TYPE_BLEND_AND_SKYBOX 4\n#define LIGHT_TYPE_DIRECTIONAL 0.0\n#define LIGHT_TYPE_SPHERE 1.0\n#define LIGHT_TYPE_SPOT 2.0\n#define LIGHT_TYPE_POINT 3.0\n#define LIGHT_TYPE_RANGED_DIRECTIONAL 4.0\n#define IS_DIRECTIONAL_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_DIRECTIONAL)) < EPSILON_LOWP)\n#define IS_SPHERE_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_SPHERE)) < EPSILON_LOWP)\n#define IS_SPOT_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_SPOT)) < EPSILON_LOWP)\n#define IS_POINT_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_POINT)) < EPSILON_LOWP)\n#define IS_RANGED_DIRECTIONAL_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_RANGED_DIRECTIONAL)) < EPSILON_LOWP)\n#define TONE_MAPPING_ACES 0\n#define TONE_MAPPING_LINEAR 1\n#define SURFACES_MAX_TRANSMIT_DEPTH_VALUE 999999.0\n#ifndef CC_SURFACES_DEBUG_VIEW_SINGLE\n  #define CC_SURFACES_DEBUG_VIEW_SINGLE 1\n#endif\n#ifndef CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC\n  #define CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC 2\n#endif\n#if defined(CC_USE_METAL) || defined(CC_USE_WGPU)\n#define CC_HANDLE_SAMPLE_NDC_FLIP_STATIC(y) y = -y\n#else\n#define CC_HANDLE_SAMPLE_NDC_FLIP_STATIC(y)\n#endif\nfloat GetCameraDepthRH(float depthHS, mat4 matProj)\n{\n    return -matProj[3][2] / (depthHS + matProj[2][2]);\n}\nfloat GetCameraDepthRH(float depthHS, float matProj32, float matProj22)\n{\n    return -matProj32 / (depthHS + matProj22);\n}\n#if CC_USE_FOG != 4\n#endif\n       uniform vec4 uvDepthToEyePosParams;\n       uniform vec4 radiusParam;\n       uniform vec4 miscParam;\n       uniform vec4 blurParam;\n#define NUM_STEPS 8.0\n#define NUM_STEPS_LOOP NUM_STEPS*2.0\n#define INV_NUM_DIRECTIONS 0.125\n#define NUM_DIRECTIONS 8\n#define CONSTANT_2PI 6.2831852\n#define g_AOResolution cc_screenSize.xy\n#define g_InvAOResolution cc_screenSize.zw\n#define g_UVToViewA uvDepthToEyePosParams.xy\n#define g_UVToViewB uvDepthToEyePosParams.zw\n#define g_R radiusParam.x\n#define g_R2 radiusParam.y\n#define g_NegInvR2 radiusParam.z\n#define g_MaxRadiusPixels radiusParam.w\n#define g_FocalLen miscParam.xy\n#define g_TanAngleBias miscParam.z\n#define g_Strength miscParam.w\nuniform sampler2D DepthTex;\nuniform sampler2D RandomTex;\nvec2 fixUV(vec2 uv)\n{\n  return saturate(uv);\n}\nfloat GetLinearDepth(vec2 uv) {\n  float depthHS = 0.0;\n  #if defined(CC_USE_WGPU)\n      depthHS = texture2DLod(DepthTex, fixUV(uv), 0.0).r * 2.0 - 1.0;\n  #else\n      depthHS = texture2D(DepthTex, fixUV(uv)).r * 2.0 - 1.0;\n  #endif\n  return -GetCameraDepthRH(depthHS, cc_matProj);\n}\n#define KERNEL_FALLOFF 3.0f\n#define KERNEL_RADIUS 8\n#define HALF_KERNEL_RADIUS 4\n#define g_InvFullResolution cc_screenSize.zw\n#define g_BlurFallOff blurParam.x\n#define g_BlurDepthThreshold blurParam.y\n#define g_BlurSimpleRadius blurParam.z\nuniform sampler2D AOTexNearest;\n#define AOTexBilinear AOTexNearest\n#define GetLinearDepthBilinear GetLinearDepth\nfloat CrossBilateralWeight(float r, float d, float d0, float blurFalloff, float blurDepthThreshold)\n{\n  return exp2(-r*r*blurFalloff) * (abs(d - d0) < blurDepthThreshold ? 1.0 : 0.0);\n}\nvec2 SumWeightedAO(int i, float centerCameraDepth, vec2 centerUV, vec2 sampleDir, float blurFalloff, float blurDepthThreshold)\n{\n  float r = 2.0 * float(i) + 0.5;\n  vec2 sampleUV = centerUV + r * sampleDir * g_InvFullResolution;\n  float d = GetLinearDepthBilinear(sampleUV);\n  float sampledAO = texture2D(AOTexNearest, fixUV(sampleUV)).x;\n  float w = CrossBilateralWeight(r, d, centerCameraDepth, blurFalloff, blurDepthThreshold);\n  return vec2(w * sampledAO, w);\n}\nfloat BlurCore(vec2 uv, vec2 sampleDir)\n{\n  float ao = texture2D(AOTexNearest, uv).x;\n  float weight = 1.0;\n  float centerCameraDepth = GetLinearDepth(uv);\n  for(int i = -HALF_KERNEL_RADIUS; i <= HALF_KERNEL_RADIUS; i++)\n  {\n    vec2 aoAndW = SumWeightedAO(i, centerCameraDepth, uv, sampleDir, g_BlurFallOff, g_BlurDepthThreshold);\n    ao += aoAndW.x;\n    weight += aoAndW.y;\n  }\n  return ao / weight;\n}\n#define g_AOSaturation blurParam.w\nvarying vec2 v_uv;\nvoid main () {\n  float ao = BlurCore(v_uv, vec2(1.0, 0.0));\n  gl_FragColor = vec4(ao, ao, ao, 1.0);\n}"}, "builtins": {"globals": {"blocks": [{"name": "CCGlobal", "defines": []}, {"name": "CCCamera", "defines": []}], "samplerTextures": [], "buffers": [], "images": []}, "locals": {"blocks": [], "samplerTextures": [], "buffers": [], "images": []}, "statistics": {"CC_EFFECT_USED_VERTEX_UNIFORM_VECTORS": 42, "CC_EFFECT_USED_FRAGMENT_UNIFORM_VECTORS": 47}}, "defines": [{"name": "USE_INSTANCING", "type": "boolean", "defines": [], "editor": {"elevated": true}}, {"name": "CC_USE_SKINNING", "type": "boolean", "defines": []}, {"name": "CC_USE_BAKED_ANIMATION", "type": "boolean", "defines": ["USE_INSTANCING"]}, {"name": "CC_USE_LIGHTMAP", "type": "boolean", "defines": ["USE_INSTANCING"]}, {"name": "CC_USE_REFLECTION_PROBE", "type": "boolean", "defines": ["USE_INSTANCING"]}, {"name": "CC_RECEIVE_SHADOW", "type": "boolean", "defines": ["USE_INSTANCING"]}, {"name": "CC_USE_LIGHT_PROBE", "type": "boolean", "defines": ["USE_INSTANCING"]}, {"name": "CC_USE_MORPH", "type": "boolean", "defines": []}, {"name": "CC_USE_FOG", "type": "number", "defines": [], "range": [0, 4]}], "name": "pipeline/post-process/hbao|hbao-vs|blurx-fs"}, {"blocks": [{"name": "hbaoUBO", "members": [{"name": "uvDepthToEyePosParams", "type": 16, "count": 1}, {"name": "radiusParam", "type": 16, "count": 1}, {"name": "miscParam", "type": 16, "count": 1}, {"name": "randomTexSize", "type": 16, "count": 1}, {"name": "blurParam", "type": 16, "count": 1}], "defines": [], "stageFlags": 16, "binding": 0}], "samplerTextures": [{"name": "RandomTex", "type": 28, "count": 1, "defines": [], "stageFlags": 16, "sampleType": 0, "binding": 2}], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": [], "attributes": [{"name": "a_position", "defines": [], "format": 32, "location": 0}, {"name": "a_normal", "defines": [], "format": 32, "location": 1}, {"name": "a_texCoord", "defines": [], "format": 21, "location": 2}, {"name": "a_tangent", "defines": [], "format": 44, "location": 3}, {"name": "a_joints", "defines": ["CC_USE_SKINNING"], "location": 4}, {"name": "a_weights", "defines": ["CC_USE_SKINNING"], "format": 44, "location": 5}, {"name": "a_jointAnimInfo", "defines": ["USE_INSTANCING", "CC_USE_BAKED_ANIMATION"], "format": 44, "isInstanced": true, "location": 6}, {"name": "a_matWorld0", "defines": ["USE_INSTANCING"], "format": 44, "isInstanced": true, "location": 7}, {"name": "a_matWorld1", "defines": ["USE_INSTANCING"], "format": 44, "isInstanced": true, "location": 8}, {"name": "a_matWorld2", "defines": ["USE_INSTANCING"], "format": 44, "isInstanced": true, "location": 9}, {"name": "a_lightingMapUVParam", "defines": ["USE_INSTANCING", "CC_USE_LIGHTMAP"], "format": 44, "isInstanced": true, "location": 10}, {"name": "a_localShadowBiasAndProbeId", "defines": ["USE_INSTANCING"], "format": 44, "isInstanced": true, "location": 11}, {"name": "a_reflectionProbeData", "defines": ["USE_INSTANCING", "CC_USE_REFLECTION_PROBE"], "format": 44, "isInstanced": true, "location": 12}, {"name": "a_sh_linear_const_r", "defines": ["USE_INSTANCING", "CC_USE_LIGHT_PROBE"], "format": 44, "isInstanced": true, "location": 13}, {"name": "a_sh_linear_const_g", "defines": ["USE_INSTANCING", "CC_USE_LIGHT_PROBE"], "format": 44, "isInstanced": true, "location": 14}, {"name": "a_sh_linear_const_b", "defines": ["USE_INSTANCING", "CC_USE_LIGHT_PROBE"], "format": 44, "isInstanced": true, "location": 15}, {"name": "a_vertexId", "defines": ["CC_USE_MORPH"], "format": 11, "location": 16}], "varyings": [{"name": "v_uv", "type": 14, "count": 1, "defines": [], "stageFlags": 17, "location": 0}], "fragColors": [{"name": "fragColor", "typename": "vec4", "type": 16, "count": 1, "defines": [], "stageFlags": 16, "location": 0}], "descriptors": [{"rate": 0, "blocks": [], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}, {"rate": 1, "blocks": [{"name": "hbaoUBO", "members": [{"name": "uvDepthToEyePosParams", "type": 16, "count": 1}, {"name": "radiusParam", "type": 16, "count": 1}, {"name": "miscParam", "type": 16, "count": 1}, {"name": "randomTexSize", "type": 16, "count": 1}, {"name": "blurParam", "type": 16, "count": 1}], "defines": [], "stageFlags": 16, "binding": 0}], "samplerTextures": [{"name": "RandomTex", "type": 28, "count": 1, "defines": [], "stageFlags": 16, "sampleType": 0, "binding": 2}], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}, {"rate": 2, "blocks": [], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}, {"rate": 3, "blocks": [{"tags": {"builtin": "global"}, "name": "CCGlobal", "members": [{"name": "cc_time", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_screenSize", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_nativeSize", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_probeInfo", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_debug_view_mode", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}], "defines": [], "stageFlags": 17}, {"tags": {"builtin": "global"}, "name": "CCCamera", "members": [{"name": "cc_matView", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matViewInv", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matProj", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matProjInv", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matViewProj", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matViewProjInv", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_cameraPos", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_surfaceTransform", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_screenScale", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_exposure", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_mainLitDir", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_mainLitColor", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_ambientSky", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_ambientGround", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_fogColor", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_fogBase", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_fogAdd", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_nearFar", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_viewPort", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}], "defines": [], "stageFlags": 17}], "samplerTextures": [{"name": "DepthTex", "type": 28, "count": 1, "defines": [], "stageFlags": 16, "sampleType": 0, "binding": 1}, {"name": "AOTexNearest", "type": 28, "count": 1, "defines": [], "stageFlags": 16, "sampleType": 0, "binding": 3}], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}], "hash": 1325701544, "glsl4": {"vert": "\nprecision highp float;\n#define QUATER_PI         0.78539816340\n#define HALF_PI           1.57079632679\n#define PI                3.14159265359\n#define PI2               6.28318530718\n#define PI4               12.5663706144\n#define INV_QUATER_PI     1.27323954474\n#define INV_HALF_PI       0.63661977237\n#define INV_PI            0.31830988618\n#define INV_PI2           0.15915494309\n#define INV_PI4           0.07957747155\n#define EPSILON           1e-6\n#define EPSILON_LOWP      1e-4\n#define LOG2              1.442695\n#define EXP_VALUE         2.71828183\n#define FP_MAX            65504.0\n#define FP_SCALE          0.0009765625\n#define FP_SCALE_INV      1024.0\n#define GRAY_VECTOR       vec3(0.299, 0.587, 0.114)\n#define LIGHT_MAP_TYPE_DISABLED 0\n#define LIGHT_MAP_TYPE_ALL_IN_ONE 1\n#define LIGHT_MAP_TYPE_INDIRECT_OCCLUSION 2\n#define REFLECTION_PROBE_TYPE_NONE 0\n#define REFLECTION_PROBE_TYPE_CUBE 1\n#define REFLECTION_PROBE_TYPE_PLANAR 2\n#define REFLECTION_PROBE_TYPE_BLEND 3\n#define REFLECTION_PROBE_TYPE_BLEND_AND_SKYBOX 4\n#define LIGHT_TYPE_DIRECTIONAL 0.0\n#define LIGHT_TYPE_SPHERE 1.0\n#define LIGHT_TYPE_SPOT 2.0\n#define LIGHT_TYPE_POINT 3.0\n#define LIGHT_TYPE_RANGED_DIRECTIONAL 4.0\n#define IS_DIRECTIONAL_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_DIRECTIONAL)) < EPSILON_LOWP)\n#define IS_SPHERE_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_SPHERE)) < EPSILON_LOWP)\n#define IS_SPOT_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_SPOT)) < EPSILON_LOWP)\n#define IS_POINT_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_POINT)) < EPSILON_LOWP)\n#define IS_RANGED_DIRECTIONAL_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_RANGED_DIRECTIONAL)) < EPSILON_LOWP)\n#define TONE_MAPPING_ACES 0\n#define TONE_MAPPING_LINEAR 1\n#define SURFACES_MAX_TRANSMIT_DEPTH_VALUE 999999.0\n#ifndef CC_SURFACES_DEBUG_VIEW_SINGLE\n  #define CC_SURFACES_DEBUG_VIEW_SINGLE 1\n#endif\n#ifndef CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC\n  #define CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC 2\n#endif\nstruct StandardVertInput {\n  highp vec4 position;\n  vec3 normal;\n  vec4 tangent;\n};\nlayout(location = 0) in vec3 a_position;\nlayout(location = 1) in vec3 a_normal;\nlayout(location = 2) in vec2 a_texCoord;\nlayout(location = 3) in vec4 a_tangent;\n#if CC_USE_SKINNING\n    layout(location = 4) in u32vec4 a_joints;\n  layout(location = 5) in vec4 a_weights;\n#endif\n#if USE_INSTANCING\n  #if CC_USE_BAKED_ANIMATION\n    layout(location = 6) in highp vec4 a_jointAnimInfo;\n  #endif\n  layout(location = 7) in vec4 a_matWorld0;\n  layout(location = 8) in vec4 a_matWorld1;\n  layout(location = 9) in vec4 a_matWorld2;\n  #if CC_USE_LIGHTMAP\n    layout(location = 10) in vec4 a_lightingMapUVParam;\n  #endif\n  #if CC_USE_REFLECTION_PROBE || CC_RECEIVE_SHADOW\n    #if CC_RECEIVE_SHADOW\n    #endif\n    layout(location = 11) in vec4 a_localShadowBiasAndProbeId;\n  #endif\n  #if CC_USE_REFLECTION_PROBE\n    layout(location = 12) in vec4 a_reflectionProbeData;\n  #endif\n  #if CC_USE_LIGHT_PROBE\n    layout(location = 13) in vec4 a_sh_linear_const_r;\n    layout(location = 14) in vec4 a_sh_linear_const_g;\n    layout(location = 15) in vec4 a_sh_linear_const_b;\n  #endif\n#endif\n#if CC_USE_MORPH\n#endif\nlayout(set = 0, binding = 0) uniform CCGlobal {\n  highp   vec4 cc_time;\n  mediump vec4 cc_screenSize;\n  mediump vec4 cc_nativeSize;\n  mediump vec4 cc_probeInfo;\n  mediump vec4 cc_debug_view_mode;\n};\nlayout(set = 0, binding = 1) uniform CCCamera {\n  highp   mat4 cc_matView;\n  highp   mat4 cc_matViewInv;\n  highp   mat4 cc_matProj;\n  highp   mat4 cc_matProjInv;\n  highp   mat4 cc_matViewProj;\n  highp   mat4 cc_matViewProjInv;\n  highp   vec4 cc_cameraPos;\n  mediump vec4 cc_surfaceTransform;\n  mediump vec4 cc_screenScale;\n  mediump vec4 cc_exposure;\n  mediump vec4 cc_mainLitDir;\n  mediump vec4 cc_mainLitColor;\n  mediump vec4 cc_ambientSky;\n  mediump vec4 cc_ambientGround;\n  mediump vec4 cc_fogColor;\n  mediump vec4 cc_fogBase;\n  mediump vec4 cc_fogAdd;\n  mediump vec4 cc_nearFar;\n  mediump vec4 cc_viewPort;\n};\nlayout(location = 0) out vec2 v_uv;\nvoid main () {\n  StandardVertInput In;\n    In.position = vec4(a_position, 1.0);\n    In.normal = a_normal;\n    In.tangent = a_tangent;\n  In.position.xy = cc_cameraPos.w == 0.0 ? vec2(In.position.xy.x, -In.position.xy.y) : In.position.xy;\n  gl_Position = In.position;\n  v_uv = a_texCoord;\n}", "frag": "\nprecision highp float;\nprecision highp float;\nlayout(set = 0, binding = 0) uniform CCGlobal {\n  highp   vec4 cc_time;\n  mediump vec4 cc_screenSize;\n  mediump vec4 cc_nativeSize;\n  mediump vec4 cc_probeInfo;\n  mediump vec4 cc_debug_view_mode;\n};\nlayout(set = 0, binding = 1) uniform CCCamera {\n  highp   mat4 cc_matView;\n  highp   mat4 cc_matViewInv;\n  highp   mat4 cc_matProj;\n  highp   mat4 cc_matProjInv;\n  highp   mat4 cc_matViewProj;\n  highp   mat4 cc_matViewProjInv;\n  highp   vec4 cc_cameraPos;\n  mediump vec4 cc_surfaceTransform;\n  mediump vec4 cc_screenScale;\n  mediump vec4 cc_exposure;\n  mediump vec4 cc_mainLitDir;\n  mediump vec4 cc_mainLitColor;\n  mediump vec4 cc_ambientSky;\n  mediump vec4 cc_ambientGround;\n  mediump vec4 cc_fogColor;\n  mediump vec4 cc_fogBase;\n  mediump vec4 cc_fogAdd;\n  mediump vec4 cc_nearFar;\n  mediump vec4 cc_viewPort;\n};\n#define QUATER_PI         0.78539816340\n#define HALF_PI           1.57079632679\n#define PI                3.14159265359\n#define PI2               6.28318530718\n#define PI4               12.5663706144\n#define INV_QUATER_PI     1.27323954474\n#define INV_HALF_PI       0.63661977237\n#define INV_PI            0.31830988618\n#define INV_PI2           0.15915494309\n#define INV_PI4           0.07957747155\n#define EPSILON           1e-6\n#define EPSILON_LOWP      1e-4\n#define LOG2              1.442695\n#define EXP_VALUE         2.71828183\n#define FP_MAX            65504.0\n#define FP_SCALE          0.0009765625\n#define FP_SCALE_INV      1024.0\n#define GRAY_VECTOR       vec3(0.299, 0.587, 0.114)\nfloat saturate(float value) { return clamp(value, 0.0, 1.0); }\nvec2 saturate(vec2 value) { return clamp(value, vec2(0.0), vec2(1.0)); }\nvec3 saturate(vec3 value) { return clamp(value, vec3(0.0), vec3(1.0)); }\nvec4 saturate(vec4 value) { return clamp(value, vec4(0.0), vec4(1.0)); }\n#define LIGHT_MAP_TYPE_DISABLED 0\n#define LIGHT_MAP_TYPE_ALL_IN_ONE 1\n#define LIGHT_MAP_TYPE_INDIRECT_OCCLUSION 2\n#define REFLECTION_PROBE_TYPE_NONE 0\n#define REFLECTION_PROBE_TYPE_CUBE 1\n#define REFLECTION_PROBE_TYPE_PLANAR 2\n#define REFLECTION_PROBE_TYPE_BLEND 3\n#define REFLECTION_PROBE_TYPE_BLEND_AND_SKYBOX 4\n#define LIGHT_TYPE_DIRECTIONAL 0.0\n#define LIGHT_TYPE_SPHERE 1.0\n#define LIGHT_TYPE_SPOT 2.0\n#define LIGHT_TYPE_POINT 3.0\n#define LIGHT_TYPE_RANGED_DIRECTIONAL 4.0\n#define IS_DIRECTIONAL_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_DIRECTIONAL)) < EPSILON_LOWP)\n#define IS_SPHERE_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_SPHERE)) < EPSILON_LOWP)\n#define IS_SPOT_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_SPOT)) < EPSILON_LOWP)\n#define IS_POINT_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_POINT)) < EPSILON_LOWP)\n#define IS_RANGED_DIRECTIONAL_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_RANGED_DIRECTIONAL)) < EPSILON_LOWP)\n#define TONE_MAPPING_ACES 0\n#define TONE_MAPPING_LINEAR 1\n#define SURFACES_MAX_TRANSMIT_DEPTH_VALUE 999999.0\n#ifndef CC_SURFACES_DEBUG_VIEW_SINGLE\n  #define CC_SURFACES_DEBUG_VIEW_SINGLE 1\n#endif\n#ifndef CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC\n  #define CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC 2\n#endif\n#if defined(CC_USE_METAL) || defined(CC_USE_WGPU)\n#define CC_HANDLE_SAMPLE_NDC_FLIP_STATIC(y) y = -y\n#else\n#define CC_HANDLE_SAMPLE_NDC_FLIP_STATIC(y)\n#endif\nfloat GetCameraDepthRH(float depthHS, mat4 matProj)\n{\n    return -matProj[3][2] / (depthHS + matProj[2][2]);\n}\nfloat GetCameraDepthRH(float depthHS, float matProj32, float matProj22)\n{\n    return -matProj32 / (depthHS + matProj22);\n}\n#if CC_USE_FOG != 4\n#endif\nlayout(set = 1, binding = 0) uniform hbaoUBO {\n    vec4 uvDepthToEyePosParams;\n    vec4 radiusParam;\n    vec4 miscParam;\n    vec4 randomTexSize;\n    vec4 blurParam;\n};\n#define NUM_STEPS 8.0\n#define NUM_STEPS_LOOP NUM_STEPS*2.0\n#define INV_NUM_DIRECTIONS 0.125\n#define NUM_DIRECTIONS 8\n#define CONSTANT_2PI 6.2831852\n#define g_AOResolution cc_screenSize.xy\n#define g_InvAOResolution cc_screenSize.zw\n#define g_UVToViewA uvDepthToEyePosParams.xy\n#define g_UVToViewB uvDepthToEyePosParams.zw\n#define g_R radiusParam.x\n#define g_R2 radiusParam.y\n#define g_NegInvR2 radiusParam.z\n#define g_MaxRadiusPixels radiusParam.w\n#define g_FocalLen miscParam.xy\n#define g_TanAngleBias miscParam.z\n#define g_Strength miscParam.w\nlayout(set = 1, binding = 1) uniform sampler2D DepthTex;\nlayout(set = 1, binding = 2) uniform sampler2D RandomTex;\nvec2 fixUV(vec2 uv)\n{\n  return saturate(uv);\n}\nfloat GetLinearDepth(vec2 uv) {\n  float depthHS = 0.0;\n  #if defined(CC_USE_WGPU)\n      depthHS = textureLod(DepthTex, fixUV(uv), 0.0).r * 2.0 - 1.0;\n  #else\n      depthHS = texture(DepthTex, fixUV(uv)).r * 2.0 - 1.0;\n  #endif\n  return -GetCameraDepthRH(depthHS, cc_matProj);\n}\n#define KERNEL_FALLOFF 3.0f\n#define KERNEL_RADIUS 8\n#define HALF_KERNEL_RADIUS 4\n#define g_InvFullResolution cc_screenSize.zw\n#define g_BlurFallOff blurParam.x\n#define g_BlurDepthThreshold blurParam.y\n#define g_BlurSimpleRadius blurParam.z\nlayout(set = 1, binding = 3) uniform sampler2D AOTexNearest;\n#define AOTexBilinear AOTexNearest\n#define GetLinearDepthBilinear GetLinearDepth\nfloat CrossBilateralWeight(float r, float d, float d0, float blurFalloff, float blurDepthThreshold)\n{\n  return exp2(-r*r*blurFalloff) * (abs(d - d0) < blurDepthThreshold ? 1.0 : 0.0);\n}\nvec2 SumWeightedAO(int i, float centerCameraDepth, vec2 centerUV, vec2 sampleDir, float blurFalloff, float blurDepthThreshold)\n{\n  float r = 2.0 * float(i) + 0.5;\n  vec2 sampleUV = centerUV + r * sampleDir * g_InvFullResolution;\n  float d = GetLinearDepthBilinear(sampleUV);\n  float sampledAO = texture(AOTexNearest, fixUV(sampleUV)).x;\n  float w = CrossBilateralWeight(r, d, centerCameraDepth, blurFalloff, blurDepthThreshold);\n  return vec2(w * sampledAO, w);\n}\nfloat BlurCore(vec2 uv, vec2 sampleDir)\n{\n  float ao = texture(AOTexNearest, uv).x;\n  float weight = 1.0;\n  float centerCameraDepth = GetLinearDepth(uv);\n  for(int i = -HALF_KERNEL_RADIUS; i <= HALF_KERNEL_RADIUS; i++)\n  {\n    vec2 aoAndW = SumWeightedAO(i, centerCameraDepth, uv, sampleDir, g_BlurFallOff, g_BlurDepthThreshold);\n    ao += aoAndW.x;\n    weight += aoAndW.y;\n  }\n  return ao / weight;\n}\n#define g_AOSaturation blurParam.w\nlayout(location = 0) in vec2 v_uv;\nlayout(location = 0) out vec4 fragColor;\nvoid main () {\n  float ao = BlurCore(v_uv, vec2(0.0, 1.0));\n  fragColor = vec4(ao, ao, ao, 1.0);\n}"}, "glsl3": {"vert": "\nprecision highp float;\n#define QUATER_PI         0.78539816340\n#define HALF_PI           1.57079632679\n#define PI                3.14159265359\n#define PI2               6.28318530718\n#define PI4               12.5663706144\n#define INV_QUATER_PI     1.27323954474\n#define INV_HALF_PI       0.63661977237\n#define INV_PI            0.31830988618\n#define INV_PI2           0.15915494309\n#define INV_PI4           0.07957747155\n#define EPSILON           1e-6\n#define EPSILON_LOWP      1e-4\n#define LOG2              1.442695\n#define EXP_VALUE         2.71828183\n#define FP_MAX            65504.0\n#define FP_SCALE          0.0009765625\n#define FP_SCALE_INV      1024.0\n#define GRAY_VECTOR       vec3(0.299, 0.587, 0.114)\n#define LIGHT_MAP_TYPE_DISABLED 0\n#define LIGHT_MAP_TYPE_ALL_IN_ONE 1\n#define LIGHT_MAP_TYPE_INDIRECT_OCCLUSION 2\n#define REFLECTION_PROBE_TYPE_NONE 0\n#define REFLECTION_PROBE_TYPE_CUBE 1\n#define REFLECTION_PROBE_TYPE_PLANAR 2\n#define REFLECTION_PROBE_TYPE_BLEND 3\n#define REFLECTION_PROBE_TYPE_BLEND_AND_SKYBOX 4\n#define LIGHT_TYPE_DIRECTIONAL 0.0\n#define LIGHT_TYPE_SPHERE 1.0\n#define LIGHT_TYPE_SPOT 2.0\n#define LIGHT_TYPE_POINT 3.0\n#define LIGHT_TYPE_RANGED_DIRECTIONAL 4.0\n#define IS_DIRECTIONAL_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_DIRECTIONAL)) < EPSILON_LOWP)\n#define IS_SPHERE_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_SPHERE)) < EPSILON_LOWP)\n#define IS_SPOT_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_SPOT)) < EPSILON_LOWP)\n#define IS_POINT_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_POINT)) < EPSILON_LOWP)\n#define IS_RANGED_DIRECTIONAL_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_RANGED_DIRECTIONAL)) < EPSILON_LOWP)\n#define TONE_MAPPING_ACES 0\n#define TONE_MAPPING_LINEAR 1\n#define SURFACES_MAX_TRANSMIT_DEPTH_VALUE 999999.0\n#ifndef CC_SURFACES_DEBUG_VIEW_SINGLE\n  #define CC_SURFACES_DEBUG_VIEW_SINGLE 1\n#endif\n#ifndef CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC\n  #define CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC 2\n#endif\nstruct StandardVertInput {\n  highp vec4 position;\n  vec3 normal;\n  vec4 tangent;\n};\nin vec3 a_position;\nin vec3 a_normal;\nin vec2 a_texCoord;\nin vec4 a_tangent;\n#if CC_USE_SKINNING\n    in vec4 a_joints;\n  in vec4 a_weights;\n#endif\n#if USE_INSTANCING\n  #if CC_USE_BAKED_ANIMATION\n    in highp vec4 a_jointAnimInfo;\n  #endif\n  in vec4 a_matWorld0;\n  in vec4 a_matWorld1;\n  in vec4 a_matWorld2;\n  #if CC_USE_LIGHTMAP\n    in vec4 a_lightingMapUVParam;\n  #endif\n  #if CC_USE_REFLECTION_PROBE || CC_RECEIVE_SHADOW\n    #if CC_RECEIVE_SHADOW\n    #endif\n    in vec4 a_localShadowBiasAndProbeId;\n  #endif\n  #if CC_USE_REFLECTION_PROBE\n    in vec4 a_reflectionProbeData;\n  #endif\n  #if CC_USE_LIGHT_PROBE\n    in vec4 a_sh_linear_const_r;\n    in vec4 a_sh_linear_const_g;\n    in vec4 a_sh_linear_const_b;\n  #endif\n#endif\n#if CC_USE_MORPH\n    in float a_vertexId;\n#endif\nlayout(std140) uniform CCGlobal {\n  highp   vec4 cc_time;\n  mediump vec4 cc_screenSize;\n  mediump vec4 cc_nativeSize;\n  mediump vec4 cc_probeInfo;\n  mediump vec4 cc_debug_view_mode;\n};\nlayout(std140) uniform CCCamera {\n  highp   mat4 cc_matView;\n  highp   mat4 cc_matViewInv;\n  highp   mat4 cc_matProj;\n  highp   mat4 cc_matProjInv;\n  highp   mat4 cc_matViewProj;\n  highp   mat4 cc_matViewProjInv;\n  highp   vec4 cc_cameraPos;\n  mediump vec4 cc_surfaceTransform;\n  mediump vec4 cc_screenScale;\n  mediump vec4 cc_exposure;\n  mediump vec4 cc_mainLitDir;\n  mediump vec4 cc_mainLitColor;\n  mediump vec4 cc_ambientSky;\n  mediump vec4 cc_ambientGround;\n  mediump vec4 cc_fogColor;\n  mediump vec4 cc_fogBase;\n  mediump vec4 cc_fogAdd;\n  mediump vec4 cc_nearFar;\n  mediump vec4 cc_viewPort;\n};\nout vec2 v_uv;\nvoid main () {\n  StandardVertInput In;\n    In.position = vec4(a_position, 1.0);\n    In.normal = a_normal;\n    In.tangent = a_tangent;\n  In.position.xy = cc_cameraPos.w == 0.0 ? vec2(In.position.xy.x, -In.position.xy.y) : In.position.xy;\n  gl_Position = In.position;\n  v_uv = a_texCoord;\n}", "frag": "\nprecision highp float;\nprecision highp float;\nlayout(std140) uniform CCGlobal {\n  highp   vec4 cc_time;\n  mediump vec4 cc_screenSize;\n  mediump vec4 cc_nativeSize;\n  mediump vec4 cc_probeInfo;\n  mediump vec4 cc_debug_view_mode;\n};\nlayout(std140) uniform CCCamera {\n  highp   mat4 cc_matView;\n  highp   mat4 cc_matViewInv;\n  highp   mat4 cc_matProj;\n  highp   mat4 cc_matProjInv;\n  highp   mat4 cc_matViewProj;\n  highp   mat4 cc_matViewProjInv;\n  highp   vec4 cc_cameraPos;\n  mediump vec4 cc_surfaceTransform;\n  mediump vec4 cc_screenScale;\n  mediump vec4 cc_exposure;\n  mediump vec4 cc_mainLitDir;\n  mediump vec4 cc_mainLitColor;\n  mediump vec4 cc_ambientSky;\n  mediump vec4 cc_ambientGround;\n  mediump vec4 cc_fogColor;\n  mediump vec4 cc_fogBase;\n  mediump vec4 cc_fogAdd;\n  mediump vec4 cc_nearFar;\n  mediump vec4 cc_viewPort;\n};\n#define QUATER_PI         0.78539816340\n#define HALF_PI           1.57079632679\n#define PI                3.14159265359\n#define PI2               6.28318530718\n#define PI4               12.5663706144\n#define INV_QUATER_PI     1.27323954474\n#define INV_HALF_PI       0.63661977237\n#define INV_PI            0.31830988618\n#define INV_PI2           0.15915494309\n#define INV_PI4           0.07957747155\n#define EPSILON           1e-6\n#define EPSILON_LOWP      1e-4\n#define LOG2              1.442695\n#define EXP_VALUE         2.71828183\n#define FP_MAX            65504.0\n#define FP_SCALE          0.0009765625\n#define FP_SCALE_INV      1024.0\n#define GRAY_VECTOR       vec3(0.299, 0.587, 0.114)\nfloat saturate(float value) { return clamp(value, 0.0, 1.0); }\nvec2 saturate(vec2 value) { return clamp(value, vec2(0.0), vec2(1.0)); }\nvec3 saturate(vec3 value) { return clamp(value, vec3(0.0), vec3(1.0)); }\nvec4 saturate(vec4 value) { return clamp(value, vec4(0.0), vec4(1.0)); }\n#define LIGHT_MAP_TYPE_DISABLED 0\n#define LIGHT_MAP_TYPE_ALL_IN_ONE 1\n#define LIGHT_MAP_TYPE_INDIRECT_OCCLUSION 2\n#define REFLECTION_PROBE_TYPE_NONE 0\n#define REFLECTION_PROBE_TYPE_CUBE 1\n#define REFLECTION_PROBE_TYPE_PLANAR 2\n#define REFLECTION_PROBE_TYPE_BLEND 3\n#define REFLECTION_PROBE_TYPE_BLEND_AND_SKYBOX 4\n#define LIGHT_TYPE_DIRECTIONAL 0.0\n#define LIGHT_TYPE_SPHERE 1.0\n#define LIGHT_TYPE_SPOT 2.0\n#define LIGHT_TYPE_POINT 3.0\n#define LIGHT_TYPE_RANGED_DIRECTIONAL 4.0\n#define IS_DIRECTIONAL_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_DIRECTIONAL)) < EPSILON_LOWP)\n#define IS_SPHERE_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_SPHERE)) < EPSILON_LOWP)\n#define IS_SPOT_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_SPOT)) < EPSILON_LOWP)\n#define IS_POINT_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_POINT)) < EPSILON_LOWP)\n#define IS_RANGED_DIRECTIONAL_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_RANGED_DIRECTIONAL)) < EPSILON_LOWP)\n#define TONE_MAPPING_ACES 0\n#define TONE_MAPPING_LINEAR 1\n#define SURFACES_MAX_TRANSMIT_DEPTH_VALUE 999999.0\n#ifndef CC_SURFACES_DEBUG_VIEW_SINGLE\n  #define CC_SURFACES_DEBUG_VIEW_SINGLE 1\n#endif\n#ifndef CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC\n  #define CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC 2\n#endif\n#if defined(CC_USE_METAL) || defined(CC_USE_WGPU)\n#define CC_HANDLE_SAMPLE_NDC_FLIP_STATIC(y) y = -y\n#else\n#define CC_HANDLE_SAMPLE_NDC_FLIP_STATIC(y)\n#endif\nfloat GetCameraDepthRH(float depthHS, mat4 matProj)\n{\n    return -matProj[3][2] / (depthHS + matProj[2][2]);\n}\nfloat GetCameraDepthRH(float depthHS, float matProj32, float matProj22)\n{\n    return -matProj32 / (depthHS + matProj22);\n}\n#if CC_USE_FOG != 4\n#endif\nlayout(std140) uniform hbaoUBO {\n    vec4 uvDepthToEyePosParams;\n    vec4 radiusParam;\n    vec4 miscParam;\n    vec4 randomTexSize;\n    vec4 blurParam;\n};\n#define NUM_STEPS 8.0\n#define NUM_STEPS_LOOP NUM_STEPS*2.0\n#define INV_NUM_DIRECTIONS 0.125\n#define NUM_DIRECTIONS 8\n#define CONSTANT_2PI 6.2831852\n#define g_AOResolution cc_screenSize.xy\n#define g_InvAOResolution cc_screenSize.zw\n#define g_UVToViewA uvDepthToEyePosParams.xy\n#define g_UVToViewB uvDepthToEyePosParams.zw\n#define g_R radiusParam.x\n#define g_R2 radiusParam.y\n#define g_NegInvR2 radiusParam.z\n#define g_MaxRadiusPixels radiusParam.w\n#define g_FocalLen miscParam.xy\n#define g_TanAngleBias miscParam.z\n#define g_Strength miscParam.w\nuniform sampler2D DepthTex;\nuniform sampler2D RandomTex;\nvec2 fixUV(vec2 uv)\n{\n  return saturate(uv);\n}\nfloat GetLinearDepth(vec2 uv) {\n  float depthHS = 0.0;\n  #if defined(CC_USE_WGPU)\n      depthHS = textureLod(DepthTex, fixUV(uv), 0.0).r * 2.0 - 1.0;\n  #else\n      depthHS = texture(DepthTex, fixUV(uv)).r * 2.0 - 1.0;\n  #endif\n  return -GetCameraDepthRH(depthHS, cc_matProj);\n}\n#define KERNEL_FALLOFF 3.0f\n#define KERNEL_RADIUS 8\n#define HALF_KERNEL_RADIUS 4\n#define g_InvFullResolution cc_screenSize.zw\n#define g_BlurFallOff blurParam.x\n#define g_BlurDepthThreshold blurParam.y\n#define g_BlurSimpleRadius blurParam.z\nuniform sampler2D AOTexNearest;\n#define AOTexBilinear AOTexNearest\n#define GetLinearDepthBilinear GetLinearDepth\nfloat CrossBilateralWeight(float r, float d, float d0, float blurFalloff, float blurDepthThreshold)\n{\n  return exp2(-r*r*blurFalloff) * (abs(d - d0) < blurDepthThreshold ? 1.0 : 0.0);\n}\nvec2 SumWeightedAO(int i, float centerCameraDepth, vec2 centerUV, vec2 sampleDir, float blurFalloff, float blurDepthThreshold)\n{\n  float r = 2.0 * float(i) + 0.5;\n  vec2 sampleUV = centerUV + r * sampleDir * g_InvFullResolution;\n  float d = GetLinearDepthBilinear(sampleUV);\n  float sampledAO = texture(AOTexNearest, fixUV(sampleUV)).x;\n  float w = CrossBilateralWeight(r, d, centerCameraDepth, blurFalloff, blurDepthThreshold);\n  return vec2(w * sampledAO, w);\n}\nfloat BlurCore(vec2 uv, vec2 sampleDir)\n{\n  float ao = texture(AOTexNearest, uv).x;\n  float weight = 1.0;\n  float centerCameraDepth = GetLinearDepth(uv);\n  for(int i = -HALF_KERNEL_RADIUS; i <= HALF_KERNEL_RADIUS; i++)\n  {\n    vec2 aoAndW = SumWeightedAO(i, centerCameraDepth, uv, sampleDir, g_BlurFallOff, g_BlurDepthThreshold);\n    ao += aoAndW.x;\n    weight += aoAndW.y;\n  }\n  return ao / weight;\n}\n#define g_AOSaturation blurParam.w\nin vec2 v_uv;\nlayout(location = 0) out vec4 fragColor;\nvoid main () {\n  float ao = BlurCore(v_uv, vec2(0.0, 1.0));\n  fragColor = vec4(ao, ao, ao, 1.0);\n}"}, "glsl1": {"vert": "\nprecision highp float;\n#define QUATER_PI         0.78539816340\n#define HALF_PI           1.57079632679\n#define PI                3.14159265359\n#define PI2               6.28318530718\n#define PI4               12.5663706144\n#define INV_QUATER_PI     1.27323954474\n#define INV_HALF_PI       0.63661977237\n#define INV_PI            0.31830988618\n#define INV_PI2           0.15915494309\n#define INV_PI4           0.07957747155\n#define EPSILON           1e-6\n#define EPSILON_LOWP      1e-4\n#define LOG2              1.442695\n#define EXP_VALUE         2.71828183\n#define FP_MAX            65504.0\n#define FP_SCALE          0.0009765625\n#define FP_SCALE_INV      1024.0\n#define GRAY_VECTOR       vec3(0.299, 0.587, 0.114)\n#define LIGHT_MAP_TYPE_DISABLED 0\n#define LIGHT_MAP_TYPE_ALL_IN_ONE 1\n#define LIGHT_MAP_TYPE_INDIRECT_OCCLUSION 2\n#define REFLECTION_PROBE_TYPE_NONE 0\n#define REFLECTION_PROBE_TYPE_CUBE 1\n#define REFLECTION_PROBE_TYPE_PLANAR 2\n#define REFLECTION_PROBE_TYPE_BLEND 3\n#define REFLECTION_PROBE_TYPE_BLEND_AND_SKYBOX 4\n#define LIGHT_TYPE_DIRECTIONAL 0.0\n#define LIGHT_TYPE_SPHERE 1.0\n#define LIGHT_TYPE_SPOT 2.0\n#define LIGHT_TYPE_POINT 3.0\n#define LIGHT_TYPE_RANGED_DIRECTIONAL 4.0\n#define IS_DIRECTIONAL_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_DIRECTIONAL)) < EPSILON_LOWP)\n#define IS_SPHERE_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_SPHERE)) < EPSILON_LOWP)\n#define IS_SPOT_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_SPOT)) < EPSILON_LOWP)\n#define IS_POINT_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_POINT)) < EPSILON_LOWP)\n#define IS_RANGED_DIRECTIONAL_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_RANGED_DIRECTIONAL)) < EPSILON_LOWP)\n#define TONE_MAPPING_ACES 0\n#define TONE_MAPPING_LINEAR 1\n#define SURFACES_MAX_TRANSMIT_DEPTH_VALUE 999999.0\n#ifndef CC_SURFACES_DEBUG_VIEW_SINGLE\n  #define CC_SURFACES_DEBUG_VIEW_SINGLE 1\n#endif\n#ifndef CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC\n  #define CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC 2\n#endif\nstruct StandardVertInput {\n  highp vec4 position;\n  vec3 normal;\n  vec4 tangent;\n};\nattribute vec3 a_position;\nattribute vec3 a_normal;\nattribute vec2 a_texCoord;\nattribute vec4 a_tangent;\n#if CC_USE_SKINNING\n    attribute vec4 a_joints;\n  attribute vec4 a_weights;\n#endif\n#if USE_INSTANCING\n  #if CC_USE_BAKED_ANIMATION\n    attribute highp vec4 a_jointAnimInfo;\n  #endif\n  attribute vec4 a_matWorld0;\n  attribute vec4 a_matWorld1;\n  attribute vec4 a_matWorld2;\n  #if CC_USE_LIGHTMAP\n    attribute vec4 a_lightingMapUVParam;\n  #endif\n  #if CC_USE_REFLECTION_PROBE || CC_RECEIVE_SHADOW\n    #if CC_RECEIVE_SHADOW\n    #endif\n    attribute vec4 a_localShadowBiasAndProbeId;\n  #endif\n  #if CC_USE_REFLECTION_PROBE\n    attribute vec4 a_reflectionProbeData;\n  #endif\n  #if CC_USE_LIGHT_PROBE\n    attribute vec4 a_sh_linear_const_r;\n    attribute vec4 a_sh_linear_const_g;\n    attribute vec4 a_sh_linear_const_b;\n  #endif\n#endif\n#if CC_USE_MORPH\n    attribute float a_vertexId;\n#endif\nuniform highp vec4 cc_cameraPos;\nvarying vec2 v_uv;\nvoid main () {\n  StandardVertInput In;\n    In.position = vec4(a_position, 1.0);\n    In.normal = a_normal;\n    In.tangent = a_tangent;\n  In.position.xy = cc_cameraPos.w == 0.0 ? vec2(In.position.xy.x, -In.position.xy.y) : In.position.xy;\n  gl_Position = In.position;\n  v_uv = a_texCoord;\n}", "frag": "\nprecision highp float;\nprecision highp float;\nuniform mediump vec4 cc_screenSize;\nuniform highp mat4 cc_matProj;\n#define QUATER_PI         0.78539816340\n#define HALF_PI           1.57079632679\n#define PI                3.14159265359\n#define PI2               6.28318530718\n#define PI4               12.5663706144\n#define INV_QUATER_PI     1.27323954474\n#define INV_HALF_PI       0.63661977237\n#define INV_PI            0.31830988618\n#define INV_PI2           0.15915494309\n#define INV_PI4           0.07957747155\n#define EPSILON           1e-6\n#define EPSILON_LOWP      1e-4\n#define LOG2              1.442695\n#define EXP_VALUE         2.71828183\n#define FP_MAX            65504.0\n#define FP_SCALE          0.0009765625\n#define FP_SCALE_INV      1024.0\n#define GRAY_VECTOR       vec3(0.299, 0.587, 0.114)\nfloat saturate(float value) { return clamp(value, 0.0, 1.0); }\nvec2 saturate(vec2 value) { return clamp(value, vec2(0.0), vec2(1.0)); }\nvec3 saturate(vec3 value) { return clamp(value, vec3(0.0), vec3(1.0)); }\nvec4 saturate(vec4 value) { return clamp(value, vec4(0.0), vec4(1.0)); }\n#define LIGHT_MAP_TYPE_DISABLED 0\n#define LIGHT_MAP_TYPE_ALL_IN_ONE 1\n#define LIGHT_MAP_TYPE_INDIRECT_OCCLUSION 2\n#define REFLECTION_PROBE_TYPE_NONE 0\n#define REFLECTION_PROBE_TYPE_CUBE 1\n#define REFLECTION_PROBE_TYPE_PLANAR 2\n#define REFLECTION_PROBE_TYPE_BLEND 3\n#define REFLECTION_PROBE_TYPE_BLEND_AND_SKYBOX 4\n#define LIGHT_TYPE_DIRECTIONAL 0.0\n#define LIGHT_TYPE_SPHERE 1.0\n#define LIGHT_TYPE_SPOT 2.0\n#define LIGHT_TYPE_POINT 3.0\n#define LIGHT_TYPE_RANGED_DIRECTIONAL 4.0\n#define IS_DIRECTIONAL_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_DIRECTIONAL)) < EPSILON_LOWP)\n#define IS_SPHERE_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_SPHERE)) < EPSILON_LOWP)\n#define IS_SPOT_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_SPOT)) < EPSILON_LOWP)\n#define IS_POINT_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_POINT)) < EPSILON_LOWP)\n#define IS_RANGED_DIRECTIONAL_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_RANGED_DIRECTIONAL)) < EPSILON_LOWP)\n#define TONE_MAPPING_ACES 0\n#define TONE_MAPPING_LINEAR 1\n#define SURFACES_MAX_TRANSMIT_DEPTH_VALUE 999999.0\n#ifndef CC_SURFACES_DEBUG_VIEW_SINGLE\n  #define CC_SURFACES_DEBUG_VIEW_SINGLE 1\n#endif\n#ifndef CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC\n  #define CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC 2\n#endif\n#if defined(CC_USE_METAL) || defined(CC_USE_WGPU)\n#define CC_HANDLE_SAMPLE_NDC_FLIP_STATIC(y) y = -y\n#else\n#define CC_HANDLE_SAMPLE_NDC_FLIP_STATIC(y)\n#endif\nfloat GetCameraDepthRH(float depthHS, mat4 matProj)\n{\n    return -matProj[3][2] / (depthHS + matProj[2][2]);\n}\nfloat GetCameraDepthRH(float depthHS, float matProj32, float matProj22)\n{\n    return -matProj32 / (depthHS + matProj22);\n}\n#if CC_USE_FOG != 4\n#endif\n       uniform vec4 uvDepthToEyePosParams;\n       uniform vec4 radiusParam;\n       uniform vec4 miscParam;\n       uniform vec4 blurParam;\n#define NUM_STEPS 8.0\n#define NUM_STEPS_LOOP NUM_STEPS*2.0\n#define INV_NUM_DIRECTIONS 0.125\n#define NUM_DIRECTIONS 8\n#define CONSTANT_2PI 6.2831852\n#define g_AOResolution cc_screenSize.xy\n#define g_InvAOResolution cc_screenSize.zw\n#define g_UVToViewA uvDepthToEyePosParams.xy\n#define g_UVToViewB uvDepthToEyePosParams.zw\n#define g_R radiusParam.x\n#define g_R2 radiusParam.y\n#define g_NegInvR2 radiusParam.z\n#define g_MaxRadiusPixels radiusParam.w\n#define g_FocalLen miscParam.xy\n#define g_TanAngleBias miscParam.z\n#define g_Strength miscParam.w\nuniform sampler2D DepthTex;\nuniform sampler2D RandomTex;\nvec2 fixUV(vec2 uv)\n{\n  return saturate(uv);\n}\nfloat GetLinearDepth(vec2 uv) {\n  float depthHS = 0.0;\n  #if defined(CC_USE_WGPU)\n      depthHS = texture2DLod(DepthTex, fixUV(uv), 0.0).r * 2.0 - 1.0;\n  #else\n      depthHS = texture2D(DepthTex, fixUV(uv)).r * 2.0 - 1.0;\n  #endif\n  return -GetCameraDepthRH(depthHS, cc_matProj);\n}\n#define KERNEL_FALLOFF 3.0f\n#define KERNEL_RADIUS 8\n#define HALF_KERNEL_RADIUS 4\n#define g_InvFullResolution cc_screenSize.zw\n#define g_BlurFallOff blurParam.x\n#define g_BlurDepthThreshold blurParam.y\n#define g_BlurSimpleRadius blurParam.z\nuniform sampler2D AOTexNearest;\n#define AOTexBilinear AOTexNearest\n#define GetLinearDepthBilinear GetLinearDepth\nfloat CrossBilateralWeight(float r, float d, float d0, float blurFalloff, float blurDepthThreshold)\n{\n  return exp2(-r*r*blurFalloff) * (abs(d - d0) < blurDepthThreshold ? 1.0 : 0.0);\n}\nvec2 SumWeightedAO(int i, float centerCameraDepth, vec2 centerUV, vec2 sampleDir, float blurFalloff, float blurDepthThreshold)\n{\n  float r = 2.0 * float(i) + 0.5;\n  vec2 sampleUV = centerUV + r * sampleDir * g_InvFullResolution;\n  float d = GetLinearDepthBilinear(sampleUV);\n  float sampledAO = texture2D(AOTexNearest, fixUV(sampleUV)).x;\n  float w = CrossBilateralWeight(r, d, centerCameraDepth, blurFalloff, blurDepthThreshold);\n  return vec2(w * sampledAO, w);\n}\nfloat BlurCore(vec2 uv, vec2 sampleDir)\n{\n  float ao = texture2D(AOTexNearest, uv).x;\n  float weight = 1.0;\n  float centerCameraDepth = GetLinearDepth(uv);\n  for(int i = -HALF_KERNEL_RADIUS; i <= HALF_KERNEL_RADIUS; i++)\n  {\n    vec2 aoAndW = SumWeightedAO(i, centerCameraDepth, uv, sampleDir, g_BlurFallOff, g_BlurDepthThreshold);\n    ao += aoAndW.x;\n    weight += aoAndW.y;\n  }\n  return ao / weight;\n}\n#define g_AOSaturation blurParam.w\nvarying vec2 v_uv;\nvoid main () {\n  float ao = BlurCore(v_uv, vec2(0.0, 1.0));\n  gl_FragColor = vec4(ao, ao, ao, 1.0);\n}"}, "builtins": {"globals": {"blocks": [{"name": "CCGlobal", "defines": []}, {"name": "CCCamera", "defines": []}], "samplerTextures": [], "buffers": [], "images": []}, "locals": {"blocks": [], "samplerTextures": [], "buffers": [], "images": []}, "statistics": {"CC_EFFECT_USED_VERTEX_UNIFORM_VECTORS": 42, "CC_EFFECT_USED_FRAGMENT_UNIFORM_VECTORS": 47}}, "defines": [{"name": "USE_INSTANCING", "type": "boolean", "defines": [], "editor": {"elevated": true}}, {"name": "CC_USE_SKINNING", "type": "boolean", "defines": []}, {"name": "CC_USE_BAKED_ANIMATION", "type": "boolean", "defines": ["USE_INSTANCING"]}, {"name": "CC_USE_LIGHTMAP", "type": "boolean", "defines": ["USE_INSTANCING"]}, {"name": "CC_USE_REFLECTION_PROBE", "type": "boolean", "defines": ["USE_INSTANCING"]}, {"name": "CC_RECEIVE_SHADOW", "type": "boolean", "defines": ["USE_INSTANCING"]}, {"name": "CC_USE_LIGHT_PROBE", "type": "boolean", "defines": ["USE_INSTANCING"]}, {"name": "CC_USE_MORPH", "type": "boolean", "defines": []}, {"name": "CC_USE_FOG", "type": "number", "defines": [], "range": [0, 4]}], "name": "pipeline/post-process/hbao|hbao-vs|blury-fs"}, {"blocks": [{"name": "hbaoUBO", "members": [{"name": "uvDepthToEyePosParams", "type": 16, "count": 1}, {"name": "radiusParam", "type": 16, "count": 1}, {"name": "miscParam", "type": 16, "count": 1}, {"name": "randomTexSize", "type": 16, "count": 1}, {"name": "blurParam", "type": 16, "count": 1}], "defines": [], "stageFlags": 16, "binding": 0}], "samplerTextures": [{"name": "RandomTex", "type": 28, "count": 1, "defines": [], "stageFlags": 16, "sampleType": 0, "binding": 2}], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": [], "attributes": [{"name": "a_position", "defines": [], "format": 32, "location": 0}, {"name": "a_normal", "defines": [], "format": 32, "location": 1}, {"name": "a_texCoord", "defines": [], "format": 21, "location": 2}, {"name": "a_tangent", "defines": [], "format": 44, "location": 3}, {"name": "a_joints", "defines": ["CC_USE_SKINNING"], "location": 4}, {"name": "a_weights", "defines": ["CC_USE_SKINNING"], "format": 44, "location": 5}, {"name": "a_jointAnimInfo", "defines": ["USE_INSTANCING", "CC_USE_BAKED_ANIMATION"], "format": 44, "isInstanced": true, "location": 6}, {"name": "a_matWorld0", "defines": ["USE_INSTANCING"], "format": 44, "isInstanced": true, "location": 7}, {"name": "a_matWorld1", "defines": ["USE_INSTANCING"], "format": 44, "isInstanced": true, "location": 8}, {"name": "a_matWorld2", "defines": ["USE_INSTANCING"], "format": 44, "isInstanced": true, "location": 9}, {"name": "a_lightingMapUVParam", "defines": ["USE_INSTANCING", "CC_USE_LIGHTMAP"], "format": 44, "isInstanced": true, "location": 10}, {"name": "a_localShadowBiasAndProbeId", "defines": ["USE_INSTANCING"], "format": 44, "isInstanced": true, "location": 11}, {"name": "a_reflectionProbeData", "defines": ["USE_INSTANCING", "CC_USE_REFLECTION_PROBE"], "format": 44, "isInstanced": true, "location": 12}, {"name": "a_sh_linear_const_r", "defines": ["USE_INSTANCING", "CC_USE_LIGHT_PROBE"], "format": 44, "isInstanced": true, "location": 13}, {"name": "a_sh_linear_const_g", "defines": ["USE_INSTANCING", "CC_USE_LIGHT_PROBE"], "format": 44, "isInstanced": true, "location": 14}, {"name": "a_sh_linear_const_b", "defines": ["USE_INSTANCING", "CC_USE_LIGHT_PROBE"], "format": 44, "isInstanced": true, "location": 15}, {"name": "a_vertexId", "defines": ["CC_USE_MORPH"], "format": 11, "location": 16}], "varyings": [{"name": "v_uv", "type": 14, "count": 1, "defines": [], "stageFlags": 17, "location": 0}], "fragColors": [{"name": "fragColor", "typename": "vec4", "type": 16, "count": 1, "defines": [], "stageFlags": 16, "location": 0}], "descriptors": [{"rate": 0, "blocks": [], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}, {"rate": 1, "blocks": [{"name": "hbaoUBO", "members": [{"name": "uvDepthToEyePosParams", "type": 16, "count": 1}, {"name": "radiusParam", "type": 16, "count": 1}, {"name": "miscParam", "type": 16, "count": 1}, {"name": "randomTexSize", "type": 16, "count": 1}, {"name": "blurParam", "type": 16, "count": 1}], "defines": [], "stageFlags": 16, "binding": 0}], "samplerTextures": [{"name": "RandomTex", "type": 28, "count": 1, "defines": [], "stageFlags": 16, "sampleType": 0, "binding": 2}], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}, {"rate": 2, "blocks": [], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}, {"rate": 3, "blocks": [{"tags": {"builtin": "global"}, "name": "CCGlobal", "members": [{"name": "cc_time", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_screenSize", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_nativeSize", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_probeInfo", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_debug_view_mode", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}], "defines": [], "stageFlags": 17}, {"tags": {"builtin": "global"}, "name": "CCCamera", "members": [{"name": "cc_matView", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matViewInv", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matProj", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matProjInv", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matViewProj", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matViewProjInv", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_cameraPos", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_surfaceTransform", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_screenScale", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_exposure", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_mainLitDir", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_mainLitColor", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_ambientSky", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_ambientGround", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_fogColor", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_fogBase", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_fogAdd", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_nearFar", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_viewPort", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}], "defines": [], "stageFlags": 17}], "samplerTextures": [{"name": "DepthTex", "type": 28, "count": 1, "defines": [], "stageFlags": 16, "sampleType": 0, "binding": 1}, {"name": "AOTexNearest", "type": 28, "count": 1, "defines": [], "stageFlags": 16, "sampleType": 0, "binding": 3}], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}], "hash": 2936562803, "glsl4": {"vert": "\nprecision highp float;\n#define QUATER_PI         0.78539816340\n#define HALF_PI           1.57079632679\n#define PI                3.14159265359\n#define PI2               6.28318530718\n#define PI4               12.5663706144\n#define INV_QUATER_PI     1.27323954474\n#define INV_HALF_PI       0.63661977237\n#define INV_PI            0.31830988618\n#define INV_PI2           0.15915494309\n#define INV_PI4           0.07957747155\n#define EPSILON           1e-6\n#define EPSILON_LOWP      1e-4\n#define LOG2              1.442695\n#define EXP_VALUE         2.71828183\n#define FP_MAX            65504.0\n#define FP_SCALE          0.0009765625\n#define FP_SCALE_INV      1024.0\n#define GRAY_VECTOR       vec3(0.299, 0.587, 0.114)\n#define LIGHT_MAP_TYPE_DISABLED 0\n#define LIGHT_MAP_TYPE_ALL_IN_ONE 1\n#define LIGHT_MAP_TYPE_INDIRECT_OCCLUSION 2\n#define REFLECTION_PROBE_TYPE_NONE 0\n#define REFLECTION_PROBE_TYPE_CUBE 1\n#define REFLECTION_PROBE_TYPE_PLANAR 2\n#define REFLECTION_PROBE_TYPE_BLEND 3\n#define REFLECTION_PROBE_TYPE_BLEND_AND_SKYBOX 4\n#define LIGHT_TYPE_DIRECTIONAL 0.0\n#define LIGHT_TYPE_SPHERE 1.0\n#define LIGHT_TYPE_SPOT 2.0\n#define LIGHT_TYPE_POINT 3.0\n#define LIGHT_TYPE_RANGED_DIRECTIONAL 4.0\n#define IS_DIRECTIONAL_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_DIRECTIONAL)) < EPSILON_LOWP)\n#define IS_SPHERE_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_SPHERE)) < EPSILON_LOWP)\n#define IS_SPOT_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_SPOT)) < EPSILON_LOWP)\n#define IS_POINT_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_POINT)) < EPSILON_LOWP)\n#define IS_RANGED_DIRECTIONAL_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_RANGED_DIRECTIONAL)) < EPSILON_LOWP)\n#define TONE_MAPPING_ACES 0\n#define TONE_MAPPING_LINEAR 1\n#define SURFACES_MAX_TRANSMIT_DEPTH_VALUE 999999.0\n#ifndef CC_SURFACES_DEBUG_VIEW_SINGLE\n  #define CC_SURFACES_DEBUG_VIEW_SINGLE 1\n#endif\n#ifndef CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC\n  #define CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC 2\n#endif\nstruct StandardVertInput {\n  highp vec4 position;\n  vec3 normal;\n  vec4 tangent;\n};\nlayout(location = 0) in vec3 a_position;\nlayout(location = 1) in vec3 a_normal;\nlayout(location = 2) in vec2 a_texCoord;\nlayout(location = 3) in vec4 a_tangent;\n#if CC_USE_SKINNING\n    layout(location = 4) in u32vec4 a_joints;\n  layout(location = 5) in vec4 a_weights;\n#endif\n#if USE_INSTANCING\n  #if CC_USE_BAKED_ANIMATION\n    layout(location = 6) in highp vec4 a_jointAnimInfo;\n  #endif\n  layout(location = 7) in vec4 a_matWorld0;\n  layout(location = 8) in vec4 a_matWorld1;\n  layout(location = 9) in vec4 a_matWorld2;\n  #if CC_USE_LIGHTMAP\n    layout(location = 10) in vec4 a_lightingMapUVParam;\n  #endif\n  #if CC_USE_REFLECTION_PROBE || CC_RECEIVE_SHADOW\n    #if CC_RECEIVE_SHADOW\n    #endif\n    layout(location = 11) in vec4 a_localShadowBiasAndProbeId;\n  #endif\n  #if CC_USE_REFLECTION_PROBE\n    layout(location = 12) in vec4 a_reflectionProbeData;\n  #endif\n  #if CC_USE_LIGHT_PROBE\n    layout(location = 13) in vec4 a_sh_linear_const_r;\n    layout(location = 14) in vec4 a_sh_linear_const_g;\n    layout(location = 15) in vec4 a_sh_linear_const_b;\n  #endif\n#endif\n#if CC_USE_MORPH\n#endif\nlayout(set = 0, binding = 0) uniform CCGlobal {\n  highp   vec4 cc_time;\n  mediump vec4 cc_screenSize;\n  mediump vec4 cc_nativeSize;\n  mediump vec4 cc_probeInfo;\n  mediump vec4 cc_debug_view_mode;\n};\nlayout(set = 0, binding = 1) uniform CCCamera {\n  highp   mat4 cc_matView;\n  highp   mat4 cc_matViewInv;\n  highp   mat4 cc_matProj;\n  highp   mat4 cc_matProjInv;\n  highp   mat4 cc_matViewProj;\n  highp   mat4 cc_matViewProjInv;\n  highp   vec4 cc_cameraPos;\n  mediump vec4 cc_surfaceTransform;\n  mediump vec4 cc_screenScale;\n  mediump vec4 cc_exposure;\n  mediump vec4 cc_mainLitDir;\n  mediump vec4 cc_mainLitColor;\n  mediump vec4 cc_ambientSky;\n  mediump vec4 cc_ambientGround;\n  mediump vec4 cc_fogColor;\n  mediump vec4 cc_fogBase;\n  mediump vec4 cc_fogAdd;\n  mediump vec4 cc_nearFar;\n  mediump vec4 cc_viewPort;\n};\nlayout(location = 0) out vec2 v_uv;\nvoid main () {\n  StandardVertInput In;\n    In.position = vec4(a_position, 1.0);\n    In.normal = a_normal;\n    In.tangent = a_tangent;\n  In.position.xy = cc_cameraPos.w == 0.0 ? vec2(In.position.xy.x, -In.position.xy.y) : In.position.xy;\n  gl_Position = In.position;\n  v_uv = a_texCoord;\n}", "frag": "\nprecision highp float;\nprecision highp float;\nlayout(set = 0, binding = 0) uniform CCGlobal {\n  highp   vec4 cc_time;\n  mediump vec4 cc_screenSize;\n  mediump vec4 cc_nativeSize;\n  mediump vec4 cc_probeInfo;\n  mediump vec4 cc_debug_view_mode;\n};\nlayout(set = 0, binding = 1) uniform CCCamera {\n  highp   mat4 cc_matView;\n  highp   mat4 cc_matViewInv;\n  highp   mat4 cc_matProj;\n  highp   mat4 cc_matProjInv;\n  highp   mat4 cc_matViewProj;\n  highp   mat4 cc_matViewProjInv;\n  highp   vec4 cc_cameraPos;\n  mediump vec4 cc_surfaceTransform;\n  mediump vec4 cc_screenScale;\n  mediump vec4 cc_exposure;\n  mediump vec4 cc_mainLitDir;\n  mediump vec4 cc_mainLitColor;\n  mediump vec4 cc_ambientSky;\n  mediump vec4 cc_ambientGround;\n  mediump vec4 cc_fogColor;\n  mediump vec4 cc_fogBase;\n  mediump vec4 cc_fogAdd;\n  mediump vec4 cc_nearFar;\n  mediump vec4 cc_viewPort;\n};\n#define QUATER_PI         0.78539816340\n#define HALF_PI           1.57079632679\n#define PI                3.14159265359\n#define PI2               6.28318530718\n#define PI4               12.5663706144\n#define INV_QUATER_PI     1.27323954474\n#define INV_HALF_PI       0.63661977237\n#define INV_PI            0.31830988618\n#define INV_PI2           0.15915494309\n#define INV_PI4           0.07957747155\n#define EPSILON           1e-6\n#define EPSILON_LOWP      1e-4\n#define LOG2              1.442695\n#define EXP_VALUE         2.71828183\n#define FP_MAX            65504.0\n#define FP_SCALE          0.0009765625\n#define FP_SCALE_INV      1024.0\n#define GRAY_VECTOR       vec3(0.299, 0.587, 0.114)\n#define LIGHT_MAP_TYPE_DISABLED 0\n#define LIGHT_MAP_TYPE_ALL_IN_ONE 1\n#define LIGHT_MAP_TYPE_INDIRECT_OCCLUSION 2\n#define REFLECTION_PROBE_TYPE_NONE 0\n#define REFLECTION_PROBE_TYPE_CUBE 1\n#define REFLECTION_PROBE_TYPE_PLANAR 2\n#define REFLECTION_PROBE_TYPE_BLEND 3\n#define REFLECTION_PROBE_TYPE_BLEND_AND_SKYBOX 4\n#define LIGHT_TYPE_DIRECTIONAL 0.0\n#define LIGHT_TYPE_SPHERE 1.0\n#define LIGHT_TYPE_SPOT 2.0\n#define LIGHT_TYPE_POINT 3.0\n#define LIGHT_TYPE_RANGED_DIRECTIONAL 4.0\n#define IS_DIRECTIONAL_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_DIRECTIONAL)) < EPSILON_LOWP)\n#define IS_SPHERE_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_SPHERE)) < EPSILON_LOWP)\n#define IS_SPOT_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_SPOT)) < EPSILON_LOWP)\n#define IS_POINT_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_POINT)) < EPSILON_LOWP)\n#define IS_RANGED_DIRECTIONAL_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_RANGED_DIRECTIONAL)) < EPSILON_LOWP)\n#define TONE_MAPPING_ACES 0\n#define TONE_MAPPING_LINEAR 1\n#define SURFACES_MAX_TRANSMIT_DEPTH_VALUE 999999.0\n#ifndef CC_SURFACES_DEBUG_VIEW_SINGLE\n  #define CC_SURFACES_DEBUG_VIEW_SINGLE 1\n#endif\n#ifndef CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC\n  #define CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC 2\n#endif\n#if defined(CC_USE_METAL) || defined(CC_USE_WGPU)\n#define CC_HANDLE_SAMPLE_NDC_FLIP_STATIC(y) y = -y\n#else\n#define CC_HANDLE_SAMPLE_NDC_FLIP_STATIC(y)\n#endif\n#if CC_USE_FOG != 4\n#endif\nlayout(set = 1, binding = 0) uniform hbaoUBO {\n    vec4 uvDepthToEyePosParams;\n    vec4 radiusParam;\n    vec4 miscParam;\n    vec4 randomTexSize;\n    vec4 blurParam;\n};\n#define NUM_STEPS 8.0\n#define NUM_STEPS_LOOP NUM_STEPS*2.0\n#define INV_NUM_DIRECTIONS 0.125\n#define NUM_DIRECTIONS 8\n#define CONSTANT_2PI 6.2831852\n#define g_AOResolution cc_screenSize.xy\n#define g_InvAOResolution cc_screenSize.zw\n#define g_UVToViewA uvDepthToEyePosParams.xy\n#define g_UVToViewB uvDepthToEyePosParams.zw\n#define g_R radiusParam.x\n#define g_R2 radiusParam.y\n#define g_NegInvR2 radiusParam.z\n#define g_MaxRadiusPixels radiusParam.w\n#define g_FocalLen miscParam.xy\n#define g_TanAngleBias miscParam.z\n#define g_Strength miscParam.w\nlayout(set = 1, binding = 1) uniform sampler2D DepthTex;\nlayout(set = 1, binding = 2) uniform sampler2D RandomTex;\n#define KERNEL_FALLOFF 3.0f\n#define KERNEL_RADIUS 8\n#define HALF_KERNEL_RADIUS 4\n#define g_InvFullResolution cc_screenSize.zw\n#define g_BlurFallOff blurParam.x\n#define g_BlurDepthThreshold blurParam.y\n#define g_BlurSimpleRadius blurParam.z\nlayout(set = 1, binding = 3) uniform sampler2D AOTexNearest;\n#define AOTexBilinear AOTexNearest\n#define GetLinearDepthBilinear GetLinearDepth\n#define g_AOSaturation blurParam.w\nfloat Combine(vec2 uv)\n{\n  float ao = texture(AOTexNearest, uv).x;\n  return g_AOSaturation > 1.0 ? pow(ao, g_AOSaturation) : mix(1.0, ao, g_AOSaturation);\n}\nlayout(location = 0) in vec2 v_uv;\nlayout(location = 0) out vec4 fragColor;\nvoid main () {\n  float ao = Combine(v_uv);\n  fragColor = vec4(1.0, 1.0, 1.0, ao);\n}"}, "glsl3": {"vert": "\nprecision highp float;\n#define QUATER_PI         0.78539816340\n#define HALF_PI           1.57079632679\n#define PI                3.14159265359\n#define PI2               6.28318530718\n#define PI4               12.5663706144\n#define INV_QUATER_PI     1.27323954474\n#define INV_HALF_PI       0.63661977237\n#define INV_PI            0.31830988618\n#define INV_PI2           0.15915494309\n#define INV_PI4           0.07957747155\n#define EPSILON           1e-6\n#define EPSILON_LOWP      1e-4\n#define LOG2              1.442695\n#define EXP_VALUE         2.71828183\n#define FP_MAX            65504.0\n#define FP_SCALE          0.0009765625\n#define FP_SCALE_INV      1024.0\n#define GRAY_VECTOR       vec3(0.299, 0.587, 0.114)\n#define LIGHT_MAP_TYPE_DISABLED 0\n#define LIGHT_MAP_TYPE_ALL_IN_ONE 1\n#define LIGHT_MAP_TYPE_INDIRECT_OCCLUSION 2\n#define REFLECTION_PROBE_TYPE_NONE 0\n#define REFLECTION_PROBE_TYPE_CUBE 1\n#define REFLECTION_PROBE_TYPE_PLANAR 2\n#define REFLECTION_PROBE_TYPE_BLEND 3\n#define REFLECTION_PROBE_TYPE_BLEND_AND_SKYBOX 4\n#define LIGHT_TYPE_DIRECTIONAL 0.0\n#define LIGHT_TYPE_SPHERE 1.0\n#define LIGHT_TYPE_SPOT 2.0\n#define LIGHT_TYPE_POINT 3.0\n#define LIGHT_TYPE_RANGED_DIRECTIONAL 4.0\n#define IS_DIRECTIONAL_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_DIRECTIONAL)) < EPSILON_LOWP)\n#define IS_SPHERE_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_SPHERE)) < EPSILON_LOWP)\n#define IS_SPOT_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_SPOT)) < EPSILON_LOWP)\n#define IS_POINT_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_POINT)) < EPSILON_LOWP)\n#define IS_RANGED_DIRECTIONAL_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_RANGED_DIRECTIONAL)) < EPSILON_LOWP)\n#define TONE_MAPPING_ACES 0\n#define TONE_MAPPING_LINEAR 1\n#define SURFACES_MAX_TRANSMIT_DEPTH_VALUE 999999.0\n#ifndef CC_SURFACES_DEBUG_VIEW_SINGLE\n  #define CC_SURFACES_DEBUG_VIEW_SINGLE 1\n#endif\n#ifndef CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC\n  #define CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC 2\n#endif\nstruct StandardVertInput {\n  highp vec4 position;\n  vec3 normal;\n  vec4 tangent;\n};\nin vec3 a_position;\nin vec3 a_normal;\nin vec2 a_texCoord;\nin vec4 a_tangent;\n#if CC_USE_SKINNING\n    in vec4 a_joints;\n  in vec4 a_weights;\n#endif\n#if USE_INSTANCING\n  #if CC_USE_BAKED_ANIMATION\n    in highp vec4 a_jointAnimInfo;\n  #endif\n  in vec4 a_matWorld0;\n  in vec4 a_matWorld1;\n  in vec4 a_matWorld2;\n  #if CC_USE_LIGHTMAP\n    in vec4 a_lightingMapUVParam;\n  #endif\n  #if CC_USE_REFLECTION_PROBE || CC_RECEIVE_SHADOW\n    #if CC_RECEIVE_SHADOW\n    #endif\n    in vec4 a_localShadowBiasAndProbeId;\n  #endif\n  #if CC_USE_REFLECTION_PROBE\n    in vec4 a_reflectionProbeData;\n  #endif\n  #if CC_USE_LIGHT_PROBE\n    in vec4 a_sh_linear_const_r;\n    in vec4 a_sh_linear_const_g;\n    in vec4 a_sh_linear_const_b;\n  #endif\n#endif\n#if CC_USE_MORPH\n    in float a_vertexId;\n#endif\nlayout(std140) uniform CCGlobal {\n  highp   vec4 cc_time;\n  mediump vec4 cc_screenSize;\n  mediump vec4 cc_nativeSize;\n  mediump vec4 cc_probeInfo;\n  mediump vec4 cc_debug_view_mode;\n};\nlayout(std140) uniform CCCamera {\n  highp   mat4 cc_matView;\n  highp   mat4 cc_matViewInv;\n  highp   mat4 cc_matProj;\n  highp   mat4 cc_matProjInv;\n  highp   mat4 cc_matViewProj;\n  highp   mat4 cc_matViewProjInv;\n  highp   vec4 cc_cameraPos;\n  mediump vec4 cc_surfaceTransform;\n  mediump vec4 cc_screenScale;\n  mediump vec4 cc_exposure;\n  mediump vec4 cc_mainLitDir;\n  mediump vec4 cc_mainLitColor;\n  mediump vec4 cc_ambientSky;\n  mediump vec4 cc_ambientGround;\n  mediump vec4 cc_fogColor;\n  mediump vec4 cc_fogBase;\n  mediump vec4 cc_fogAdd;\n  mediump vec4 cc_nearFar;\n  mediump vec4 cc_viewPort;\n};\nout vec2 v_uv;\nvoid main () {\n  StandardVertInput In;\n    In.position = vec4(a_position, 1.0);\n    In.normal = a_normal;\n    In.tangent = a_tangent;\n  In.position.xy = cc_cameraPos.w == 0.0 ? vec2(In.position.xy.x, -In.position.xy.y) : In.position.xy;\n  gl_Position = In.position;\n  v_uv = a_texCoord;\n}", "frag": "\nprecision highp float;\nprecision highp float;\nlayout(std140) uniform CCGlobal {\n  highp   vec4 cc_time;\n  mediump vec4 cc_screenSize;\n  mediump vec4 cc_nativeSize;\n  mediump vec4 cc_probeInfo;\n  mediump vec4 cc_debug_view_mode;\n};\nlayout(std140) uniform CCCamera {\n  highp   mat4 cc_matView;\n  highp   mat4 cc_matViewInv;\n  highp   mat4 cc_matProj;\n  highp   mat4 cc_matProjInv;\n  highp   mat4 cc_matViewProj;\n  highp   mat4 cc_matViewProjInv;\n  highp   vec4 cc_cameraPos;\n  mediump vec4 cc_surfaceTransform;\n  mediump vec4 cc_screenScale;\n  mediump vec4 cc_exposure;\n  mediump vec4 cc_mainLitDir;\n  mediump vec4 cc_mainLitColor;\n  mediump vec4 cc_ambientSky;\n  mediump vec4 cc_ambientGround;\n  mediump vec4 cc_fogColor;\n  mediump vec4 cc_fogBase;\n  mediump vec4 cc_fogAdd;\n  mediump vec4 cc_nearFar;\n  mediump vec4 cc_viewPort;\n};\n#define QUATER_PI         0.78539816340\n#define HALF_PI           1.57079632679\n#define PI                3.14159265359\n#define PI2               6.28318530718\n#define PI4               12.5663706144\n#define INV_QUATER_PI     1.27323954474\n#define INV_HALF_PI       0.63661977237\n#define INV_PI            0.31830988618\n#define INV_PI2           0.15915494309\n#define INV_PI4           0.07957747155\n#define EPSILON           1e-6\n#define EPSILON_LOWP      1e-4\n#define LOG2              1.442695\n#define EXP_VALUE         2.71828183\n#define FP_MAX            65504.0\n#define FP_SCALE          0.0009765625\n#define FP_SCALE_INV      1024.0\n#define GRAY_VECTOR       vec3(0.299, 0.587, 0.114)\n#define LIGHT_MAP_TYPE_DISABLED 0\n#define LIGHT_MAP_TYPE_ALL_IN_ONE 1\n#define LIGHT_MAP_TYPE_INDIRECT_OCCLUSION 2\n#define REFLECTION_PROBE_TYPE_NONE 0\n#define REFLECTION_PROBE_TYPE_CUBE 1\n#define REFLECTION_PROBE_TYPE_PLANAR 2\n#define REFLECTION_PROBE_TYPE_BLEND 3\n#define REFLECTION_PROBE_TYPE_BLEND_AND_SKYBOX 4\n#define LIGHT_TYPE_DIRECTIONAL 0.0\n#define LIGHT_TYPE_SPHERE 1.0\n#define LIGHT_TYPE_SPOT 2.0\n#define LIGHT_TYPE_POINT 3.0\n#define LIGHT_TYPE_RANGED_DIRECTIONAL 4.0\n#define IS_DIRECTIONAL_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_DIRECTIONAL)) < EPSILON_LOWP)\n#define IS_SPHERE_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_SPHERE)) < EPSILON_LOWP)\n#define IS_SPOT_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_SPOT)) < EPSILON_LOWP)\n#define IS_POINT_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_POINT)) < EPSILON_LOWP)\n#define IS_RANGED_DIRECTIONAL_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_RANGED_DIRECTIONAL)) < EPSILON_LOWP)\n#define TONE_MAPPING_ACES 0\n#define TONE_MAPPING_LINEAR 1\n#define SURFACES_MAX_TRANSMIT_DEPTH_VALUE 999999.0\n#ifndef CC_SURFACES_DEBUG_VIEW_SINGLE\n  #define CC_SURFACES_DEBUG_VIEW_SINGLE 1\n#endif\n#ifndef CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC\n  #define CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC 2\n#endif\n#if defined(CC_USE_METAL) || defined(CC_USE_WGPU)\n#define CC_HANDLE_SAMPLE_NDC_FLIP_STATIC(y) y = -y\n#else\n#define CC_HANDLE_SAMPLE_NDC_FLIP_STATIC(y)\n#endif\n#if CC_USE_FOG != 4\n#endif\nlayout(std140) uniform hbaoUBO {\n    vec4 uvDepthToEyePosParams;\n    vec4 radiusParam;\n    vec4 miscParam;\n    vec4 randomTexSize;\n    vec4 blurParam;\n};\n#define NUM_STEPS 8.0\n#define NUM_STEPS_LOOP NUM_STEPS*2.0\n#define INV_NUM_DIRECTIONS 0.125\n#define NUM_DIRECTIONS 8\n#define CONSTANT_2PI 6.2831852\n#define g_AOResolution cc_screenSize.xy\n#define g_InvAOResolution cc_screenSize.zw\n#define g_UVToViewA uvDepthToEyePosParams.xy\n#define g_UVToViewB uvDepthToEyePosParams.zw\n#define g_R radiusParam.x\n#define g_R2 radiusParam.y\n#define g_NegInvR2 radiusParam.z\n#define g_MaxRadiusPixels radiusParam.w\n#define g_FocalLen miscParam.xy\n#define g_TanAngleBias miscParam.z\n#define g_Strength miscParam.w\nuniform sampler2D DepthTex;\nuniform sampler2D RandomTex;\n#define KERNEL_FALLOFF 3.0f\n#define KERNEL_RADIUS 8\n#define HALF_KERNEL_RADIUS 4\n#define g_InvFullResolution cc_screenSize.zw\n#define g_BlurFallOff blurParam.x\n#define g_BlurDepthThreshold blurParam.y\n#define g_BlurSimpleRadius blurParam.z\nuniform sampler2D AOTexNearest;\n#define AOTexBilinear AOTexNearest\n#define GetLinearDepthBilinear GetLinearDepth\n#define g_AOSaturation blurParam.w\nfloat Combine(vec2 uv)\n{\n  float ao = texture(AOTexNearest, uv).x;\n  return g_AOSaturation > 1.0 ? pow(ao, g_AOSaturation) : mix(1.0, ao, g_AOSaturation);\n}\nin vec2 v_uv;\nlayout(location = 0) out vec4 fragColor;\nvoid main () {\n  float ao = Combine(v_uv);\n  fragColor = vec4(1.0, 1.0, 1.0, ao);\n}"}, "glsl1": {"vert": "\nprecision highp float;\n#define QUATER_PI         0.78539816340\n#define HALF_PI           1.57079632679\n#define PI                3.14159265359\n#define PI2               6.28318530718\n#define PI4               12.5663706144\n#define INV_QUATER_PI     1.27323954474\n#define INV_HALF_PI       0.63661977237\n#define INV_PI            0.31830988618\n#define INV_PI2           0.15915494309\n#define INV_PI4           0.07957747155\n#define EPSILON           1e-6\n#define EPSILON_LOWP      1e-4\n#define LOG2              1.442695\n#define EXP_VALUE         2.71828183\n#define FP_MAX            65504.0\n#define FP_SCALE          0.0009765625\n#define FP_SCALE_INV      1024.0\n#define GRAY_VECTOR       vec3(0.299, 0.587, 0.114)\n#define LIGHT_MAP_TYPE_DISABLED 0\n#define LIGHT_MAP_TYPE_ALL_IN_ONE 1\n#define LIGHT_MAP_TYPE_INDIRECT_OCCLUSION 2\n#define REFLECTION_PROBE_TYPE_NONE 0\n#define REFLECTION_PROBE_TYPE_CUBE 1\n#define REFLECTION_PROBE_TYPE_PLANAR 2\n#define REFLECTION_PROBE_TYPE_BLEND 3\n#define REFLECTION_PROBE_TYPE_BLEND_AND_SKYBOX 4\n#define LIGHT_TYPE_DIRECTIONAL 0.0\n#define LIGHT_TYPE_SPHERE 1.0\n#define LIGHT_TYPE_SPOT 2.0\n#define LIGHT_TYPE_POINT 3.0\n#define LIGHT_TYPE_RANGED_DIRECTIONAL 4.0\n#define IS_DIRECTIONAL_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_DIRECTIONAL)) < EPSILON_LOWP)\n#define IS_SPHERE_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_SPHERE)) < EPSILON_LOWP)\n#define IS_SPOT_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_SPOT)) < EPSILON_LOWP)\n#define IS_POINT_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_POINT)) < EPSILON_LOWP)\n#define IS_RANGED_DIRECTIONAL_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_RANGED_DIRECTIONAL)) < EPSILON_LOWP)\n#define TONE_MAPPING_ACES 0\n#define TONE_MAPPING_LINEAR 1\n#define SURFACES_MAX_TRANSMIT_DEPTH_VALUE 999999.0\n#ifndef CC_SURFACES_DEBUG_VIEW_SINGLE\n  #define CC_SURFACES_DEBUG_VIEW_SINGLE 1\n#endif\n#ifndef CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC\n  #define CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC 2\n#endif\nstruct StandardVertInput {\n  highp vec4 position;\n  vec3 normal;\n  vec4 tangent;\n};\nattribute vec3 a_position;\nattribute vec3 a_normal;\nattribute vec2 a_texCoord;\nattribute vec4 a_tangent;\n#if CC_USE_SKINNING\n    attribute vec4 a_joints;\n  attribute vec4 a_weights;\n#endif\n#if USE_INSTANCING\n  #if CC_USE_BAKED_ANIMATION\n    attribute highp vec4 a_jointAnimInfo;\n  #endif\n  attribute vec4 a_matWorld0;\n  attribute vec4 a_matWorld1;\n  attribute vec4 a_matWorld2;\n  #if CC_USE_LIGHTMAP\n    attribute vec4 a_lightingMapUVParam;\n  #endif\n  #if CC_USE_REFLECTION_PROBE || CC_RECEIVE_SHADOW\n    #if CC_RECEIVE_SHADOW\n    #endif\n    attribute vec4 a_localShadowBiasAndProbeId;\n  #endif\n  #if CC_USE_REFLECTION_PROBE\n    attribute vec4 a_reflectionProbeData;\n  #endif\n  #if CC_USE_LIGHT_PROBE\n    attribute vec4 a_sh_linear_const_r;\n    attribute vec4 a_sh_linear_const_g;\n    attribute vec4 a_sh_linear_const_b;\n  #endif\n#endif\n#if CC_USE_MORPH\n    attribute float a_vertexId;\n#endif\nuniform highp vec4 cc_cameraPos;\nvarying vec2 v_uv;\nvoid main () {\n  StandardVertInput In;\n    In.position = vec4(a_position, 1.0);\n    In.normal = a_normal;\n    In.tangent = a_tangent;\n  In.position.xy = cc_cameraPos.w == 0.0 ? vec2(In.position.xy.x, -In.position.xy.y) : In.position.xy;\n  gl_Position = In.position;\n  v_uv = a_texCoord;\n}", "frag": "\nprecision highp float;\nprecision highp float;\nuniform mediump vec4 cc_screenSize;\n#define QUATER_PI         0.78539816340\n#define HALF_PI           1.57079632679\n#define PI                3.14159265359\n#define PI2               6.28318530718\n#define PI4               12.5663706144\n#define INV_QUATER_PI     1.27323954474\n#define INV_HALF_PI       0.63661977237\n#define INV_PI            0.31830988618\n#define INV_PI2           0.15915494309\n#define INV_PI4           0.07957747155\n#define EPSILON           1e-6\n#define EPSILON_LOWP      1e-4\n#define LOG2              1.442695\n#define EXP_VALUE         2.71828183\n#define FP_MAX            65504.0\n#define FP_SCALE          0.0009765625\n#define FP_SCALE_INV      1024.0\n#define GRAY_VECTOR       vec3(0.299, 0.587, 0.114)\n#define LIGHT_MAP_TYPE_DISABLED 0\n#define LIGHT_MAP_TYPE_ALL_IN_ONE 1\n#define LIGHT_MAP_TYPE_INDIRECT_OCCLUSION 2\n#define REFLECTION_PROBE_TYPE_NONE 0\n#define REFLECTION_PROBE_TYPE_CUBE 1\n#define REFLECTION_PROBE_TYPE_PLANAR 2\n#define REFLECTION_PROBE_TYPE_BLEND 3\n#define REFLECTION_PROBE_TYPE_BLEND_AND_SKYBOX 4\n#define LIGHT_TYPE_DIRECTIONAL 0.0\n#define LIGHT_TYPE_SPHERE 1.0\n#define LIGHT_TYPE_SPOT 2.0\n#define LIGHT_TYPE_POINT 3.0\n#define LIGHT_TYPE_RANGED_DIRECTIONAL 4.0\n#define IS_DIRECTIONAL_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_DIRECTIONAL)) < EPSILON_LOWP)\n#define IS_SPHERE_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_SPHERE)) < EPSILON_LOWP)\n#define IS_SPOT_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_SPOT)) < EPSILON_LOWP)\n#define IS_POINT_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_POINT)) < EPSILON_LOWP)\n#define IS_RANGED_DIRECTIONAL_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_RANGED_DIRECTIONAL)) < EPSILON_LOWP)\n#define TONE_MAPPING_ACES 0\n#define TONE_MAPPING_LINEAR 1\n#define SURFACES_MAX_TRANSMIT_DEPTH_VALUE 999999.0\n#ifndef CC_SURFACES_DEBUG_VIEW_SINGLE\n  #define CC_SURFACES_DEBUG_VIEW_SINGLE 1\n#endif\n#ifndef CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC\n  #define CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC 2\n#endif\n#if defined(CC_USE_METAL) || defined(CC_USE_WGPU)\n#define CC_HANDLE_SAMPLE_NDC_FLIP_STATIC(y) y = -y\n#else\n#define CC_HANDLE_SAMPLE_NDC_FLIP_STATIC(y)\n#endif\n#if CC_USE_FOG != 4\n#endif\n       uniform vec4 uvDepthToEyePosParams;\n       uniform vec4 radiusParam;\n       uniform vec4 miscParam;\n       uniform vec4 blurParam;\n#define NUM_STEPS 8.0\n#define NUM_STEPS_LOOP NUM_STEPS*2.0\n#define INV_NUM_DIRECTIONS 0.125\n#define NUM_DIRECTIONS 8\n#define CONSTANT_2PI 6.2831852\n#define g_AOResolution cc_screenSize.xy\n#define g_InvAOResolution cc_screenSize.zw\n#define g_UVToViewA uvDepthToEyePosParams.xy\n#define g_UVToViewB uvDepthToEyePosParams.zw\n#define g_R radiusParam.x\n#define g_R2 radiusParam.y\n#define g_NegInvR2 radiusParam.z\n#define g_MaxRadiusPixels radiusParam.w\n#define g_FocalLen miscParam.xy\n#define g_TanAngleBias miscParam.z\n#define g_Strength miscParam.w\nuniform sampler2D DepthTex;\nuniform sampler2D RandomTex;\n#define KERNEL_FALLOFF 3.0f\n#define KERNEL_RADIUS 8\n#define HALF_KERNEL_RADIUS 4\n#define g_InvFullResolution cc_screenSize.zw\n#define g_BlurFallOff blurParam.x\n#define g_BlurDepthThreshold blurParam.y\n#define g_BlurSimpleRadius blurParam.z\nuniform sampler2D AOTexNearest;\n#define AOTexBilinear AOTexNearest\n#define GetLinearDepthBilinear GetLinearDepth\n#define g_AOSaturation blurParam.w\nfloat Combine(vec2 uv)\n{\n  float ao = texture2D(AOTexNearest, uv).x;\n  return g_AOSaturation > 1.0 ? pow(ao, g_AOSaturation) : mix(1.0, ao, g_AOSaturation);\n}\nvarying vec2 v_uv;\nvoid main () {\n  float ao = Combine(v_uv);\n  gl_FragColor = vec4(1.0, 1.0, 1.0, ao);\n}"}, "builtins": {"globals": {"blocks": [{"name": "CCGlobal", "defines": []}, {"name": "CCCamera", "defines": []}], "samplerTextures": [], "buffers": [], "images": []}, "locals": {"blocks": [], "samplerTextures": [], "buffers": [], "images": []}, "statistics": {"CC_EFFECT_USED_VERTEX_UNIFORM_VECTORS": 42, "CC_EFFECT_USED_FRAGMENT_UNIFORM_VECTORS": 47}}, "defines": [{"name": "USE_INSTANCING", "type": "boolean", "defines": [], "editor": {"elevated": true}}, {"name": "CC_USE_SKINNING", "type": "boolean", "defines": []}, {"name": "CC_USE_BAKED_ANIMATION", "type": "boolean", "defines": ["USE_INSTANCING"]}, {"name": "CC_USE_LIGHTMAP", "type": "boolean", "defines": ["USE_INSTANCING"]}, {"name": "CC_USE_REFLECTION_PROBE", "type": "boolean", "defines": ["USE_INSTANCING"]}, {"name": "CC_RECEIVE_SHADOW", "type": "boolean", "defines": ["USE_INSTANCING"]}, {"name": "CC_USE_LIGHT_PROBE", "type": "boolean", "defines": ["USE_INSTANCING"]}, {"name": "CC_USE_MORPH", "type": "boolean", "defines": []}, {"name": "CC_USE_FOG", "type": "number", "defines": [], "range": [0, 4]}], "name": "pipeline/post-process/hbao|hbao-vs|combine-fs"}], "combinations": [], "hideInEditor": false}