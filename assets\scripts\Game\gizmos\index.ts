/**
 * Gizmo System Index
 * 
 * This file exports all components of the Gizmo system for easy importing
 * and provides utilities for setting up the gizmo system.
 */

// Core gizmo system
export { GizmoDrawer } from './GizmoDrawer';
export { GizmoManager } from './GizmoManager';

// Specific gizmo drawers
export { EmitterGizmo } from './EmitterGizmo';
export { EmitterArcGizmo } from './EmitterArcGizmo';

// Setup utilities
import { GizmoManager } from './GizmoManager';
import { EmitterGizmo } from './EmitterGizmo';
import { EmitterArcGizmo } from './EmitterArcGizmo';

/**
 * Setup the default gizmo system with common drawers
 * Call this once when your game starts or in the editor
 */
export function setupDefaultGizmos(): void {
    // Register default gizmo drawers
    GizmoManager.registerDrawer(new EmitterGizmo());
    GizmoManager.registerDrawer(new EmitterArcGizmo());
    
    console.log('Gizmo System: Default gizmos registered');
}

/**
 * Create a gizmo manager node and attach it to the scene
 * @param sceneName Optional scene name, if not provided uses current scene
 * @returns The created gizmo manager node
 */
export function createGizmoManagerNode(sceneName?: string): any | null {
    const { Node, find, director } = require('cc');

    let scene = null;
    if (sceneName) {
        scene = find(sceneName);
    } else {
        scene = director.getScene();
    }

    if (!scene) {
        console.error('GizmoSystem: Could not find scene to attach gizmo manager');
        return null;
    }

    // Create gizmo manager node
    const gizmoNode = new Node('GizmoManager');
    gizmoNode.addComponent(GizmoManager);

    // Add to scene
    scene.addChild(gizmoNode);

    console.log('GizmoSystem: Created gizmo manager node');
    return gizmoNode;
}

/**
 * Quick setup function that creates the gizmo manager and registers default drawers
 * @param sceneName Optional scene name
 * @returns The created gizmo manager node
 */
export function quickSetupGizmos(sceneName?: string): any | null {
    const gizmoNode = createGizmoManagerNode(sceneName);
    if (gizmoNode) {
        setupDefaultGizmos();
    }
    return gizmoNode;
}

/**
 * Version information for the Gizmo system
 */
export const GIZMO_SYSTEM_VERSION = "1.0.0";

/**
 * Example usage documentation
 * 
 * Basic setup in your game initialization:
 * ```typescript
 * import { quickSetupGizmos } from "./Game/gizmos";
 * 
 * // In your game startup code
 * quickSetupGizmos();
 * ```
 * 
 * Manual setup:
 * ```typescript
 * import { 
 *     GizmoManager, 
 *     EmitterArcGizmo, 
 *     EmitterGizmo,
 *     setupDefaultGizmos 
 * } from "./Game/gizmos";
 * 
 * // Create gizmo manager node manually
 * const gizmoNode = new Node('GizmoManager');
 * gizmoNode.addComponent(GizmoManager);
 * scene.addChild(gizmoNode);
 * 
 * // Register default drawers
 * setupDefaultGizmos();
 * 
 * // Or register custom drawers
 * const customEmitterGizmo = new EmitterArcGizmo();
 * customEmitterGizmo.configure({
 *     showRadius: true,
 *     showDirections: true,
 *     directionColor: Color.BLUE
 * });
 * GizmoManager.registerDrawer(customEmitterGizmo);
 * ```
 * 
 * Creating custom gizmo drawers:
 * ```typescript
 * import { GizmoDrawer } from "./Game/gizmos";
 * import { MyCustomComponent } from "./MyCustomComponent";
 * 
 * class MyCustomGizmo extends GizmoDrawer<MyCustomComponent> {
 *     public readonly componentType = MyCustomComponent;
 *     public readonly drawerName = "MyCustomGizmo";
 *     
 *     public drawGizmos(component: MyCustomComponent, graphics: Graphics, node: Node): void {
 *         // Your custom drawing logic here
 *         this.drawCross(graphics, 0, 0, 10, Color.GREEN);
 *     }
 * }
 * 
 * // Register your custom drawer
 * GizmoManager.registerDrawer(new MyCustomGizmo());
 * ```
 */
