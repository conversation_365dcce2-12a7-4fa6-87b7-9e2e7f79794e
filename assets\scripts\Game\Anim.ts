import { _decorator, Component, Node, find } from 'cc';
import { AnimFactory } from './factroy/AnimFactory';
import { PersistNode } from './PersistNode';
const { ccclass, property } = _decorator;

@ccclass('Anim')
export class Anim extends Component {
    animFactory: AnimFactory = null;

    onLoad(){
        this.animFactory = find("PersistNode").getComponent(PersistNode).animFactory;
    }
    
    recycle() {
        this.animFactory.recycleProduct(this.node);
    }
}

