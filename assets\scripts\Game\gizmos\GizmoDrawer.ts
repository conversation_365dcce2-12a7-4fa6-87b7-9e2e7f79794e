import { _decorator, Component, Graphics, Color, Node } from 'cc';
import { EDITOR } from 'cc/env';
import { GizmoUtils } from './GizmoUtils';

// Registry for auto-registration
const gizmoDrawerRegistry: Array<new () => GizmoDrawer> = [];

/**
 * Decorator to automatically register a gizmo drawer
 * Usage: @RegisterGizmoDrawer class MyGizmo extends GizmoDrawer { ... }
 */
export function RegisterGizmoDrawer<T extends new () => GizmoDrawer>(constructor: T): T {
    // Add to registry for auto-registration
    gizmoDrawerRegistry.push(constructor);
    if (EDITOR) {
        console.log(`GizmoDrawer: Registered ${constructor.name} for auto-registration`);
    }
    
    return constructor;
}

/**
 * Get all registered gizmo drawer constructors
 */
export function getRegisteredGizmoDrawers(): Array<new () => GizmoDrawer> {
    return [...gizmoDrawerRegistry];
}

/**
 * Auto-register all decorated gizmo drawers to a GizmoManager
 * This function should be called from GizmoManager to avoid circular dependencies
 */
export function autoRegisterGizmoDrawers(registerFunction: (drawer: GizmoDrawer) => void): void {
    for (const DrawerConstructor of gizmoDrawerRegistry) {
        try {
            const drawer = new DrawerConstructor();
            registerFunction(drawer);
        } catch (error) {
            console.error(`Failed to auto-register gizmo drawer ${DrawerConstructor.name}:`, error);
        }
    }

    if (EDITOR && gizmoDrawerRegistry.length > 0) {
        console.log(`GizmoDrawer: Auto-registered ${gizmoDrawerRegistry.length} gizmo drawers`);
    }
}

/**
 * Abstract base class for drawing gizmos for specific component types
 * This is not a component itself, but a drawer that can be registered to GizmoManager
 */
export abstract class GizmoDrawer<T extends Component = Component> {
    
    /**
     * The component type this drawer handles
     */
    public abstract readonly componentType: new (...args: any[]) => T;
    
    /**
     * Name of this gizmo drawer for debugging
     */
    public abstract readonly drawerName: string;
    
    /**
     * Whether this drawer is enabled
     */
    public enabled: boolean = true;
    
    /**
     * Draw gizmos for the given component
     * @param component The component to draw gizmos for
     * @param graphics The graphics component to draw with
     * @param node The node that contains the component
     */
    public abstract drawGizmos(component: T, graphics: Graphics, node: Node): void;
    
    /**
     * Check if this drawer can handle the given component
     * @param component The component to check
     * @returns true if this drawer can handle the component
     */
    public canHandle(component: Component): component is T {
        return component instanceof this.componentType;
    }
    
    /**
     * Called when the drawer is registered to the manager
     * Override this to perform any initialization
     */
    public onRegister(): void {
        if (EDITOR) {
            console.log(`GizmoDrawer: Registered ${this.drawerName}`);
        }
    }
    
    /**
     * Called when the drawer is unregistered from the manager
     * Override this to perform any cleanup
     */
    public onUnregister(): void {
        if (EDITOR) {
            console.log(`GizmoDrawer: Unregistered ${this.drawerName}`);
        }
    }
    
    /**
     * Get the priority of this drawer (higher priority draws last/on top)
     * Override this to change drawing order
     */
    public getPriority(): number {
        return 0;
    }
    
    /**
     * Helper method to draw a cross at the given position
     */
    protected drawCross(graphics: Graphics, x: number, y: number, size: number, color: Color): void {
        GizmoUtils.drawCross(graphics, x, y, size, color);
    }

    /**
     * Helper method to draw a circle
     */
    protected drawCircle(graphics: Graphics, x: number, y: number, radius: number, color: Color, filled: boolean = false): void {
        GizmoUtils.drawCircle(graphics, x, y, radius, color, filled);
    }

    /**
     * Helper method to draw an arrow
     */
    protected drawArrow(graphics: Graphics, startX: number, startY: number, endX: number, endY: number, color: Color, arrowSize: number = 8): void {
        GizmoUtils.drawArrow(graphics, startX, startY, endX, endY, color, arrowSize);
    }

    /**
     * Helper method to draw a line
     */
    protected drawLine(graphics: Graphics, startX: number, startY: number, endX: number, endY: number, color: Color, lineWidth: number = 1): void {
        GizmoUtils.drawLine(graphics, startX, startY, endX, endY, color, lineWidth);
    }

    /**
     * Helper method to draw a rectangle
     */
    protected drawRect(graphics: Graphics, x: number, y: number, width: number, height: number, color: Color, filled: boolean = false): void {
        GizmoUtils.drawRect(graphics, x, y, width, height, color, filled);
    }
    
    /**
     * Helper method to draw text (simple implementation)
     * Note: For more complex text rendering, consider using Label components
     */
    protected drawText(_graphics: Graphics, text: string, x: number, y: number, _color: Color): void {
        // This is a placeholder - in a real implementation you might want to use Label components
        // or a more sophisticated text rendering system
        if (EDITOR) {
            console.log(`Gizmo Text at (${x}, ${y}): ${text}`);
        }
    }
}
