import { _decorator, Component, Graphics, Color, Node } from 'cc';
import { EDITOR } from 'cc/env';

/**
 * Abstract base class for drawing gizmos for specific component types
 * This is not a component itself, but a drawer that can be registered to GizmoManager
 */
export abstract class GizmoDrawer<T extends Component = Component> {
    
    /**
     * The component type this drawer handles
     */
    public abstract readonly componentType: new (...args: any[]) => T;
    
    /**
     * Name of this gizmo drawer for debugging
     */
    public abstract readonly drawerName: string;
    
    /**
     * Whether this drawer is enabled
     */
    public enabled: boolean = true;
    
    /**
     * Draw gizmos for the given component
     * @param component The component to draw gizmos for
     * @param graphics The graphics component to draw with
     * @param node The node that contains the component
     */
    public abstract drawGizmos(component: T, graphics: Graphics, node: Node): void;
    
    /**
     * Check if this drawer can handle the given component
     * @param component The component to check
     * @returns true if this drawer can handle the component
     */
    public canHandle(component: Component): component is T {
        return component instanceof this.componentType;
    }
    
    /**
     * Called when the drawer is registered to the manager
     * Override this to perform any initialization
     */
    public onRegister(): void {
        if (EDITOR) {
            console.log(`GizmoDrawer: Registered ${this.drawerName}`);
        }
    }
    
    /**
     * Called when the drawer is unregistered from the manager
     * Override this to perform any cleanup
     */
    public onUnregister(): void {
        if (EDITOR) {
            console.log(`GizmoDrawer: Unregistered ${this.drawerName}`);
        }
    }
    
    /**
     * Get the priority of this drawer (higher priority draws last/on top)
     * Override this to change drawing order
     */
    public getPriority(): number {
        return 0;
    }
    
    /**
     * Helper method to draw a cross at the given position
     */
    protected drawCross(graphics: Graphics, x: number, y: number, size: number, color: Color): void {
        graphics.strokeColor = color;
        graphics.lineWidth = 2;
        
        graphics.moveTo(x - size, y);
        graphics.lineTo(x + size, y);
        graphics.moveTo(x, y - size);
        graphics.lineTo(x, y + size);
        graphics.stroke();
    }
    
    /**
     * Helper method to draw a circle
     */
    protected drawCircle(graphics: Graphics, x: number, y: number, radius: number, color: Color, filled: boolean = false): void {
        graphics.strokeColor = color;
        graphics.lineWidth = 1;
        
        if (filled) {
            graphics.fillColor = color;
            graphics.circle(x, y, radius);
            graphics.fill();
        } else {
            graphics.circle(x, y, radius);
            graphics.stroke();
        }
    }
    
    /**
     * Helper method to draw an arrow
     */
    protected drawArrow(graphics: Graphics, startX: number, startY: number, endX: number, endY: number, color: Color, arrowSize: number = 8): void {
        graphics.strokeColor = color;
        graphics.lineWidth = 2;
        
        // Draw main line
        graphics.moveTo(startX, startY);
        graphics.lineTo(endX, endY);
        
        // Calculate arrow head
        const dx = endX - startX;
        const dy = endY - startY;
        const length = Math.sqrt(dx * dx + dy * dy);
        
        if (length > 0) {
            const dirX = dx / length;
            const dirY = dy / length;
            
            // Arrow head angle (30 degrees)
            const arrowAngle = Math.PI / 6;
            
            // Left arrow point
            const leftX = endX - arrowSize * (dirX * Math.cos(arrowAngle) - dirY * Math.sin(arrowAngle));
            const leftY = endY - arrowSize * (dirY * Math.cos(arrowAngle) + dirX * Math.sin(arrowAngle));
            
            // Right arrow point
            const rightX = endX - arrowSize * (dirX * Math.cos(-arrowAngle) - dirY * Math.sin(-arrowAngle));
            const rightY = endY - arrowSize * (dirY * Math.cos(-arrowAngle) + dirX * Math.sin(-arrowAngle));
            
            // Draw arrow head
            graphics.moveTo(endX, endY);
            graphics.lineTo(leftX, leftY);
            graphics.moveTo(endX, endY);
            graphics.lineTo(rightX, rightY);
        }
        
        graphics.stroke();
    }
    
    /**
     * Helper method to draw text (simple implementation)
     * Note: For more complex text rendering, consider using Label components
     */
    protected drawText(graphics: Graphics, text: string, x: number, y: number, color: Color): void {
        // This is a placeholder - in a real implementation you might want to use Label components
        // or a more sophisticated text rendering system
        if (EDITOR) {
            console.log(`Gizmo Text at (${x}, ${y}): ${text}`);
        }
    }
}
