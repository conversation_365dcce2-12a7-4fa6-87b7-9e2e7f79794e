import { _decorator, Component, Node, director } from 'cc';
import { EDITOR } from 'cc/env';
import { quickSetupGizmos, GizmoManager } from './index';

const { ccclass, property } = _decorator;

/**
 * Helper component to automatically setup gizmos in a scene
 * Add this to any node in your scene to automatically initialize the gizmo system
 */
@ccclass('GizmoSetup')
export class GizmoSetup extends Component {
    
    @property
    public autoSetup: boolean = true;
    
    @property
    public setupInPlayMode: boolean = false;
    
    protected onLoad(): void {
        if (!EDITOR && !this.setupInPlayMode) return;
        
        if (this.autoSetup) {
            this.setupGizmos();
        }
    }
    
    /**
     * Setup the gizmo system
     */
    public setupGizmos(): void {
        // Check if gizmo manager already exists
        const existingManager = GizmoManager.getInstance();
        if (existingManager) {
            console.log('GizmoSetup: Gizmo manager already exists, skipping setup');
            return;
        }
        
        // Setup gizmos
        const gizmoNode = quickSetupGizmos();
        if (gizmoNode) {
            console.log('GizmoSetup: Gizmo system initialized successfully');
        } else {
            console.error('GizmoSetup: Failed to initialize gizmo system');
        }
    }
    
    /**
     * Manual setup method that can be called from editor or code
     */
    public static setupGizmosForScene(): void {
        const setup = new GizmoSetup();
        setup.setupGizmos();
    }
}

/**
 * Example usage:
 * 
 * 1. Add GizmoSetup component to any node in your scene
 * 2. Set autoSetup to true (default)
 * 3. The gizmo system will be automatically initialized when the scene loads
 * 
 * Or call manually:
 * ```typescript
 * import { GizmoSetup } from "./Game/gizmos/GizmoSetup";
 * 
 * // In your game initialization code
 * GizmoSetup.setupGizmosForScene();
 * ```
 */
