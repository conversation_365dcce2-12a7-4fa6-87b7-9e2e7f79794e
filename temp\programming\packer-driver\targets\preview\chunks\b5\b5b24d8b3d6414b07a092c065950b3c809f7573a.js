System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, instantiate, Global, Goods, PersistNode, GameFactory, GoodsFactory, _crd, ccclass, property;

  function _reportPossibleCrUseOfGlobal(extras) {
    _reporterNs.report("Global", "../Global", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGoods(extras) {
    _reporterNs.report("Goods", "../Goods", _context.meta, extras);
  }

  function _reportPossibleCrUseOfPersistNode(extras) {
    _reporterNs.report("PersistNode", "../PersistNode", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameFactory(extras) {
    _reporterNs.report("GameFactory", "./GameFactory", _context.meta, extras);
  }

  _export("GoodsFactory", void 0);

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      instantiate = _cc.instantiate;
    }, function (_unresolved_2) {
      Global = _unresolved_2.Global;
    }, function (_unresolved_3) {
      Goods = _unresolved_3.Goods;
    }, function (_unresolved_4) {
      PersistNode = _unresolved_4.PersistNode;
    }, function (_unresolved_5) {
      GameFactory = _unresolved_5.GameFactory;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "6a38bZeQ31DQa8x4DfIkP7T", "GoodsFactory", undefined);

      __checkObsolete__(['_decorator', 'Component', 'Node', 'instantiate']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("GoodsFactory", GoodsFactory = class GoodsFactory extends (_crd && GameFactory === void 0 ? (_reportPossibleCrUseOfGameFactory({
        error: Error()
      }), GameFactory) : GameFactory) {
        createProduct(productType) {
          var goodsTemp = null;

          if (this.productPool.size() > 0) {
            goodsTemp = this.productPool.get(); //如果池里有物资，就直接拿来用
          } else {
            goodsTemp = instantiate(this.persistNode.getComponent(_crd && PersistNode === void 0 ? (_reportPossibleCrUseOfPersistNode({
              error: Error()
            }), PersistNode) : PersistNode).goodsPreb); //从常驻节点拿到预制体原料
          }

          switch (productType) {
            case (_crd && Global === void 0 ? (_reportPossibleCrUseOfGlobal({
              error: Error()
            }), Global) : Global).BLOOD_GOODS:
              goodsTemp.getComponent(_crd && Goods === void 0 ? (_reportPossibleCrUseOfGoods({
                error: Error()
              }), Goods) : Goods).init(productType, this.persistNode.getComponent(_crd && PersistNode === void 0 ? (_reportPossibleCrUseOfPersistNode({
                error: Error()
              }), PersistNode) : PersistNode).bloodGoods);
              break;

            case (_crd && Global === void 0 ? (_reportPossibleCrUseOfGlobal({
              error: Error()
            }), Global) : Global).LIGHT_GOODS:
              goodsTemp.getComponent(_crd && Goods === void 0 ? (_reportPossibleCrUseOfGoods({
                error: Error()
              }), Goods) : Goods).init(productType, this.persistNode.getComponent(_crd && PersistNode === void 0 ? (_reportPossibleCrUseOfPersistNode({
                error: Error()
              }), PersistNode) : PersistNode).lightGoods);
              break;

            case (_crd && Global === void 0 ? (_reportPossibleCrUseOfGlobal({
              error: Error()
            }), Global) : Global).MISSILE_GOODS:
              goodsTemp.getComponent(_crd && Goods === void 0 ? (_reportPossibleCrUseOfGoods({
                error: Error()
              }), Goods) : Goods).init(productType, this.persistNode.getComponent(_crd && PersistNode === void 0 ? (_reportPossibleCrUseOfPersistNode({
                error: Error()
              }), PersistNode) : PersistNode).missileGoods);
              break;
          }

          return goodsTemp;
        }

      });

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=b5b24d8b3d6414b07a092c065950b3c809f7573a.js.map