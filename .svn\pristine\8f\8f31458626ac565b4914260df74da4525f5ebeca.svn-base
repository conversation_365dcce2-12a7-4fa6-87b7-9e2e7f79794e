import { _decorator, Component, Node, Vec3, Vec2, Rect, Camera, tween, Tween } from 'cc';
import { CameraSettings } from '../core/LevelData';
import { CameraFollowMode, RectUtils } from '../core/Types';

const { ccclass, property } = _decorator;

/**
 * Camera Manager handles camera movement, following, and viewport tracking
 */
@ccclass('CameraManager')
export class CameraManager extends Component {
    @property({ type: Camera })
    public camera: Camera | null = null;
    
    @property
    public autoScrollSpeed: number = 100; // Units per second
    
    @property
    public followSmoothing: number = 5.0; // Camera follow smoothing factor
    
    @property
    public boundaryPadding: number = 50; // Padding from camera bounds
    
    // Camera settings
    private cameraSettings: CameraSettings | null = null;
    
    // Movement state
    private velocity: Vec3 = Vec3.ZERO;
    private targetPosition: Vec3 = Vec3.ZERO;
    private followMode: CameraFollowMode = CameraFollowMode.AutoScroll;
    private followTarget: Node | null = null;
    
    // Bounds and constraints
    private movementBounds: Rect | null = null;
    private isConstrainedToBounds: boolean = false;
    
    // Viewport tracking
    private currentViewport: Rect = new Rect();
    private lastPosition: Vec3 = Vec3.ZERO;
    
    // Events
    public onPositionChanged?: (position: Vec3, viewport: Rect) => void;
    public onBoundsReached?: (bounds: Rect, direction: Vec2) => void;
    
    protected onLoad(): void {
        if (!this.camera) {
            this.camera = this.getComponent(Camera) || Camera.main;
        }
        
        if (this.camera) {
            this.lastPosition = this.camera.node.position.clone();
            this.targetPosition = this.lastPosition.clone();
            this.updateViewport();
        }
    }
    
    protected start(): void {
        console.log('CameraManager initialized');
    }
    
    protected update(deltaTime: number): void {
        if (!this.camera) return;
        
        // Update camera movement based on follow mode
        this.updateCameraMovement(deltaTime);
        
        // Apply movement constraints
        this.applyMovementConstraints();
        
        // Update velocity tracking
        this.updateVelocityTracking(deltaTime);
        
        // Update viewport
        this.updateViewport();
        
        // Check for position changes
        this.checkPositionChanges();
    }
    
    /**
     * Initialize camera with settings
     */
    public initializeCamera(settings: CameraSettings): void {
        this.cameraSettings = settings;
        
        if (this.camera) {
            // Apply viewport size
            if (settings.viewportSize) {
                this.camera.orthoHeight = settings.viewportSize.height / 2;
            }
            
            // Set scroll speed
            this.autoScrollSpeed = settings.scrollSpeed || this.autoScrollSpeed;
            
            // Set smoothing
            this.followSmoothing = settings.smoothing || this.followSmoothing;
            
            // Set bounds
            if (settings.bounds) {
                this.setMovementBounds(settings.bounds);
            }
            
            // Set follow target
            if (settings.followTarget) {
                this.setFollowTarget(settings.followTarget);
            }
        }
        
        console.log('Camera initialized with settings');
    }
    
    /**
     * Set camera follow mode
     */
    public setFollowMode(mode: CameraFollowMode): void {
        this.followMode = mode;
        
        switch (mode) {
            case CameraFollowMode.AutoScroll:
                this.velocity = new Vec3(0, this.autoScrollSpeed, 0);
                break;
            case CameraFollowMode.None:
                this.velocity = Vec3.ZERO;
                break;
            default:
                // Other modes will be handled in updateCameraMovement
                break;
        }
        
        console.log(`Camera follow mode set to: ${mode}`);
    }
    
    /**
     * Set follow target by name or node
     */
    public setFollowTarget(target: string | Node): void {
        if (typeof target === 'string') {
            // Find node by name
            this.followTarget = this.node.scene?.getChildByName(target) || null;
        } else {
            this.followTarget = target;
        }
        
        if (this.followTarget) {
            this.setFollowMode(CameraFollowMode.Entity);
            console.log(`Camera following target: ${this.followTarget.name}`);
        } else {
            console.warn(`Follow target not found: ${target}`);
        }
    }
    
    /**
     * Set movement bounds for camera
     */
    public setMovementBounds(bounds: Rect): void {
        this.movementBounds = bounds;
        this.isConstrainedToBounds = true;
        console.log('Camera movement bounds set');
    }
    
    /**
     * Remove movement bounds
     */
    public removeMovementBounds(): void {
        this.movementBounds = null;
        this.isConstrainedToBounds = false;
        console.log('Camera movement bounds removed');
    }
    
    /**
     * Move camera to position with optional smoothing
     */
    public moveTo(position: Vec3, smooth: boolean = true, duration: number = 1.0): void {
        if (!this.camera) return;
        
        this.targetPosition = position.clone();
        
        if (smooth) {
            // Use tween for smooth movement
            Tween.stopAllByTarget(this.camera.node);
            tween(this.camera.node)
                .to(duration, { position: this.targetPosition })
                .start();
        } else {
            // Immediate movement
            this.camera.node.position = this.targetPosition.clone();
        }
    }
    
    /**
     * Get current camera velocity
     */
    public getVelocity(): Vec3 {
        return this.velocity.clone();
    }
    
    /**
     * Get current camera viewport in world space
     */
    public getViewport(): Rect {
        return new Rect(
            this.currentViewport.x,
            this.currentViewport.y,
            this.currentViewport.width,
            this.currentViewport.height
        );
    }
    
    /**
     * Get camera position
     */
    public getPosition(): Vec3 {
        return this.camera?.node.position.clone() || Vec3.ZERO;
    }
    
    /**
     * Check if a point is visible in camera
     */
    public isPointVisible(point: Vec3): boolean {
        return this.currentViewport.contains(new Vec2(point.x, point.y));
    }
    
    /**
     * Check if a rectangle is visible in camera
     */
    public isRectVisible(rect: Rect): boolean {
        return RectUtils.intersects(this.currentViewport, rect);
    }
    
    /**
     * Update camera movement based on follow mode
     */
    private updateCameraMovement(deltaTime: number): void {
        if (!this.camera) return;
        
        switch (this.followMode) {
            case CameraFollowMode.AutoScroll:
                this.updateAutoScroll(deltaTime);
                break;
                
            case CameraFollowMode.Player:
            case CameraFollowMode.Entity:
                this.updateFollowTarget(deltaTime);
                break;
                
            case CameraFollowMode.Path:
                this.updatePathFollow(deltaTime);
                break;
                
            case CameraFollowMode.None:
                // No automatic movement
                break;
        }
    }
    
    /**
     * Update auto-scroll movement
     */
    private updateAutoScroll(deltaTime: number): void {
        if (!this.camera) return;
        
        const movement = this.velocity.clone().multiplyScalar(deltaTime);
        const newPosition = this.camera.node.position.clone().add(movement);
        this.camera.node.position = newPosition;
    }
    
    /**
     * Update follow target movement
     */
    private updateFollowTarget(deltaTime: number): void {
        if (!this.camera || !this.followTarget) return;
        
        const targetPos = this.followTarget.position;
        const currentPos = this.camera.node.position;
        
        // Calculate smooth follow
        const difference = targetPos.clone().subtract(currentPos);
        const smoothedMovement = difference.multiplyScalar(this.followSmoothing * deltaTime);
        
        const newPosition = currentPos.add(smoothedMovement);
        this.camera.node.position = newPosition;
        
        // Update velocity for tracking
        this.velocity = smoothedMovement.multiplyScalar(1 / deltaTime);
    }
    
    /**
     * Update path following (placeholder)
     */
    private updatePathFollow(deltaTime: number): void {
        // TODO: Implement path following when Path system is ready
    }
    
    /**
     * Apply movement constraints
     */
    private applyMovementConstraints(): void {
        if (!this.camera || !this.isConstrainedToBounds || !this.movementBounds) return;
        
        const position = this.camera.node.position;
        const viewport = this.getViewportSize();
        
        // Calculate constrained position
        let constrainedX = position.x;
        let constrainedY = position.y;
        
        // X constraints
        const minX = this.movementBounds.x + viewport.width / 2 + this.boundaryPadding;
        const maxX = this.movementBounds.x + this.movementBounds.width - viewport.width / 2 - this.boundaryPadding;
        
        if (constrainedX < minX) {
            constrainedX = minX;
            this.onBoundsReached?.(this.movementBounds, new Vec2(-1, 0));
        } else if (constrainedX > maxX) {
            constrainedX = maxX;
            this.onBoundsReached?.(this.movementBounds, new Vec2(1, 0));
        }
        
        // Y constraints
        const minY = this.movementBounds.y + viewport.height / 2 + this.boundaryPadding;
        const maxY = this.movementBounds.y + this.movementBounds.height - viewport.height / 2 - this.boundaryPadding;
        
        if (constrainedY < minY) {
            constrainedY = minY;
            this.onBoundsReached?.(this.movementBounds, new Vec2(0, -1));
        } else if (constrainedY > maxY) {
            constrainedY = maxY;
            this.onBoundsReached?.(this.movementBounds, new Vec2(0, 1));
        }
        
        // Apply constraints
        this.camera.node.position = new Vec3(constrainedX, constrainedY, position.z);
    }
    
    /**
     * Update velocity tracking
     */
    private updateVelocityTracking(deltaTime: number): void {
        if (!this.camera || deltaTime <= 0) return;
        
        const currentPosition = this.camera.node.position;
        const movement = currentPosition.clone().subtract(this.lastPosition);
        
        // Only update velocity if not following (following updates velocity separately)
        if (this.followMode === CameraFollowMode.AutoScroll || this.followMode === CameraFollowMode.None) {
            this.velocity = movement.multiplyScalar(1 / deltaTime);
        }
        
        this.lastPosition = currentPosition.clone();
    }
    
    /**
     * Update viewport rectangle
     */
    private updateViewport(): void {
        if (!this.camera) return;
        
        const position = this.camera.node.position;
        const size = this.getViewportSize();
        
        this.currentViewport = new Rect(
            position.x - size.width / 2,
            position.y - size.height / 2,
            size.width,
            size.height
        );
    }
    
    /**
     * Get viewport size in world units
     */
    private getViewportSize(): { width: number; height: number } {
        if (!this.camera) return { width: 1920, height: 1080 };
        
        const orthoHeight = this.camera.orthoHeight || 10;
        const aspect = 16 / 9; // Should be configurable
        const orthoWidth = orthoHeight * aspect;
        
        return {
            width: orthoWidth * 2,
            height: orthoHeight * 2
        };
    }
    
    /**
     * Check for position changes and trigger events
     */
    private checkPositionChanges(): void {
        if (!this.camera) return;
        
        const currentPos = this.camera.node.position;
        const threshold = 0.1; // Minimum movement to trigger event
        
        if (Vec3.distance(currentPos, this.lastPosition) > threshold) {
            this.onPositionChanged?.(currentPos.clone(), this.getViewport());
        }
    }
}
