import { _decorator, Component, Node, Sprite<PERSON>rame, Sprite, find, Vec3, Collider2D, Contact2DType, IPhysics2DContact, ProgressBar } from 'cc';
import { GameFactory } from './factroy/GameFactory';
import { Global } from './Global';
import { PersistNode } from './PersistNode';
import { Player } from './Player';
const { ccclass, property } = _decorator;

@ccclass('Goods')
export class Goods extends Component {
    
    goodType: string = null;

    bloodGoodsMoveSpeed: number = 0;    //加血物资移动速度

    lightGoodsMoveSpeed: number = 0;      //激光物资移动速度

    missileGoodsMoveSpeed: number = 0;      //导弹物资移动速度

    persistNode: Node = null;

    goodsFactory: GameFactory = null;

    curPos: Vec3 = null;

    onLoad() {
        this.persistNode = find("PersistNode");
        this.goodsFactory = this.persistNode.getComponent(PersistNode).goodsFactory;

        //拿到面板值
        this.bloodGoodsMoveSpeed = this.persistNode.getComponent(PersistNode).bloodGoodsMoveSpeed;
        this.lightGoodsMoveSpeed = this.persistNode.getComponent(PersistNode).lightGoodsMoveSpeed;
        this.missileGoodsMoveSpeed = this.persistNode.getComponent(PersistNode).missileGoodsMoveSpeed;

        let collider = this.node.getComponent(Collider2D);

        if (collider) {
            collider.on(Contact2DType.BEGIN_CONTACT, this.onBeginContact, this);
        }
    }

    onBeginContact(selfCollider: Collider2D, otherCollider: Collider2D, contact: IPhysics2DContact | null) {

        //判断吃到了那种道具
        if (this.goodType == Global.BLOOD_GOODS) {
            otherCollider.node.getChildByName("Blood").getComponent(ProgressBar).progress = 1;
            otherCollider.node.getComponent(Player).planeBlood = otherCollider.node.getComponent(Player).planeTotalBlood;
        } else if (this.goodType == Global.LIGHT_GOODS) {
            otherCollider.node.getComponent(Player).isShootLight = true;
        } else if (this.goodType == Global.MISSILE_GOODS) {
            otherCollider.node.getComponent(Player).isShootMissile = true;
        }

        this.goodsFactory.recycleProduct(this.node);
    }

    /**
     * 物资初始化函数
     */
     init(goodType: string, spriteFrame: SpriteFrame) {
        this.goodType = goodType;
        this.node.getComponent(Sprite).spriteFrame = spriteFrame;
    }

    update(deltaTime: number) {
        if (this.goodType == Global.BLOOD_GOODS) {     
            this.bloodGoodsMove(deltaTime);            
        } else if (this.goodType == Global.LIGHT_GOODS) {      
            this.lightGoodsMove(deltaTime);
        } else if (this.goodType == Global.MISSILE_GOODS) {      
            this.missileGoodsMove(deltaTime);
        }
    }

    /**
     * 导弹物资移动
     * @param deltaTime 
     */
    missileGoodsMove(deltaTime: number) {
        this.curPos = this.node.getPosition();
        this.curPos.y -= this.missileGoodsMoveSpeed * deltaTime;
        this.node.setPosition(this.curPos);

        if (this.curPos.y < -Global.HEIGHT / 2) {
            this.goodsFactory.recycleProduct(this.node);
        }
    }

    /**
     * 激光物资移动
     * @param deltaTime 
     */
    lightGoodsMove(deltaTime: number) {
        this.curPos = this.node.getPosition();
        this.curPos.y -= this.lightGoodsMoveSpeed * deltaTime;
        this.node.setPosition(this.curPos);

        if (this.curPos.y < -Global.HEIGHT / 2) {
            this.goodsFactory.recycleProduct(this.node);
        }
    }


    /**
     * 加血物资移动
     * @param deltaTime 
     */
    bloodGoodsMove(deltaTime: number) {
        this.curPos = this.node.getPosition();
        this.curPos.y -= this.bloodGoodsMoveSpeed * deltaTime;
        this.node.setPosition(this.curPos);

        if (this.curPos.y < -Global.HEIGHT / 2) {
            this.goodsFactory.recycleProduct(this.node);
        }
    }
}
